import { useAccess } from "@umijs/max";
interface IProps {
  // 页面地址
  pageUrl: string;
  // 权限代码
  code: string;
  children?: React.ReactNode;
  noAuthRender?: React.ReactNode;
}
interface Model_Props {
  // 页面地址
  pageUrl: string;
}
export const useModelAuth = (props: Model_Props) => {
  const { pageUrl } = props;
  const { auth } = useAccess();
  const pageAccess = auth?.btn_model?.[pageUrl] || [];
  return (code: string) => {
    const hasAccess = pageAccess?.includes(code);
    return hasAccess;
  };
};

const AuthButton = (props: IProps) => {
  const { code, children, pageUrl, noAuthRender = null } = props;
  const { auth } = useAccess();
  const pageAccess = auth?.btn_model?.[pageUrl] || [];
  const hasAccess = pageAccess?.includes(code);
  return <>{hasAccess ? children : noAuthRender}</>;
};
//按钮鉴权
export default AuthButton;

// 单独获取页面权限
export const Page_Auths = (props: Model_Props) => {
  const { pageUrl } = props;
  const { auth } = useAccess();
  const pageAccess = auth?.btn_model?.[pageUrl] || [];
  return pageAccess;
};
