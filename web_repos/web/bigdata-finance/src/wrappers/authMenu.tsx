import type { MenuDataItem } from "@ant-design/pro-components";

// 剔除无权限的菜单页面
const excludeNoAuth = (routes: MenuDataItem[], auth: string[]) => {
  return routes.reduce((total, cur) => {
    const { children, path } = cur;
    if (auth.includes(path!)) {
      total.push({ ...cur, children: excludeNoAuth(children || [], auth) });
    }
    return total;
  }, []);
};
//菜单鉴权
export default (menuData: MenuDataItem[], auth: string[]) => {
  // 默认的页面
  const defaultPage = ["/home", "/login", "/loginerror", "/"];
  const menuPage = [...defaultPage, ...auth];
  return excludeNoAuth(menuData, menuPage);
};
