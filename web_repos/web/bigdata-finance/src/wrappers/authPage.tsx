import { Outlet, useAccess, useLocation } from "@umijs/max";
import NoAuth from "@/pages/403";

//页面鉴权
export default () => {
  const { auth } = useAccess();
  const { pathname } = useLocation();
  const pageAuth = auth["page"];
  if (pathname.split("/").length >= 4) {
    // 目前路由只有 一级 二级
    // 当 二级有权限 ，则 二级下的三级默认有权限
    const ifLevel2 = pageAuth.findIndex((ele: string) => {
      if (pathname?.startsWith(ele) && ele?.split("/").length >= 3) {
        return true;
      }
      return false;
    });
    if (ifLevel2 > -1) {
      return <Outlet />;
    }
  }

  if (pageAuth.includes(pathname)) {
    return <Outlet />;
  } else {
    return <NoAuth />;
  }
};
