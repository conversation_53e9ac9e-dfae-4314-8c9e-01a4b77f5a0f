import apis from "@/services/management/globalOptions";
import { useEffect, useState } from "react";
import { returnTargetObject } from "@/utils";
interface IProps {
  dimName: string;
  pageNo?: number;
  pageSize?: number;
}
export default (props: IProps) => {
  const { dimName } = props;
  if (!dimName) return [[], {}];
  const [result, setResult] = useState([[], {}]);
  const getList = async () => {
    const { status, entry } = await apis.getList({ pageSize: 10000, ...props });
    if (status) {
      const opt = (entry || []).filter(item => item.enable).map(item => ({ label: item.optionName, value: item.optionId }));
      const optObj = returnTargetObject(opt);
      setResult([opt, optObj]);
    } else {
      setResult([[], {}]);
    }
  };
  useEffect(() => {
    if (dimName) getList();
  }, [dimName]);
  return result;
};
