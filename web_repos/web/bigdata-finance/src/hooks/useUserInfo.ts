/**
 * @file 用户信息
 */
// 用户信息
import apis from "@/services/users";
import { navToLogin } from "@/utils";
export default async () => {
  /* 用户信息 */
  let userInfo = {};
  const getLoginout = async () => {
    const { status } = await apis.loginout();
    if (status) {
      navToLogin();
    }
  };
  try {
    const { status, entry } = await apis.getUserInfo();
    userInfo = status ? entry : {};
  } catch (e) {
    console.error(e);
  }
  return {
    userInfo: userInfo,
    loginout: getLoginout
  };
};
