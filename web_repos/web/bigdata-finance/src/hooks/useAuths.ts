/**
 * 用户权限
 */
import apisUser from "@/services/management/users";
import { writePermissionFormat, readPermissionFormat } from "@/utils/auths";
import { flattenTree } from "@/utils";
export default async params => {
  // 存放处理后的 服务器返回的 一级页面/二级页面 路由信息
  let authList = [];
  // 存放 页面的模块和按钮权限信息
  let routesModuleAuth: Recordable<Record<string, any>> = {};
  // 根据 页面的 path 获取 页面用于的 模块、按钮、指标 权限
  let moduleAndBtnAuth: Recordable<Record<string, any>> = {};
  const { status, entry } = await apisUser.queryUserAuthorizedResources(params);
  if (status) {
    authList = (entry || []).map(item => {
      const { children = [] } = item;
      for (let level2Children of children) {
        const { children: level3Children } = level2Children;
        for (let level3Child of level3Children) {
          const path = level3Child.path;
          routesModuleAuth[path] = level3Child.children;
          level3Child.children = [];
        }
      }
      return { ...item, path: item.path };
    });
    for (let module in routesModuleAuth) {
      if (routesModuleAuth.hasOwnProperty(module)) {
        const item = routesModuleAuth[module];
        // 将页面的 权限数据 进行平铺 返回 模块、按钮、指标的 code
        moduleAndBtnAuth[module] = flattenTree(item, "children").map(item => {
          const { type, readPermission, writePermission, code } = item;
          let result = code;
          if (type === 4) {
            if (readPermission) {
              result += readPermissionFormat;
            }
            if (writePermission) {
              result += writePermissionFormat;
            }
          }
          return result;
        });
      }
    }
  }
  return {
    preRoutersAuthed: authList,
    auth: {
      btn_model: moduleAndBtnAuth,
      page: flattenTree(authList, "children").map(item => item.path)
    }
  };
};
