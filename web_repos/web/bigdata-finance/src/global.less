@import "./assets/styles/common.less";
@import "./assets/styles/global.less";
@import "./assets/styles/reset.less";

abbr,
address,
article,
aside,
audio,
b,
blockquote,
body,
canvas,
caption,
cite,
code,
dd,
del,
details,
dfn,
div,
dl,
dt,
em,
fieldset,
figcaption,
figure,
footer,
form,
h1,
h2,
h3,
h4,
h5,
h6,
header,
hgroup,
html,
i,
iframe,
img,
ins,
kbd,
label,
legend,
li,
mark,
menu,
nav,
object,
ol,
p,
pre,
q,
samp,
section,
small,
span,
strong,
sub,
summary,
sup,
table,
tbody,
td,
tfoot,
th,
thead,
time,
tr,
ul,
var,
video {
  margin: 0;
  padding: 0;
  border: 0;
  outline: 0;
  background: transparent;
  box-sizing: border-box;
}

html,
body,
#root {
  height: 100%;
}

html,
body {
  /* 定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸 */
  ::-webkit-scrollbar {
    width: 0;
    height: 6px;
    background-color: #fff;
  }

  /* 定义滑块 内阴影+圆角 */
  ::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background-color: #e5e6eb;

    &:hover {
      background-color: #c9cdd4;
    }
  }
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Helvetica Neue", Helvetica, "Segoe UI", Arial, Roboto, "PingFang SC", miui, "Hiragino Sans GB",
    "Microsoft Yahei", sans-serif;
  // background-color: red;
  font-size: 14px;
}

a {
  word-break: keep-all;
  text-decoration: none;
}

input,
button,
select,
textarea {
  outline: none;
  border: none;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

a,
button,
input,
optgroup,
select,
textarea {
  -webkit-tap-highlight-color: rgb(0 0 0 / 0%);

  /* 去掉a、input和button点击时的蓝色外边框和灰色半透明背景 */
}

img {
  object-fit: cover;
}
