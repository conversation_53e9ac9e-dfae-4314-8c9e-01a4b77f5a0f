import { NOVALUE, SIZE } from "@/utils/constant";
import { returnEllipsisTooltip, returnTargetObject } from "@/utils/index";
import { Space, Button, Tag } from "antd";
// 审核状态
export const audit_status_has = 1;
export const audit_status_un = 0;
export const auditStatusOptions = [
  {
    label: "已审核",
    value: audit_status_has
  },
  {
    label: "待审核",
    value: audit_status_un
  }
];
const auditStatusObj = returnTargetObject(auditStatusOptions);
// 审核结果
export const audit_result_pass = 1;
export const audit_result_reject = 2;
export const auditResultOptions = [
  {
    label: "通过",
    value: audit_result_pass
  },
  {
    label: "未通过",
    value: audit_result_reject
  }
];

const auditResultObj = returnTargetObject(auditResultOptions);
const columns_search = ({}: any): Array<TableListItem> => {
  return [
    {
      title: "文件名称",
      dataIndex: "fileName",
      hideInTable: true
    },
    {
      title: "上传人",
      dataIndex: "uploadCrew",
      hideInTable: true
    },
    {
      title: "审核人",
      dataIndex: "auditCrew",
      hideInTable: true
    },
    {
      title: "审核状态",
      dataIndex: "auditStatusList",
      hideInTable: true,
      valueType: "checkbox",
      fieldProps: {
        options: auditStatusOptions,
        style: { minWidth: "210px" }
      }
    },
    {
      title: "上传时间",
      dataIndex: "gmtUpload",
      valueType: "dateRange",
      hideInTable: true
    },
    {
      title: "审核时间",
      dataIndex: "gmtAudit",
      valueType: "dateRange",
      hideInTable: true
    },
    {
      title: "审核结果",
      dataIndex: "auditResultList",
      hideInTable: true,
      valueType: "checkbox",
      fieldProps: {
        options: auditResultOptions,
        style: { minWidth: "210px" }
      }
    }
  ].map(item => {
    return {
      ...item,
      showInGroup: false
    };
  });
};
export const columns = ({ handlePass, handleReject, disableExamine }: any): Array<TableListItem> => {
  return [
    ...columns_search({}),
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      editable: false,
      hideInSearch: true,
      fixed: "left",
      showInGroup: false,
      render: (_, record, index) => {
        return index + 1;
      }
    },
    {
      title: "文件名称",
      dataIndex: "fileName",
      width: 200,
      hideInSearch: true,
      render(text, record) {
        const { fileUrl } = record;
        return (
          <Space>
            {fileUrl ? (
              <a
                onClick={() => {
                  window.open(fileUrl);
                }}
              >
                {returnEllipsisTooltip({ title: text, typographyProps: { underline: true, style: { color: "blue" } } })}
              </a>
            ) : (
              returnEllipsisTooltip({ title: text })
            )}
          </Space>
        );
      }
    },

    {
      title: "上传人",
      dataIndex: "uploadCrew",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "上传时间",
      dataIndex: "gmtUpload",
      width: 140,
      hideInSearch: true
    },
    {
      title: "审核人",
      dataIndex: "auditCrew",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "审核时间",
      dataIndex: "gmtAudit",
      width: 140,
      hideInSearch: true
    },
    {
      title: "审核状态",
      dataIndex: "auditStatus",
      width: 100,
      hideInSearch: true,
      render(text) {
        const cur = auditStatusObj[text] ?? "";
        return <>{cur !== "" ? <Tag color={text === audit_status_has ? "success" : "error"}>{cur}</Tag> : NOVALUE}</>;
      }
    },
    {
      title: "审核结果",
      dataIndex: "auditResult",
      width: 100,
      hideInSearch: true,
      render(text) {
        const cur = auditResultObj[text] ?? "";
        return <>{cur !== "" ? <Tag color={text === audit_result_pass ? "success" : "error"}>{cur}</Tag> : NOVALUE}</>;
      }
    },
    {
      title: "问题备注",
      dataIndex: "problemNote",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "操作",
      width: 100,
      valueType: "option",
      fixed: "right",
      showInGroup: false,
      render(_, record) {
        const { auditStatus } = record;
        return (
          <>
            {auditStatus === audit_status_has || disableExamine(record) ? null : (
              <Space size={[0, 0]}>
                <Button type="link" onClick={() => handlePass(record)}>
                  通过
                </Button>
                <Button type="link" danger size={SIZE} onClick={() => handleReject(record)}>
                  驳回
                </Button>
              </Space>
            )}
          </>
        );
      }
    }
  ].map(item => {
    const { title } = item;
    return {
      ...item,
      align: "center",
      title: returnEllipsisTooltip({ title })
    };
  });
};
