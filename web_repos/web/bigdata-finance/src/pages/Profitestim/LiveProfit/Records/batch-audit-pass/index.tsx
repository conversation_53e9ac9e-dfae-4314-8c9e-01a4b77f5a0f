import React from "react";
import { Modal, ModalProps } from "antd";
import { SIZE } from "@/utils/constant";
import apis from "@/services/Profitestim/auditrecords";
export interface IAuditProps extends ModalProps {
  params: {
    ids: any[];
    auditResult?: 1 | 2;
    // 1(直播利润预估) 2(短视频利润预估)
    recordType: 1 | 2;
  };
  onFresh?: () => void;
  onClose: () => void;
}
const Res: React.FC<IAuditProps> = props => {
  const { open, onClose, onFresh, title, params } = props;
  const handleCancel = () => {
    onClose?.();
  };
  const handleOk = async () => {
    const { status } = await apis.batchAudit(params);
    if (status) {
      onClose?.();
      onFresh?.();
      handleCancel();
    }
  };
  return (
    <>
      <Modal
        title={title}
        okButtonProps={{ size: SIZE }}
        cancelButtonProps={{ size: SIZE }}
        centered
        open={open}
        onOk={handleOk}
        onCancel={handleCancel}
      >
        审核通过后相关数据将应用到利润预估表中，请谨慎操作。
      </Modal>
    </>
  );
};

export default React.memo(Res);
