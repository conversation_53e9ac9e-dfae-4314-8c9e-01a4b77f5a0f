import React, { useEffect, useLayoutEffect, useState } from "react";
import { Tabs, Tooltip, Space } from "antd";
import KeepAlive, { useAliveController } from "react-activation";
import { SIZE, tabs_tabBarStyle } from "@/utils/constant";
import { useSearchParams, history } from "@umijs/max";
import { InfoCircleOutlined } from "@ant-design/icons";
import { ifEmptyObj } from "@/utils";
import Profitestim from "./Profitestim";
import ActualProfitestim from "./ActualProfitestim";
import Records from "./Records";
import { model_auths } from "./auths";
import { useModelAuth } from "@/wrappers/authButton";
export const FieldsContext = React.createContext<any>(null);
export const tab_profitestim = model_auths.model_profitestim_liveprofit_tab;
export const tab_actual_profitestim = model_auths.model_profitestim_actual_liveprofit_tab;
export const tab_records = model_auths.model_profitestim_records_tab;
export const PageUrl = "/profitestim/liveprofit";
interface IProps {
  urlParams: Record<string, any>;
}
const Res: React.FC<IProps> = props => {
  const authModel = useModelAuth({ pageUrl: PageUrl });
  const { urlParams } = props;
  const [curTab, setCurTab] = useState(() => {
    const { $_tab } = urlParams;
    if ($_tab) {
      return $_tab;
    }
    return tab_profitestim;
  });
  const [shouldRecordsRefresh, setShouldRecordsRefresh] = useState(false);
  const [treatedUrlParams, setTreatedUrlParams] = useState({});
  const tabItems = [
    {
      key: tab_profitestim,
      label: (
        <Space>
          利润预估
          <Tooltip title="基于直播24H的数据进行利润预估">
            <InfoCircleOutlined />
          </Tooltip>
        </Space>
      ),
      children: <Profitestim />
    },

    {
      key: tab_actual_profitestim,
      label: (
        <Space>
          实际利润预估
          <Tooltip title="基于直播后实际的动态数据进行利润预估">
            <InfoCircleOutlined />
          </Tooltip>
        </Space>
      ),
      children: <ActualProfitestim />
    },
    {
      key: tab_records,
      label: "操作审核",
      children: <Records />
    }
  ].filter(item => {
    return authModel(item.key);
  });
  useLayoutEffect(() => {
    if (!curTab) {
      setCurTab(tabItems?.[0]?.key || "");
    }
  }, [tabItems, curTab]);

  useEffect(() => {
    if (ifEmptyObj(urlParams)) {
      setTreatedUrlParams(urlParams);
    }
  }, [urlParams]);
  return (
    <FieldsContext.Provider value={{ urlParams: treatedUrlParams, setCurTab, shouldRecordsRefresh, setShouldRecordsRefresh }}>
      <Tabs activeKey={curTab} tabBarStyle={tabs_tabBarStyle} size={SIZE} onChange={setCurTab} items={tabItems} />
    </FieldsContext.Provider>
  );
};
/**
 * 上一个页面的状态
 * 用于比较 携带参数跳页 与 正常路由跳页时，页面进行缓存刷新
 */
const prePageStatus = {
  search: ""
};
const AliveRecord = () => {
  const [searchParams] = useSearchParams();
  const hasSearch = searchParams.size > 0;
  let urlParams: any = {};
  if (hasSearch) {
    const $_tab = searchParams.get("$_tab");
    const tagIdList = searchParams.get("tagIdList");
    const modelLoss = searchParams.get("modelLoss");
    const liveDate = searchParams.get("liveDate");
    const scopeType = searchParams.get("scopeType");
    urlParams.scopeType = scopeType;
    urlParams.$_tab = $_tab;
    if (liveDate === "false") {
      urlParams.liveDate = [];
    }
    if (tagIdList) {
      urlParams.tagIdList = [tagIdList];
    }
    if (modelLoss) {
      urlParams.modelLoss = modelLoss;
    }
  }
  let aliveCont = useAliveController();
  const { location } = history;
  // 当 url 上携带参数时，刷新 页面缓存
  if (location.search !== prePageStatus.search) {
    prePageStatus.search = location.search;
    aliveCont.refreshScope(PageUrl);
  }
  return (
    <KeepAlive name={PageUrl}>
      <Res urlParams={urlParams} />
    </KeepAlive>
  );
};
export default React.memo(AliveRecord);
