import React, { useState, useRef } from "react";
import TableList from "@/components/TableList";
import TargetConfig, { ColumnsState } from "@/components/target-config";
import { columns, advance_columns_search } from "./variable";
import { NOVALUE, Protable_FROM_CONFIG } from "@/utils/constant";
import apis from "@/services/Profitestim/liveprofit";
import dayjs from "dayjs";
import { PageUrl, tab_actual_profitestim } from "..";
import { splitSpaceComma } from "@/utils";
import useCommonOptions from "@/hooks/useCommonOptions";
const Res: React.FC<any> = () => {
  const tableFormRef = useRef<any>();
  const actionRef = useRef<any>();

  const [modelNameOptions, modelNameObj] = useCommonOptions({ dimName: "模式" });
  const [shopTypeOptions] = useCommonOptions({ dimName: "店铺性质" });
  const [columnsState, setColumnsState] = useState<Record<string, ColumnsState>>({});
  const handleTargetsChange = column => {
    setColumnsState(column);
  };
  const handlePageParmas = (params: any) => {
    const { liveDate, current, shopTypeList, stdCategory = {}, brandNameList, bigDepartmentList, ...rest } = params;
    if (typeof shopTypeList !== "undefined") {
      rest.shopTypeList = [shopTypeList];
    }
    if (liveDate?.length) {
      const [liveDateStart, liveDateEnd] = liveDate;
      rest.liveDateStart = dayjs(liveDateStart).format("YYYY-MM-DD");
      rest.liveDateEnd = dayjs(liveDateEnd).format("YYYY-MM-DD");
    }
    if (brandNameList) {
      rest.brandNameList = [brandNameList];
    }
    if (bigDepartmentList) {
      rest.bigDepartmentList = [bigDepartmentList];
    }
    rest.itemIdList = splitSpaceComma(rest.itemIdList);
    return { ...rest, ...stdCategory, pageNo: current };
  };
  const columnsProps = { modelNameOptions, modelNameObj };
  const advanceColumnsProps = { shopTypeOptions };
  return (
    <div>
      <TableList
        headerTitle="直播实际利润预估"
        formRef={tableFormRef}
        actionRef={actionRef}
        columnsState={{ value: columnsState }}
        columnEmptyText={NOVALUE}
        columns={columns(columnsProps)}
        scroll={{ x: "max-content", y: "calc(100vh - 420px)" }}
        form={{
          ...Protable_FROM_CONFIG,
          initialValues: {
            liveDate: [dayjs().subtract(6, "month"), dayjs()]
          }
        }}
        advanceColumns={advance_columns_search(advanceColumnsProps)}
        api={apis.getLiveActualForecastPage}
        downloadApi={apis.downloadLiveItemActualForecast}
        summaryApi={apis.getFinanceLiveItemActualForecastTotal}
        preFetch={handlePageParmas}
        toolbar={{
          actions: [
            <>
              <TargetConfig columns={columns(columnsProps)} pageUrl={PageUrl + tab_actual_profitestim} onChange={handleTargetsChange} />
            </>
          ]
        }}
        rowKey={"id"}
      />
    </div>
  );
};

export default React.memo(Res);
