import { Select } from "antd";
import { filterOptionLabel, returnEllipsisTooltip } from "@/utils/index";
import BrandDebounSelect from "@/components/BrandDebounSelect";
import DepartmentSelect from "@/components/DepartmentSelect";
import AnchorSelect from "@/components/AnchorSelect";
import { SIZE } from "@/utils/constant";
import CategorySelect from "@/components/CategorySelect";
import SupplierNameSelect from "@/components/SupplierNameSelect";
export const advance_columns_search = ({ shopTypeOptions }: any): Array<TableListItem> => {
  return [
    {
      title: "基础信息",
      valueType: "group",
      colProps: { span: 24 },
      columns: [
        {
          title: "二级模式",
          dataIndex: "secondaryModelName",
          hideInSearch: false,
          hideInTable: true
        },
        {
          title: "店铺名称",
          dataIndex: "shopName",
          hideInTable: true
        },
        {
          title: "店铺性质",
          dataIndex: "shopTypeList",
          hideInTable: true,
          renderFormItem() {
            return <Select placeholder="请选择" options={shopTypeOptions} filterOption={filterOptionLabel} showSearch allowClear />;
          }
        },
        {
          title: "大部门",
          dataIndex: "bigDepartmentList",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem: () => {
            return <DepartmentSelect key="bigDepartmentList" style={{ width: "100%" }} />;
          }
        },
        {
          title: "对接人",
          dataIndex: "purchasePrincipal",
          hideInTable: true
        },
        {
          title: "品牌",
          dataIndex: "brandNameList",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem() {
            return <BrandDebounSelect key="brandName" showSearch allowClear />;
          }
        },
        {
          title: "类目",
          dataIndex: "category",
          hideInTable: true
        },
        {
          title: "品类",
          dataIndex: "uploadCategory",
          hideInTable: true
        },
        {
          title: "商品编码",
          dataIndex: "itemCode",
          hideInTable: true
        }
      ]
    }
  ];
};
const columns_search = ({ modelNameOptions }: any): Array<TableListItem> => {
  return [
    {
      title: "直播日期",
      dataIndex: "liveDate",
      valueType: "dateRange",
      fieldProps: {
        allowClear: true
      },
      hideInTable: true
    },
    {
      title: "主播名称",
      dataIndex: "anchorNameList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem() {
        return <AnchorSelect showSearch allowClear maxTagCount="responsive" mode="multiple" />;
      }
    },
    {
      title: "商品ID",
      dataIndex: "itemIdList",
      hideInSearch: false,
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "商品名称",
      dataIndex: "itemTitle",
      hideInTable: true
    },
    {
      title: "模式",
      dataIndex: "modelIdList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem() {
        return (
          <Select
            placeholder="请选择"
            options={modelNameOptions}
            filterOption={filterOptionLabel}
            showSearch
            allowClear
            maxTagCount="responsive"
            mode="multiple"
          />
        );
      }
    },
    {
      title: "供应商名称",
      dataIndex: "supplierName",
      hideInTable: true,
      renderFormItem() {
        return <SupplierNameSelect />;
      }
    },
    {
      // 单选
      title: "商品类目",
      dataIndex: "stdCategory",
      hideInTable: true,
      renderFormItem() {
        return <CategorySelect />;
      }
    }
  ].map(item => {
    return {
      ...item,
      showInGroup: false
    };
  });
};
export const columns = ({ modelNameOptions, tagOptions }: any): Array<TableListItem> => {
  return [
    ...columns_search({ modelNameOptions, tagOptions }),
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      hideInSearch: true,
      fixed: "left",
      showInGroup: false,
      render: (_, record, index) => {
        return index + 1;
      }
    },
    {
      title: "直播日期",
      dataIndex: "liveDateStr",
      width: 120,
      fixed: "left",
      hideInSearch: true
    },

    {
      title: "主播ID",
      dataIndex: "anchorId",
      width: 120,
      fixed: "left",
      hideInSearch: true
    },
    {
      title: "主播名称",
      dataIndex: "anchorName",
      width: 120,
      fixed: "left",
      hideInSearch: true
    },
    {
      title: "商品ID",
      dataIndex: "itemId",
      width: 120,
      fixed: "left",
      hideInSearch: true
    },
    {
      title: "商品名称",
      dataIndex: "itemTitle",
      width: 160,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      // 筛选框
      title: "模式",
      dataIndex: "modelName",
      width: 120,
      hideInSearch: true,
      fieldProps: { size: SIZE },
      render(_) {
        return returnEllipsisTooltip({ title: _ });
      }
    },
    {
      title: "二级模式",
      dataIndex: "secondaryModelName",
      width: 120,
      hideInSearch: true
    },
    {
      title: "标签",
      dataIndex: "itemLabelIdStr",
      width: 120,
      groupName: "结算信息",
      hideInSearch: true
    },
    {
      title: "商家利润分成比例",
      dataIndex: "supplierProfitRateStr",
      width: 120,
      groupName: "结算信息",
      hideInSearch: true
    },
    {
      title: "主播净利润分成比例",
      dataIndex: "anchorProfitRateStr",
      width: 120,
      groupName: "结算信息",
      hideInSearch: true
    },
    {
      title: "店铺ID",
      dataIndex: "shopId",
      width: 120,
      hideInSearch: true
    },
    {
      title: "店铺名称",
      dataIndex: "shopName",
      width: 120,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "店铺性质",
      dataIndex: "shopTypeDesc",
      width: 120,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "供应商名称",
      dataIndex: "supplierName",
      width: 160,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "我司主体",
      dataIndex: "contractName",
      width: 160,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "大部门",
      dataIndex: "bigDepartment",
      width: 120,
      hideInSearch: true
    },
    {
      title: "提报部门",
      dataIndex: "submitDepartment",
      width: 120,
      hideInSearch: true
    },
    {
      title: "对接人",
      dataIndex: "purchasePrincipal",
      width: 120,
      hideInSearch: true
    },
    {
      title: "品牌",
      dataIndex: "brandName",
      width: 120,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "类目",
      dataIndex: "catName",
      width: 120,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },

    {
      // 文本
      title: "品类",
      dataIndex: "categoryName",
      width: 120,
      hideInSearch: true
    },
    {
      title: "一级类目",
      dataIndex: "catLv1Name",
      width: 120,
      hideInSearch: true
    },
    {
      title: "二级类目",
      dataIndex: "catLv2Name",
      width: 120,
      hideInSearch: true
    },
    {
      title: "三级类目",
      dataIndex: "catLv3Name",
      width: 120,
      hideInSearch: true
    },
    {
      title: "四级类目",
      dataIndex: "catLv4Name",
      width: 120,
      hideInSearch: true
    },
    {
      title: "商品编码",
      dataIndex: "itemCode",
      width: 120,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "发货内容（第一列为主产品）",
      dataIndex: "delieveryContent",
      width: 120,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "直播价格",
      dataIndex: "livePrice",
      width: 140,
      hideInSearch: true
    },
    {
      title: "支付件数",
      dataIndex: "payItemVolume",
      width: 140,
      groupName: "结算信息",
      hideInSearch: true
    },
    {
      title: "支付金额",
      dataIndex: "payItemAmountStr",
      width: 140,
      groupName: "结算信息",
      hideInSearch: true
    },
    {
      title: "更新支付订单数（加购）",
      dataIndex: "updateOrderVolume",
      width: 140,
      groupName: "结算信息",
      hideInSearch: true
    },
    {
      title: "已结算件数",
      dataIndex: "settleItemVolume",
      width: 140,
      groupName: "结算信息",
      hideInSearch: true
    },
    {
      title: "已结算金额",
      dataIndex: "settleItemAmountStr",
      width: 140,
      groupName: "结算信息",
      hideInSearch: true
    },
    {
      title: "预估待结算件数",
      dataIndex: "preSettleItemVolume",
      width: 140,
      groupName: "结算信息",
      hideInSearch: true
    },
    {
      title: "预估待结算金额",
      dataIndex: "preSettleItemAmountStr",
      width: 140,
      groupName: "结算信息",
      hideInSearch: true
    },
    {
      title: "总预估结算件数",
      dataIndex: "totalSettleItemVolume",
      width: 140,
      groupName: "结算信息",
      hideInSearch: true
    },
    {
      title: "总预估结算金额",
      dataIndex: "totalSettleItemAmountStr",
      width: 140,
      groupName: "结算信息",
      hideInSearch: true
    },
    {
      title: "预估结算退款率",
      dataIndex: "preRefundRateStr",
      width: 140,
      groupName: "结算信息",
      hideInSearch: true
    },
    {
      title: "线上佣金比例",
      dataIndex: "onlineCommisionRateStr",
      width: 140,
      groupName: "结算信息",
      hideInSearch: true
    },
    {
      title: "线下佣金比例",
      dataIndex: "offlineCommisionRateStr",
      width: 140,
      groupName: "结算信息",
      hideInSearch: true
    },
    {
      title: "总线上佣金金额",
      dataIndex: "onlineCommisionAmountStr",
      width: 140,
      groupName: "结算信息",
      hideInSearch: true
    },
    {
      title: "总线下佣金金额",
      dataIndex: "offlineCommisionAmountStr",
      width: 140,
      groupName: "结算信息",
      hideInSearch: true
    },
    {
      // 填，数字框整数
      title: "辛选网络、广州和翊合同约定链接费",
      dataIndex: "xxwlgzhxFeeStr",
      width: 140,
      groupName: "结算信息",
      hideInSearch: true
    },
    {
      // 填，数字框整数
      title: "辛选网络、广州和翊未达标，应收链接费",
      dataIndex: "xxwlgzhxwdbFeeStr",
      width: 140,
      groupName: "结算信息",
      hideInSearch: true
    },
    {
      // 填，数字框整数
      title: "年框合同、预估可收链接费",
      dataIndex: "nkhtygLinkFeeStr",
      width: 140,
      groupName: "结算信息",
      hideInSearch: true
    },
    {
      title: "预估链接费金额",
      dataIndex: "predictLinkFeeStr",
      width: 140,
      groupName: "结算信息",
      hideInSearch: true
    },
    {
      title: "快手技术服务费承担方备注",
      dataIndex: "undertakerRemarkStr",
      width: 100,
      groupName: "成本信息",
      hideInSearch: true
    },

    {
      title: "主品成本单价",
      dataIndex: "mainProductCostPriceStr",
      width: 140,
      groupName: "成本信息",
      hideInSearch: true
    },
    {
      title: "赠品成本单价",
      dataIndex: "giveawayProductCostPriceStr",
      width: 140,
      groupName: "成本信息",
      hideInSearch: true
    },
    {
      title: "赠品成本承担方",
      dataIndex: "giftCostBearerStr",
      width: 140,
      groupName: "成本信息",
      hideInSearch: true
    },
    {
      title: "赠品免单数量",
      dataIndex: "giftFreeVolume",
      width: 140,
      groupName: "成本信息",
      hideInSearch: true
    },
    {
      title: "赠品免单承担方",
      dataIndex: "giftFreeBearerStr",
      width: 140,
      groupName: "成本信息",
      hideInSearch: true
    },
    {
      title: "采购总成本1",
      dataIndex: "purchaseAmountStr",
      width: 140,
      groupName: "成本信息",
      hideInSearch: true
    },
    {
      // 4位小数 毫
      title: "（邮费+包装费）单价",
      dataIndex: "materExpressPriceStr",
      width: 140,
      groupName: "成本信息",
      hideInSearch: true
    },
    {
      title: "（邮费+包装费）总成本2",
      dataIndex: "materExpressAmountStr",
      width: 140,
      groupName: "成本信息",
      hideInSearch: true
    },
    {
      // 百分比  0、1
      title: "快手服务费率",
      dataIndex: "ksServiceRateStr",
      width: 140,
      groupName: "成本信息",
      hideInSearch: true
    },

    {
      title: "平台费用合计3",
      dataIndex: "platformAmountStr",
      width: 140,
      groupName: "成本信息",
      hideInSearch: true
    },
    {
      // 2位小数
      title: "主播红包单价",
      dataIndex: "anchorRedPriceStr",
      width: 140,
      groupName: "成本信息",
      hideInSearch: true
    },
    {
      title: "主播红包金额4",
      dataIndex: "anchorRedAmountStr",
      width: 140,
      groupName: "成本信息",
      hideInSearch: true
    },
    {
      title: "主播补贴比例",
      dataIndex: "anchorAllowRateStr",
      width: 140,
      groupName: "成本信息",
      hideInSearch: true
    },
    {
      title: "返款单价",
      dataIndex: "rebatePriceStr",
      width: 140,
      groupName: "成本信息",
      hideInSearch: true
    },

    {
      title: "返款总金额",
      dataIndex: "rebateAmountStr",
      width: 140,
      hideInSearch: true
    },
    {
      title: "返款承担方",
      dataIndex: "rebateBearerStr",
      width: 140,
      hideInSearch: true
    },

    {
      title: "总成本9",
      dataIndex: "totalCostFeeStr",
      width: 140,
      groupName: "成本信息",
      hideInSearch: true
    },
    {
      title: "利润",
      dataIndex: "profitAmountStr",
      width: 140,
      groupName: "利润",
      hideInSearch: true
    },
    {
      title: "线上佣金利润",
      dataIndex: "onlineProfitAmountStr",
      width: 140,
      groupName: "利润",
      hideInSearch: true
    },
    {
      title: "线下利润",
      dataIndex: "offlineProfitAmountStr",
      width: 140,
      groupName: "利润",
      hideInSearch: true
    },
    {
      title: "利润率",
      dataIndex: "profitRateStr",
      width: 140,
      groupName: "利润",
      hideInSearch: true
    },
    {
      title: "年框扣费比例",
      dataIndex: "yearlyDudectRateStr",
      width: 140,
      groupName: "结算信息",
      hideInSearch: true
    },
    {
      title: "辛选年框扣费",
      dataIndex: "yearlyDudectAmountStr",
      width: 140,
      groupName: "结算信息",
      hideInSearch: true
    },
    {
      title: "净利润",
      dataIndex: "netProfitAmountStr",
      width: 140,
      groupName: "利润",
      hideInSearch: true
    },
    {
      title: "主播利润",
      dataIndex: "anchorNetProfitAmountStr",
      width: 140,
      groupName: "利润",
      hideInSearch: true
    },
    {
      title: "公司利润",
      dataIndex: "companyNetProfitAmountStr",
      width: 140,
      groupName: "利润",
      hideInSearch: true
    },
    {
      title: "净利润率",
      dataIndex: "netProfitRateStr",
      width: 140,
      groupName: "利润",
      hideInSearch: true
    },
    {
      title: "项目团长佣金",
      dataIndex: "projectCommisionRateStr",
      width: 140,
      groupName: "利润",
      hideInSearch: true
    }
  ].map(item => {
    const { title } = item;
    return {
      ...item,
      align: "center",
      title: returnEllipsisTooltip({ title })
    };
  });
};
