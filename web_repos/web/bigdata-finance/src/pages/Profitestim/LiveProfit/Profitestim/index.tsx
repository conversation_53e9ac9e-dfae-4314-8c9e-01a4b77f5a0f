import React, { useRef, useState, useMemo, useEffect, useContext } from "react";
import { Button, message, Form } from "antd";
import { columns, advance_columns_search } from "./variable";
import { NOVALUE, Protable_FROM_CONFIG } from "@/utils/constant";
import { ifEmptyObj, pushExportHistory, splitSpaceComma } from "@/utils";
import UploadModal from "@/components/UploadModal";
import apis from "@/services/Profitestim/liveprofit";
import GoodscostEdit from "@/pages/Profitestim/LiveGoodscost/GoodscostEdit";
import dayjs from "dayjs";
import useCommonOptions from "@/hooks/useCommonOptions";
import TableList from "@/components/TableList";
import TargetConfig, { ColumnsState } from "@/components/target-config";
import { PageUrl, FieldsContext, tab_profitestim, tab_records } from "..";
const Res: React.FC<any> = () => {
  const { urlParams, setCurTab, setShouldRecordsRefresh } = useContext(FieldsContext);
  // url上携带的其他参数，无法在 查询区进行处理
  const [otherParams, setOtherParams] = useState({});
  const [editForm] = Form.useForm();
  const [modelNameOptions, modelNameObj] = useCommonOptions({ dimName: "模式" });
  const [isReconciliationOptions, isReconciliationObj] = useCommonOptions({ dimName: "是否对账" });
  const [undertakerRemarkOptions, undertakerRemarkObj] = useCommonOptions({ dimName: "快手技术服务费承担方" });
  const [ruleClass] = useCommonOptions({ dimName: "直播-加购商品规则大类" });
  const [tagOptions] = useCommonOptions({ dimName: "标签" });
  const [shopTypeOptions, shopTypeObj] = useCommonOptions({ dimName: "店铺性质" });
  const proTableFormRef = useRef<any>();
  const tableListRef = useRef<any>();
  const actionRef = useRef<any>();
  const [pageParmas, setPageParmas] = useState<any>({});
  const [showExcelUpload, setshowExcelUpload] = useState(false);
  const [columnsState, setColumnsState] = useState<Record<string, ColumnsState>>({});

  const [editProps, setEditProps] = useState<any>({
    open: false,
    fresh: () => {
      // actionRef.current?.reload();
    },
    onClose: () => {
      setEditProps({ ...editProps, open: false });
    }
  });
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const handleAddGoods = (record: any) => {
    const { anchorId, itemId, anchorName, liveDate } = record;
    setEditProps({
      ...editProps,
      open: true,
      ruleClass,
      params: {
        anchorId,
        itemId,
        liveDate,
        anchorName
      }
    });
  };

  const columnsProps = {
    handleAddGoods,
    modelNameOptions,
    modelNameObj,
    isReconciliationOptions,
    isReconciliationObj,
    undertakerRemarkOptions,
    undertakerRemarkObj,
    tagOptions
  };
  // 上传excel modal属性
  const UploadModalProps = useMemo(() => {
    return {
      title: "直播利润预估导入",
      visible: showExcelUpload,
      targetType: 0,
      api: apis.addLiveProfitFromExcel,
      message: (
        <>
          <span>1. 必须严格按照模板格式填写，否则会上传失败</span>
        </>
      ),
      tempUrl: "https://s.xinc818.com/files/webcim52at1wckqt61h/直播利润预估表-模版.xlsx",
      afterOk: () => {
        setShouldRecordsRefresh(true);
        setCurTab(tab_records);
        setshowExcelUpload(false);
      },
      close: () => {
        setshowExcelUpload(false);
      }
    };
  }, [showExcelUpload]);
  const editable: any = {
    type: "single",
    form: editForm,
    onSave: async (key, editData) => {
      const params = { ...editData };
      if (Array.isArray(editData.tagIdList)) {
        params.tagIdList = editData.tagIdList?.join();
      }
      const { status } = await apis.saveOrUpdateLiveProfit(params);
      editForm.resetFields([key]);
      if (status) {
        message.success("修改成功！");
      }
      actionRef.current?.reload();
    },
    editableKeys,
    onChange: setEditableRowKeys,
    actionRender: (row, config, { save, cancel }) => [save, cancel]
  };
  const handleExportRecon = async () => {
    const { status } = await apis.exportStatementGoods(pageParmas);
    if (status) {
      pushExportHistory();
    }
  };
  // 处理请求参数
  const handlePageParmas = (params: any) => {
    setEditableRowKeys([]);
    const { liveDate, current, shopTypeList, stdCategory = {}, brandNameList, bigDepartmentList, ...rest } = params;
    if (typeof shopTypeList !== "undefined") {
      rest.shopTypeList = [shopTypeObj?.[shopTypeList] ?? ""];
    }
    if (liveDate?.length) {
      const [liveDateStart, liveDateEnd] = liveDate;
      rest.liveDateStart = dayjs(liveDateStart).format("YYYY-MM-DD");
      rest.liveDateEnd = dayjs(liveDateEnd).format("YYYY-MM-DD");
    }
    if (brandNameList) {
      rest.brandNameList = [brandNameList];
    }
    if (bigDepartmentList) {
      rest.bigDepartmentList = [bigDepartmentList];
    }
    rest.itemIdList = splitSpaceComma(rest.itemIdList);
    rest.orderContractIdList = splitSpaceComma(rest.orderContractIdList);
    return { ...rest, ...stdCategory, pageNo: current };
  };
  const handleTargetsChange = column => {
    setColumnsState(column);
  };
  const advanceColumnsProps = { shopTypeOptions, tagOptions };
  const handleParams = p => {
    const params = proTableFormRef.current.getFieldsValue();
    const res = handlePageParmas?.({ ...p, ...params });
    setPageParmas(res);
    return res;
  };
  const onReset = () => {
    setOtherParams({});
    tableListRef?.current?.onReset();
  };
  useEffect(() => {
    if (ifEmptyObj(urlParams)) {
      proTableFormRef.current.resetFields();
      const { tagIdList, liveDate, ...rest } = urlParams;
      setOtherParams(rest);
      proTableFormRef.current.setFieldsValue({ tagIdList, liveDate });
      actionRef.current.reload();
    }
  }, [urlParams]);
  return (
    <div>
      <TableList
        tableListRef={tableListRef}
        headerTitle="直播利润预估"
        formRef={proTableFormRef}
        actionRef={actionRef}
        columnsState={{ value: columnsState }}
        columnEmptyText={NOVALUE}
        columns={columns(columnsProps)}
        scroll={{ x: "max-content", y: "calc(100vh - 420px)" }}
        form={{
          ...Protable_FROM_CONFIG,
          initialValues: {
            liveDate: [dayjs().subtract(6, "month"), dayjs()]
          }
        }}
        advanceColumns={advance_columns_search(advanceColumnsProps)}
        api={apis.getFinanceLiveRecordPage}
        downloadApi={apis.downloadFinanceLiveRecord}
        summaryApi={apis.getFinanceLiveRecordTotal}
        preFetch={handleParams}
        params={otherParams}
        onReset={onReset}
        toolbar={{
          actions: [
            <>
              <Button type="primary" onClick={handleExportRecon} size="small">
                对账商品导出
              </Button>
            </>,
            <>
              <Button type="primary" onClick={() => setshowExcelUpload(true)} size="small">
                上传
              </Button>
            </>,
            <>
              <TargetConfig columns={columns(columnsProps)} pageUrl={PageUrl + tab_profitestim} onChange={handleTargetsChange} />
            </>
          ]
        }}
        rowKey={"id"}
        editable={editable}
      />
      {showExcelUpload && <UploadModal {...UploadModalProps} />}
      {editProps.open ? <GoodscostEdit {...editProps} /> : null}
    </div>
  );
};
export default React.memo(Res);
