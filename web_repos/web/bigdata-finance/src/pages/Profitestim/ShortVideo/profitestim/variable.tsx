import { filterOptionLabel, returnEllipsisTooltip } from "@/utils";
import { message, Select, Tooltip, Space, Tag } from "antd";
import { InfoCircleOutlined } from "@ant-design/icons";
import DepartmentSelect from "@/components/DepartmentSelect";
import AnchorSelect from "@/components/AnchorSelect";
import BrandDebounSelect from "@/components/BrandDebounSelect";
import CategorySelect from "@/components/CategorySelect";
import SupplierNameSelect from "@/components/SupplierNameSelect";
import { NOVALUE, SIZE } from "@/utils/constant";
import EditSelect from "@/components/EditSelect";
import EditInputNumberPercent from "@/components/EditInputNumberPercent";
import EditInputNumber from "@/components/EditInputNumber";
export const advance_columns_search = ({ shopTypeOptions }: any): Array<TableListItem> => {
  return [
    {
      title: "基础信息",
      valueType: "group",
      colProps: { span: 24 },
      columns: [
        {
          // 单选
          title: "商品类目",
          dataIndex: "stdCategory",
          hideInTable: true,
          renderFormItem() {
            return <CategorySelect />;
          }
        },
        {
          title: "二级模式",
          dataIndex: "secondaryModelName",
          editable: false,
          hideInSearch: false,
          hideInTable: true
        },
        {
          title: "店铺名称",
          dataIndex: "shopName",
          hideInTable: true
        },
        {
          title: "店铺性质",
          dataIndex: "shopTypeList",
          hideInTable: true,
          renderFormItem() {
            return <Select placeholder="请选择" options={shopTypeOptions} filterOption={filterOptionLabel} showSearch allowClear />;
          }
        },
        {
          title: "大部门",
          dataIndex: "bigDepartmentList",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem: () => {
            return <DepartmentSelect key="bigDepartmentList" style={{ width: "100%" }} maxTagCount="responsive" mode="multiple" />;
          }
        },
        {
          title: "品牌",
          dataIndex: "brandNameList",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem() {
            return <BrandDebounSelect key="brandName" showSearch allowClear maxTagCount="responsive" mode="multiple" />;
          }
        },
        {
          title: "对接人",
          dataIndex: "purchasePrincipal",
          hideInTable: true
        },
        {
          title: "类目",
          dataIndex: "category",
          hideInTable: true
        },
        {
          title: "品类",
          dataIndex: "uploadCategory",
          hideInTable: true
        },
        {
          title: "商品编码",
          dataIndex: "itemCode",
          hideInTable: true
        },
        {
          title: "合同号",
          dataIndex: "orderContractIdList",
          hideInTable: true,
          fieldProps: {
            placeholder: "多个使用英文逗号/空格"
          }
        }
      ]
    }
  ];
};
const columns_search = ({ modelNameOptions, tagOptions }: any) => {
  return [
    {
      title: "发布时间",
      dataIndex: "videoStartDate",
      hideInSearch: false,
      hideInTable: true,
      valueType: "dateRange"
    },
    {
      title: "主播名称",
      dataIndex: "anchorNameList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem() {
        return <AnchorSelect key="anchorNameList" showSearch allowClear maxTagCount="responsive" mode="multiple" />;
      }
    },

    {
      title: "商品ID",
      dataIndex: "itemIdList",
      hideInSearch: false,
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "商品名称",
      dataIndex: "itemTitle",
      hideInSearch: false,
      hideInTable: true
    },
    {
      title: "模式",
      dataIndex: "modelIdList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem() {
        return (
          <Select
            placeholder="请选择"
            options={modelNameOptions}
            filterOption={filterOptionLabel}
            showSearch
            allowClear
            maxTagCount="responsive"
            mode="multiple"
          />
        );
      }
    },

    {
      title: "供应商名称",
      dataIndex: "supplierName",
      hideInTable: true,
      renderFormItem() {
        return <SupplierNameSelect />;
      }
    },

    {
      title: "标签",
      dataIndex: "tagIdList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem() {
        return (
          <Select
            placeholder="请选择"
            options={tagOptions}
            filterOption={filterOptionLabel}
            showSearch
            allowClear
            maxTagCount="responsive"
            mode="multiple"
          />
        );
      }
    }
  ].map(item => {
    return {
      ...item,
      showInGroup: false
    };
  });
};
export const columns = ({
  handleAddGoods,
  handleRelateVideo,
  modelNameOptions,
  tagOptions,
  modelNameObj,
  undertakerRemarkOptions
}: any): Array<TableListItem> => {
  return [
    ...columns_search({ modelNameOptions, tagOptions }),
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      hideInSearch: true,
      showInGroup: false,
      fixed: "left",
      editable: false,
      render: (_, record, index) => {
        return index + 1;
      }
    },
    {
      title: "短视频发布起始时间",
      dataIndex: "videoStartDate",
      width: 120,
      fixed: "left",
      editable: false,
      hideInSearch: true
    },
    {
      title: "短视频发布结束时间",
      dataIndex: "videoEndDate",
      width: 120,
      fixed: "left",
      editable: false,
      hideInSearch: true
    },
    {
      title: "关联短视频数",
      dataIndex: "videoCount",
      width: 120,
      fixed: "left",
      editable: false,
      hideInSearch: true
    },
    {
      title: "主播ID",
      dataIndex: "anchorId",
      width: 120,
      fixed: "left",
      editable: false,
      hideInSearch: true
    },
    {
      title: "主播名称",
      dataIndex: "anchorName",
      editable: false,
      width: 120,
      hideInSearch: true
    },
    {
      title: "商品ID",
      dataIndex: "itemId",
      editable: false,
      width: 120,
      hideInSearch: true
    },
    {
      title: "商品名称",
      dataIndex: "itemTitle",
      editable: false,
      width: 160,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      // 筛选框
      title: "模式",
      dataIndex: "modelId",
      width: 120,
      editable: true,
      hideInSearch: true,
      fieldProps: { size: SIZE },
      render(_) {
        return returnEllipsisTooltip({ title: modelNameObj[_] });
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditSelect size={SIZE} options={modelNameOptions} />;
      }
    },
    {
      title: "二级模式",
      dataIndex: "secondaryModelName",
      width: 120,
      editable: true,
      hideInSearch: true,
      fieldProps: { size: SIZE }
    },
    {
      title: "标签",
      dataIndex: "tagIdList",
      width: 120,
      editable: true,
      groupName: "结算信息",
      hideInSearch: true,
      render(_, record) {
        const { tagIdListStr } = record;
        const tags = (tagIdListStr?.split(",") || []).filter(item => item);
        return tags.length ? (
          <Space size={1}>
            {tags.map(tag => {
              return (
                <Tag key="tag" color="volcano">
                  {tag}
                </Tag>
              );
            })}
          </Space>
        ) : (
          NOVALUE
        );
      },
      renderFormItem: (record, { isEditable }) => {
        // 系统标签禁用
        const systemTags = ["6", "7", "8", "9"];
        const {
          entry: { tagIdList }
        } = record;
        let val = [];
        if (Array.isArray(tagIdList)) {
          val = tagIdList;
        } else if (tagIdList) {
          val = tagIdList?.split(",");
        }
        return (
          isEditable && (
            <EditSelect
              defaultValue={val}
              style={{ textAlign: "left" }}
              size={SIZE}
              placeholder="请选择"
              options={tagOptions?.map(tag => {
                const { value } = tag;
                if (systemTags.includes(value)) {
                  return {
                    ...tag,
                    disabled: true
                  };
                }
                return {
                  ...tag
                };
              })}
              filterOption={filterOptionLabel}
              showSearch
              allowClear
              maxTagCount="responsive"
              mode="multiple"
            />
          )
        );
      }
    },
    {
      title: "商家利润分成比例",
      dataIndex: "shareRatio",
      width: 120,
      groupName: "结算信息",
      editable: true,
      render(_, record) {
        const { shareRatioStr } = record;
        return returnEllipsisTooltip({ title: shareRatioStr });
      },
      renderFormItem: (_, { isEditable }) => {
        return (
          isEditable && (
            <EditInputNumberPercent formatter={value => `${value}%`} style={{ width: "100%" }} max={100} step={1} placeholder="分成比例" />
          )
        );
      },
      hideInSearch: true
    },
    {
      title: "主播净利润分成比例",
      dataIndex: "anchorNetProfitShareRatio",
      width: 120,
      groupName: "结算信息",
      editable: true,
      renderFormItem: (_, { isEditable }) => {
        return (
          isEditable && (
            <EditInputNumberPercent formatter={value => `${value}%`} style={{ width: "100%" }} max={100} step={1} placeholder="分成比例" />
          )
        );
      },
      render(_, record) {
        const { anchorNetProfitShareRatioStr } = record;
        return returnEllipsisTooltip({ title: anchorNetProfitShareRatioStr });
      },
      hideInSearch: true
    },
    {
      title: "店铺ID",
      dataIndex: "shopId",
      editable: false,
      width: 120,
      hideInSearch: true
    },
    {
      title: "店铺名称",
      dataIndex: "shopName",
      editable: false,
      width: 120,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "店铺性质",
      dataIndex: "shopType",
      editable: false,
      width: 120,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "供应商名称",
      dataIndex: "supplierName",
      editable: false,
      width: 160,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "我司主体",
      dataIndex: "contractName",
      editable: false,
      width: 160,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "大部门",
      dataIndex: "bigDepartment",
      editable: false,
      width: 120,
      hideInSearch: true
    },
    {
      title: "提报部门",
      dataIndex: "submitDepartment",
      editable: false,
      width: 120,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "对接人",
      dataIndex: "purchasePrincipal",
      editable: false,
      width: 120,
      hideInSearch: true
    },
    {
      title: "品牌",
      dataIndex: "brandName",
      editable: false,
      width: 120,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "类目",
      dataIndex: "category",
      width: 120,
      editable: false,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "品类",
      dataIndex: "uploadCategory",
      width: 120,
      editable: true,
      hideInSearch: true,
      fieldProps: {
        size: SIZE
      }
    },
    {
      title: "一级类目",
      dataIndex: "stdCatLv1Name",
      editable: false,
      width: 120,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "二级类目",
      dataIndex: "stdCatLv2Name",
      editable: false,
      width: 120,
      hideInSearch: true
    },
    {
      title: "三级类目",
      dataIndex: "stdCatLv3Name",
      editable: false,
      width: 120,
      hideInSearch: true
    },
    {
      title: "四级类目",
      dataIndex: "stdCatLv4Name",
      editable: false,
      width: 120,
      hideInSearch: true
    },
    {
      title: "商品编码",
      dataIndex: "itemCode",
      editable: false,
      width: 120,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "发货内容（第一列为主产品）",
      dataIndex: "deliveryContent",
      editable: false,
      width: 120,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "直播价格",
      dataIndex: "itemPriceStr",
      editable: false,
      width: 120,
      hideInSearch: true
    },
    {
      title: "5天支付件数",
      dataIndex: "payVolume5dStr",
      editable: false,
      width: 140,
      groupName: "结算信息",
      hideInSearch: true
    },
    {
      title: "5天支付金额",
      dataIndex: "payAmount5dStr",
      editable: false,
      width: 140,
      groupName: "结算信息",
      hideInSearch: true
    },
    {
      title: "5天净支付件数",
      dataIndex: "netPayVolume5dStr",
      editable: false,
      width: 140,
      groupName: "结算信息",
      hideInSearch: true
    },
    {
      title: "5天净支付金额",
      dataIndex: "netPayAmount5dStr",
      editable: false,
      width: 140,
      groupName: "结算信息",
      hideInSearch: true
    },
    {
      title: "更新支付订单数（加购）",
      dataIndex: "addOnNumStr",
      editable: false,
      width: 140,
      groupName: "结算信息",
      hideInSearch: true
    },
    {
      title: "5天退款率",
      dataIndex: "refundRate5dStr",
      editable: false,
      width: 120,
      groupName: "结算信息",
      hideInSearch: true
    },
    {
      title: "预估结算件数",
      dataIndex: "preNetPayVolumeStr",
      editable: false,
      width: 140,
      groupName: "结算信息",
      hideInSearch: true
    },
    {
      title: "预估结算金额",
      dataIndex: "preNetPayAmountStr",
      editable: false,
      width: 140,
      groupName: "结算信息",
      hideInSearch: true
    },
    {
      title: "线上佣金比例",
      dataIndex: "onlineComisRateStr",
      editable: false,
      width: 120,
      groupName: "结算信息",
      hideInSearch: true
    },
    {
      title: "佣金收入（线上）",
      dataIndex: "totalCommissionAmountStr",
      editable: false,
      width: 140,
      groupName: "结算信息",
      hideInSearch: true
    },
    {
      title: "巴伽传媒-快捷单、营销服务费、推广费",
      dataIndex: "bagaMediaFee",
      editable: true,
      width: 140,
      groupName: "链接费",
      hideInSearch: true,
      valueType: "digit",
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
      },
      render(_, record) {
        const { bagaMediaFeeStr } = record;
        return returnEllipsisTooltip({ title: bagaMediaFeeStr });
      }
    },
    {
      title: "深圳云商、海南云商-链接费、服务费",
      dataIndex: "szhzysFee",
      editable: true,
      width: 140,
      groupName: "链接费",
      hideInSearch: true,
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
      },
      render(_, record) {
        const { szhzysFeeStr } = record;
        return returnEllipsisTooltip({ title: szhzysFeeStr });
      }
    },
    {
      title: "辛选网络、广州和翊合同约定链接费",
      dataIndex: "xxwlgzhxFee",
      editable: true,
      width: 140,
      groupName: "链接费",
      hideInSearch: true,
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
      },
      render(_, record) {
        const { xxwlgzhxFeeStr } = record;
        return returnEllipsisTooltip({ title: xxwlgzhxFeeStr });
      }
    },
    {
      title: "辛选网络、广州和翊未达标，应收链接费",
      dataIndex: "xxwlgzhxwdbFee",
      editable: true,
      width: 140,
      hideInSearch: true,
      groupName: "链接费",
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
      },
      render(_, record) {
        const { xxwlgzhxwdbFeeStr } = record;
        return returnEllipsisTooltip({ title: xxwlgzhxwdbFeeStr });
      }
    },
    {
      title: "年框合同、预估可收链接费",
      dataIndex: "nkhtygLinkFee",
      editable: true,
      width: 140,
      groupName: "链接费",
      hideInSearch: true,
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
      },
      render(_, record) {
        const { nkhtygLinkFeeStr } = record;
        return returnEllipsisTooltip({ title: nkhtygLinkFeeStr });
      }
    },
    {
      title: "已收链接费",
      dataIndex: "chargedLinkFeeStr",
      editable: false,
      width: 140,
      groupName: "链接费",
      hideInSearch: true
    },
    {
      title: "待收链接费",
      dataIndex: "prepareLinkFeeStr",
      editable: false,
      width: 140,
      groupName: "链接费",
      hideInSearch: true
    },
    {
      title: "链接费备注",
      dataIndex: "linkFeeRemark",
      editable: true,
      width: 140,
      groupName: "链接费",
      hideInSearch: true,
      fieldProps: {
        size: SIZE
      },
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "链接费保比",
      dataIndex: "targetRoi",
      editable: false,
      width: 140,
      groupName: "链接费",
      hideInSearch: true
    },
    {
      title: "GMV取值统计时间",
      dataIndex: "gmvStatisticsTime",
      editable: false,
      width: 140,
      groupName: "链接费",
      hideInSearch: true
    },
    {
      title: "保比GMV",
      dataIndex: "targetRoiGmvStr",
      editable: false,
      width: 140,
      groupName: "链接费",
      hideInSearch: true
    },
    {
      // 显示百分比，填写小数 1 、-1
      title: "线下佣金比例-需对账",
      dataIndex: "offlineCommissionRate",
      width: 140,
      groupName: "成本信息",
      editable: true,
      renderFormItem: (_, { isEditable }) => {
        return (
          isEditable && (
            <EditInputNumberPercent formatter={value => `${value}%`} style={{ width: "100%" }} max={100} step={1} placeholder="分成比例" />
          )
        );
      },
      render(_, record) {
        const { offlineCommissionRateStr } = record;
        return returnEllipsisTooltip({ title: offlineCommissionRateStr });
      },
      hideInSearch: true
    },
    {
      // 2位小数
      title: "线下收佣/线下退佣-需对账",
      dataIndex: "offlineCommissionRebateRate",
      width: 140,
      groupName: "成本信息",
      editable: false,
      render(_, record) {
        const { offlineCommissionRebateRate } = record;
        return returnEllipsisTooltip({ title: offlineCommissionRebateRate });
      },
      hideInSearch: true
    },
    {
      title: "快手技术服务费承担方备注",
      dataIndex: "undertakerRemark",
      editable: true,
      width: 140,
      groupName: "成本信息",
      hideInSearch: true,
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditSelect size={SIZE} options={undertakerRemarkOptions} />;
      },
      render(_, record) {
        const { undertakerRemarkStr } = record;
        return returnEllipsisTooltip({ title: undertakerRemarkStr });
      }
    },
    {
      title: "主品成本单价",
      dataIndex: "mainProductCostPrice",
      editable: true,
      width: 140,
      groupName: "成本信息",
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
      },
      render(_, record) {
        const { mainProductCostPriceStr } = record;
        return returnEllipsisTooltip({ title: mainProductCostPriceStr });
      },
      hideInSearch: true
    },
    {
      title: "赠品成本单价",
      dataIndex: "giveawayProductCostPrice",
      editable: true,
      width: 140,
      groupName: "成本信息",
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
      },
      render(_, record) {
        const { giveawayProductCostPriceStr } = record;
        return returnEllipsisTooltip({ title: giveawayProductCostPriceStr });
      },
      hideInSearch: true
    },
    {
      title: "赠品成本承担方",
      dataIndex: "giftCostBearer",
      width: 140,
      editable: true,
      groupName: "成本信息",
      hideInSearch: true,
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditSelect size={SIZE} options={undertakerRemarkOptions} />;
      },
      render(_, record) {
        const { giftCostBearerStr } = record;
        return returnEllipsisTooltip({ title: giftCostBearerStr });
      }
    },
    {
      title: "赠品免单数量",
      dataIndex: "giftFreeVolume",
      width: 140,
      groupName: "成本信息",
      editable: true,
      hideInSearch: true,
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={1} precision={0} style={{ width: "100%" }} />;
      },
      render(_, record) {
        const { giftFreeVolume } = record;
        return returnEllipsisTooltip({ title: giftFreeVolume });
      }
    },
    {
      title: "赠品免单单价",
      dataIndex: "giftFreePrice",
      width: 140,
      groupName: "成本信息",
      editable: true,
      hideInSearch: true,
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
      },
      render(_, record) {
        const { giftFreePriceStr } = record;
        return returnEllipsisTooltip({ title: giftFreePriceStr });
      }
    },
    {
      title: "赠品免单承担方",
      dataIndex: "giftFreeBearer",
      editable: true,
      width: 140,
      groupName: "成本信息",
      hideInSearch: true,
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditSelect size={SIZE} options={undertakerRemarkOptions} />;
      },
      render(_, record) {
        const { giftFreeBearerStr } = record;
        return returnEllipsisTooltip({ title: giftFreeBearerStr });
      }
    },

    {
      title: "采购总成本1",
      dataIndex: "totalPurchaseCostStr",
      editable: false,
      width: 140,
      groupName: "成本信息",
      hideInSearch: true
    },
    {
      title: "（邮费+包装费）单价",
      dataIndex: "postagePackagePrice",
      editable: true,
      width: 140,
      groupName: "成本信息",
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
      },
      render(_, record) {
        const { postagePackagePriceStr } = record;
        return returnEllipsisTooltip({ title: postagePackagePriceStr });
      },
      hideInSearch: true
    },
    {
      title: "（邮费+包装费）总成本2",
      dataIndex: "totalCostStr",
      editable: false,
      width: 140,
      hideInSearch: true
    },
    {
      title: "快手服务费率",
      dataIndex: "ksServiceChargeRate",
      editable: true,
      width: 140,
      groupName: "成本信息",
      renderFormItem: (_, { isEditable }) => {
        return (
          isEditable && (
            <EditInputNumberPercent formatter={value => `${value}%`} style={{ width: "100%" }} max={100} step={1} placeholder="分成比例" />
          )
        );
      },
      render(_, record) {
        const { ksServiceChargeRateStr } = record;
        return returnEllipsisTooltip({ title: ksServiceChargeRateStr });
      },
      hideInSearch: true
    },
    {
      title: "快手技术服务费",
      dataIndex: "ksTechnicalServiceStr",
      editable: false,
      width: 140,
      groupName: "成本信息",
      hideInSearch: true
    },
    {
      title: "阿里技术服务费",
      dataIndex: "aliTechnicalServiceStr",
      editable: false,
      width: 120,
      groupName: "成本信息",
      hideInSearch: true
    },
    {
      title: "提现手续费（佣金10%）",
      dataIndex: "withdrawalFeeStr",
      editable: false,
      width: 120,
      groupName: "成本信息",
      hideInSearch: true
    },
    {
      title: "平台费用合计3",
      dataIndex: "totalPlatformFeesStr",
      editable: false,
      width: 140,
      groupName: "成本信息",
      hideInSearch: true
    },
    {
      title: "主播红包单价",
      dataIndex: "anchorRedEnvelopePrice",
      editable: true,
      width: 140,
      groupName: "成本信息",
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
      },
      render(_, record) {
        const { anchorRedEnvelopePriceStr } = record;
        return returnEllipsisTooltip({ title: anchorRedEnvelopePriceStr });
      },
      hideInSearch: true
    },
    {
      title: "主播红包金额4",
      dataIndex: "anchorRedEnvelopeAmountStr",
      editable: false,
      width: 140,
      groupName: "成本信息",
      hideInSearch: true
    },
    {
      title: "主播补贴比例",
      dataIndex: "anchorSubsidyRateStr",
      editable: false,
      width: 120,
      groupName: "成本信息",
      hideInSearch: true
    },
    {
      title: "收款手续费1%",
      dataIndex: "collectionFeeStr",
      editable: false,
      width: 140,
      groupName: "成本信息",
      hideInSearch: true
    },
    {
      title: "运费险单价",
      dataIndex: "freightPrice",
      width: 140,
      groupName: "成本信息",
      editable: true,
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
      },
      render(_, record) {
        const { freightPriceStr } = record;
        return returnEllipsisTooltip({ title: freightPriceStr });
      },
      hideInSearch: true
    },
    {
      title: "运费险0.5",
      dataIndex: "freightInsuranceStr",
      editable: false,
      width: 120,
      groupName: "成本信息",
      hideInSearch: true
    },
    {
      title: "天猫佣金5%",
      dataIndex: "tmallCommissionStr",
      editable: false,
      width: 120,
      groupName: "成本信息",
      hideInSearch: true
    },
    {
      title: "代扣返点（0.5%）",
      dataIndex: "withholdingRebatesStr",
      editable: false,
      width: 120,
      groupName: "成本信息",
      hideInSearch: true
    },
    {
      title: "支付宝总成本5",
      dataIndex: "alipayTotalCostStr",
      editable: false,
      width: 120,
      groupName: "成本信息",
      hideInSearch: true
    },
    {
      title: "返款单价",
      dataIndex: "rebatePrice",
      width: 140,
      groupName: "成本信息",
      editable: true,
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
      },
      render(_, record) {
        const { rebatePriceStr } = record;
        return returnEllipsisTooltip({ title: rebatePriceStr });
      },
      hideInSearch: true
    },
    {
      title: "返款总金额",
      dataIndex: "rebateAmountTotalStr",
      editable: false,
      width: 120,
      groupName: "成本信息",
      hideInSearch: true
    },
    {
      title: "返款承担方",
      dataIndex: "rebateBearer",
      width: 140,
      groupName: "成本信息",
      editable: true,
      hideInSearch: true,
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditSelect size={SIZE} options={undertakerRemarkOptions} />;
      },
      render(_, record) {
        const { rebateBearerStr } = record;
        return returnEllipsisTooltip({ title: rebateBearerStr });
      }
    },

    {
      title: "请明星费用6",
      dataIndex: "pleaseStarCost",
      width: 140,
      groupName: "成本信息",
      editable: true,
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
      },
      render(_, record) {
        const { pleaseStarCostStr } = record;
        return returnEllipsisTooltip({ title: pleaseStarCostStr });
      },
      hideInSearch: true
    },
    {
      title: "赠送奖品金额7",
      dataIndex: "giftAmount",
      groupName: "成本信息",
      width: 140,
      editable: true,
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
      },
      render(_, record) {
        const { giftAmountStr } = record;
        return returnEllipsisTooltip({ title: giftAmountStr });
      },
      hideInSearch: true
    },
    {
      title: "推广费用8",
      dataIndex: "promotionFee",
      groupName: "成本信息",
      width: 140,
      editable: true,
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
      },
      render(_, record) {
        const { promotionFeeStr } = record;
        return returnEllipsisTooltip({ title: promotionFeeStr });
      },
      hideInSearch: true
    },
    {
      title: "总成本9",
      dataIndex: "allTotalCostStr",
      editable: false,
      width: 140,
      groupName: "成本信息",
      hideInSearch: true
    },
    {
      title: "总单价",
      dataIndex: "totalUnitPriceStr",
      editable: false,
      width: 140,
      groupName: "成本信息",
      hideInSearch: true
    },
    {
      title: "利润",
      dataIndex: "totalProfitStr",
      editable: false,
      width: 140,
      hideInSearch: true
    },
    {
      title: "线上佣金利润",
      dataIndex: "onlineCommissionProfitStr",
      editable: false,
      width: 140,
      groupName: "利润",
      hideInSearch: true
    },
    {
      title: "线下利润",
      dataIndex: "offlineProfitStr",
      editable: false,
      width: 140,
      groupName: "利润",
      hideInSearch: true
    },
    {
      title: "对公综合税费-线上",
      dataIndex: "onlineComprehensiveCorporateTax",
      width: 140,
      groupName: "利润",
      editable: true,
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
      },
      render(_, record) {
        const { onlineComprehensiveCorporateTaxStr } = record;
        return returnEllipsisTooltip({ title: onlineComprehensiveCorporateTaxStr });
      },
      hideInSearch: true
    },
    {
      title: "对公综合税费-线下",
      dataIndex: "offlineComprehensiveCorporateTax",
      width: 140,
      groupName: "利润",
      editable: true,
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
      },
      render(_, record) {
        const { offlineComprehensiveCorporateTaxStr } = record;
        return returnEllipsisTooltip({ title: offlineComprehensiveCorporateTaxStr });
      },
      hideInSearch: true
    },
    {
      title: "主播线上利润",
      dataIndex: "onlineAnchorOnlineProfit",
      width: 140,
      groupName: "利润",
      editable: true,
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
      },
      render(_, record) {
        const { onlineAnchorOnlineProfitStr } = record;
        return returnEllipsisTooltip({ title: onlineAnchorOnlineProfitStr });
      },
      hideInSearch: true
    },
    {
      title: "主播线下利润",
      dataIndex: "offlineAnchorOnlineProfit",
      width: 140,
      groupName: "利润",
      editable: true,
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
      },
      render(_, record) {
        const { offlineAnchorOnlineProfitStr } = record;
        return returnEllipsisTooltip({ title: offlineAnchorOnlineProfitStr });
      },
      hideInSearch: true
    },
    {
      title: "主播利润",
      dataIndex: "anchorProfit",
      width: 140,
      groupName: "利润",
      editable: true,
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
      },
      render(_, record) {
        const { anchorProfitStr } = record;
        return returnEllipsisTooltip({ title: anchorProfitStr });
      },
      hideInSearch: true
    },
    {
      title: "公司利润",
      dataIndex: "bagaProfit",
      width: 140,
      groupName: "利润",
      editable: true,
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
      },
      render(_, record) {
        const { bagaProfitStr } = record;
        return returnEllipsisTooltip({ title: bagaProfitStr });
      },
      hideInSearch: true
    },
    {
      title: "净利润率",
      dataIndex: "profitRateStr",
      editable: false,
      width: 120,
      groupName: "利润",
      hideInSearch: true
    },
    {
      title: "毛利率",
      dataIndex: "grossProfitMarginStr",
      editable: false,
      width: 120,
      groupName: "利润",
      hideInSearch: true
    },
    {
      title: "是否项目",
      dataIndex: "project",
      editable: false,
      width: 120,
      groupName: "结算信息",
      hideInSearch: true
    },
    {
      title: "辛选佣金比例-项目",
      dataIndex: "xinxuanCommissionRateProject",
      width: 140,
      groupName: "结算信息",
      editable: true,
      renderFormItem: (_, { isEditable }) => {
        return (
          isEditable && (
            <EditInputNumberPercent formatter={value => `${value}%`} style={{ width: "100%" }} max={100} step={1} placeholder="辛选佣金比例-项目" />
          )
        );
      },
      render(_, record) {
        const { xinxuanCommissionRateProjectStr } = record;
        return returnEllipsisTooltip({ title: xinxuanCommissionRateProjectStr });
      },
      hideInSearch: true
    },
    {
      title: "辛选佣金金额-项目",
      dataIndex: "xinxuanCommissionAmountProjectStr",
      editable: false,
      width: 140,
      groupName: "结算信息",
      hideInSearch: true
    },
    {
      title: "快手平台类目",
      dataIndex: "kuaiCatName",
      editable: false,
      width: 120,
      groupName: "结算信息",
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "店铺侧辛选年框扣费比例",
      dataIndex: "shopNkRateStr",
      editable: false,
      width: 140,
      groupName: "结算信息",
      hideInSearch: true
    },
    {
      title: "店铺侧辛选年框扣费",
      dataIndex: "shopXinxuanNkChargingAmountStr",
      editable: false,
      width: 140,
      groupName: "结算信息",
      hideInSearch: true
    },
    {
      title: "主播侧辛选年框扣费比例",
      dataIndex: "anchorNkRateStr",
      editable: false,
      width: 120,
      groupName: "结算信息",
      hideInSearch: true
    },
    {
      title: "主播侧辛选年框扣费",
      dataIndex: "anchorXinxuanNkChargingAmountStr",
      editable: false,
      width: 120,
      groupName: "结算信息",
      hideInSearch: true
    },
    {
      title: "项目团长佣金",
      dataIndex: "projectRegimentalPromotionCommissionStr",
      editable: false,
      width: 120,
      groupName: "利润",
      hideInSearch: true
    },
    {
      title: "辛选利润",
      dataIndex: "xinxuanProfitStr",
      editable: false,
      width: 140,
      groupName: "利润",
      hideInSearch: true
    },
    {
      title: "项目利润",
      dataIndex: "projectProfitStr",
      editable: false,
      width: 140,
      groupName: "利润",
      hideInSearch: true
    },
    {
      title: "发货方",
      dataIndex: "deliveryPlace",
      editable: false,
      width: 120,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "合同号",
      dataIndex: "contractNo",
      width: 140,
      editable: true,
      fieldProps: {
        size: SIZE
      },
      hideInSearch: true,
      render(_, record) {
        const { contractNoBySys, contractNoByUpload } = record;
        const hasUploadVal = contractNoByUpload ?? false;
        if (hasUploadVal && contractNoBySys !== contractNoByUpload) {
          return (
            <Space>
              <span style={{ color: "red" }}>{contractNoBySys ?? "暂无合同"}</span>
              <Tooltip
                title={
                  <>
                    <span>财务填报：{contractNoByUpload ?? NOVALUE}</span>
                    <br />
                    <span>系统获取：{contractNoBySys ?? NOVALUE}</span>
                  </>
                }
              >
                <InfoCircleOutlined />
              </Tooltip>
            </Space>
          );
        } else {
          return contractNoBySys ?? NOVALUE;
        }
      }
    },
    {
      title: "钉钉流程编号",
      dataIndex: "dingdingNo",
      editable: false,
      width: 120,
      hideInSearch: true
    },
    {
      title: "备注",
      dataIndex: "remark",
      width: 140,
      editable: true,
      fieldProps: {
        size: SIZE
      },
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "数据更新时间",
      dataIndex: "procTime",
      editable: false,
      width: 120,
      hideInSearch: true
    },
    {
      title: "团长类型",
      dataIndex: "leaderName",
      editable: false,
      width: 120,
      hideInSearch: true
    },
    {
      title: "团长佣金比例",
      dataIndex: "leaderComisRateStr",
      editable: false,
      width: 140,
      hideInSearch: true
    },
    {
      title: "是否对账",
      dataIndex: "isReconciliation",
      width: 140,
      editable: true,
      fieldProps: {
        size: SIZE
      },
      hideInSearch: true
    },

    {
      title: "操作",
      width: 120,
      valueType: "option",
      fixed: "right",
      showInGroup: false,
      render(_, record: Record<string, any>, index: number, action: any) {
        const { id } = record;
        return (
          <Space direction="vertical">
            <a
              onClick={() => {
                if (id) {
                  action?.startEditable(id);
                } else {
                  message.info("ID 字段的没有可读可写权限，无法编辑！！！");
                }
              }}
            >
              编辑
            </a>
            <Space>
              <a onClick={() => handleRelateVideo(record)}>相关短视频</a>
              <a onClick={() => handleAddGoods(record)}>新增加购商品</a>
            </Space>
          </Space>
        );
      }
    }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      ...item,
      align: "center",
      title: returnEllipsisTooltip({ title })
    };
  });
};
