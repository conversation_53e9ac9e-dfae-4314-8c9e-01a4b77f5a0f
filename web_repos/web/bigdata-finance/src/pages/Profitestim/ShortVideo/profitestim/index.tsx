import React, { useEffect, useMemo, useRef, useState, useContext } from "react";
import { Button, Form, message } from "antd";
import { ifEmptyObj, splitSpaceComma } from "@/utils";
import { NOVALUE, SIZE, Protable_FROM_CONFIG } from "@/utils/constant";
import { columns, advance_columns_search } from "./variable";
import dayjs from "dayjs";
import UploadModal from "@/components/UploadModal";
import apis from "@/services/Profitestim/shortvideo";
import queryString from "query-string";
import { mysteryEnvUrl } from "@/utils/url";
import GoodscostEdit from "@/pages/Profitestim/Goodscost/GoodscostEdit";
import TableList from "@/components/TableList";
import useCommonOptions from "@/hooks/useCommonOptions";
import TargetConfig, { ColumnsState } from "@/components/target-config";
import { PageUrl, FieldsContext, tab_video_profitestim, tab_video_records } from "..";
const Res: React.FC<any> = () => {
  const { urlParams, setCurTab, setShouldRecordsRefresh } = useContext(FieldsContext);
  // url上携带的其他参数，无法在 查询区进行处理
  const [otherParams, setOtherParams] = useState({});
  const [editForm] = Form.useForm();
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const [showUpload, setshowUpload] = useState(false);
  const [modelNameOptions, modelNameObj] = useCommonOptions({ dimName: "模式" });
  const [ruleClass] = useCommonOptions({ dimName: "直播-加购商品规则大类" });
  const [shopTypeOptions, shopTypeObj] = useCommonOptions({ dimName: "店铺性质" });
  const [undertakerRemarkOptions, undertakerRemarkObj] = useCommonOptions({ dimName: "快手技术服务费承担方" });
  const [tagOptions] = useCommonOptions({ dimName: "标签" });
  const formRef = useRef<any>();
  const tableListRef = useRef<any>();
  const actionRef = useRef<any>();
  const [columnsState, setColumnsState] = useState<Record<string, ColumnsState>>({});
  const [editProps, setEditProps] = useState<any>({
    open: false,
    fresh: () => {
      // actionRef.current?.reload();
    },
    onClose: () => {
      setEditProps({ ...editProps, open: false });
    }
  });
  const editable: any = {
    type: "single",
    form: editForm,
    onSave: async (key, editData) => {
      const params = { ...editData };
      if (Array.isArray(editData.tagIdList)) {
        params.tagIdList = editData.tagIdList?.join();
      }
      const { status } = await apis.saveOrUpdateShortVideoProfit(params);
      editForm.resetFields([key]);
      if (status) {
        message.success("修改成功！");
      }
      actionRef.current?.reload();
    },
    editableKeys,
    onChange: setEditableRowKeys,
    actionRender: (row, config, { save, cancel }) => [save, cancel]
  };
  const handleAddGoods = record => {
    const { anchorId, itemId, anchorName, videoStartDate } = record;

    setEditProps({
      ...editProps,
      open: true,
      ruleClass,
      params: {
        anchorId,
        itemId,
        videoStartDate,
        anchorName
      }
    });
  };
  const handleRelateVideo = record => {
    // 开始时间、结束时间、主播、商品ID
    const { anchorName, itemId, videoEndDate, videoStartDate } = record;
    const params = {
      anchorName: [anchorName],
      itemId,
      publishEndTime: videoEndDate,
      publishStartTime: videoStartDate
    };

    window.open(`${mysteryEnvUrl}/#/fineBI/shortBandGoods?${queryString.stringify(params)}`);
  };

  // 上传excel modal属性
  const UploadModalProps = useMemo(() => {
    return {
      title: "Excel导入短视频利润预估",
      visible: showUpload,
      targetType: 1001009,
      api: apis.addShortVideoProfitFromExcel,
      tempUrl: "https://s.xinc818.com/files/webcim52auhlr8osyr4/短视频利润预估表-模版.xlsx",
      message: (
        <>
          <span>1. 必须严格按照模板格式填写，否则会上传失败</span>
        </>
      ),
      afterOk: () => {
        setShouldRecordsRefresh(true);
        setCurTab(tab_video_records);
        setshowUpload(false);
      },
      close: () => {
        setshowUpload(false);
      }
    };
  }, [showUpload]);

  const columnsProps = {
    handleAddGoods,
    handleRelateVideo,
    modelNameOptions,
    modelNameObj,
    undertakerRemarkOptions,
    undertakerRemarkObj,
    tagOptions
  };

  // 处理请求参数
  const handlePageParmas = (params: any) => {
    setEditableRowKeys([]);
    const { videoStartDate, current, shopTypeList, stdCategory = {}, ...rest } = params;

    if (videoStartDate?.length) {
      const [videoStartDateStart, videoStartDateEnd] = videoStartDate;
      rest.videoStartDateStart = dayjs(videoStartDateStart).format("YYYY-MM-DD");
      rest.videoStartDateEnd = dayjs(videoStartDateEnd).format("YYYY-MM-DD");
    }
    if (typeof shopTypeList !== "undefined") {
      rest.shopTypeList = [shopTypeObj?.[shopTypeList] ?? ""];
    }
    rest.itemIdList = splitSpaceComma(rest.itemIdList);
    rest.orderContractIdList = splitSpaceComma(rest.orderContractIdList);
    return { ...rest, ...stdCategory, pageNo: current };
  };
  const handleParams = p => {
    const params = formRef.current.getFieldsValue();
    const res = handlePageParmas?.({ ...p, ...params });
    return res;
  };
  const handleTargetsChange = column => {
    setColumnsState(column);
  };
  const advanceColumnsProps = { shopTypeOptions };
  const onReset = () => {
    setOtherParams({});
    tableListRef.current?.onReset();
  };
  useEffect(() => {
    if (ifEmptyObj(urlParams)) {
      formRef.current.resetFields();
      const { tagIdList, videoStartDate, ...rest } = urlParams;
      setOtherParams(rest);
      formRef.current.setFieldsValue({ tagIdList, videoStartDate });
      actionRef.current.reload();
    }
  }, [urlParams]);
  return (
    <>
      <TableList
        headerTitle="短视频利润预估"
        tableListRef={tableListRef}
        size={SIZE}
        formRef={formRef}
        actionRef={actionRef}
        columnsState={{ value: columnsState }}
        columnEmptyText={NOVALUE}
        columns={columns(columnsProps)}
        advanceColumns={advance_columns_search(advanceColumnsProps)}
        scroll={{ x: "max-content", y: "calc(100vh - 420px)" }}
        form={{
          ...Protable_FROM_CONFIG,
          initialValues: {
            videoStartDate: [dayjs().subtract(6, "month"), dayjs()]
          }
        }}
        api={apis.getFinanceShortVideoRecordPage}
        downloadApi={apis.downloadFinanceShortVideoRecord}
        summaryApi={apis.getFinanceShortVideoRecordTotal}
        preFetch={handleParams}
        params={otherParams}
        onReset={onReset}
        toolbar={{
          actions: [
            <>
              <Button type="primary" onClick={() => setshowUpload(true)} size="small">
                上传
              </Button>
            </>,
            <TargetConfig
              key={"target-config"}
              columns={columns(columnsProps)}
              pageUrl={PageUrl + tab_video_profitestim}
              onChange={handleTargetsChange}
            />
          ]
        }}
        rowKey="id"
        editable={editable}
      />

      {showUpload ? <UploadModal {...UploadModalProps} /> : null}
      {editProps.open ? <GoodscostEdit {...editProps} /> : null}
    </>
  );
};

export default React.memo(Res);
