import React from "react";
import { Modal, Form, Input } from "antd";
import { SIZE } from "@/utils/constant";
import apis from "@/services/Profitestim/auditrecords";
import { IAuditProps } from "../batch-audit-pass";
const Res: React.FC<IAuditProps> = props => {
  const { open, onClose, onFresh, title, params } = props;
  const [form] = Form.useForm();
  const handleCancel = () => {
    form.resetFields();
    onClose?.();
  };
  const handleOk = async () => {
    form.validateFields().then(async values => {
      const { status } = await apis.batchAudit({ ...params, ...values });
      if (status) {
        onClose?.();
        onFresh?.();
        handleCancel();
      }
    });
  };

  return (
    <>
      <Modal
        title={title}
        open={open}
        centered
        okButtonProps={{ size: SIZE }}
        cancelButtonProps={{ size: SIZE }}
        onOk={handleOk}
        onCancel={handleCancel}
      >
        <Form form={form}>
          <Form.Item label="驳回原因" name="problemNote" rules={[{ required: true }]}>
            <Input.TextArea rows={4} placeholder="请输入驳回原因" />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default React.memo(Res);
