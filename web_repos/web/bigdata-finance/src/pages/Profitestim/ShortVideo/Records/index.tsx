import React, { useState, useRef, useContext, useEffect } from "react";
import TableList from "@/components/TableList";
import { TableRowSelection } from "antd/es/table/interface";
import { columns, audit_status_has } from "./variable";
import apis from "@/services/Profitestim/auditrecords";
import BatchAuditPass from "./batch-audit-pass";
import BatchAuditReject from "./batch-audit-reject";
import { Button } from "antd";
import { SIZE } from "@/utils/constant";
import dayjs from "dayjs";
import styles from "./index.less";
import { FieldsContext } from "../index";
import { useAccess } from "@umijs/max";
const Res = () => {
  const { userInfo } = useAccess();
  const { shouldRecordsRefresh, setShouldRecordsRefresh } = useContext(FieldsContext);
  const actionRef = useRef<any>();
  const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([]);
  const [selectedRows, setselectedRows] = useState([]);
  const [batchPassProps, setBatchPassProps] = useState({
    title: "",
    open: false,
    params: {
      idList: [],
      recordType: 2
    },
    onFresh: () => {
      actionRef?.current?.reload();
    },
    onClose: () => {
      setBatchPassProps({ ...batchPassProps, open: false });
    }
  });
  const [batchRejectProps, setBatchRejectProps] = useState({
    title: "",
    open: false,
    params: {
      idList: [],
      recordType: 2
    },
    onFresh: () => {
      actionRef?.current?.reload();
    },
    onClose: () => {
      setBatchRejectProps({ ...batchRejectProps, open: false });
    }
  });
  const handlePass = record => {
    const { id } = record;
    setBatchPassProps({
      ...batchPassProps,
      title: "审核",
      open: true,
      params: {
        ...batchPassProps.params,
        idList: [id],
        auditResult: 1
      }
    });
  };
  const handleBatchPass = () => {
    setBatchPassProps({
      ...batchPassProps,
      title: "批量审核",
      open: true,
      params: {
        ...batchPassProps.params,
        idList: selectedKeys,
        auditResult: 1
      }
    });
  };
  const handleReject = record => {
    const { id } = record;
    setBatchRejectProps({
      ...batchRejectProps,
      title: "驳回",
      open: true,
      params: {
        ...batchRejectProps.params,
        idList: [id],
        auditResult: 2
      }
    });
  };
  const handleBatchReject = () => {
    setBatchRejectProps({
      ...batchRejectProps,
      title: "批量驳回",
      open: true,
      params: {
        ...batchRejectProps.params,
        idList: selectedKeys,
        auditResult: 2
      }
    });
  };
  const disableExamine = record => {
    // 禁用审核
    // 自己不能审核自己上传的数据
    const { uploadCrew } = record;
    return uploadCrew === userInfo.displayName;
  };
  const rowSelection: TableRowSelection<any> = {
    selectedRowKeys: selectedKeys,
    columnWidth: 50,
    getCheckboxProps: record => {
      return {
        disabled: record.auditStatus === audit_status_has || disableExamine(record)
      };
    },
    onChange(selected, selectedRows) {
      setSelectedKeys(selected);
      setselectedRows(selectedRows);
    }
  };
  const resetSelected = () => {
    setSelectedKeys([]);
    setselectedRows([]);
  };
  const columnsProps = { handlePass, handleReject, disableExamine };
  const actions = () => {
    return [
      <Button key="batch_pass" disabled={selectedRows.length === 0} size={SIZE} type="primary" onClick={handleBatchPass}>
        批量审核
      </Button>,
      <Button key="batch_reject" disabled={selectedRows.length === 0} size={SIZE} danger onClick={handleBatchReject}>
        批量驳回
      </Button>
    ];
  };
  // 处理请求参数
  const handlePageParmas = (params: any) => {
    resetSelected();
    const { current, gmtUpload, gmtAudit, ...rest } = params;
    if (gmtUpload?.length) {
      const [gmtUploadStart, gmtUploadEnd] = gmtUpload;
      rest.gmtUploadStart = dayjs(gmtUploadStart).format("YYYY-MM-DD");
      rest.gmtUploadEnd = dayjs(gmtUploadEnd).format("YYYY-MM-DD");
    }
    if (gmtAudit?.length) {
      const [gmtAuditStart, gmtAuditEnd] = gmtAudit;
      rest.gmtAuditStart = dayjs(gmtAuditStart).format("YYYY-MM-DD");
      rest.gmtAuditEnd = dayjs(gmtAuditEnd).format("YYYY-MM-DD");
    }
    return { ...rest, pageNo: current };
  };
  useEffect(() => {
    if (shouldRecordsRefresh) {
      actionRef?.current?.reload();
      setShouldRecordsRefresh(false);
    }
  }, [shouldRecordsRefresh]);

  return (
    <>
      <TableList
        className={styles["table-records"]}
        headerTitle="操作审核"
        actionRef={actionRef}
        columns={columns(columnsProps)}
        scroll={{ y: "calc(100vh - 370px)" }}
        form={{
          initialValues: {
            auditStatusList: [],
            auditResultList: []
          }
        }}
        preFetch={handlePageParmas}
        api={apis.getFinanceProfitAuditRecordPage}
        downloadApi={apis.downloadFinanceProfitAuditRecord}
        params={{ recordType: 2 }}
        toolbar={{
          actions: actions()
        }}
        rowSelection={rowSelection}
      />
      {batchPassProps.open ? <BatchAuditPass {...batchPassProps} /> : null}
      {batchRejectProps.open ? <BatchAuditReject {...batchRejectProps} /> : null}
    </>
  );
};

export default React.memo(Res);
