import React, { useState, useRef } from "react";
import TableList from "@/components/TableList";
import TargetConfig, { ColumnsState } from "@/components/target-config";
import { columns, advance_columns_search } from "./variable";
import { NOVALUE, Protable_FROM_CONFIG } from "@/utils/constant";
import dayjs from "dayjs";
import { PageUrl, tab_video_actual_profitestim } from "..";
import { splitSpaceComma } from "@/utils";
import useCommonOptions from "@/hooks/useCommonOptions";
import apis from "@/services/Profitestim/shortvideo";
const Res: React.FC<any> = () => {
  const tableFormRef = useRef<any>();
  const actionRef = useRef<any>();
  const [tagOptions] = useCommonOptions({ dimName: "标签" });
  const [modelNameOptions] = useCommonOptions({ dimName: "模式" });
  const [shopTypeOptions] = useCommonOptions({ dimName: "店铺性质" });
  const [columnsState, setColumnsState] = useState<Record<string, ColumnsState>>({});
  const handleTargetsChange = column => {
    setColumnsState(column);
  };
  const handlePageParmas = (params: any) => {
    const { videoStartDate, current, shopTypeList, bigDepartmentList, brandNameList, stdCategory = {}, ...rest } = params;
    if (videoStartDate?.length) {
      const [videoStartDateStart, videoStartDateEnd] = videoStartDate;
      rest.videoStartDateStart = dayjs(videoStartDateStart).format("YYYY-MM-DD");
      rest.videoStartDateEnd = dayjs(videoStartDateEnd).format("YYYY-MM-DD");
    }
    if (typeof shopTypeList !== "undefined") {
      rest.shopTypeList = [shopTypeList];
    }
    if (typeof bigDepartmentList !== "undefined") {
      rest.bigDepartmentList = [bigDepartmentList];
    }
    if (typeof brandNameList !== "undefined") {
      rest.brandNameList = [brandNameList];
    }
    rest.itemIdList = splitSpaceComma(rest.itemIdList);
    return { ...rest, ...stdCategory, pageNo: current };
  };
  const columnsProps = { modelNameOptions };
  const advanceColumnsProps = { shopTypeOptions, tagOptions };
  return (
    <div>
      <TableList
        headerTitle="短视频实际利润预估"
        formRef={tableFormRef}
        actionRef={actionRef}
        columnsState={{ value: columnsState }}
        columnEmptyText={NOVALUE}
        columns={columns(columnsProps)}
        scroll={{ x: "max-content", y: "calc(100vh - 420px)" }}
        form={{
          ...Protable_FROM_CONFIG,
          initialValues: {
            videoStartDate: [dayjs().subtract(6, "month"), dayjs()]
          }
        }}
        api={apis.getFinanceShortVideoItemActualForecastPage}
        downloadApi={apis.downloadFinanceShortVideoItemActualForecast}
        summaryApi={apis.getFinanceShortVideoItemActualForecastTotal}
        advanceColumns={advance_columns_search(advanceColumnsProps)}
        preFetch={handlePageParmas}
        toolbar={{
          actions: [
            <>
              <TargetConfig columns={columns(columnsProps)} pageUrl={PageUrl + tab_video_actual_profitestim} onChange={handleTargetsChange} />
            </>
          ]
        }}
        rowKey={"id"}
      />
    </div>
  );
};

export default React.memo(Res);
