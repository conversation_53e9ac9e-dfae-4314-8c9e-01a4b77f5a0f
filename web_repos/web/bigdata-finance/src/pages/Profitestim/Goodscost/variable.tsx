import { Button, Space, Popconfirm, Select } from "antd";
import { NOVALUE, SIZE } from "@/utils/constant";
import { filterOptionLabel, returnEllipsisTooltip } from "@/utils/index";

import AnchorSelect from "@/components/AnchorSelect";
const columns_search = ({ ruleClassOptions }: any) => {
  return [
    {
      title: "短视频发布起始时间",
      dataIndex: "videoStartDate",
      valueType: "dateRange",
      hideInTable: true
    },
    {
      title: "主播",
      dataIndex: "anchorNameList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem() {
        return <AnchorSelect key="anchorNameList" showSearch allowClear maxTagCount="responsive" mode="multiple" />;
      }
    },
    {
      title: "商品ID",
      dataIndex: "itemIdList",
      hideInSearch: false,
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "商品名称",
      dataIndex: "itemTitle",
      hideInSearch: false,
      hideInTable: true
    },
    {
      // 单选
      title: "规则大类",
      dataIndex: "ruleClassList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem() {
        return <Select placeholder="请选择" options={ruleClassOptions} filterOption={filterOptionLabel} showSearch allowClear />;
      }
    },
    {
      title: "SKU信息",
      dataIndex: "skuName",
      hideInSearch: false,
      hideInTable: true
    },
    {
      title: "加购规则",
      dataIndex: "buyRule",
      hideInSearch: false,
      hideInTable: true
    }
  ];
};
export const columns = ({ handleDelete, handleEdit, ruleClassObj, ruleClassOptions }: any): Array<TableListItem> => {
  return [
    ...columns_search({ ruleClassOptions }),
    {
      title: "序号",
      dataIndex: "index",
      align: "center",
      fixed: "left",
      width: 60,
      hideInSearch: true,
      render: (text: any, row: any, index: number) => {
        return index + 1;
      }
    },
    {
      // TODO
      title: "短视频发布起始时间",
      dataIndex: "videoStartDate",
      hideInSearch: true,
      width: 80
    },

    {
      title: "主播",
      dataIndex: "anchorName",
      width: 120,
      hideInTable: false,
      hideInSearch: true,
      fixed: "left"
    },

    {
      title: "商品ID",
      dataIndex: "itemId",
      hideInSearch: true,
      width: 140,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品名称",
      dataIndex: "itemTitle",
      hideInSearch: true,
      width: 160,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "发货内容",
      dataIndex: "deliveryContent",
      width: 160,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "规则大类",
      dataIndex: "ruleClass",
      width: 80,
      hideInSearch: true,
      render(text: string, record: any) {
        const { ruleClass } = record;
        return ruleClassObj[ruleClass] || NOVALUE;
      }
    },
    {
      title: "SKU信息",
      dataIndex: "skuNameInfo",
      width: 140,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "加购规则",
      dataIndex: "buyRule",
      width: 80,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "净支付件数",
      dataIndex: "manualNetPayVolume",
      width: 100,
      hideInSearch: true
    },
    {
      // 2位小数
      title: "净支付金额（元）",
      dataIndex: "manualNetPayAmountStr",
      width: 100,
      hideInSearch: true
    },
    {
      // 百分比 数字
      title: "线上佣金比例",
      dataIndex: "totalCommissionRateStr",
      width: 100,
      hideInSearch: true
    },
    {
      // 百分比 数字
      title: "线下佣金比例",
      dataIndex: "offlineCommissionRateStr",
      width: 100,
      hideInSearch: true
    },
    {
      // 百分比 数字
      title: "退货率",
      dataIndex: "refundRateStr",
      width: 80,
      hideInSearch: true
    },
    {
      // 整数
      title: "加购支付订单数",
      dataIndex: "buyPayOrderCnt",
      width: 100,
      hideInSearch: true
    },
    {
      // 2位小数
      title: "主品成本单价（元）",
      dataIndex: "mainProductCostPriceStr",
      width: 100,
      hideInSearch: true
    },
    {
      // 2位小数
      title: "赠品成本单价（元）",
      dataIndex: "giveawayProductCostPriceStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "采购总成本1（元）",
      dataIndex: "totalPurchaseCostStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "（邮费+包装费）单价（元）",
      dataIndex: "postagePackagePriceStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "（邮费+包装费）总成本2（元）",
      dataIndex: "totalCostStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "操作",
      width: 100,
      valueType: "option",
      fixed: "right",
      align: "center",
      render: (text: string, record: any) => {
        return (
          <Space size={SIZE}>
            <Button type="link" size={SIZE} onClick={() => handleEdit(record)}>
              编辑
            </Button>
            <Popconfirm title="是否确认删除？" onConfirm={() => handleDelete(record)} onCancel={() => {}} okText="确认" cancelText="取消">
              <Button type="link" size={SIZE}>
                删除
              </Button>
            </Popconfirm>
          </Space>
        );
      }
    }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      ...item,
      align: "center",
      title: returnEllipsisTooltip({ title })
    };
  });
};
