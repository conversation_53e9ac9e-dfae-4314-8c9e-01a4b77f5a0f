import React, { useRef, useState } from "react";
import { splitSpaceComma } from "@/utils";
import { NOVALUE, SIZE, Protable_FROM_CONFIG } from "@/utils/constant";
import { columns } from "./variable";
import KeepAlive from "react-activation";
import apis from "@/services/Profitestim/goodscost";
import dayjs from "dayjs";
import useCommonOptions from "@/hooks/useCommonOptions";
import GoodscostEdit from "./GoodscostEdit";
import TableList from "@/components/TableList";
import { Button } from "antd";
import { history } from "@umijs/max";
import { PageUrl as LiveBroadcastingUrl } from "@/pages/Profitestim/LiveBroadcasting";
import queryString from "query-string";
import MergeSKU from "./GoodscostEdit/merge-SKU";

const PageUrl = `/profitestim/goodscost`;
const Res = () => {
  const [ruleClassOptions, ruleClassObj] = useCommonOptions({ dimName: "直播-加购商品规则大类" });

  const [pageParams, setPageParams] = useState({});

  const formRef = useRef<any>();
  const actionRef = useRef<any>();
  const [editProps, setEditProps] = useState<any>({
    open: false,
    fresh: () => {
      actionRef.current?.reload();
    },
    onClose: () => {
      setEditProps({ ...editProps, open: false });
    }
  });
  const handleDelete = async (record: any) => {
    const { status } = await apis.delete({
      id: record.id
    });
    if (status) {
      actionRef.current?.reload();
    }
  };
  const handleEdit = (record: any) => {
    const { id, anchorId, itemId, videoStartDate, anchorName } = record;
    setEditProps({
      ...editProps,
      ruleClass: ruleClassOptions,
      open: true,
      params: { id, anchorId, itemId, videoStartDate, anchorName }
    });
  };
  const handleDownloadSKU = () => {
    const { itemIdList, anchorNameList }: any = pageParams;
    history.push({
      pathname: LiveBroadcastingUrl,
      search: queryString.stringify({
        // liveDate: JSON.stringify(videoStartDate),
        itemIdList: JSON.stringify(itemIdList),
        anchorNameList: JSON.stringify(anchorNameList)
      })
    });
  };

  const [mergeProps, setMergeProps] = useState({
    title: "批量合并SKU",
    visible: false,
    params: {},
    targetType: 0,
    showDownload: false,
    fresh: () => {},
    close: () => {
      setMergeProps({ ...mergeProps, visible: false });
    }
  });

  const actions = () => {
    return [
      <Button key="upload" type="primary" size={SIZE} onClick={handleDownloadSKU}>
        下载系统SKU列表
      </Button>,
      <Button key="merge_sku" type="primary" size={SIZE} onClick={() => setMergeProps({ ...mergeProps, visible: true })}>
        批量合并SKU
      </Button>
    ];
  };

  const columnsProps = {
    handleDelete,
    handleEdit,
    ruleClassObj,
    ruleClassOptions
  };
  // 处理请求参数
  const handlePageParmas = (params: any) => {
    const { videoStartDate, current, ruleClassList, ...rest } = params;
    setPageParams(params);
    // 单独处理 直播日期
    if (videoStartDate) {
      const [videoStartDateStart, videoStartDateEnd] = videoStartDate;
      rest.videoStartDateStart = dayjs(videoStartDateStart).format("YYYY-MM-DD");
      rest.videoStartDateEnd = dayjs(videoStartDateEnd).format("YYYY-MM-DD");
    }
    rest.itemIdList = splitSpaceComma(rest.itemIdList);

    if (typeof ruleClassList !== "undefined") {
      rest.ruleClassList = [ruleClassList];
    }
    return { ...rest, pageNo: current };
  };

  return (
    <div>
      <TableList
        headerTitle="短视频加购商品成本"
        size={SIZE}
        formRef={formRef}
        actionRef={actionRef}
        columnEmptyText={NOVALUE}
        columns={columns(columnsProps)}
        scroll={{ x: "max-content", y: "calc(100vh - 320px)" }}
        form={{
          ...Protable_FROM_CONFIG,
          initialValues: {
            videoStartDate: [dayjs().subtract(6, "month"), dayjs()]
          }
        }}
        api={apis.getShortVideoBuyItemCostPage}
        preFetch={handlePageParmas}
        rowKey="id"
        toolbar={{
          actions: actions()
        }}
      />
      {editProps.open ? <GoodscostEdit {...editProps} /> : null}
      {mergeProps.visible ? <MergeSKU {...mergeProps} /> : null}
    </div>
  );
};
const AliveRecord = () => (
  <KeepAlive name={PageUrl}>
    <Res />
  </KeepAlive>
);
export default React.memo(AliveRecord);
