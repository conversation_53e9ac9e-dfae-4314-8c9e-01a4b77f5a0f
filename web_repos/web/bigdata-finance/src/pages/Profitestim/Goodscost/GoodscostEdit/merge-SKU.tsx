import React, { useState, useEffect, useMemo } from "react";
import { Modal, Form, Button, Alert, Space } from "antd";
import RSSUpload from "@/components/RSS/RSSUpload";
import { pushImportHistory } from "@/utils";
import { SIZE } from "@/utils/constant";
import { UploadExcelModalProps } from "@/components/UploadExcelModal";
import apis from "@/services/Profitestim/goodscost";
import dayjs from "dayjs";
export type IProps = UploadExcelModalProps & { params: Record<string, any>; showDownload: boolean };
const Res = (props: IProps) => {
  const { visible, close, fresh, params, title, targetType, showDownload = true } = props;
  const [form] = Form.useForm();
  const [uploadParams, setUploadParams] = useState(() => ({ filePath: "", fileName: "", targetType }));
  const [modalOkDisable, setModalOkDisable] = useState<boolean>(true);
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);

  useEffect(() => {
    setModalOkDisable(uploadParams.filePath ? false : true);
  }, [uploadParams]);

  const handleExcelCancel = () => {
    form.resetFields();
    close?.();
  };

  const requestAddItemFromExcel = async () => {
    const { entry } = await apis.addShortVideoBuyItemMappingFromExcel(uploadParams);
    const { success } = entry || {};
    setConfirmLoading(false);
    if (success) {
      close?.();
      fresh?.();
    } else {
      pushImportHistory();
    }
  };

  const handleExcelOk = (e: React.BaseSyntheticEvent) => {
    e.preventDefault();
    setConfirmLoading(true);
    requestAddItemFromExcel();
  };

  const fileChangeHandle = (url: string, fileName: string) => {
    setUploadParams({ ...uploadParams, fileName, filePath: url });
  };
  const onRemoveHandle = () => {
    setUploadParams({ ...uploadParams, fileName: "", filePath: "" });
  };
  // 下载导入商品Excel模板
  const handleDownloadExample = async (e: React.BaseSyntheticEvent) => {
    e.stopPropagation();
    const { anchorId, anchorName, itemId, videoStartDate } = params;
    const { status, entry } = await apis.downloadShortVideoBuyItemSkuList({
      anchorId,
      itemId,
      anchorName,
      videoStartDate: dayjs(videoStartDate).format("YYYY-MM-DD")
    });
    if (status) {
      window.open(entry);
    }
  };
  const returnMessage = useMemo(() => {
    return (
      <>
        <span>1. 下载系统SKU列表后，按照模版调整数据上传，系统数据以最新上传为准。操作可能对现有账单产生变更，请谨慎处理。</span>
        <br />
        <span>
          2. 黄色和紫色为必填信息，紫色部分指标系统会基于合并后SKU自动累加，黄色部分为不可累加指标，每个合并后的SKU系统只会取一条，填写一样即可。
        </span>
      </>
    );
  }, []);

  return (
    <Modal
      centered={true}
      title={title}
      open={visible}
      onOk={handleExcelOk}
      onCancel={handleExcelCancel}
      maskClosable={false}
      confirmLoading={confirmLoading}
      okButtonProps={{
        size: SIZE,
        disabled: modalOkDisable
      }}
      cancelButtonProps={{
        size: SIZE
      }}
    >
      <Form form={form}>
        <Form.Item>
          <Alert message={returnMessage} type="info" showIcon></Alert>
        </Form.Item>
        <Form.Item>
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              flexDirection: "column",
              alignItems: "center"
            }}
          >
            <Space direction="vertical" align="center">
              {showDownload ? (
                <Button size={SIZE} type="primary" onClick={handleDownloadExample}>
                  [下载系统SKU列表]
                </Button>
              ) : null}
              <RSSUpload
                accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
                fileChange={(ossResultUrl: string, file: any, fileName: string) => fileChangeHandle(ossResultUrl, fileName)}
                fileRemove={() => onRemoveHandle()}
                onRemove={false}
                size={30 * 1024 * 1024}
                type={["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "application/vnd.ms-exce", "text/csv"]}
                fileLength={1}
                fileList={uploadParams.filePath ? [uploadParams.filePath] : []}
              />
            </Space>
          </div>
        </Form.Item>
      </Form>
    </Modal>
  );
};
export default React.memo(Res);
