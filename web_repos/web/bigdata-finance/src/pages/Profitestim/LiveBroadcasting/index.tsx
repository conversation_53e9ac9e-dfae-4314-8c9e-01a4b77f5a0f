import React, { useEffect, useRef, useState } from "react";
import { But<PERSON>, message } from "antd";
import TableList from "@/components/TableList";
import dayjs from "dayjs";
import KeepAlive, { useAliveController } from "react-activation";
import { useSearchParams, history } from "@umijs/max";
import { NOVALUE, Protable_FROM_CONFIG, SIZE } from "@/utils/constant";
import api from "@/services/Profitestim/liveBroadcasting";
import { ifEmptyObj, pushExportHistory, splitSpaceComma } from "@/utils";
import { columns } from "./columns";
export const PageUrl = "/profitestim/liveBroadcasting";
const dateFormat = "YYYYMMDD";
interface IProps {
  urlParams: Record<string, any>;
}
const Res: React.FC<IProps> = props => {
  const { urlParams } = props;
  const formRef = useRef<any>();
  const actionRef = useRef<any>();
  const [pageParams, setPageParams] = useState({});
  const handlePageParmas = (p: any) => {
    const { current, liveDate, itemIdList, skuIdList, ...rest } = p;
    if (liveDate) {
      const [startTime, endTime] = liveDate;
      delete p.startTime;
      delete p.endTime;
      rest.startTime = dayjs(startTime).format(dateFormat);
      rest.endTime = dayjs(endTime).format(dateFormat);
    }

    rest.itemIdList = splitSpaceComma(itemIdList);
    rest.skuIdList = splitSpaceComma(skuIdList);
    return {
      ...rest,
      pageNo: current
    };
  };
  const handleParams = p => {
    const params = formRef.current.getFieldsValue();
    const res = handlePageParmas?.({ ...p, ...params });
    setPageParams(res);
    return res;
  };
  const handleDownloadSKU = async () => {
    const { itemIdList } = pageParams;
    if (itemIdList?.length > 0) {
      const { status } = await api.downloadMergeLiveBuyItemSkuList(pageParams);
      if (status) {
        pushExportHistory();
      }
    } else {
      return message.info("请输入商品ID后，再进行下载！");
    }
  };
  const actions = () => {
    return [
      <Button key="upload" type="primary" size={SIZE} onClick={handleDownloadSKU}>
        下载合并直播加购商品SKU
      </Button>
    ];
  };
  useEffect(() => {
    if (ifEmptyObj(urlParams)) {
      formRef.current.resetFields();
      const { liveDate, itemIdList, anchorNameList } = urlParams;
      formRef.current.setFieldsValue({ liveDate, itemIdList, anchorNameList });
      actionRef.current.reload();
    }
  }, [urlParams]);
  return (
    <>
      <TableList
        formRef={formRef}
        actionRef={actionRef}
        columnEmptyText={NOVALUE}
        scroll={{ x: "max-content", y: "calc(100vh - 340px)" }}
        form={{
          ...Protable_FROM_CONFIG,
          initialValues: {
            liveDate: [dayjs().subtract(1, "month").startOf("day"), dayjs().endOf("day")]
          }
        }}
        columns={columns({})}
        api={api.listLiveItemInfos}
        downloadApi={api.downloadSPUList}
        preFetch={handleParams}
        toolbar={{
          actions: actions()
        }}
      />
    </>
  );
};
/**
 * 上一个页面的状态
 * 用于比较 携带参数跳页 与 正常路由跳页时，页面进行缓存刷新
 */
const prePageStatus = {
  search: ""
};
const AliveRecord = () => {
  const [searchParams] = useSearchParams();
  const hasSearch = searchParams.size > 0;
  let urlParams: any = {};
  if (hasSearch) {
    const liveDate = searchParams.get("liveDate");
    const itemIdList = searchParams.get("itemIdList");
    const anchorNameList = searchParams.get("anchorNameList");
    if (liveDate) {
      urlParams.liveDate = JSON.parse(liveDate);
    }
    if (itemIdList) {
      urlParams.itemIdList = JSON.parse(itemIdList);
    }
    if (anchorNameList) {
      urlParams.anchorNameList = JSON.parse(anchorNameList);
    }
  }
  let aliveCont = useAliveController();
  const { location } = history;
  // 当 url 上携带参数时，刷新 页面缓存
  if (location.search !== prePageStatus.search) {
    prePageStatus.search = location.search;
    aliveCont.refreshScope(PageUrl);
  }
  return (
    <KeepAlive name={PageUrl}>
      <Res urlParams={urlParams} />
    </KeepAlive>
  );
};

export default React.memo(AliveRecord);
