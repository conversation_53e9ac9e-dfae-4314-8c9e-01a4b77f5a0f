import AnchorSelect from "@/components/AnchorSelect";
import { returnEllipsisTooltip } from "@/utils";

const columns_search = ({}) => {
  return [
    {
      title: "直播日期",
      dataIndex: "liveDate",
      hideInTable: true,
      valueType: "dateRange"
    },
    {
      title: "主播名称",
      dataIndex: "anchorNameList",
      hideInTable: true,
      renderFormItem() {
        return <AnchorSelect showSearch allowClear maxTagCount="responsive" mode="multiple" />;
      }
    },
    {
      title: "商品ID",
      dataIndex: "itemIdList",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "商品名称",
      dataIndex: "itemTitle",
      hideInTable: true
    },
    {
      title: "SKU_ID",
      dataIndex: "skuIdList",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "SKU属性",
      dataIndex: "skuPropName",
      hideInTable: true
    }
  ];
};
export const columns = (): Array<TableListItem> => {
  return [
    ...columns_search({}),

    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      fixed: "left",
      hideInSearch: true,
      render: (text: any, row: any, index: number) => {
        return index + 1;
      }
    },
    {
      title: "直播时间",
      dataIndex: "liveDate",
      width: 100,
      fixed: "left",
      hideInSearch: true
    },
    {
      title: "主播名称",
      dataIndex: "anchorName",
      width: 100,
      fixed: "left",
      hideInSearch: true
    },
    {
      title: "商品ID",
      dataIndex: "itemId",
      width: 100,
      hideInSearch: true
    },
    {
      title: "商品名称",
      dataIndex: "itemTitle",
      width: 200,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "SKUID",
      dataIndex: "skuId",
      width: 100,
      hideInSearch: true
    },
    {
      title: "SKU属性",
      dataIndex: "skuPropName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "下单件数",
      dataIndex: "orderVolumeStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "下单金额",
      dataIndex: "orderAmountStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "净支付件数",
      dataIndex: "netPayVolumeStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "净支付金额",
      dataIndex: "netPayAmountStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "净支付子订单数",
      dataIndex: "netOrderVolumeStr",
      width: 120,
      hideInSearch: true
    }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      ...item,
      align: "center",
      title: returnEllipsisTooltip({ title })
    };
  });
};
