import React, { useMemo, useEffect, useState } from "react";
import { Form, Input, Space, Button, Select, InputNumber, Drawer, Row, Col, DrawerProps } from "antd";
import apis from "@/services/Profitestim/livegoodscost";
import { SIZE, SPLIT_FLAG } from "@/utils/constant";
import dayjs from "dayjs";
import { returnEllipsisTooltip } from "@/utils";
import useCommonOptions from "@/hooks/useCommonOptions";
import MergeSKU from "./merge-SKU";
const { Option } = Select;

const formItemLayout = {
  labelCol: {
    xs: { span: 12 },
    sm: { span: 8 }
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 16 }
  }
};
interface IProps extends DrawerProps {
  ruleClass: IOptions[];
  params: Record<string, any>;
  fresh?: () => void;
  onClose: () => void;
}
// 规则大类 排N
const ruleClassPaiN = 1;
const GoodcostEdit: React.FC<IProps> = props => {
  const { params, onClose, fresh, ruleClass, ...rest } = props;
  let { id, itemId, anchorId, anchorName, liveDate } = params;
  const [modelNameOptions, modelNameObj] = useCommonOptions({ dimName: "模式" });
  const [form] = Form.useForm();
  const [initialValues, setInitialValues] = useState({});
  const [ruleClassVal, setRuleClassVal] = useState(-1);
  const [showMerge, setShowMerge] = useState(false);
  const [SKUItem, setSKUItem] = useState<(IOptions & { disabled?: boolean })[]>([]);
  const returnTitle = useMemo(() => {
    return <span>{`${id ? "编辑" : "新增"}`}加购商品成本</span>;
  }, [id]);
  // 根据id 获取数据详情
  const fetchLiveBuyItemSkuList = async () => {
    // SKU信息：SKUID+属性+下单件数+下单金额，单选，已经选中编辑过的SKU不可再选择
    const { status, entry } = await apis.getLiveBuyItemSkuList({ itemId, anchorId, anchorName, liveDate: dayjs(liveDate).format("YYYYMMDD") });
    let result = [];
    if (status) {
      result = entry.map(item => {
        const { skuId, skuName, orderVolumeStr, orderAmountStr, skuNameInfo, selectFlag } = item;
        const label = `${skuId}+${skuName}+${orderVolumeStr}+${orderAmountStr}`;
        return {
          label: label,
          value: skuNameInfo,
          disabled: selectFlag
        };
      });
    } else {
      result = [];
    }
    setSKUItem(result);
    return result;
  };
  // list - 根据主播ID, 直播日期, 商品ID获取合同凭证概要信息;
  const fetchDetailWhenAdd = async () => {
    await fetchLiveBuyItemSkuList();

    const { status, entry = {} } = await apis.getLiveBuyItemCostBaseInfo({
      anchorId,
      itemId,
      anchorName,
      liveDate
    });
    if (status) {
      const result = entry ?? {};
      const { refundRate, totalCommissionRate = 0, ruleClass, modelId } = result;
      result.refundRate = refundRate;
      result.totalCommissionRate = totalCommissionRate;
      result.modelId = typeof modelId !== "undefined" && modelId !== null ? modelId + "" : null;
      setRuleClassVal(ruleClass);
      setInitialValues(entry);
      form.setFieldsValue(entry);
    }
  };
  // 根据id 获取数据详情
  const fetchDetail = async () => {
    const SKUItem = await fetchLiveBuyItemSkuList();
    const { status, entry } = await apis.getLiveBuyItemCost({ id });
    if (status) {
      const result = entry ?? {};
      const { refundRate, offlineCommissionRate, totalCommissionRate = 0, skuNameInfo, ruleClass, modelId } = result;
      result.refundRate = refundRate;
      result.offlineCommissionRate = offlineCommissionRate;
      result.totalCommissionRate = totalCommissionRate;
      result.modelId = typeof modelId !== "undefined" && modelId !== null ? modelId + "" : null;
      // 针对历史数据 ，需要 从 SKU信息列表中匹配值
      const hasSku = (SKUItem || []).some(item => {
        return item.value === skuNameInfo;
      });
      if (!hasSku) {
        result.skuNameInfo = null;
      }
      setRuleClassVal(ruleClass);
      setInitialValues(result);
      form.setFieldsValue(result);
    }
  };

  useEffect(() => {
    if (id) {
      // 编辑
      fetchDetail();
    } else {
      // 新增
      fetchDetailWhenAdd();
    }
  }, [id]);
  const handleSave = () => {
    form.validateFields().then(async values => {
      const { modelId } = values;

      let params = {
        ...initialValues,
        ...values
      };
      if (typeof modelId !== "undefined") {
        params.modelName = modelNameObj[modelId];
      }
      if (params.ruleClass === ruleClassPaiN) {
        params.skuNameInfo = "";
        params.skuId = "";
        params.skuName = "";
        params.orderVolume = 0;
        params.orderAmount = 0;
      }
      if (params.skuNameInfo) {
        const [skuId, skuName, orderVolume, orderAmount] = params.skuNameInfo?.split(SPLIT_FLAG);
        params.skuId = skuId;
        params.skuName = skuName;
        params.orderVolume = orderVolume || 0;
        params.orderAmount = orderAmount || 0;
      }
      if (id) {
        // 编辑
        const { status } = await apis.saveOrUpdateLiveBuyItemCost(params);
        if (status) {
          onClose?.();
          fresh?.();
        }
      } else {
        const { status } = await apis.saveOrUpdateLiveBuyItemCost({
          anchorId: initialValues.anchorId,
          videoEndDate: initialValues.videoEndDate,
          liveDate: initialValues.liveDate,
          ...params
        });
        if (status) {
          onClose?.();
          fresh?.();
        }
      }
    });
  };
  const mergeProps = {
    title: "合并SKU",
    visible: showMerge,
    params: params,
    targetType: 0,
    fresh: () => {
      form.resetFields();
      id = void 0;
      fetchDetailWhenAdd();
    },
    close: () => {
      setShowMerge(false);
    }
  };
  return (
    <Drawer
      title={returnTitle}
      width={800}
      closeIcon={false}
      onClose={onClose}
      footer={
        <div style={{ textAlign: "center" }}>
          <Space align="center">
            <Button type="primary" size={SIZE} onClick={handleSave}>
              保存
            </Button>
            <Button size={SIZE} onClick={onClose}>
              取消
            </Button>
          </Space>
        </div>
      }
      {...rest}
    >
      {/* initialValues={initialValues} */}
      <Form labelWrap={true} size={SIZE} {...formItemLayout} form={form} onFinish={() => {}}>
        <Row>
          <Col span={12}>
            <Form.Item name="liveDate" label={returnEllipsisTooltip({ title: "直播日期" })}>
              <Input disabled={true} placeholder="直播日期"></Input>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="anchorName" label={returnEllipsisTooltip({ title: "主播名称" })}>
              <Input disabled={true} placeholder="主播名称"></Input>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="itemId" label={returnEllipsisTooltip({ title: "商品ID" })}>
              <Input disabled={true} placeholder="商品ID"></Input>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="itemTitle" label={returnEllipsisTooltip({ title: "产品名称" })}>
              <Input.TextArea disabled={true} placeholder="产品名称" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="deliveryContent" label={returnEllipsisTooltip({ title: "发货内容" })}>
              <Input.TextArea disabled={true} placeholder="发货内容" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="totalCommissionRate" label={returnEllipsisTooltip({ title: "线上佣金比例" })}>
              <InputNumber
                disabled={true}
                style={{ width: "100%" }}
                formatter={value => `${value}%`}
                max={100}
                step={1}
                defaultValue={0}
                placeholder="线上佣金比例"
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="refundRate" label={returnEllipsisTooltip({ title: "退货率" })}>
              <InputNumber disabled={true} style={{ width: "100%" }} max={100} step={1} defaultValue={0} placeholder="退货率" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="ruleClass" label={returnEllipsisTooltip({ title: "规则大类" })} rules={[{ required: true, message: "规则大类" }]}>
              <Select allowClear style={{ width: "100%" }} placeholder="请选择" onChange={val => setRuleClassVal(val)}>
                {ruleClass?.map(item => {
                  const { value } = item;
                  return (
                    <Option key={`accountType${value}`} value={+item.value}>
                      {item.label}
                    </Option>
                  );
                })}
              </Select>
            </Form.Item>
          </Col>
          {ruleClassVal !== ruleClassPaiN ? (
            <>
              <Col span={19}>
                <Form.Item
                  labelCol={{ span: 5 }}
                  name="skuNameInfo"
                  label={returnEllipsisTooltip({ title: "SKU信息" })}
                  rules={[{ required: true, message: "SKU信息" }]}
                >
                  <Select allowClear placeholder="请选择">
                    {SKUItem?.map(item => {
                      const { value, label, disabled } = item;
                      return (
                        <Option key={value} value={value} disabled={disabled}>
                          {label}
                        </Option>
                      );
                    })}
                  </Select>
                </Form.Item>
              </Col>
              <Col>
                <Button type="link" onClick={() => setShowMerge(true)}>
                  合并SKU
                </Button>
              </Col>
            </>
          ) : null}
          <Col span={12}>
            <Form.Item name="modelId" label={returnEllipsisTooltip({ title: "模式" })} rules={[{ required: true, message: "模式" }]}>
              <Select allowClear placeholder="请选择">
                {modelNameOptions?.map(item => {
                  const { value, label } = item;
                  return (
                    <Option key={value} value={value}>
                      {label}
                    </Option>
                  );
                })}
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="buyRule" label={returnEllipsisTooltip({ title: "加购规则" })} rules={[{ required: true, message: "加购规则" }]}>
              <Input placeholder="加购规则" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="manualNetPayVolume"
              label={returnEllipsisTooltip({ title: "净支付件数" })}
              rules={[{ required: true, message: "净支付件数" }]}
            >
              <InputNumber style={{ width: "100%" }} min="0" precision={0} placeholder="净支付件数" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="manualNetPayAmount"
              label={returnEllipsisTooltip({ title: "净支付金额" })}
              rules={[{ required: true, message: "净支付金额" }]}
            >
              <InputNumber style={{ width: "100%" }} min="0" precision={2} placeholder="净支付金额" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="buyPayOrderCnt"
              label={returnEllipsisTooltip({ title: "加购-支付订单数" })}
              rules={[{ required: true, message: "加购-支付订单数" }]}
            >
              <InputNumber style={{ width: "100%" }} min="0" placeholder="加购-支付订单数" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="mainProductCostPrice"
              label={returnEllipsisTooltip({ title: "主品成本单价" })}
              rules={[{ required: true, message: "主品成本单价" }]}
            >
              <InputNumber style={{ width: "100%" }} min="0" precision={4} placeholder="主品成本单价" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="giveawayProductCostPrice"
              label={returnEllipsisTooltip({ title: "赠品成本单价" })}
              rules={[{ required: true, message: "赠品成本单价" }]}
            >
              <InputNumber style={{ width: "100%" }} min="0" precision={4} placeholder="赠品成本单价" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="postagePackagePrice"
              label={returnEllipsisTooltip({ title: "(邮费+包装费)单价" })}
              rules={[{ required: true, message: "(邮费+包装费)单价" }]}
            >
              <InputNumber style={{ width: "100%" }} min="0" precision={4} placeholder="(邮费+包装费)单价" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="offlineCommissionRate"
              rules={[
                { required: true, message: "线下佣金比例" },
                {
                  pattern: /^[-\d]+(\.\d{1,2})?$/,
                  message: "最多支持小数点后2位"
                }
              ]}
              label={returnEllipsisTooltip({ title: "线下佣金比例" })}
            >
              <InputNumber style={{ width: "100%" }} step={1} addonAfter="%" placeholder="线下佣金比例" />
            </Form.Item>
          </Col>
        </Row>
      </Form>
      {showMerge ? <MergeSKU {...mergeProps} /> : null}
    </Drawer>
  );
};

export default GoodcostEdit;
