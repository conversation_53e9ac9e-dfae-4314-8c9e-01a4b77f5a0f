import React, { useRef, useState } from "react";
import KeepAlive from "react-activation";
import { Button } from "antd";
import { columns } from "./variable";
import { NOVALUE, Protable_FROM_CONFIG, SIZE } from "@/utils/constant";
import { splitSpaceComma } from "@/utils";
import dayjs from "dayjs";
import apis from "@/services/Profitestim/livegoodscost";
import GoodscostEdit from "./GoodscostEdit";
import useCommonOptions from "@/hooks/useCommonOptions";
import TableList from "@/components/TableList";
import MergeSKU from "./GoodscostEdit/merge-SKU";
import { history } from "@umijs/max";
import queryString from "query-string";
import { PageUrl as LiveBroadcastingUrl } from "@/pages/Profitestim/LiveBroadcasting";
const PageUrl = "/profitestim/livegoodscost";
const Res: React.FC = () => {
  const proTableFormRef = useRef<any>();
  const [pageParams, setPageParams] = useState({});
  const [ruleClassOptions, ruleClassObj] = useCommonOptions({ dimName: "直播-加购商品规则大类" });

  const actionRef = useRef<any>();
  const [editProps, setEditProps] = useState<any>({
    open: false,
    fresh: () => {
      actionRef.current?.reload();
    },
    onClose: () => {
      setEditProps({ ...editProps, open: false });
    }
  });
  const handleDelete = async (record: any) => {
    const { status } = await apis.delete({
      id: record.id
    });
    if (status) {
      actionRef.current?.reload();
    }
  };
  const handleEdit = (record: any) => {
    const { id, anchorId, itemId, liveDate, anchorName } = record;
    setEditProps({
      ...editProps,
      ruleClass: ruleClassOptions,
      open: true,
      params: { id, anchorId, itemId, liveDate, anchorName }
    });
  };
  const handleDownloadSKU = () => {
    const { liveDate, itemIdList, anchorNameList }: any = pageParams;
    history.push({
      pathname: LiveBroadcastingUrl,
      search: queryString.stringify({
        liveDate: JSON.stringify(liveDate),
        itemIdList: JSON.stringify(itemIdList),
        anchorNameList: JSON.stringify(anchorNameList)
      })
    });
  };
  const [mergeProps, setMergeProps] = useState({
    title: "批量合并SKU",
    visible: false,
    params: {},
    showDownload: false,
    targetType: 0,
    fresh: () => {},
    close: () => {
      setMergeProps({ ...mergeProps, visible: false });
    }
  });
  const actions = () => {
    return [
      <Button key="upload" type="primary" size={SIZE} onClick={handleDownloadSKU}>
        下载系统SKU列表
      </Button>,
      <Button key="merge_sku" type="primary" size={SIZE} onClick={() => setMergeProps({ ...mergeProps, visible: true })}>
        批量合并SKU
      </Button>
    ];
  };
  const columnsProps = {
    handleDelete,
    handleEdit,
    ruleClassObj,
    ruleClassOptions
  };
  // 处理请求参数
  const handlePageParmas = (params: any) => {
    const { liveDate, current, ruleClassList, ...rest } = params;
    setPageParams(params);
    // 单独处理 直播日期
    if (liveDate) {
      const [liveDateStart, liveDateEnd] = liveDate;
      rest.liveDateStart = dayjs(liveDateStart).format("YYYY-MM-DD");
      rest.liveDateEnd = dayjs(liveDateEnd).format("YYYY-MM-DD");
    }
    rest.itemIdList = splitSpaceComma(rest.itemIdList);

    if (typeof ruleClassList !== "undefined") {
      rest.ruleClassList = [ruleClassList];
    }
    return { ...rest, pageNo: current };
  };
  return (
    <>
      <TableList
        headerTitle="直播加购商品成本"
        formRef={proTableFormRef}
        actionRef={actionRef}
        columnEmptyText={NOVALUE}
        columns={columns(columnsProps)}
        scroll={{ x: "max-content", y: "calc(100vh - 350px)" }}
        form={{
          ...Protable_FROM_CONFIG,
          initialValues: {
            liveDate: [dayjs().subtract(6, "month"), dayjs()]
          }
        }}
        api={apis.downloadFinanceShortVideoRecord}
        preFetch={handlePageParmas}
        rowKey={"id"}
        toolbar={{
          actions: actions()
        }}
      />

      {editProps.open ? <GoodscostEdit {...editProps} /> : null}
      {mergeProps.visible ? <MergeSKU {...mergeProps} /> : null}
    </>
  );
};

const AliveRecord = () => (
  <KeepAlive name={PageUrl}>
    <Res />
  </KeepAlive>
);
export default React.memo(AliveRecord);
