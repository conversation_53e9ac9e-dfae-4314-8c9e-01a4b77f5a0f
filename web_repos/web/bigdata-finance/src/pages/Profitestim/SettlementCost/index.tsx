import React, { useRef } from "react";
import TableList from "@/components/TableList";
import { columns } from "./columns";
import dayjs from "dayjs";
import KeepAlive from "react-activation";
import { paginationOptions, splitSpaceComma } from "@/utils";
import { NOVALUE, Protable_FROM_CONFIG } from "@/utils/constant";
import apis from "@/services/Profitestim/settlementCost";
const dateFormat = "YYYYMMDD";
const earningMonthFormat = "YYYYMM";
const PageUrl = "/profitestim/settlementCost";
const Res = () => {
  const formRef = useRef<any>();
  const actionRef = useRef<any>();
  const handleParams = (p: any) => {
    const { current, liveDate, earningMonth, itemIdList, liveItemCodeList, ...rest } = p;
    if (liveDate) {
      const [startTime, endTime] = liveDate;
      rest.startTime = dayjs(startTime).format(dateFormat);
      rest.endTime = dayjs(endTime).format(dateFormat);
    }
    if (earningMonth) {
      const [earningMonthStart, earningMonthEnd] = earningMonth;
      rest.earningMonthStart = dayjs(earningMonthStart).format(earningMonthFormat);
      rest.earningMonthEnd = dayjs(earningMonthEnd).format(earningMonthFormat);
    }
    rest.itemIdList = splitSpaceComma(itemIdList);
    rest.liveItemCodeList = splitSpaceComma(liveItemCodeList);
    return {
      ...rest,
      pageNo: current
    };
  };

  return (
    <>
      <TableList
        formRef={formRef}
        actionRef={actionRef}
        columnEmptyText={NOVALUE}
        scroll={{ x: "max-content", y: "calc(100vh - 320px)" }}
        form={{
          ...Protable_FROM_CONFIG,
          initialValues: {
            liveDate: [dayjs().subtract(1, "month").startOf("day"), dayjs().endOf("day")]
          }
        }}
        rowKey="id"
        pagination={{
          showQuickJumper: true,
          ...paginationOptions
        }}
        columns={columns({})}
        api={apis.getList}
        downloadApi={apis.downloadFile}
        preFetch={handleParams}
      />
    </>
  );
};

const AliveRecord = () => (
  <KeepAlive name={PageUrl}>
    <Res />
  </KeepAlive>
);
export default React.memo(AliveRecord);
