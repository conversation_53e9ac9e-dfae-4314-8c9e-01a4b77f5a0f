import React, { useEffect, useState } from "react";
import { SelectProps, Select } from "antd";
import apis from "@/services/Profitestim/settlementCost";
type IProps = SelectProps;
const OrderTypeSelect = (props: IProps) => {
  const [lists, setList] = useState<any>([]);
  const getList = async () => {
    let { entry = [], status } = await apis.getOrderType();
    if (status && entry?.length) {
      setList(entry.map(item => ({ label: item, value: item })));
    }
  };
  useEffect(() => {
    getList();
  }, []);
  return <Select placeholder="请选择" options={lists} showSearch allowClear {...props} />;
};
export default React.memo(OrderTypeSelect);
