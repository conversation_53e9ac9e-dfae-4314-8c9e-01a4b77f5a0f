import { DatePicker } from "antd";
import AnchorSelect from "@/components/AnchorSelect";
import { returnEllipsisTooltip } from "@/utils";
import OrderTypeSelect from "./components/OrderTypeSelect";
const columns_search = ({}) => {
  return [
    {
      title: "直播日期",
      dataIndex: "liveDate",
      hideInTable: true,
      valueType: "dateRange"
    },
    {
      title: "结算月份",
      dataIndex: "earningMonth",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return <DatePicker.RangePicker picker="month" allowClear />;
      }
    },
    {
      title: "主播PID",
      dataIndex: "anchorId",
      hideInTable: true
    },
    {
      title: "主播名称",
      dataIndex: "anchorNameList",
      hideInTable: true,
      renderFormItem: () => {
        return <AnchorSelect maxTagCount="responsive" mode="multiple" />;
      }
    },
    {
      title: "商品ID",
      dataIndex: "itemIdList",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "店铺名称",
      dataIndex: "shopName",
      hideInTable: true
    },

    {
      title: "商品编码",
      dataIndex: "liveItemCodeList",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "商品名称",
      dataIndex: "itemName",
      hideInTable: true
    },
    {
      title: "店铺ID",
      dataIndex: "shopId",
      hideInTable: true
    },
    {
      title: "订单类型",
      dataIndex: "orderTypeList",
      hideInTable: true,
      renderFormItem: () => {
        return <OrderTypeSelect maxTagCount="responsive" mode="multiple" />;
      }
    }
  ];
};
export const columns = ({}): Array<TableListItem> => {
  return [
    ...columns_search({}),
    {
      title: "序号",
      dataIndex: "index",
      fixed: "left",
      width: 60,
      hideInSearch: true,
      render: (text: any, row: any, index: number) => {
        return index + 1;
      }
    },
    {
      title: "直播日期",
      dataIndex: "liveDate",
      width: 100,
      hideInSearch: true
    },
    {
      title: "主播PID",
      dataIndex: "anchorId",
      width: 100,
      hideInSearch: true
    },
    {
      title: "主播名称",
      dataIndex: "anchorName",
      width: 100,
      hideInSearch: true
    },
    {
      title: "商品ID",
      dataIndex: "itemId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品名称",
      dataIndex: "liveItemName",
      width: 200,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品编码",
      dataIndex: "liveItemCode",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "店铺ID",
      dataIndex: "shopId",
      width: 100,
      hideInSearch: true
    },
    {
      title: "店铺名称",
      dataIndex: "shopName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "订单类型",
      dataIndex: "orderType",
      width: 100,
      hideInSearch: true
    },
    {
      title: "结算月份",
      dataIndex: "earningMonth",
      width: 100,
      hideInSearch: true
    },
    {
      title: "数量",
      dataIndex: "settleItemVolume",
      width: 100,
      hideInSearch: true
    }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      ...item,
      align: "center",
      title: returnEllipsisTooltip({ title })
    };
  });
};
