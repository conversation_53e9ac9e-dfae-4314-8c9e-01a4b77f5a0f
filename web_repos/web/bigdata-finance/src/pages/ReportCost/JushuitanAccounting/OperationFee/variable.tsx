import { DatePicker } from "antd";
import AnchorSelect from "@/components/AnchorSelect";
import { returnEllipsisTooltip } from "@/utils";
const columns_search = () => {
  return [
    {
      title: "退款月份",
      dataIndex: "returnLiveDate",
      hideInTable: true,
      renderFormItem: () => {
        return <DatePicker.RangePicker picker="month" allowClear />;
      }
    },

    {
      title: "直播日期",
      dataIndex: "liveDate",
      hideInTable: true,
      valueType: "dateRange"
    },
    {
      title: "主播名称",
      dataIndex: "anchorName",
      hideInTable: true,
      renderFormItem() {
        return <AnchorSelect showSearch allowClear maxTagCount="responsive" mode="multiple" />;
      }
    },
    {
      title: "商品ID",
      dataIndex: "itemId",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号"
      }
    }
  ];
};
export const columns = ({}): Array<TableListItem> => {
  return [
    ...columns_search(),
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      hideInSearch: true,
      fixed: "left",
      render: (_, record, index) => {
        return index + 1;
      }
    },
    {
      title: "直播日期",
      dataIndex: "liveDate",
      width: 100,
      hideInSearch: true
    },
    {
      title: "退款月份",
      dataIndex: "refundMonth",
      width: 100,
      hideInSearch: true
    },

    {
      title: "收货仓",
      dataIndex: "confirmWarehouse",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "主播pid",
      dataIndex: "pid",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "主播",
      dataIndex: "pidName",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "店铺id",
      dataIndex: "shopId",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "店铺名称",
      dataIndex: "shopName",
      width: 180,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品ID",
      dataIndex: "itemId",
      width: 120,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品名称",
      dataIndex: "itemTitle",
      width: 240,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },

    {
      title: "退款订单数量",
      dataIndex: "refundOrderCntStr",
      width: 120,
      hideInSearch: true
    },

    {
      title: "操作费单价（元）",
      dataIndex: "operatePriceStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "操作费汇总（元）",
      dataIndex: "totalOperatePriceStr",
      width: 100,
      hideInSearch: true
    }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      align: "center",
      ...item,
      title: returnEllipsisTooltip({ title })
    };
  });
};
