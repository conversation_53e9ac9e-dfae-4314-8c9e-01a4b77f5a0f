import { Tabs } from "antd";
import React, { useState } from "react";
import KeepAlive from "react-activation";
import RefundAfterSale from "./RefundAfterSale";
import OperationFee from "./OperationFee";
import { SIZE, tabs_tabBarStyle } from "@/utils/constant";

const pageUrl = "/reportCost/jushuitanAccounting";
export const aftersael_refund = "a";
export const operation_fee = "b";
const Res = () => {
  const [curTab, setCurTab] = useState(aftersael_refund);
  const tabItems = [
    {
      key: aftersael_refund,
      label: "售后退款",
      children: <RefundAfterSale />
    },
    {
      key: operation_fee,
      label: "售后操作费",
      children: <OperationFee />
    }
  ];
  return <Tabs activeKey={curTab} tabBarStyle={tabs_tabBarStyle} size={SIZE} onChange={setCurTab} items={tabItems} />;
};

const AliveAnchorItemSale = props => (
  <KeepAlive name={pageUrl}>
    <Res {...props} />
  </KeepAlive>
);

export default React.memo(AliveAnchorItemSale);
