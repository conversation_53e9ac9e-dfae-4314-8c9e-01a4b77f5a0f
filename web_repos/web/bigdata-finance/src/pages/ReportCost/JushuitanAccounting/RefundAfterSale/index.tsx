import { Button } from "antd";
import React, { useState } from "react";
import apis from "@/services/settlement/jushuitanAccounting";
import { pushExportHistory, splitSpaceComma } from "@/utils";
import TableList from "@/components/TableList";
import { columns } from "./variable";
import dayjs from "dayjs";
const Res = ({}) => {
  const [pageParams, setPageParams] = useState({});
  const handlePageParmas = (params: any) => {
    const { current, liveDate, returnLiveDate, itemId, ...rest } = params;
    if (liveDate) {
      const [startTime, endTime] = liveDate;
      rest.startTime = dayjs(startTime).format("YYYYMMDD");
      rest.endTime = dayjs(endTime).format("YYYYMMDD");
    }
    if (returnLiveDate) {
      const [startRefundMonth, endRefundMonth] = returnLiveDate;
      rest.startRefundMonth = dayjs(startRefundMonth).format("YYYYMM");
      rest.endRefundMonth = dayjs(endRefundMonth).format("YYYYMM");
    }
    rest.itemId = splitSpaceComma(itemId);
    return { ...rest, pageNo: current };
  };

  const handleDownload = async () => {
    const { status } = await apis.downloadAfterRefund(pageParams);
    if (status) {
      pushExportHistory();
    }
  };

  return (
    <>
      <TableList
        scroll={{ y: "calc(100vh - 370px)" }}
        form={{
          initialValues: {
            returnLiveDate: [dayjs().subtract(1, "month"), dayjs()]
          }
        }}
        columns={columns({})}
        api={apis.getAfterRefundList}
        preFetch={handlePageParmas}
        paramsChange={setPageParams}
        rowKey="uuid"
        toolbar={{
          actions: [
            <Button key="export" size="small" onClick={() => handleDownload()}>
              导出
            </Button>
          ]
        }}
      />
    </>
  );
};

export default React.memo(Res);
