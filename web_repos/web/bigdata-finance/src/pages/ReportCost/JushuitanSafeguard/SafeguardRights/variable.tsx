import { DatePicker } from "antd";
import { returnEllipsisTooltip } from "@/utils";
import AnchorSelect from "@/components/AnchorSelect";
const columns_search = () => {
  return [
    {
      title: "退款月份",
      dataIndex: "returnLiveDate",
      hideInTable: true,
      renderFormItem: () => {
        return <DatePicker.RangePicker picker="month" allowClear />;
      }
    },

    {
      title: "直播日期",
      dataIndex: "liveDate",
      hideInTable: true,
      valueType: "dateRange"
    },

    {
      title: "主播名称",
      dataIndex: "anchorName",
      hideInTable: true,
      renderFormItem() {
        return <AnchorSelect showSearch allowClear maxTagCount="responsive" mode="multiple" />;
      }
    },
    {
      title: "商品编码",
      dataIndex: "itemCode",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },

    {
      title: "商品ID",
      dataIndex: "itemId",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    }
  ];
};
export const columns = ({}: any): Array<TableListItem> => {
  return [
    ...columns_search(),
    {
      title: "序号",
      dataIndex: "index",
      width: 60,
      fixed: "left",
      hideInSearch: true,
      render: (text: any, row: any, index: number) => {
        return index + 1;
      }
    },
    {
      title: "数据来源",
      dataIndex: "dataSource",
      width: 80,
      hideInSearch: true
    },
    {
      title: "直播日期",
      dataIndex: "liveDate",
      width: 100,
      valueType: "date",
      hideInSearch: true
    },
    {
      title: "主播pid",
      dataIndex: "pid",
      width: 80,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "主播名称",
      dataIndex: "pidName",
      width: 80,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "平台商品ID",
      dataIndex: "itemId",
      width: 90,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "平台商品名称",
      dataIndex: "itemTitle",
      width: 120,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "店铺ID",
      dataIndex: "shopId",
      width: 80,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "店铺名称",
      dataIndex: "shopName",
      width: 80,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品编码",
      dataIndex: "itemCode",
      width: 80,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "款式编码",
      dataIndex: "styleCode",
      width: 80,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "聚水潭商品名称",
      dataIndex: "jstItemName",
      width: 120,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "退款月份",
      dataIndex: "refundMonth",
      width: 90,
      valueType: "dateMonth",
      hideInSearch: true
    },
    {
      title: "结算月份",
      dataIndex: "earnMonth",
      width: 90,
      valueType: "dateMonth",
      hideInSearch: true
    },
    {
      title: "维权月份",
      dataIndex: "weiQuanMonth",
      width: 90,
      valueType: "dateMonth",
      hideInSearch: true
    },

    {
      title: "退款件数",
      dataIndex: "actualRefundCntStr",
      width: 80,
      hideInSearch: true
    },

    {
      title: "退款金额",
      dataIndex: "refundAmtStr",
      width: 80,
      hideInSearch: true
    }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      align: "center",
      ...item,
      title: returnEllipsisTooltip({ title })
    };
  });
};
