import React from "react";
import apis from "@/services/settlement/jushuitanSafeguard";
import { splitSpaceComma } from "@/utils";
import TableList from "@/components/TableList";
import { columns } from "./variable";
import dayjs from "dayjs";

const dateFormatMonth = "YYYYMM";
const dateFormat = "YYYYMMDD";
const Res = () => {
  const handlePageParmas = (params: any) => {
    const { current, liveDate, returnLiveDate, itemId, itemCode, ...rest } = params;
    if (returnLiveDate) {
      const [startRefundMonth, endRefundMonth] = returnLiveDate;
      rest.startRefundMonth = dayjs(startRefundMonth).format(dateFormatMonth);
      rest.endRefundMonth = dayjs(endRefundMonth).format(dateFormatMonth);
    }
    if (liveDate) {
      const [startTime, endTime] = liveDate;
      rest.startTime = dayjs(startTime).format(dateFormat);
      rest.endTime = dayjs(endTime).format(dateFormat);
    }

    rest.itemId = splitSpaceComma(itemId);
    rest.itemCode = splitSpaceComma(itemCode);
    return { ...rest, pageNo: current };
  };

  return (
    <TableList
      scroll={{ y: "calc(100vh - 340px)" }}
      form={{
        initialValues: {
          returnLiveDate: [dayjs().subtract(1, "month"), dayjs()]
        }
      }}
      columns={columns({})}
      api={apis.getAfterProtectRightList}
      downloadApi={apis.downloadAfterProtectRight}
      preFetch={handlePageParmas}
    />
  );
};

export default React.memo(Res);
