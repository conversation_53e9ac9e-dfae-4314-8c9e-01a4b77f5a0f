import React, { useState } from "react";
import { Tabs } from "antd";
import KeepAlive from "react-activation";
import { SIZE, tabs_tabBarStyle } from "@/utils/constant";
import SafeguardRights from "./SafeguardRights";
import OperationFee from "./OperationFee";
export const PageUrl = "/reportCost/jushuitanSafeguard";
export const tab_guard = "a";
export const tab_bill = "b";
const Res = () => {
  const [curTab, setCurTab] = useState(tab_guard);

  const tabItems = [
    {
      key: tab_guard,
      label: "售后维权",
      children: <SafeguardRights />
    },
    {
      key: tab_bill,
      label: "售后账单",
      children: <OperationFee />
    }
  ];
  return <Tabs activeKey={curTab} tabBarStyle={tabs_tabBarStyle} size={SIZE} onChange={setCurTab} items={tabItems} />;
};
const AliveRecord = props => (
  <KeepAlive name={PageUrl}>
    <Res {...props} />
  </KeepAlive>
);

export default React.memo(AliveRecord);
