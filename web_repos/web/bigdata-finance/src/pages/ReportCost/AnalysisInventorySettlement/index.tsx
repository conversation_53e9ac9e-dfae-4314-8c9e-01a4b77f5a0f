import React, { useState, useRef } from "react";
import TableList from "@/components/TableList";
import { columns } from "./variable";
import KeepAlive from "react-activation";
import { Button } from "antd";
import UploadModal from "@/components/UploadModal";
import { SIZE } from "@/utils/constant";
import apis from "@/services/settlement/analysisInventorySettlement";
import { splitSpaceComma } from "@/utils";
import useCommonOptions from "@/hooks/useCommonOptions";
export const PageUrl = "/reportCost/analysisInventorySettlement";
const Res = () => {
  const actions = useRef<any>();
  const formRef = useRef<any>();
  const [propNameOptions] = useCommonOptions({ dimName: "进销存结算分析-属性" });
  const [showExcelUpload, setshowExcelUpload] = useState(false);
  const handlePageParmas = (params: any) => {
    const { current, styleCodeList, itemCodeList, ...rest } = params;
    rest.styleCodeList = splitSpaceComma(styleCodeList);
    rest.itemCodeList = splitSpaceComma(itemCodeList);
    return { ...rest, pageNo: current };
  };
  const toolbarActions = () => {
    return [
      <Button key="upload" type="primary" onClick={() => setshowExcelUpload(true)} size={SIZE}>
        上传
      </Button>
    ];
  };
  const UploadModalProps = {
    title: "上传项目信息",
    visible: showExcelUpload,
    targetType: 0,
    api: apis.addInvoicingSettlementAnalysisFromExcel,
    message: (
      <>
        <span>1. 模板中黄色背景字段为必填项</span>
        <br />
        <span>2. 每个商品编码对应一个项目，多次上传以最后一次数据为准</span>
        <br />
      </>
    ),
    tempUrl: "https://s.xinc818.com/files/webcim43m8h4jcchnot/进销存结算分析-导入模版.xlsx",
    fresh: () => {
      actions?.current?.reload();
    },
    close: () => {
      setshowExcelUpload(false);
    }
  };
  const columnsProps = {
    propNameOptions
  };
  return (
    <>
      <TableList
        headerTitle="进销存结算分析"
        actionRef={actions}
        formRef={formRef}
        scroll={{ y: "calc(100vh - 380px)" }}
        form={{
          initialValues: {}
        }}
        columns={columns(columnsProps)}
        api={apis.getInvoicingSettlementAnalysisPage}
        downloadApi={apis.downloadInvoicingSettlementAnalysis}
        preFetch={handlePageParmas}
        toolbar={{
          actions: toolbarActions()
        }}
      />
      {showExcelUpload ? <UploadModal {...UploadModalProps} /> : null}
    </>
  );
};
const AliveAnchorItemSale = props => (
  <KeepAlive name={PageUrl}>
    <Res {...props} />
  </KeepAlive>
);

export default React.memo(AliveAnchorItemSale);
