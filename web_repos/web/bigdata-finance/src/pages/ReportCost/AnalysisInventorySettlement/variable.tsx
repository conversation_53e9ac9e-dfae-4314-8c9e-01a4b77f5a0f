import { Select } from "antd";
import { filterOptionLabel, returnEllipsisTooltip } from "@/utils";
const columns_search = ({ propNameOptions }) => {
  return [
    {
      title: "项目",
      dataIndex: "projectName",
      hideInTable: true
    },

    {
      title: "项目负责人",
      dataIndex: "projectLeader",
      hideInTable: true
    },
    {
      title: "款式编码",
      dataIndex: "styleCodeList",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号"
      }
    },
    {
      title: "商品编码",
      dataIndex: "itemCodeList",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号"
      }
    },
    {
      title: "商品名称",
      dataIndex: "itemTitle",
      hideInTable: true
    },
    {
      title: "属性",
      dataIndex: "propName",
      hideInTable: true,
      renderFormItem() {
        return <Select placeholder="请选择" allowClear options={propNameOptions} filterOption={filterOptionLabel} />;
      }
    },
    {
      title: "采购主体",
      dataIndex: "purchasingSubject",
      hideInTable: true
    }
  ];
};
export const columns = ({ ...rest }): Array<TableListItem> => {
  return [
    ...columns_search(rest),
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      hideInSearch: true,
      fixed: "left",
      render: (_, record, index) => {
        return index + 1;
      }
    },
    {
      title: "项目",
      dataIndex: "projectName",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "项目明细",
      dataIndex: "projectDetail",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },

    {
      title: "项目负责人",
      dataIndex: "projectLeader",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "款式编码",
      dataIndex: "styleCode",
      width: 110,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品编码",
      dataIndex: "itemCode",
      width: 110,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品名称",
      dataIndex: "itemTitle",
      width: 160,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "属性",
      dataIndex: "propName",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "采购主体",
      dataIndex: "purchasingSubject",
      width: 180,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "发出情况",
      hideInSearch: true,
      children: [
        {
          title: "订单发货",
          dataIndex: "consignItemCnt",
          width: 100
        },
        {
          title: "订单补发",
          dataIndex: "reissueConsignItemCnt",
          width: 100
        },
        {
          title: "返修出库",
          dataIndex: "repairOutWhItemCnt",
          width: 100
        },
        {
          title: "返修入库",
          dataIndex: "repairInWhItemCnt",
          width: 120
        },
        {
          title: "其他出库",
          dataIndex: "otherOutWhItemCnt",
          width: 100
        }
      ]
    },
    {
      title: "订单结算情况",
      hideInSearch: true,
      children: [
        {
          title: "未结算数量",
          dataIndex: "notSettlementOrderCnt",
          width: 100
        },
        {
          title: "已结算数量",
          dataIndex: "settlementOrderCnt",
          width: 100
        },
        {
          title: "订单总数",
          dataIndex: "orderCnt",
          width: 100
        }
      ]
    },
    {
      title: "未结算原因说明",
      hideInSearch: true,
      children: [
        {
          title: "待结算（未到结算期）",
          dataIndex: "waitSettlementOrderCnt",
          width: 120
        },
        {
          title: "售后退货",
          dataIndex: "sellAfterRefundOrderCnt",
          width: 100
        },
        {
          title: "发错货已结算",
          dataIndex: "consignErrorSettlementOrderCnt",
          width: 100
        },
        {
          title: "丢件补发",
          dataIndex: "consignLossRetryOrderCnt",
          width: 100
        },
        {
          title: "产品质量问题仅退款",
          dataIndex: "qualityProblemRefundOrderCnt",
          width: 120
        },
        {
          title: "仓库录入单据失误",
          dataIndex: "whDocumentErrorOrderCnt",
          width: 100
        },
        {
          title: "待核实差异",
          dataIndex: "otherQuestionOrderCnt",
          width: 100
        }
      ]
    },
    {
      title: "返修仓",
      dataIndex: "repairWhItemCnt",
      hideInSearch: true,
      width: 100
    },
    {
      title: "在仓数",
      dataIndex: "whItemCnt",
      hideInSearch: true,
      width: 100
    },
    {
      title: "在仓库存分析",
      hideInSearch: true,
      children: [
        {
          title: "库龄3个月以内",
          dataIndex: "stockAge3mBeforeItemCnt",
          width: 100
        },
        {
          title: "库龄3-6月以内",
          dataIndex: "stockAge3m6mItemCnt",
          width: 100
        },
        {
          title: "库龄6-12月以内",
          dataIndex: "stockAge6m12mItemCnt",
          width: 100
        },
        {
          title: "库龄1-2年以内",
          dataIndex: "stockAge1y2yItemCnt",
          width: 100
        },
        {
          title: "库龄2年以上",
          dataIndex: "stockAge2yAfterItemCnt",
          width: 100
        }
      ]
    }
  ].map(item => {
    const { title, children = [] } = item;
    return {
      align: "center",
      ...item,
      title: returnEllipsisTooltip({ title }),
      children: children?.map(item => {
        const { title } = item;
        return {
          align: "center",
          ...item,
          title: returnEllipsisTooltip({ title })
        };
      })
    };
  });
};
