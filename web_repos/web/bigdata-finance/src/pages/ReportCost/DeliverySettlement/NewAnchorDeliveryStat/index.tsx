import React from "react";
import apis from "@/services/settlement/deliverySettlement";
import { splitSpaceComma } from "@/utils";
import { columns } from "./variable";
import dayjs from "dayjs";
import TableList from "@/components/TableList";

const dateFormat = "YYYYMMDD";
const NewAnchorDeliveryStat = () => {
  const columns_props = {};
  const handlePageParmas = (params: any) => {
    const { current, deliveryDate, liveDate, itemIds, ...rest } = params;
    if (deliveryDate) {
      const [startTime, endTime] = deliveryDate;
      rest.startTime = dayjs(startTime).format(dateFormat);
      rest.endTime = dayjs(endTime).format(dateFormat);
    }
    if (liveDate) {
      const [startTime, endTime] = liveDate;
      rest.liveStartTime = dayjs(startTime).format(dateFormat);
      rest.liveEndTime = dayjs(endTime).format(dateFormat);
    }
    rest.itemIds = splitSpaceComma(itemIds);
    rest.groupByColum =
      "deliveryDate,liveDate,pid,pidName,province,warehouseType,itemId,itemName,shopId,shopName,deliveryWarehouse,expressCompany,jstSkuId,orderSource,orderType";
    return { ...rest, pageNo: current };
  };

  return (
    <TableList
      scroll={{ y: "calc(100vh - 410px)" }}
      form={{
        initialValues: {
          deliveryDate: [dayjs().subtract(1, "month"), dayjs()]
        }
      }}
      columns={columns(columns_props)}
      api={apis.getAnchorExpressSummary}
      downloadApi={apis.downloadAnchorSummary}
      preFetch={handlePageParmas}
      rowKey={record => {
        const {
          deliveryDate,
          liveDate,
          pid,
          pidName,
          province,
          warehouseType,
          itemId,
          itemName,
          shopId,
          shopName,
          deliveryWarehouse,
          expressCompany,
          jstSkuId,
          orderSource,
          orderType
        } = record;
        return (
          deliveryDate +
          liveDate +
          pid +
          pidName +
          province +
          warehouseType +
          itemId +
          itemName +
          shopId +
          shopName +
          deliveryWarehouse +
          expressCompany +
          jstSkuId +
          orderSource +
          orderType
        );
      }}
    />
  );
};

export default React.memo(NewAnchorDeliveryStat);
