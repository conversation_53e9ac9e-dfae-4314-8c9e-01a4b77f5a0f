import { returnEllipsisTooltip } from "@/utils";
import SelectLabel from "@/components/select-label";
import apis from "@/services/settlement/deliverySettlement";

const columns_search = ({}: any) => {
  return [
    {
      title: "发货日期",
      dataIndex: "deliveryDate",
      hideInTable: true,
      valueType: "dateRange"
    },
    {
      title: "直播日期",
      dataIndex: "liveDate",
      hideInTable: true,
      valueType: "dateRange"
    },
    {
      title: "匹配店铺名称",
      dataIndex: "shopName",
      hideInTable: true
    },
    {
      title: "匹配店铺ID",
      dataIndex: "shopId",
      hideInTable: true
    },
    {
      title: "订单来源",
      dataIndex: "orderSource",
      hideInTable: true,
      renderFormItem() {
        return <SelectLabel api={apis.getPullDownData} field="orderSource" type={1} />;
      }
    },
    {
      title: "订单类型",
      dataIndex: "orderType",
      hideInTable: true,
      renderFormItem() {
        return <SelectLabel api={apis.getPullDownData} field="orderType" type={1} />;
      }
    },
    {
      title: "匹配商品名称",
      dataIndex: "itemName",
      hideInTable: true
    },
    {
      title: "省份",
      dataIndex: "province",
      hideInTable: true,
      renderFormItem() {
        return <SelectLabel api={apis.getPullDownData} field="province" type={1} />;
      }
    },
    {
      title: "快递公司",
      dataIndex: "expressCompany",
      hideInTable: true,
      renderFormItem() {
        return <SelectLabel api={apis.getPullDownData} field="expressCompany" type={1} />;
      }
    },
    {
      title: "推广者名称",
      dataIndex: "anchorNames",
      hideInTable: true,
      renderFormItem() {
        return <SelectLabel api={apis.getPullDownData} field="pidName" type={1} maxTagCount="responsive" mode="multiple" />;
      }
    },
    {
      title: "匹配商品ID",
      dataIndex: "itemIds",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "仓库类型",
      dataIndex: "warehouseType",
      hideInTable: true,
      renderFormItem() {
        return <SelectLabel api={apis.getPullDownData} field="warehouseType" type={1} />;
      }
    }
  ];
};
export const columns = ({}: any): Array<TableListItem> => {
  return [
    ...columns_search({}),
    {
      title: "序号",
      dataIndex: "$index",
      fixed: "left",
      width: 60,
      hideInSearch: true,
      render: (text: any, row: any, index: number) => {
        return index + 1;
      }
    },
    {
      title: "发货日期",
      dataIndex: "deliveryDate",
      width: 100,
      valueType: "date",
      hideInSearch: true
    },
    {
      title: "直播日期",
      dataIndex: "liveDate",
      width: 100,
      valueType: "date",
      hideInSearch: true
    },
    {
      title: "推广者ID",
      dataIndex: "pid",
      width: 80,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "推广者名称",
      dataIndex: "pidName",
      width: 90,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "省份",
      dataIndex: "province",
      width: 60,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "仓库类型",
      dataIndex: "warehouseType",
      width: 80,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "匹配商品ID",
      dataIndex: "itemId",
      width: 90,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "匹配商品名称",
      dataIndex: "itemName",
      width: 120,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "是否挂链",
      dataIndex: "chainingState",
      width: 80,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "匹配店铺ID",
      dataIndex: "shopId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "匹配店铺名称",
      dataIndex: "shopName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "发货仓库",
      dataIndex: "deliveryWarehouse",
      width: 80,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "快递公司",
      dataIndex: "expressCompany",
      width: 80,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "聚水潭商品编码",
      dataIndex: "jstSkuId",
      width: 120,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "订单来源",
      dataIndex: "orderSource",
      width: 80,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "订单类型",
      dataIndex: "orderType",
      width: 80,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "快递单量",
      dataIndex: "orderExpressCnt",
      width: 80,
      hideInSearch: true,
      render(_, record) {
        const { orderExpressCntStr } = record;
        return returnEllipsisTooltip({ title: orderExpressCntStr });
      }
    },
    {
      title: "销售数量",
      dataIndex: "itemVolume",
      width: 80,
      hideInSearch: true,
      render(_, record) {
        const { itemVolumeStr } = record;
        return returnEllipsisTooltip({ title: itemVolumeStr });
      }
    },
    {
      title: "销售金额",
      dataIndex: "itemAmount",
      width: 80,
      hideInSearch: true,
      render(_, record) {
        const { itemAmountStr } = record;
        return returnEllipsisTooltip({ title: itemAmountStr });
      }
    },
    {
      title: "包材重量",
      dataIndex: "materWeight",
      width: 80,
      hideInSearch: true
    },
    {
      title: "订单重量",
      dataIndex: "itemWeight",
      width: 80,
      hideInSearch: true
    },
    {
      title: "包裹总重量",
      dataIndex: "orderExpressWeight",
      width: 90,
      hideInSearch: true
    },
    {
      title: "操作费",
      dataIndex: "operFee",
      width: 80,
      hideInSearch: true,
      render(_, record) {
        const { operFeeStr } = record;
        return returnEllipsisTooltip({ title: operFeeStr });
      }
    },
    {
      title: "包材费",
      dataIndex: "materFee",
      width: 80,
      hideInSearch: true,
      render(_, record) {
        const { materFeeStr } = record;
        return returnEllipsisTooltip({ title: materFeeStr });
      }
    },
    {
      title: "快递费",
      dataIndex: "expressFee",
      width: 80,
      hideInSearch: true,
      render(_, record) {
        const { expressFeeStr } = record;
        return returnEllipsisTooltip({ title: expressFeeStr });
      }
    },
    {
      title: "总快递费",
      dataIndex: "orderExpressFee",
      width: 80,
      hideInSearch: true,
      render(_, record) {
        const { orderExpressFeeStr } = record;
        return returnEllipsisTooltip({ title: orderExpressFeeStr });
      }
    }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      align: "center",
      ...item,
      title: returnEllipsisTooltip({ title })
    };
  });
};
