import { Tabs } from "antd";
import React, { useState } from "react";
import KeepAlive from "react-activation";
import NewAnchorDeliveryStat from "./NewAnchorDeliveryStat";
import NewDeliveryFee from "./NewDeliveryFee";
import DeliveryFee from "./DeliveryFee";
import JSTDeliverySettlement from "./JSTDeliverySettlement";
import JSTItemDelivery from "./JSTItemDelivery";
import { SIZE, tabs_tabBarStyle } from "@/utils/constant";
export const anchorDeliveryNew = "anchorDeliveryNew";
export const deliveryFeeNew = "deliveryFeeNew";
export const deliveryFeeDetail = "deliveryFeeDetail";
export const deliverySettlement = "deliverySettlement";
export const itemDelivery = "itemDelivery";
export const PageUrl = "/reportCost/deliverySettlement";

const Res = () => {
  const [curTab, setCurTab] = useState(anchorDeliveryNew);

  const tabItems = [
    {
      key: anchorDeliveryNew,
      label: "新规则主播快递统计表",
      children: <NewAnchorDeliveryStat />
    },
    {
      key: deliveryFeeNew,
      label: "新规则快递费明细表",
      children: <NewDeliveryFee />
    },
    {
      key: deliveryFeeDetail,
      label: "快递费明细表",
      children: <DeliveryFee />
    },
    {
      key: deliverySettlement,
      label: "聚水潭快递结算表",
      children: <JSTDeliverySettlement />
    },
    {
      key: itemDelivery,
      label: "聚水潭商品发货表",
      children: <JSTItemDelivery />
    }
  ];

  return <Tabs activeKey={curTab} tabBarStyle={tabs_tabBarStyle} size={SIZE} onChange={setCurTab} items={tabItems} />;
};

const AliveRecord = props => (
  <KeepAlive name={PageUrl}>
    <Res {...props} />
  </KeepAlive>
);

export default React.memo(AliveRecord);
