import React from "react";
import apis from "@/services/settlement/deliverySettlement";
import { splitSpaceComma } from "@/utils";
import { columns } from "./variable";
import dayjs from "dayjs";
import TableList from "@/components/TableList";

const dateFormat = "YYYYMMDD";
const NewAnchorDeliveryStat = () => {
  const handlePageParmas = (params: any) => {
    const { current, deliveryDate, liveDate, itemIds, shopId, ...rest } = params;
    if (deliveryDate) {
      const [startTime, endTime] = deliveryDate;
      rest.startTime = dayjs(startTime).format(dateFormat);
      rest.endTime = dayjs(endTime).format(dateFormat);
    }
    if (liveDate) {
      const [liveStartTime, liveEndTime] = liveDate;
      rest.liveStartTime = dayjs(liveStartTime).format(dateFormat);
      rest.liveEndTime = dayjs(liveEndTime).format(dateFormat);
    }

    rest.itemIds = splitSpaceComma(itemIds);
    rest.shopId = splitSpaceComma(shopId);
    return { ...rest, pageNo: current };
  };

  return (
    <TableList
      scroll={{ y: "calc(100vh - 410px)" }}
      form={{
        initialValues: {
          deliveryDate: [dayjs().subtract(1, "month"), dayjs()]
        }
      }}
      columns={columns({})}
      api={apis.getExpressFeeDetailList}
      downloadApi={apis.downloadExpressFeeDetailList}
      preFetch={handlePageParmas}
      rowKey={record => {
        const { liveDate, deliveryDate, shopId, exprssNo, pid, jstSkuId, itemId } = record;
        return liveDate + deliveryDate + shopId + exprssNo + pid + jstSkuId + itemId;
      }}
    />
  );
};

export default React.memo(NewAnchorDeliveryStat);
