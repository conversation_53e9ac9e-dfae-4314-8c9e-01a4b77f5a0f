import apis from "@/services/settlement/deliverySettlement";
import SelectLabel from "@/components/select-label";
import { returnEllipsisTooltip } from "@/utils";

const columns_search = () => {
  return [
    {
      title: "发货日期",
      dataIndex: "deliveryDate",
      hideInTable: true,
      valueType: "dateRange"
    },
    {
      title: "直播日期",
      dataIndex: "liveDate",
      hideInTable: true,
      valueType: "dateRange"
    },
    {
      title: "商品ID",
      dataIndex: "itemIds",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "店铺ID",
      dataIndex: "shopId",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "主播名称",
      dataIndex: "anchorNames",
      hideInTable: true,
      renderFormItem() {
        return <SelectLabel api={apis.getPullDownData} field="pidName" maxTagCount="responsive" mode="multiple" type={2} />;
      }
    },
    {
      title: "订单来源",
      dataIndex: "orderSource",
      hideInTable: true,
      renderFormItem() {
        return <SelectLabel api={apis.getPullDownData} field="orderSource" type={2} />;
      }
    },
    {
      title: "仓库类型",
      dataIndex: "warehouseType",
      hideInTable: true,
      renderFormItem() {
        return <SelectLabel api={apis.getPullDownData} field="warehouseType" type={2} />;
      }
    },
    {
      title: "店铺名称",
      dataIndex: "shopName",
      hideInTable: true,
      renderFormItem() {
        return <SelectLabel api={apis.getPullDownData} field="shopName" type={2} />;
      }
    },
    {
      title: "快递公司",
      dataIndex: "expressCompany",
      hideInTable: true,
      renderFormItem() {
        return <SelectLabel api={apis.getPullDownData} field="expressCompany" type={2} />;
      }
    },
    {
      title: "发货仓库",
      dataIndex: "deliveryWarehouse",
      hideInTable: true,
      renderFormItem() {
        return <SelectLabel api={apis.getPullDownData} field="deliveryWarehouse" type={2} />;
      }
    },
    {
      title: "商品名称",
      dataIndex: "itemName",
      hideInTable: true
    },
    {
      title: "计算方式",
      dataIndex: "summaryWay",
      hideInTable: true,
      renderFormItem() {
        return <SelectLabel api={apis.getPullDownData} field="summaryWay" type={2} />;
      }
    },
    {
      title: "快递单号",
      dataIndex: "expressNo",
      hideInTable: true
    }
  ];
};
export const columns = ({}): Array<TableListItem> => {
  return [
    ...columns_search(),
    {
      title: "序号",
      dataIndex: "index",
      width: 60,
      fixed: "left",
      hideInSearch: true,
      render: (text: any, row: any, index: number) => {
        return index + 1;
      }
    },
    {
      title: "发货日期",
      dataIndex: "deliveryDate",
      width: 100,
      valueType: "date",
      hideInSearch: true
    },
    {
      title: "直播日期",
      dataIndex: "liveDate",
      width: 100,
      valueType: "date",
      hideInSearch: true
    },
    {
      title: "匹配商品ID",
      dataIndex: "itemId",
      width: 90,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "匹配商品名称",
      dataIndex: "itemName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "是否挂链",
      dataIndex: "chainingState",
      width: 80,
      hideInSearch: true
    },
    {
      title: "匹配店铺ID",
      dataIndex: "shopId",
      width: 90,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "匹配店铺名称",
      dataIndex: "shopName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "发货仓库",
      dataIndex: "deliveryWarehouse",
      width: 80,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "仓库类型",
      dataIndex: "warehouseType",
      width: 80,
      hideInSearch: true
    },
    {
      title: "快递公司",
      dataIndex: "expressCompany",
      width: 80,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "快递单号",
      dataIndex: "exprssNo",
      width: 80,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "推广者ID",
      dataIndex: "pid",
      width: 90,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "推广者名称",
      dataIndex: "pidName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "省份",
      dataIndex: "province",
      width: 60,
      hideInSearch: true
    },
    {
      title: "聚水潭商品编码",
      dataIndex: "jstSkuId",
      width: 120,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "订单来源",
      dataIndex: "orderSource",
      width: 80,
      hideInSearch: true
    },
    {
      title: "订单类型",
      dataIndex: "orderType",
      width: 80,
      hideInSearch: true
    },
    {
      title: "计算方式",
      dataIndex: "summaryWay",
      width: 80,
      hideInSearch: true
    },
    {
      title: "面单价格",
      dataIndex: "expressPriceStr",
      width: 80,
      hideInSearch: true
    },
    {
      title: "总面单",
      dataIndex: "firstFeeStr",
      width: 80,
      hideInSearch: true
    },
    {
      title: "销售数量",
      dataIndex: "itemVolumeStr",
      width: 80,
      hideInSearch: true
    },
    {
      title: "销售金额",
      dataIndex: "itemAmountStr",
      width: 80,
      hideInSearch: true
    },
    {
      title: "商品比例",
      dataIndex: "itemRate",
      width: 80,
      hideInSearch: true
    },
    {
      title: "订单重量",
      dataIndex: "itemWeight",
      width: 80,
      hideInSearch: true
    },
    {
      title: "包材重量",
      dataIndex: "materWeight",
      width: 80,
      hideInSearch: true
    },
    {
      title: "操作费",
      dataIndex: "operFeeStr",
      width: 80,
      hideInSearch: true
    },
    {
      title: "包材总重量",
      dataIndex: "materWeightAll",
      width: 90,
      hideInSearch: true
    },
    {
      title: "包材费",
      dataIndex: "materFeeStr",
      width: 70,
      hideInSearch: true
    },
    {
      title: "包材总费用",
      dataIndex: "materFeeAllStr",
      width: 90,
      hideInSearch: true
    },
    {
      title: "快递费",
      dataIndex: "expressFeeStr",
      width: 70,
      hideInSearch: true
    },
    {
      title: "总快递费",
      dataIndex: "orderExpressFeeStr",
      width: 80,
      hideInSearch: true
    },
    {
      title: "包裹总重量",
      dataIndex: "orderExpressWeight",
      width: 90,
      hideInSearch: true
    }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      align: "center",
      ...item,
      title: returnEllipsisTooltip({ title })
    };
  });
};
