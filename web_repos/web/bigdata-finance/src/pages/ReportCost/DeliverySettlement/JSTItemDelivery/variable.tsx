import { returnEllipsisTooltip } from "@/utils";
import SelectLabel from "@/components/select-label";
import apis from "@/services/settlement/deliverySettlement";

const columns_search = () => {
  return [
    {
      title: "发货日期",
      dataIndex: "deliveryDate",
      hideInTable: true,
      valueType: "dateRange"
    },
    {
      title: "直播日期",
      dataIndex: "liveDate",
      hideInTable: true,
      valueType: "dateRange"
    },
    {
      title: "订单来源",
      dataIndex: "orderSource",
      hideInTable: true,
      renderFormItem() {
        return <SelectLabel api={apis.getPullDownData} field="orderSource" type={5} />;
      }
    },
    {
      title: "款式编码",
      dataIndex: "jstItemId",
      hideInTable: true
    },
    {
      title: "商品编码",
      dataIndex: "skuId",
      hideInTable: true
    },
    {
      title: "店铺ID",
      dataIndex: "shopId",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "店铺名称",
      dataIndex: "shopName",
      hideInTable: true
    },
    {
      title: "主播名称",
      dataIndex: "anchorNames",
      hideInTable: true,
      renderFormItem() {
        return <SelectLabel api={apis.getPullDownData} field="pidName" mode="multiple" maxTagCount="responsive" type={5} />;
      }
    },
    {
      title: "商品ID",
      dataIndex: "itemIds",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "聚水潭商品名称",
      dataIndex: "jstItemName",
      hideInTable: true
    }
  ];
};
export const columns = ({}): Array<TableListItem> => {
  return [
    ...columns_search(),
    {
      title: "序号",
      dataIndex: "index",
      fixed: "left",
      width: 60,
      hideInSearch: true,
      render: (text: any, row: any, index: number) => {
        return index + 1;
      }
    },
    {
      title: "发货日期",
      dataIndex: "deliveryDate",
      width: 100,
      valueType: "date",
      hideInSearch: true
    },
    {
      title: "直播日期",
      dataIndex: "liveDate",
      width: 100,
      valueType: "date",
      hideInSearch: true
    },
    {
      title: "PID",
      dataIndex: "pid",
      width: 70,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "主播名称",
      dataIndex: "pidName",
      width: 80,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "款式编码",
      dataIndex: "jstItemId",
      width: 80,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "聚水潭商品名称",
      dataIndex: "jstItemName",
      width: 120,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "是否挂链",
      dataIndex: "chainingState",
      width: 80,
      hideInSearch: true
    },
    {
      title: "商品编码",
      dataIndex: "jstSkuId",
      width: 80,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "店铺ID",
      dataIndex: "shopId",
      width: 80,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品ID",
      dataIndex: "itemId",
      width: 80,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品名称",
      dataIndex: "itemName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "店铺名称",
      dataIndex: "shopName",
      width: 80,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "数据来源",
      dataIndex: "orderSource",
      width: 80,
      hideInSearch: true
    },
    {
      title: "订单类型",
      dataIndex: "orderType",
      width: 80,
      hideInSearch: true
    },
    {
      title: "商品销量",
      dataIndex: "itemVolumeStr",
      width: 80,
      hideInSearch: true
    },
    {
      title: "销售额",
      dataIndex: "itemAmountStr",
      width: 60,
      hideInSearch: true
    },
    {
      title: "已付金额",
      dataIndex: "payAmountStr",
      width: 80,
      hideInSearch: true
    },
    {
      title: "应付金额",
      dataIndex: "shouldPayAmountStr",
      width: 80,
      hideInSearch: true
    },
    {
      title: "实发数量",
      dataIndex: "acctualVolumeStr",
      width: 80,
      hideInSearch: true
    },
    {
      title: "实发金额",
      dataIndex: "acctualAmountStr",
      width: 80,
      hideInSearch: true
    }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      align: "center",
      ...item,
      title: returnEllipsisTooltip({ title })
    };
  });
};
