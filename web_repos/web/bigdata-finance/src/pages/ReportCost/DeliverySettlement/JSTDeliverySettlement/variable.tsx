import apis from "@/services/settlement/deliverySettlement";
import SelectLabel from "@/components/select-label";
import { returnEllipsisTooltip } from "@/utils";
const columns_search = () => {
  return [
    {
      title: "发货日期",
      dataIndex: "deliveryDate",
      hideInTable: true,
      valueType: "dateRange"
    },
    {
      title: "直播日期",
      dataIndex: "liveDate",
      hideInTable: true,
      valueType: "dateRange"
    },
    {
      title: "仓库类型",
      dataIndex: "warehouseType",
      hideInTable: true,
      renderFormItem() {
        return <SelectLabel api={apis.getPullDownData} field="warehouseType" type={4} />;
      }
    },
    {
      title: "订单来源",
      dataIndex: "orderSource",
      hideInTable: true,
      renderFormItem() {
        return <SelectLabel api={apis.getPullDownData} field="orderSource" type={4} />;
      }
    },
    {
      title: "主播名称",
      dataIndex: "anchorNames",
      hideInTable: true,
      renderFormItem() {
        return <SelectLabel api={apis.getPullDownData} field="pidName" mode="multiple" maxTagCount="responsive" type={4} />;
      }
    },
    {
      title: "店铺ID",
      dataIndex: "shopId",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "店铺名称",
      dataIndex: "shopName",
      hideInTable: true
    },
    {
      title: "商品名称",
      dataIndex: "itemName",
      hideInTable: true
    },
    {
      title: "商品id",
      dataIndex: "itemIds",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "快递公司",
      dataIndex: "expressCompany",
      hideInTable: true,
      renderFormItem() {
        return <SelectLabel api={apis.getPullDownData} field="expressCompany" type={4} />;
      }
    }
  ];
};
export const columns = ({}): Array<TableListItem> => {
  return [
    ...columns_search(),
    {
      title: "序号",
      dataIndex: "index",
      fixed: "left",
      width: 60,
      hideInSearch: true,
      render: (text: any, row: any, index: number) => {
        return index + 1;
      }
    },
    {
      title: "发货日期",
      dataIndex: "deliveryDate",
      width: 100,
      valueType: "date",
      hideInSearch: true
    },
    {
      title: "直播日期",
      dataIndex: "liveDate",
      width: 100,
      valueType: "date",
      hideInSearch: true
    },
    {
      title: "发货仓库",
      dataIndex: "deliveryWarehouse",
      width: 80,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "快递公司",
      dataIndex: "expressCompany",
      width: 80,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "PID",
      dataIndex: "pid",
      width: 70,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "主播名称",
      dataIndex: "pidName",
      width: 80,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品ID",
      dataIndex: "itemId",
      width: 80,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品名称",
      dataIndex: "itemName",
      width: 120,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "是否挂链",
      dataIndex: "chainingState",
      width: 80,
      hideInSearch: true
    },
    {
      title: "订单类型",
      dataIndex: "orderType",
      width: 80,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "订单来源",
      dataIndex: "orderSource",
      width: 80,
      hideInSearch: true
    },
    {
      title: "省份",
      dataIndex: "province",
      width: 70,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "店铺ID",
      dataIndex: "shopId",
      width: 80,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "店铺名称",
      dataIndex: "shopName",
      width: 140,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "仓库类型",
      dataIndex: "warehouseType",
      width: 80,
      hideInSearch: true
    },
    {
      title: "商品销量",
      dataIndex: "itemVolumeStr",
      width: 80,
      hideInSearch: true
    },
    {
      title: "商品销售额",
      dataIndex: "itemAmountStr",
      width: 90,
      hideInSearch: true
    },
    {
      title: "商品重量",
      dataIndex: "itemWeight",
      width: 80,
      hideInSearch: true
    },
    {
      title: "包材重量",
      dataIndex: "materWeight",
      width: 80,
      hideInSearch: true
    },
    {
      title: "包裹重量",
      dataIndex: "orderExpressWeight",
      width: 80,
      hideInSearch: true
    },
    {
      title: "包材费",
      dataIndex: "materFeeStr",
      width: 60,
      hideInSearch: true
    },
    {
      title: "操作费",
      dataIndex: "operFeeStr",
      width: 60,
      hideInSearch: true
    },
    {
      title: "快递费",
      dataIndex: "expressFeeStr",
      width: 60,
      hideInSearch: true
    },
    {
      title: "总快递费",
      dataIndex: "orderExpressFeeStr",
      width: 80,
      hideInSearch: true
    }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      align: "center",
      ...item,
      title: returnEllipsisTooltip({ title })
    };
  });
};
