import React from "react";
import apis from "@/services/settlement/deliverySettlement";
import { splitSpaceComma } from "@/utils";
import { columns } from "./variable";
import dayjs from "dayjs";
import TableList from "@/components/TableList";

const dateFormat = "YYYYMMDD";
const NewAnchorDeliveryStat = () => {
  const handlePageParmas = (params: any) => {
    const { current, liveDate, deliveryDate, shopId, itemIds, ...rest } = params;
    if (deliveryDate) {
      const [startTime, endTime] = deliveryDate;
      rest.startTime = dayjs(startTime).format(dateFormat);
      rest.endTime = dayjs(endTime).format(dateFormat);
    }
    if (liveDate) {
      const [startTime, endTime] = liveDate;
      rest.liveStartTime = dayjs(startTime).format(dateFormat);
      rest.liveEndTime = dayjs(endTime).format(dateFormat);
    }
    rest.shopId = splitSpaceComma(shopId);
    rest.itemIds = splitSpaceComma(itemIds);
    return { ...rest, pageNo: current };
  };

  return (
    <TableList
      scroll={{ y: "calc(100vh - 370px)" }}
      form={{
        initialValues: {
          deliveryDate: [dayjs().subtract(1, "month"), dayjs()]
        }
      }}
      columns={columns({})}
      api={apis.getJTSExpressSettle}
      downloadApi={apis.downloadJSTDeliverySettle}
      preFetch={handlePageParmas}
    />
  );
};

export default React.memo(NewAnchorDeliveryStat);
