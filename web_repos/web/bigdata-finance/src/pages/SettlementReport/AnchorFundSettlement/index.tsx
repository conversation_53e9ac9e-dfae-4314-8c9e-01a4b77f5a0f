import React, { useState, useEffect } from "react";
import { Tabs } from "antd";
import KeepAlive from "react-activation";
import Total from "./total";
import Detail from "./detail";
import { SIZE, tabs_tabBarStyle } from "@/utils/constant";
import apis from "@/services/settlement/billStatisticMonth";
import dayjs from "dayjs";
const PageUrl = "/settlementReport/anchorFundSettlement";

export const FieldsContext = React.createContext<any>(null);
export const tab_total = "a";
export const tab_detail = "b";

const Res = () => {
  const [curTab, setCurTab] = useState(tab_total);
  const [detailParams, setDetailParams] = useState({});
  const handlePageParmas = (params: any) => {
    const { current, settleMonth, ...rest } = params;
    if (settleMonth) {
      const [settleMonthStart, settleMonthEnd] = settleMonth;
      rest.settleMonthStart = dayjs(settleMonthStart).format("YYYYMM");
      rest.settleMonthEnd = dayjs(settleMonthEnd).format("YYYYMM");
    }
    return { ...rest, pageNo: current };
  };
  const [CompanyNameList, setCompanyNameList] = useState([]);
  const fetchCompanyName = async () => {
    const { entry, status } = await apis.getCpsAnxinCompanyNameList();
    if (status) {
      setCompanyNameList(
        (entry || []).map((item: string) => {
          return {
            label: item,
            value: item
          };
        })
      );
    }
  };
  useEffect(() => {
    fetchCompanyName();
  }, []);
  const tabItems = [
    {
      key: tab_total,
      label: "主播资金结算汇总",
      children: <Total />
    },
    {
      key: tab_detail,
      label: "主播资金结算明细",
      children: <Detail />
    }
  ];
  return (
    <FieldsContext.Provider value={{ handlePageParmas, CompanyNameList, setCurTab, setDetailParams, detailParams }}>
      <Tabs activeKey={curTab} tabBarStyle={tabs_tabBarStyle} size={SIZE} onChange={setCurTab} items={tabItems} />
    </FieldsContext.Provider>
  );
};

const AliveAnchorItemSale = props => (
  <KeepAlive name={PageUrl}>
    <Res {...props} />
  </KeepAlive>
);

export default React.memo(AliveAnchorItemSale);
