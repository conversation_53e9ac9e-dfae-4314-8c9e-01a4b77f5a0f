import React, { useContext, useEffect, useRef } from "react";
import TableList from "@/components/TableList";
import { columns } from "./variable";
import apis from "@/services/settlementReport/anchorItemSale";
import dayjs from "dayjs";
import useCommonOptions from "@/hooks/useCommonOptions";
import { FieldsContext } from "./../index";
const Res = () => {
  const { handlePageParmas, CompanyNameList, detailParams } = useContext(FieldsContext);

  const actionRef = useRef<any>();
  const tableFormRef = useRef<any>();
  const [walletTypeOptions] = useCommonOptions({ dimName: "资金钱包类型" });
  const columns_props = { CompanyNameList, walletTypeOptions };
  useEffect(() => {
    tableFormRef.current.resetFields();
    tableFormRef.current.setFieldsValue(detailParams);
    actionRef.current?.reload();
  }, [tableFormRef, actionRef, detailParams]);
  const handleParams = p => {
    const params = tableFormRef.current.getFieldsValue();
    const res = handlePageParmas?.({ ...p, ...params });
    return res;
  };
  return (
    <>
      <TableList
        formRef={tableFormRef}
        actionRef={actionRef}
        columns={columns(columns_props)}
        scroll={{ y: "calc(100vh - 410px)" }}
        form={{
          initialValues: {
            settleMonth: [dayjs().subtract(0, "month"), dayjs()]
          }
        }}
        summaryApi={apis.getCpsBillMonthPageTotal}
        api={apis.getCpsBillMonthPage}
        downloadApi={apis.downloadCpsBillMonthDetail}
        params={{
          subTab: 2
        }}
        preFetch={handleParams}
        rowKey={record => {
          const { settleMonth, anchorId, anchorName, walletType, cpsAnxinCompanyName } = record;
          return settleMonth + anchorId + anchorName + walletType + cpsAnxinCompanyName;
        }}
      />
    </>
  );
};

export default React.memo(Res);
