import { filterOptionLabel, returnEllipsisTooltip } from "@/utils";
import { SIZE } from "@/utils/constant";
import { Select, DatePicker, Button } from "antd";
import AnchorSelect from "@/components/AnchorSelect";
const columns_search = ({ CompanyNameList }: any) => {
  return [
    {
      title: "结算月份",
      dataIndex: "settleMonth",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return <DatePicker.RangePicker picker="month" allowClear />;
      }
    },
    {
      title: "主播名称",
      dataIndex: "anchorNameList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem() {
        return <AnchorSelect showSearch allowClear maxTagCount="responsive" mode="multiple" />;
      }
    },

    {
      title: "安心钱包主体",
      dataIndex: "cpsAnxinCompanyNameList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return (
          <Select
            placeholder="请选择"
            style={{ minWidth: 120, textAlign: "left" }}
            size={SIZE}
            options={CompanyNameList}
            filterOption={filterOptionLabel}
            showSearch
            allowClear
            maxTagCount="responsive"
            mode="multiple"
          />
        );
      }
    }
  ];
};
export const columns = ({ settleModelOptions, CompanyNameList, handleDetail }: any): Array<TableListItem> => {
  return [
    ...columns_search({ settleModelOptions, CompanyNameList }),
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      hideInSearch: true,
      fixed: "left",
      render: (_, record, index) => {
        return index + 1;
      }
    },
    { title: "结算月份", dataIndex: "settleMonth", width: 100, valueType: "dateMonth", hideInSearch: true },
    { title: "主播ID", dataIndex: "anchorId", width: 100, hideInSearch: true },
    {
      title: "主播名称",
      dataIndex: "anchorName",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "安心钱包主体",
      dataIndex: "cpsAnxinCompanyName",
      width: 140,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "分销钱包总收入", dataIndex: "inAmountStr", width: 100, hideInSearch: true },
    { title: "分销钱包总支出", dataIndex: "outAmountStr", width: 100, hideInSearch: true },
    { title: "货款结算收入", dataIndex: "billSettleAmountStr", width: 100, hideInSearch: true },
    { title: "佣金金额退回支出", dataIndex: "backCommissionAmountStr", width: 100, hideInSearch: true },
    { title: "余额提现支出", dataIndex: "withdrawalAmountStr", width: 100, hideInSearch: true },
    { title: "余额提现失败收入", dataIndex: "backWithdrawalAmountStr", width: 100, hideInSearch: true },
    { title: "年框扣费支出", dataIndex: "billYearlyAmountStr", width: 100, hideInSearch: true },
    { title: "资金扣减违约金扣款支出", dataIndex: "billAntiPunishAmountStr", width: 100, hideInSearch: true },
    { title: "资金扣减违约金扣款退回收入", dataIndex: "backAntiPunishAmountStr", width: 100, hideInSearch: true },
    { title: "资金扣减支出", dataIndex: "deductAmountStr", width: 100, hideInSearch: true },
    { title: "资金扣减退回收入", dataIndex: "backDeductAmountStr", width: 100, hideInSearch: true },
    { title: "奖励收入", dataIndex: "rewardAmountStr", width: 100, hideInSearch: true },
    { title: "资金冻结支出", dataIndex: "freezeAmountStr", width: 100, hideInSearch: true },
    { title: "资金解冻收入", dataIndex: "unfreezeAmountStr", width: 100, hideInSearch: true },
    { title: "资金转账支出", dataIndex: "transAmountStr", width: 100, hideInSearch: true },
    { title: "其它类型收入", dataIndex: "otherAmountStr", width: 100, hideInSearch: true },
    {
      title: "操作",
      valueType: "option",
      fixed: "right",
      width: 100,
      render: (_, record) => {
        return (
          <Button size={SIZE} type="link" onClick={() => handleDetail(record)}>
            详情
          </Button>
        );
      }
    }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      align: "center",
      ...item,
      title: returnEllipsisTooltip({ title })
    };
  });
};
