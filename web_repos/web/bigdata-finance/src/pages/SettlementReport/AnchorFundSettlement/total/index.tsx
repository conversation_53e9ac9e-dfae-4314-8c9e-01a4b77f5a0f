import React, { useContext } from "react";
import TableList from "@/components/TableList";
import { columns } from "./variable";
import apis from "@/services/settlementReport/anchorItemSale";
import dayjs from "dayjs";
import { FieldsContext, tab_detail } from "./../index";
const Res = () => {
  const { handlePageParmas, CompanyNameList, setCurTab, setDetailParams } = useContext(FieldsContext);
  const handleDetail = record => {
    console.log("🚀 ~ handleDetail ~ record:", record);
    const { anchorName, cpsAnxinCompanyName, settleMonth } = record;
    setCurTab(tab_detail);
    setDetailParams({
      anchorNameList: [anchorName],
      cpsAnxinCompanyNameList: [cpsAnxinCompanyName],
      settleMonth: [dayjs(settleMonth), dayjs(settleMonth)]
    });
  };

  const columns_props = { handleDetail, CompanyNameList };

  return (
    <>
      <TableList
        columns={columns(columns_props)}
        scroll={{ y: "calc(100vh - 370px)" }}
        form={{
          initialValues: {
            settleMonth: [dayjs().subtract(0, "month"), dayjs()]
          }
        }}
        summaryApi={apis.getCpsBillMonthPageTotal}
        api={apis.getCpsBillMonthPage}
        downloadApi={apis.downloadCpsBillMonth}
        params={{ subTab: 1 }}
        preFetch={handlePageParmas}
        rowKey={record => {
          const { settleMonth, anchorId, anchorName, cpsAnxinCompanyName } = record;
          return settleMonth + anchorId + anchorName + cpsAnxinCompanyName;
        }}
      />
    </>
  );
};

export default React.memo(Res);
