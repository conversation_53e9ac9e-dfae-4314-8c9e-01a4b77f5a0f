import React from "react";
import KeepAlive from "react-activation";
import Records from "./Records";
export const PageUrl = "/settlementReport/mergeOffsetEntry";

export const FieldsContext = React.createContext<any>(null);

const Res = () => {
  return (
    <FieldsContext.Provider value={{}}>
      <Records />
    </FieldsContext.Provider>
  );
};

const AliveAnchorItemSale = props => (
  <KeepAlive name={PageUrl}>
    <Res {...props} />
  </KeepAlive>
);

export default React.memo(AliveAnchorItemSale);
