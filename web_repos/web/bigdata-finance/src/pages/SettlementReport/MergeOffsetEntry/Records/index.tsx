import React, { useState, useRef } from "react";
import { But<PERSON> } from "antd";
import TableList from "@/components/TableList";
import { columns, advance_columns_search } from "./variable";
import apis from "@/services/settlementReport/mergeOffsetEntry";
import dayjs from "dayjs";
import useCommonOptions from "@/hooks/useCommonOptions";
import UploadModal from "./UploadModal";
import { SIZE } from "@/utils/constant";
const Res = () => {
  const formRef = useRef<any>();
  const actionRef = useRef<any>();
  const [showUpload, setshowUpload] = useState(false);
  const [subjectTypeOptions] = useCommonOptions({ dimName: "金蝶科目类型" });
  const columns_props = {};
  const advanceColumnsProps = { subjectTypeOptions };
  // 上传excel modal属性
  const UploadModalProps = {
    title: "上传合并抵消明细",
    visible: showUpload,
    api: apis.upload,
    tempUrl: "https://s.xinc818.com/files/webcim6vv6xifop7rzp/合并抵消分录模板.xlsx",
    message: (
      <>
        <span>1. 模板中黄色背景字段为必填项</span>
        <br />
        <span>2. 每次上传需将一个月的所有抵消明细上传，重复上传会覆盖上次上传结果</span>
      </>
    ),
    fresh: () => {
      actionRef?.current?.reload();
    },
    close: () => {
      setshowUpload(false);
    }
  };
  const actions = () => {
    return [
      <Button
        key="upload"
        type="primary"
        size={SIZE}
        onClick={() => {
          setshowUpload(true);
        }}
      >
        上传
      </Button>
    ];
  };
  const handleParams = p => {
    const { current, settleYear, settleMonth, ...rest } = p;
    if (settleYear) {
      const [startSettleYear, endSettleYear] = settleYear;
      rest.startSettleYear = dayjs(startSettleYear).format("YYYY");
      rest.endSettleYear = dayjs(endSettleYear).format("YYYY");
    }
    if (settleMonth) {
      rest.startSettleMonth = settleMonth;
      rest.endSettleMonth = settleMonth;
    }
    return { ...rest, pageNo: current };
  };
  return (
    <>
      <TableList
        headerTitle="合并抵消分录"
        formRef={formRef}
        actionRef={actionRef}
        columns={columns(columns_props)}
        advanceColumns={advance_columns_search(advanceColumnsProps)}
        scroll={{ y: "calc(100vh - 370px)" }}
        form={{
          initialValues: {
            settleYear: [dayjs(), dayjs()]
          }
        }}
        api={apis.getList}
        downloadApi={apis.download}
        preFetch={handleParams}
        toolbar={{
          actions: actions()
        }}
      />
      {UploadModalProps.visible ? <UploadModal {...UploadModalProps} /> : null}
    </>
  );
};

export default React.memo(Res);
