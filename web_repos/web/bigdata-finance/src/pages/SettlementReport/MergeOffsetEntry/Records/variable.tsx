import { filterOptionLabel, returnEllipsisTooltip } from "@/utils";
import { SIZE } from "@/utils/constant";
import { DatePicker, Select } from "antd";
// 结算月份 枚举
const settleMonthOptions: IOptions[] = [
  {
    label: "1月",
    value: "01"
  },
  {
    label: "2月",
    value: "02"
  },
  {
    label: "3月",
    value: "03"
  },
  {
    label: "4月",
    value: "04"
  },
  {
    label: "5月",
    value: "05"
  },
  {
    label: "6月",
    value: "06"
  },
  {
    label: "7月",
    value: "07"
  },
  {
    label: "8月",
    value: "08"
  },
  {
    label: "9月",
    value: "09"
  },
  {
    label: "10月",
    value: "10"
  },
  {
    label: "11月",
    value: "11"
  },
  {
    label: "12月",
    value: "12"
  }
];
export const advance_columns_search = ({ subjectTypeOptions }: any): TableListItem[] => {
  return [
    {
      title: "基本信息",
      valueType: "group",
      colProps: { span: 24 },
      columns: [
        {
          title: "科目类型",
          dataIndex: "subjectType",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem: () => {
            return (
              <Select
                placeholder="请选择"
                style={{ minWidth: 120, textAlign: "left" }}
                size={SIZE}
                options={subjectTypeOptions}
                filterOption={filterOptionLabel}
                showSearch
                allowClear
              />
            );
          }
        },
        {
          title: "科目代码",
          dataIndex: "subjectCode",
          hideInSearch: false,
          hideInTable: true
        },
        {
          title: "抵消科目代码",
          dataIndex: "offsetSubjectCode",
          hideInSearch: false,
          hideInTable: true
        }
      ]
    }
  ];
};
const columns_search = ({}: any) => {
  return [
    {
      title: "结算年份",
      dataIndex: "settleYear",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return <DatePicker.RangePicker picker="year" allowClear />;
      }
    },
    {
      title: "结算月份",
      dataIndex: "settleMonth",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return (
          <Select
            placeholder="请选择"
            style={{ minWidth: 120, textAlign: "left" }}
            size={SIZE}
            options={settleMonthOptions}
            filterOption={filterOptionLabel}
            showSearch
            allowClear
          />
        );
      }
      // renderFormItem: () => {
      //   return <DatePicker.RangePicker picker="month" allowClear />;
      // }
    },
    {
      title: "公司名称",
      dataIndex: "orgName",
      hideInSearch: false,
      hideInTable: true
    },
    {
      title: "科目名称",
      dataIndex: "subjectNameLv1",
      hideInSearch: false,
      hideInTable: true
    },
    {
      title: "凭证摘要",
      dataIndex: "voucherRemark",
      hideInSearch: false,
      hideInTable: true
    },
    {
      title: "抵消公司名称",
      dataIndex: "offsetOrgName",
      hideInSearch: false,
      hideInTable: true
    },
    {
      title: "抵消科目名称",
      dataIndex: "offsetSubjectName",
      hideInSearch: false,
      hideInTable: true
    }
  ];
};
export const columns = ({}: any): Array<TableListItem> => {
  return [
    ...columns_search({}),
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      hideInSearch: true,
      fixed: "left",
      render: (_, record, index) => {
        return index + 1;
      }
    },
    { title: "结算年份", dataIndex: "settleYear", width: 100, hideInSearch: true },
    { title: "结算月份", dataIndex: "settleMonth", width: 100, hideInSearch: true },
    {
      title: "科目类型",
      dataIndex: "subjectType",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "公司ID",
      dataIndex: "orgNumber",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "公司名称",
      dataIndex: "orgName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "公司板块",
      dataIndex: "orgType",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "部门",
      dataIndex: "bigDepartment",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "店铺",
      dataIndex: "shopName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "供应商",
      dataIndex: "supplierName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "平台",
      dataIndex: "platform",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "科目代码",
      dataIndex: "subjectCode",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "科目名称",
      dataIndex: "subjectNameLv1",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "二级科目",
      dataIndex: "subjectNameLv2",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "三级科目",
      dataIndex: "subjectNameLv3",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "凭证号",
      dataIndex: "voucherCode",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "凭证摘要",
      dataIndex: "voucherRemark",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "抵消公司ID",
      dataIndex: "offsetOrgNumber",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "抵消公司名称",
      dataIndex: "offsetOrgName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "抵消公司板块",
      dataIndex: "offsetOrgType",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "抵消科目代码",
      dataIndex: "offsetSubjectCode",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "抵消科目名称",
      dataIndex: "offsetSubjectName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "借方发生额", dataIndex: "debitQtyStr", width: 100, hideInSearch: true },
    { title: "报表金额", dataIndex: "reportQtyStr", width: 100, hideInSearch: true },
    { title: "差异金额", dataIndex: "diffQtyStr", width: 100, hideInSearch: true }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      align: "center",
      ...item,
      title: returnEllipsisTooltip({ title })
    };
  });
};
