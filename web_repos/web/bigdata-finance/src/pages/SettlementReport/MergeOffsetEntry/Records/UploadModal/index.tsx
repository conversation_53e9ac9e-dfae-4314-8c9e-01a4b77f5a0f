import React, { useState, useEffect } from "react";
import { Mo<PERSON>, Form, Button, DatePicker, Alert } from "antd";
import RSSUpload from "@/components/RSS/RSSUpload";
import { pushImportHistory, returnEllipsisTooltip } from "@/utils";
import { UploadModalProps } from "@/components/UploadModal";
import { SIZE } from "@/utils/constant";
import dayjs from "dayjs";

const UploadExcelModal = (props: UploadModalProps) => {
  const { visible, close, fresh, title, tempUrl, api, message } = props;
  const [form] = Form.useForm();
  const [uploadParams, setUploadParams] = useState(() => ({
    filePath: "",
    fileName: "",
    targetType: 0
  }));
  const [modalOkDisable, setModalOkDisable] = useState<boolean>(true);
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);

  useEffect(() => {
    setModalOkDisable(uploadParams.filePath ? false : true);
  }, [uploadParams]);

  const handleExcelCancel = () => {
    close?.();
  };

  const handleExcelOk = async () => {
    form.validateFields().then(async values => {
      setConfirmLoading(true);
      const { settleMonth } = values;
      const { status } = await api?.({ ...uploadParams, settleMonth: dayjs(settleMonth).format("YYYYMM") });
      if (status) {
        pushImportHistory();
        close?.();
        fresh?.();
      }
      setConfirmLoading(false);
    });
  };

  const fileChangeHandle = (url: string, fileName: string) => {
    setUploadParams({ ...uploadParams, fileName, filePath: url });
  };
  const onRemoveHandle = () => {
    setUploadParams({ ...uploadParams, fileName: "", filePath: "" });
  };
  // 下载导入商品Excel模板
  const handleDownloadExample = (e: React.BaseSyntheticEvent) => {
    e.stopPropagation();
    window.open(tempUrl);
  };
  return (
    <Modal
      centered={true}
      title={title}
      open={visible}
      onOk={() => handleExcelOk()}
      onCancel={() => handleExcelCancel()}
      maskClosable={false}
      confirmLoading={confirmLoading}
      okButtonProps={{
        size: SIZE,
        disabled: modalOkDisable
      }}
      cancelButtonProps={{
        size: SIZE
      }}
    >
      <Form size={SIZE} form={form}>
        <Form.Item>
          <Alert message={message} type="info" showIcon></Alert>
        </Form.Item>
        <Form.Item label={returnEllipsisTooltip({ title: "结算月份" })} name="settleMonth" rules={[{ required: true }]}>
          <DatePicker picker="month" style={{ width: "100%" }}></DatePicker>
        </Form.Item>
        <Form.Item>
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              flexDirection: "column",
              alignItems: "center"
            }}
          >
            <RSSUpload
              accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
              fileChange={(ossResultUrl: string, file: any, fileName: string) => fileChangeHandle(ossResultUrl, fileName)}
              fileRemove={() => onRemoveHandle()}
              onRemove={false}
              size={30 * 1024 * 1024}
              type={["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "application/vnd.ms-exce", "text/csv"]}
              fileLength={1}
              fileList={uploadParams.filePath ? [uploadParams.filePath] : []}
            />
            <Form.Item>
              <div
                style={{
                  display: "flex",
                  justifyContent: "center",
                  flexDirection: "column",
                  alignItems: "center"
                }}
              >
                <Button type="link" onClick={handleDownloadExample}>
                  [下载模板]
                </Button>
              </div>
            </Form.Item>
          </div>
        </Form.Item>
      </Form>
    </Modal>
  );
};
export default React.memo(UploadExcelModal);
