import { Select, DatePicker } from "antd";
import { filterOptionLabel, returnEllipsisTooltip } from "@/utils/index";
import { SIZE } from "@/utils/constant";
import AnchorSelect from "@/components/AnchorSelect";
import SupplierNameSelect from "@/components/SupplierNameSelect";
export const advance_columns_search = ({ settleModelOptions, shopTypeOptions, settleTypeOptions }: any): TableListItem[] => {
  return [
    {
      title: "基础信息",
      valueType: "group",
      colProps: { span: 24 },
      columns: [
        {
          title: "结算类型",
          dataIndex: "settleTypeList",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem: () => {
            return <Select placeholder="请选择" size={SIZE} options={settleTypeOptions} filterOption={filterOptionLabel} showSearch allowClear />;
          }
        },
        {
          title: "结算模式",
          dataIndex: "settleModelList",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem: () => {
            return (
              <Select
                placeholder="请选择"
                size={SIZE}
                options={settleModelOptions}
                filterOption={filterOptionLabel}
                showSearch
                allowClear
                maxTagCount="responsive"
                mode="multiple"
              />
            );
          }
        },
        {
          title: "店铺ID",
          dataIndex: "shopId",
          hideInSearch: false,
          hideInTable: true
        },
        {
          title: "店铺名称",
          dataIndex: "shopNameList",
          hideInSearch: false,
          hideInTable: true
        },
        {
          title: "店铺性质",
          dataIndex: "shopTypeList",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem: () => {
            return <Select placeholder="请选择" size={SIZE} options={shopTypeOptions} filterOption={filterOptionLabel} showSearch allowClear />;
          }
        },
        {
          title: "一级类目",
          dataIndex: "catLv1Name",
          hideInSearch: false,
          hideInTable: true
        },
        {
          title: "供应商名称",
          dataIndex: "supplierName",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem() {
            return <SupplierNameSelect />;
          }
        },
        {
          title: "我司主体",
          dataIndex: "contractCompanyName",
          hideInSearch: false,
          hideInTable: true
        },
        {
          title: "一级部门",
          dataIndex: "deptNameLv1",
          hideInSearch: false,
          hideInTable: true
        },
        {
          title: "四级部门",
          dataIndex: "deptGroup",
          hideInSearch: false,
          hideInTable: true
        },
        {
          title: "负责人",
          dataIndex: "purchasePrincipalName",
          hideInSearch: false,
          hideInTable: true
        }
      ]
    }
  ];
};
const columns_search = ({ orderChanneOptions, CompanyNameList }: any): Array<TableListItem> => {
  return [
    {
      title: "结算月份",
      dataIndex: "settleMonth",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return <DatePicker.RangePicker picker="month" allowClear />;
      }
    },
    {
      title: "主播名称",
      dataIndex: "anchorNameList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem() {
        return <AnchorSelect showSearch allowClear maxTagCount="responsive" mode="multiple" />;
      }
    },
    {
      title: "直播日期",
      dataIndex: "liveDate",
      hideInSearch: false,
      hideInTable: true,
      valueType: "dateRange"
    },

    {
      title: "商品ID",
      dataIndex: "itemIdList",
      hideInSearch: false,
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "商品名称",
      dataIndex: "itemTitle",
      hideInSearch: false,
      hideInTable: true
    },
    {
      title: "订单渠道",
      dataIndex: "orderChannelList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return <Select placeholder="请选择" size={SIZE} options={orderChanneOptions} filterOption={filterOptionLabel} showSearch allowClear />;
      }
    },
    {
      title: "安心钱包主体",
      dataIndex: "cpsAnxinCompanyNameList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return <Select placeholder="请选择" size={SIZE} options={CompanyNameList} filterOption={filterOptionLabel} showSearch allowClear />;
      }
    }
  ];
};
export const columns = ({ CompanyNameList, orderChanneOptions }: any): Array<TableListItem> => {
  return [
    ...columns_search({ CompanyNameList, orderChanneOptions }),
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      editable: false,
      hideInSearch: true,
      fixed: "left",
      render: (_, record, index) => {
        return index + 1;
      }
    },
    { title: "结算月份", dataIndex: "settleMonthStr", fixed: "left", width: 100, hideInSearch: true },
    { title: "主播ID", dataIndex: "anchorId", width: 100, hideInSearch: true },
    {
      title: "主播名称",
      dataIndex: "anchorName",
      width: 100,
      fixed: "left",
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "安心钱包主体",
      dataIndex: "cpsAnxinCompanyName",
      width: 140,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "直播日期",
      dataIndex: "liveDateStr",
      tooltip: "在订单渠道为直播时才代表直播日期，其他订单渠道为自然日期",
      fixed: "left",
      width: 100,
      hideInSearch: true
    },
    {
      title: "商品ID",
      dataIndex: "itemId",
      width: 140,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品名称",
      dataIndex: "itemTitle",
      fixed: "left",
      width: 160,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },

    {
      title: "结算品类",
      dataIndex: "settleCategory",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "结算模式",
      dataIndex: "settleModel",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "订单渠道",
      dataIndex: "orderChannelDesc",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "结算类型",
      dataIndex: "settleTypeDesc",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "一级模式",
      dataIndex: "modelNameLv1",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "二级模式",
      dataIndex: "modelNameLv2",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "店铺ID", dataIndex: "shopId", width: 100, hideInSearch: true },
    {
      title: "店铺名称",
      dataIndex: "shopName",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "店铺性质",
      dataIndex: "shopTypeDesc",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "一级类目",
      dataIndex: "catLv1Name",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "二级类目",
      dataIndex: "catLv2Name",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "三级类目",
      dataIndex: "catLv3Name",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "四级类目",
      dataIndex: "catLv4Name",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "五级类目",
      dataIndex: "catLv5Name",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "供应商名称",
      dataIndex: "supplierName",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "我司主体",
      dataIndex: "contractCompanyName",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "一级部门",
      dataIndex: "deptNameLv1",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "二级部门",
      dataIndex: "deptNameLv2",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "三级部门",
      dataIndex: "deptNameLv3",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "四级部门",
      dataIndex: "deptGroup",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "负责人",
      dataIndex: "purchasePrincipalName",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "总付款金额", dataIndex: "totalAmountStr", width: 100, hideInSearch: true },
    { title: "结算金额", dataIndex: "settleAmountStr", width: 100, hideInSearch: true },
    { title: "主播补贴金额", dataIndex: "anchorAllowAmountStr", width: 100, hideInSearch: true },
    { title: "主播宠爱红包补贴金额", dataIndex: "petAnchorAllowAmountStr", width: 100, hideInSearch: true },
    { title: "主播红包补贴金额", dataIndex: "otherAnchorAllowAmountStr", width: 100, hideInSearch: true },
    { title: "主播佣金金额", dataIndex: "settleCommissionAmountStr", width: 100, hideInSearch: true },
    { title: "安心钱包佣金金额", dataIndex: "anxinCommissionAmountStr", width: 100, hideInSearch: true },
    { title: "聚力钱包佣金金额", dataIndex: "juliCommissionAmountStr", width: 100, hideInSearch: true },
    { title: "微信佣金金额", dataIndex: "wechatCommissionAmountStr", width: 100, hideInSearch: true },
    { title: "支付宝佣金金额", dataIndex: "alipayCommissionAmountStr", width: 100, hideInSearch: true },
    { title: "安心钱包佣金金额返回", dataIndex: "backAnxinCommissionAmountStr", width: 100, hideInSearch: true },
    { title: "聚力钱包佣金金额返回", dataIndex: "backJuliCommissionAmountStr", width: 120, hideInSearch: true },
    {
      title: "佣金备注",
      dataIndex: "remark",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    }
  ].map(item => {
    const { title } = item;
    return {
      ...item,
      align: "center",
      title: returnEllipsisTooltip({ title })
    };
  });
};
