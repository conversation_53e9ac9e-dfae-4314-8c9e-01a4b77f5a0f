import React, { useContext, useEffect, useRef } from "react";
import TableList from "@/components/TableList";
import { advance_columns_search, columns } from "./variable";
import apis from "@/services/supplierInvoice/invoiceDetails";
import dayjs from "dayjs";
import { FieldsContext } from "./../index";
import { splitSpaceComma } from "@/utils";
const Res = () => {
  const { settleModelOptions, CompanyNameList, shopTypeOptions, orderChanneOptions, settleTypeOptions, detailParams } = useContext(FieldsContext);

  const actionRef = useRef<any>();
  const tableFormRef = useRef<any>();
  const handlePageParmas = (params: any) => {
    const {
      current,
      settleMonth,
      liveDate,
      itemIdList,
      shopNameList,
      settleTypeList,
      orderChannelList,
      shopTypeList,
      cpsAnxinCompanyNameList,
      ...rest
    } = params;
    if (settleMonth) {
      const [settleMonthStart, settleMonthEnd] = settleMonth;
      rest.settleMonthStart = dayjs(settleMonthStart).format("YYYYMM");
      rest.settleMonthEnd = dayjs(settleMonthEnd).format("YYYYMM");
    }
    if (liveDate) {
      const [liveDateStart, liveDateEnd] = liveDate;
      rest.liveDateStart = dayjs(liveDateStart).format("YYYYMMDD");
      rest.liveDateEnd = dayjs(liveDateEnd).format("YYYYMMDD");
    }

    rest.itemIdList = splitSpaceComma(itemIdList);
    rest.shopNameList = splitSpaceComma(shopNameList);
    rest.settleTypeList = splitSpaceComma(settleTypeList);
    rest.orderChannelList = splitSpaceComma(orderChannelList);
    rest.shopTypeList = splitSpaceComma(shopTypeList);
    rest.cpsAnxinCompanyNameList = splitSpaceComma(cpsAnxinCompanyNameList);
    return { ...rest, pageNo: current };
  };
  useEffect(() => {
    tableFormRef.current.resetFields();
    tableFormRef.current.setFieldsValue(detailParams);
    actionRef.current?.reload();
  }, [tableFormRef, actionRef, detailParams]);
  const handleParams = p => {
    const params = tableFormRef.current.getFieldsValue();
    const res = handlePageParmas?.({ ...p, ...params });
    return res;
  };
  const columns_props = { orderChanneOptions, CompanyNameList };
  const advance_props = { settleModelOptions, shopTypeOptions, settleTypeOptions };
  return (
    <>
      <TableList
        formRef={tableFormRef}
        actionRef={actionRef}
        columns={columns(columns_props)}
        scroll={{ y: "calc(100vh - 400px)" }}
        form={{
          initialValues: {
            settleMonth: [dayjs().subtract(0, "month"), dayjs()]
          }
        }}
        advanceColumns={advance_columns_search(advance_props)}
        summaryApi={apis.getCpsSettleMonthDetailPageTotal}
        api={apis.getCpsSettleMonthDetailPage}
        downloadApi={apis.downloadCpsSettleMonthDetail}
        preFetch={handleParams}
        rowKey={record => {
          const { settleMonth, anchorId, anchorName, liveDate, itemId, settleType } = record;
          return settleMonth + anchorId + anchorName + liveDate + itemId + settleType;
        }}
      />
    </>
  );
};

export default React.memo(Res);
