import React, { useContext } from "react";
import TableList from "@/components/TableList";
import { columns } from "./variable";
import apis from "@/services/settlement/billStatisticMonth";
import dayjs from "dayjs";
import { FieldsContext, tab_detail } from "./../index";
const Res = () => {
  const { settleModelOptions, CompanyNameList, setCurTab, setDetailParams } = useContext(FieldsContext);
  const handleDetail = record => {
    const { anchorName, settleMonth, cpsAnxinCompanyName } = record;
    setCurTab(tab_detail);
    setDetailParams({
      anchorNameList: [anchorName],
      settleMonth: [dayjs(settleMonth), dayjs(settleMonth)],
      cpsAnxinCompanyNameList: cpsAnxinCompanyName
    });
  };
  const handlePageParmas = (params: any) => {
    const { current, settleMonth, ...rest } = params;
    // 结算日期
    if (settleMonth) {
      const [settleMonthStart, settleMonthEnd] = settleMonth;
      rest.settleMonthStart = dayjs(settleMonthStart).format("YYYYMM");
      rest.settleMonthEnd = dayjs(settleMonthEnd).format("YYYYMM");
    }
    return { ...rest, pageNo: current };
  };
  const columns_props = { settleModelOptions, CompanyNameList, handleDetail };

  return (
    <>
      <TableList
        columns={columns(columns_props)}
        scroll={{ y: "calc(100vh - 400px)" }}
        form={{
          initialValues: {
            settleMonth: [dayjs().subtract(0, "month"), dayjs()]
          }
        }}
        summaryApi={apis.getCpsSettleMonthPageTotal}
        api={apis.getCpsSettleMonthPage}
        downloadApi={apis.downloadCpsSettleMonth}
        preFetch={handlePageParmas}
        rowKey={record => {
          const { settleMonth, anchorId, anchorName, settleModel } = record;
          return settleMonth + anchorId + anchorName + settleModel;
        }}
      />
    </>
  );
};

export default React.memo(Res);
