import { filterOptionLabel, returnEllipsisTooltip } from "@/utils";
import { SIZE } from "@/utils/constant";
import { Select, DatePicker, Button } from "antd";
import AnchorSelect from "@/components/AnchorSelect";
const columns_search = ({ settleModelOptions, CompanyNameList }: any) => {
  return [
    {
      title: "结算月份",
      dataIndex: "settleMonth",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return <DatePicker.RangePicker picker="month" allowClear />;
      }
    },
    {
      title: "主播名称",
      dataIndex: "anchorNameList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem() {
        return <AnchorSelect showSearch allowClear maxTagCount="responsive" mode="multiple" />;
      }
    },

    {
      title: "结算模式",
      dataIndex: "settleModelList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return (
          <Select
            placeholder="请选择"
            style={{ minWidth: 120, textAlign: "left" }}
            size={SIZE}
            options={settleModelOptions}
            filterOption={filterOptionLabel}
            showSearch
            allowClear
            maxTagCount="responsive"
            mode="multiple"
          />
        );
      }
    },
    {
      title: "安心钱包主体",
      dataIndex: "cpsAnxinCompanyNameList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return (
          <Select
            placeholder="请选择"
            style={{ minWidth: 120, textAlign: "left" }}
            size={SIZE}
            options={CompanyNameList}
            filterOption={filterOptionLabel}
            showSearch
            allowClear
            maxTagCount="responsive"
            mode="multiple"
          />
        );
      }
    }
  ];
};
export const columns = ({ settleModelOptions, CompanyNameList, handleDetail }: any): Array<TableListItem> => {
  return [
    ...columns_search({ settleModelOptions, CompanyNameList }),
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      hideInSearch: true,
      fixed: "left",
      render: (_, record, index) => {
        return index + 1;
      }
    },
    { title: "结算月份", dataIndex: "settleMonthStr", width: 100, hideInSearch: true },
    { title: "主播ID", dataIndex: "anchorId", width: 100, hideInSearch: true },
    {
      title: "主播名称",
      dataIndex: "anchorName",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "结算模式",
      dataIndex: "settleModel",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "安心钱包主体",
      dataIndex: "cpsAnxinCompanyName",
      width: 140,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "总付款金额", dataIndex: "totalAmountStr", width: 100, hideInSearch: true },
    { title: "结算金额", dataIndex: "settleAmountStr", width: 100, hideInSearch: true },
    { title: "主播补贴金额", dataIndex: "anchorAllowAmountStr", width: 100, hideInSearch: true },
    { title: "主播宠爱红包补贴金额", dataIndex: "petAnchorAllowAmountStr", width: 100, hideInSearch: true },
    { title: "主播红包补贴金额", dataIndex: "otherAnchorAllowAmountStr", width: 100, hideInSearch: true },
    { title: "主播佣金金额", dataIndex: "settleCommissionAmountStr", width: 100, hideInSearch: true },
    { title: "安心钱包佣金金额", dataIndex: "anxinCommissionAmountStr", width: 100, hideInSearch: true },
    { title: "聚力钱包佣金金额", dataIndex: "juliCommissionAmountStr", width: 100, hideInSearch: true },
    { title: "微信佣金金额", dataIndex: "wechatCommissionAmountStr", width: 100, hideInSearch: true },
    { title: "支付宝佣金金额", dataIndex: "alipayCommissionAmountStr", width: 100, hideInSearch: true },
    { title: "安心钱包佣金金额返回", dataIndex: "backAnxinCommissionAmountStr", width: 100, hideInSearch: true },
    { title: "聚力钱包佣金金额返回", dataIndex: "backJuliCommissionAmountStr", width: 100, hideInSearch: true },
    {
      title: "操作",
      valueType: "option",
      fixed: "right",
      width: 100,
      render: (_, record) => {
        return (
          <Button size={SIZE} type="link" onClick={() => handleDetail(record)}>
            详情
          </Button>
        );
      }
    }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      align: "center",
      ...item,
      title: returnEllipsisTooltip({ title })
    };
  });
};
