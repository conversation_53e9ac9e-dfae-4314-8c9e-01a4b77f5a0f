import React, { useState, useEffect } from "react";
import { Tabs } from "antd";
import KeepAlive from "react-activation";
import Total from "./total";
import Detail from "./detail";
import useCommonOptions from "@/hooks/useCommonOptions";
import apis from "@/services/settlement/billStatisticMonth";
import { SIZE, tabs_tabBarStyle } from "@/utils/constant";
const PageUrl = "/settlementReport/anchorCommissionSettlement";

export const FieldsContext = React.createContext<any>(null);
export const tab_total = "a";
export const tab_detail = "b";

const Res = () => {
  const [curTab, setCurTab] = useState(tab_total);
  const [detailParams, setDetailParams] = useState({});
  const [settleModelOptions] = useCommonOptions({ dimName: "结算模式" });
  const [shopTypeOptions] = useCommonOptions({ dimName: "店铺性质" });
  const [orderChanneOptions] = useCommonOptions({ dimName: "订单渠道" });
  const [settleTypeOptions] = useCommonOptions({ dimName: "结算类型" });
  const [CompanyNameList, setCompanyNameList] = useState([]);
  const fetchCompanyName = async () => {
    const { entry, status } = await apis.getCpsAnxinCompanyNameList();
    if (status) {
      setCompanyNameList(
        (entry || []).map((item: string) => {
          return {
            label: item,
            value: item
          };
        })
      );
    }
  };
  useEffect(() => {
    fetchCompanyName();
  }, []);
  const tabItems = [
    {
      key: tab_total,
      label: "主播佣金结算汇总",
      children: <Total />
    },
    {
      key: tab_detail,
      label: "主播佣金结算明细",
      children: <Detail />
    }
  ];
  return (
    <FieldsContext.Provider
      value={{
        settleModelOptions,
        CompanyNameList,
        shopTypeOptions,
        orderChanneOptions,
        settleTypeOptions,
        setCurTab,
        setDetailParams,
        detailParams
      }}
    >
      <Tabs activeKey={curTab} tabBarStyle={tabs_tabBarStyle} size={SIZE} onChange={setCurTab} items={tabItems} />
    </FieldsContext.Provider>
  );
};

const AliveAnchorItemSale = props => (
  <KeepAlive name={PageUrl}>
    <Res {...props} />
  </KeepAlive>
);

export default React.memo(AliveAnchorItemSale);
