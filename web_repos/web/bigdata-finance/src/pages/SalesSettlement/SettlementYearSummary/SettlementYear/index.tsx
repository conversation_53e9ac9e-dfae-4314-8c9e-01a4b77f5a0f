import React, { useContext } from "react";
import TableList from "@/components/TableList";
import { columns } from "./variable";
import apis from "@/services/settlement/settlementYearSummary";
import dayjs from "dayjs";
import styles from "../index.less";
import { FieldsContext } from "./../index";
const AnchorItemSaleD = () => {
  const { handlePageParmas } = useContext(FieldsContext);
  const SettlementYearProps = {};
  return (
    <>
      <TableList
        className={styles["table-width2"]}
        columns={columns(SettlementYearProps)}
        scroll={{ y: "calc(100vh - 420px)" }}
        form={{
          initialValues: {
            liveYearMonth: [dayjs().startOf("year").month(0), dayjs()],
            summaryDate: [dayjs().subtract(1, "day")]
          }
        }}
        summaryApi={apis.anchorSettleTimeSummary}
        api={apis.anchorSettleTime}
        downloadApi={apis.anchorSettleTimeExport}
        preFetch={handlePageParmas}
        rowKey="uuid"
      />
    </>
  );
};

export default React.memo(AnchorItemSaleD);
