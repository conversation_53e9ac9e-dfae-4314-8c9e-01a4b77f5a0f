import { formatYuanToAmount, millennialsNumber, returnEllipsisTitle, returnEllipsisTooltip } from "@/utils";
import { DatePicker } from "antd";
import AnchorSelect from "@/components/AnchorSelect";
import dayjs from "dayjs";

// 年结算汇总-上年直播
const columns_search = ({}: any) => {
  return [
    {
      title: "直播年",
      dataIndex: "liveYearMonth",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return <DatePicker.RangePicker picker="month" allowClear maxDate={dayjs()} />;
      }
    },
    {
      title: "主播名称",
      dataIndex: "anchorName",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem() {
        return <AnchorSelect showSearch allowClear maxTagCount="responsive" mode="multiple" />;
      }
    },
    {
      title: "计算日期",
      dataIndex: "summaryDate",
      hideInSearch: false,
      hideInTable: true,
      fieldProps: {
        maxDate: dayjs().subtract(1, "day")
      },
      valueType: "date"
    }
  ];
};
export const columns = ({}: any): Array<TableListItem> => {
  return [
    ...columns_search({}),
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      hideInSearch: true,
      fixed: "left",
      render: (_, record, index) => {
        return index + 1;
      }
    },
    { title: "直播年", dataIndex: "year", width: 80, hideInSearch: true },
    { title: "主播名称", dataIndex: "anchorName", width: 100, hideInSearch: true },
    // { title: "付款金额(元)", dataIndex: "payAmount", width: 100, hideInSearch: true },
    {
      title: "实际结算金额",
      dataIndex: "actualSettleAmount",
      width: 100,
      hideInSearch: true,
      summaryTooltip: {
        divisor: 100,
        hoverMillennials: true,
        millennialsOptions: { minimumFractionDigits: 2 }
      },
      render(text: any) {
        const to_yuan = text / 100 || 0;
        return returnEllipsisTitle({
          title: formatYuanToAmount(to_yuan),
          tooltip: millennialsNumber(to_yuan, { minimumFractionDigits: 2 })
        });
      }
    },
    {
      title: "预估待结算金额",
      dataIndex: "preSettleAmount",
      width: 120,
      hideInSearch: true,
      summaryTooltip: {
        divisor: 100,
        hoverMillennials: true,
        millennialsOptions: { minimumFractionDigits: 2 }
      },
      render(text: any) {
        const to_yuan = text / 100 || 0;
        return returnEllipsisTitle({
          title: formatYuanToAmount(to_yuan),
          tooltip: millennialsNumber(to_yuan, { minimumFractionDigits: 2 })
        });
      }
    },
    {
      title: "已扣年框扣费",
      dataIndex: "acutalYearlyAmount",
      width: 100,
      hideInSearch: true,
      summaryTooltip: {
        divisor: 100,
        hoverMillennials: true,
        millennialsOptions: { minimumFractionDigits: 2 }
      },
      render(text: any) {
        const to_yuan = text / 100 || 0;
        return returnEllipsisTitle({
          title: formatYuanToAmount(to_yuan),
          tooltip: millennialsNumber(to_yuan, { minimumFractionDigits: 2 })
        });
      }
    }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      ...item,
      align: "center",
      title: returnEllipsisTooltip({ title })
    };
  });
};
