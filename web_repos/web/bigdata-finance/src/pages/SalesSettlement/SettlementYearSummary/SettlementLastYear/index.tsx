import React, { useContext } from "react";
import TableList from "@/components/TableList";
import { columns } from "./variable";
import apis from "@/services/settlement/settlementYearSummary";
import dayjs from "dayjs";
import styles from "../index.less";
import { FieldsContext } from "./../index";
const Res = () => {
  const { handlePageParmas } = useContext(FieldsContext);
  const columns_props = {};
  return (
    <>
      <TableList
        className={styles["table-width3"]}
        columns={columns(columns_props)}
        scroll={{ y: "calc(100vh - 420px)" }}
        form={{
          initialValues: {
            liveYearMonth: [dayjs().subtract(1, "year").startOf("year"), dayjs().subtract(1, "year").endOf("year")],
            summaryDate: [dayjs().subtract(1, "day")]
          }
        }}
        summaryApi={apis.lastYearanchorSettleTimeSummaryLast}
        api={apis.lastYearAnchorSettleTimeLast}
        downloadApi={apis.anchorSettleTimeLastExport}
        preFetch={handlePageParmas}
        rowKey="uuid"
      />
    </>
  );
};

export default React.memo(Res);
