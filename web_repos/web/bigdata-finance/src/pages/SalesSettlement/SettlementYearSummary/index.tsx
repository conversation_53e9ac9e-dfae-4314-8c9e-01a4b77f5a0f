import React, { useState } from "react";
import { Tabs } from "antd";
import KeepAlive from "react-activation";
import SettlementMonth from "./SettlementMonth";
import SettlementYear from "./SettlementYear";
import SettlementLastYear from "./SettlementLastYear";
import { SIZE, tabs_tabBarStyle } from "@/utils/constant";
import dayjs from "dayjs";
const PageUrl = "/salesSettlement/settlementYearSummary";

export const FieldsContext = React.createContext<any>(null);
export const tab_month = "a";
export const tab_year = "b";
export const tab_lastYear = "c";
const AnchorItemSale = () => {
  const [curTab, setCurTab] = useState(tab_month);
  const handlePageParmas = (params: any) => {
    const { current, liveYearMonth, summaryDate, ...rest } = params;
    if (liveYearMonth) {
      const [startLiveYearMonth, endLiveYearMonth] = liveYearMonth;
      rest.startLiveYearMonth = dayjs(startLiveYearMonth).format("YYYYMM");
      rest.endLiveYearMonth = dayjs(endLiveYearMonth).format("YYYYMM");
    }

    if (summaryDate) {
      rest.summaryDate = dayjs(summaryDate).format("YYYYMMDD");
    }
    return { ...rest, pageNo: current };
  };
  const tabItems = [
    {
      key: tab_month,
      label: "年结算汇总-月",
      children: <SettlementMonth />
    },
    {
      key: tab_year,
      label: "年结算汇总-本年直播",
      children: <SettlementYear />
    },
    {
      key: tab_lastYear,
      label: "年结算汇总-上年直播",
      children: <SettlementLastYear />
    }
  ];
  return (
    <FieldsContext.Provider value={{ handlePageParmas }}>
      <Tabs activeKey={curTab} tabBarStyle={tabs_tabBarStyle} size={SIZE} onChange={setCurTab} items={tabItems} />
    </FieldsContext.Provider>
  );
};

const AliveAnchorItemSale = props => (
  <KeepAlive name={PageUrl}>
    <AnchorItemSale {...props} />
  </KeepAlive>
);

export default React.memo(AliveAnchorItemSale);
