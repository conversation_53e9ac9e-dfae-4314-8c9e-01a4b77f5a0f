import React, { useContext } from "react";
import TableList from "@/components/TableList";
import { columns } from "./variable";
import apis from "@/services/settlement/settlementYearSummary";
import dayjs from "dayjs";
import styles from "../index.less";
import { FieldsContext } from "./../index";
const Res = () => {
  const { handlePageParmas } = useContext(FieldsContext);
  const columns_props = {};

  return (
    <>
      <TableList
        className={styles["table-width1"]}
        columns={columns(columns_props)}
        scroll={{ y: "calc(100vh - 420px)" }}
        form={{
          initialValues: {
            liveYearMonth: [dayjs("202401"), dayjs()],
            summaryDate: [dayjs().subtract(1, "day")]
          }
        }}
        summaryApi={apis.monthSettleTimeSummary}
        api={apis.monthSettleTime}
        preFetch={handlePageParmas}
        downloadApi={apis.monthSettleTimeExport}
        rowKey="uuid"
      />
    </>
  );
};

export default React.memo(Res);
