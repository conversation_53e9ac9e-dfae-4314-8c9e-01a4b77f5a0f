import React, { useState, useMemo, useLayoutEffect } from "react";
import { Tabs, Button } from "antd";
import KeepAlive from "react-activation";
import TableList from "@/components/TableList";
import { SIZE, tabs_tabBarStyle } from "@/utils/constant";
import { sale_columns, settlement_columns } from "./variable";
import apis from "@/services/settlement/videogoods";
import dayjs from "dayjs";
import { pushExportHistory, splitSpaceComma } from "@/utils";
import { btn_auths, model_auths } from "./auths";
import AuthButton, { useModelAuth } from "@/wrappers/authButton";
export const Sale_Detail = model_auths.model_videogoods_sale_detail;
export const Settlement_Detail = model_auths.model_videogoods_settlment_detail;
const PageUrl = "/salesSettlement/videogoods";
const DetailTable = () => {
  const authModel = useModelAuth({ pageUrl: PageUrl });
  const [curTab, setCurTab] = useState("");
  const [salePageParams, setSalePageParams] = useState({});
  const [settlementPageParams, setSettlementPageParams] = useState({});
  const sale_columnsProps = useMemo(() => {
    return {};
  }, []);
  const settlement_columnsProps = useMemo(() => {
    return {};
  }, []);
  // 处理请求参数
  const handlePageParmas = (params: any) => {
    const { payDate, settleDate, current, ...rest } = params;
    rest.payDateStart = void 0;
    rest.payDateEnd = void 0;
    rest.settleDateStart = void 0;
    rest.settleDateEnd = void 0;
    // 单独处理 支付日期
    if (payDate) {
      const [payDateStart, payDateEnd] = payDate;
      rest.payDateStart = dayjs(payDateStart).format("YYYYMMDD");
      rest.payDateEnd = dayjs(payDateEnd).format("YYYYMMDD");
    }
    // 结算日期
    if (settleDate) {
      const [settleDateStart, settleDateEnd] = settleDate;
      rest.settleDateStart = dayjs(settleDateStart).format("YYYYMMDD");
      rest.settleDateEnd = dayjs(settleDateEnd).format("YYYYMMDD");
    }
    rest.itemIdList = splitSpaceComma(rest.itemIdList);
    return { ...rest, pageNo: current };
  };
  const handleExport = async (type: "sale" | "settlement") => {
    if (type === "sale") {
      const { status } = await apis.downloadVideoDailySummaryRf(salePageParams);
      if (status) {
        pushExportHistory();
      }
      return;
    }
    if (type === "settlement") {
      const { status } = await apis.downloadVideoDailySettleSummaryRf(settlementPageParams);
      if (status) {
        pushExportHistory();
      }
      return;
    }
  };

  const tabItems = [
    {
      key: Sale_Detail,
      label: "商品每日销售明细",
      children: (
        <TableList
          columns={sale_columns(sale_columnsProps)}
          scroll={{ y: "calc(100vh - 430px)" }}
          api={apis.getVideoDailySummaryRfPage}
          preFetch={handlePageParmas}
          paramsChange={setSalePageParams}
          rowKey={record => {
            const { anchorId, anchorName, payDate, itemId } = record;
            return anchorId + anchorName + payDate + itemId;
          }}
          toolbar={{
            actions: [
              <AuthButton key="sale-export" code={btn_auths.btn_videogoods_sale_detail_export} pageUrl={PageUrl}>
                <Button onClick={() => handleExport("sale")} size={SIZE}>
                  导出
                </Button>
              </AuthButton>
            ]
          }}
        />
      )
    },
    {
      key: Settlement_Detail,
      label: "商品每日结算明细",
      children: (
        <TableList
          columns={settlement_columns(settlement_columnsProps)}
          scroll={{ y: "calc(100vh - 430px)" }}
          api={apis.getVideoDailySettleSummaryRfPage}
          preFetch={handlePageParmas}
          paramsChange={setSettlementPageParams}
          rowKey={record => {
            const { anchorId, anchorName, payDate, itemId, settleDate } = record;
            return anchorId + anchorName + payDate + itemId + settleDate;
          }}
          toolbar={{
            actions: [
              <AuthButton key="settlement-export" code={btn_auths.btn_videogoods_settlment_detail_export} pageUrl={PageUrl}>
                <Button onClick={() => handleExport("settlement")} size={SIZE}>
                  导出
                </Button>
              </AuthButton>
            ]
          }}
        />
      )
    }
  ].filter(item => {
    return authModel(item.key);
  });

  useLayoutEffect(() => {
    if (!curTab) {
      setCurTab(tabItems?.[0]?.key || "");
    }
  }, [tabItems, curTab]);
  return <Tabs activeKey={curTab} tabBarStyle={tabs_tabBarStyle} size={SIZE} onChange={setCurTab} items={tabItems} />;
};

const AliveRecord = () => (
  <KeepAlive name={PageUrl}>
    <DetailTable />
  </KeepAlive>
);
export default React.memo(AliveRecord);
