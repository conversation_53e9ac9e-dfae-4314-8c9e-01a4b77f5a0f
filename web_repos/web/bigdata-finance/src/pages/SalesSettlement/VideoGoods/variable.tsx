import { returnEllipsisTooltip, returnItemImgUrl } from "@/utils";
import { NOVALUE } from "@/utils/constant";
import { Image } from "antd";
import DepartmentSelect from "@/components/DepartmentSelect";
import AnchorSelect from "@/components/AnchorSelect";
import SupplierNameSelect from "@/components/SupplierNameSelect";
const sale_columns_search = () => {
  return [
    {
      title: "支付日期",
      dataIndex: "payDate",
      hideInSearch: false,
      hideInTable: true,
      valueType: "dateRange"
    },
    {
      title: "主播",
      dataIndex: "anchorNameList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem() {
        return <AnchorSelect showSearch allowClear maxTagCount="responsive" mode="multiple" />;
      }
    },
    {
      title: "商品ID",
      dataIndex: "itemIdList",
      hideInSearch: false,
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "商品名称",
      dataIndex: "itemTitle",
      hideInSearch: false,
      hideInTable: true
    },
    {
      title: "店铺名称",
      dataIndex: "shopName",
      hideInSearch: false,
      hideInTable: true
    },
    {
      title: "供应商名称",
      dataIndex: "supplierName",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem() {
        return <SupplierNameSelect />;
      }
    },
    {
      title: "大部门",
      dataIndex: "bigDepartmentList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return <DepartmentSelect key="bigDepartmentList" maxTagCount="responsive" mode="multiple" />;
      }
    },
    {
      title: "我司合同主体",
      dataIndex: "companyName",
      hideInSearch: false,
      hideInTable: true
    }
  ];
};
export const sale_columns = ({}): Array<TableListItem> => {
  return [
    ...sale_columns_search(),
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      hideInSearch: true,
      fixed: "left",
      render: (_, record, index) => {
        return index + 1;
      }
    },
    {
      title: "支付日期",
      dataIndex: "payDateStr",
      width: 100,
      hideInSearch: true,
      fixed: "left"
    },
    {
      title: "主播",
      dataIndex: "anchorName",
      width: 100,
      hideInSearch: true,
      fixed: "left"
    },
    {
      title: "商品ID",
      dataIndex: "itemId",
      width: 100,
      hideInSearch: true
    },
    {
      title: "商品信息",
      dataIndex: "itemInfo",
      width: 300,
      hideInSearch: true,
      render(_, record) {
        const { itemMainImg } = record;
        return (
          <div className="columnItemInfo">
            <Image width={50} height={50} src={returnItemImgUrl(itemMainImg)} />
            <div className="info">
              <div>{returnEllipsisTooltip({ title: record?.itemTitle, rows: 1, width: 250 })}</div>
              <div className="price">
                价格：
                <span className="value">{record?.itemPriceStr || NOVALUE}</span>
              </div>
            </div>
          </div>
        );
      }
    },
    {
      title: "店铺ID",
      dataIndex: "shopId",
      width: 100,
      hideInSearch: true
    },
    {
      title: "店铺名称",
      dataIndex: "shopName",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "供应商名称",
      dataIndex: "supplierName",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "大部门",
      dataIndex: "bigDepartment",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "我司合同主体",
      dataIndex: "companyName",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "合同号",
      dataIndex: "orderContractId",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "下单金额",
      dataIndex: "gmvAllStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "下单件数",
      dataIndex: "volumeAll",
      width: 100,
      hideInSearch: true
    },
    {
      title: "下单订单数",
      dataIndex: "subOrderCnt",
      width: 100,
      hideInSearch: true
    },
    {
      title: "付款金额",
      dataIndex: "payAmountStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "付款件数",
      dataIndex: "payVolume",
      width: 100,
      hideInSearch: true
    },
    {
      title: "付款订单数",
      dataIndex: "paySubOrderCnt",
      width: 100,
      hideInSearch: true
    },
    {
      title: "退款金额",
      dataIndex: "refundAmountStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "退款件数",
      dataIndex: "refundVolume",
      width: 100,
      hideInSearch: true
    },
    {
      title: "退款率",
      dataIndex: "refundRateStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "净支付金额",
      dataIndex: "netPayAmountStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "线上佣金比率",
      dataIndex: "onlineCommisRateStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "平台计收累计佣金金额(元)",
      dataIndex: "platSettleCommissionAmountStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "分销者累计佣金金额(元)",
      dataIndex: "shopSettleCommissionAmountStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "主播红包补贴金额",
      dataIndex: "anchorRedPacketStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "辛选年框扣费金额",
      dataIndex: "xinxuanNiankuangAmtStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "团长类型",
      dataIndex: "leaderName",
      width: 100,
      hideInSearch: true
    },
    {
      title: "团长结算金额",
      dataIndex: "leaderCommissionAmountStr",
      width: 100,
      hideInSearch: true
    }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      ...item,
      align: "center",
      title: returnEllipsisTooltip({ title })
    };
  });
};
const settlement_columns_search = () => {
  return [
    {
      title: "支付日期",
      dataIndex: "payDate",
      hideInSearch: false,
      hideInTable: true,
      valueType: "dateRange"
    },
    {
      title: "结算日期",
      dataIndex: "settleDate",
      hideInSearch: false,
      hideInTable: true,
      valueType: "dateRange"
    },
    {
      title: "主播",
      dataIndex: "anchorNameList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem() {
        return <AnchorSelect showSearch allowClear maxTagCount="responsive" mode="multiple" />;
      }
    },
    {
      title: "商品ID",
      dataIndex: "itemIdList",
      hideInSearch: false,
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "商品名称",
      dataIndex: "itemTitle",
      hideInSearch: false,
      hideInTable: true
    },
    {
      title: "店铺名称",
      dataIndex: "shopName",
      hideInSearch: false,
      hideInTable: true
    },
    {
      title: "供应商名称",
      dataIndex: "supplierName",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem() {
        return <SupplierNameSelect />;
      }
    },
    {
      title: "大部门",
      dataIndex: "bigDepartmentList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return <DepartmentSelect key="bigDepartmentList" maxTagCount="responsive" mode="multiple" />;
      }
    },
    {
      title: "我司合同主体",
      dataIndex: "companyName",
      hideInSearch: false,
      hideInTable: true
    }
  ];
};
export const settlement_columns = ({}): Array<TableListItem> => {
  return [
    ...settlement_columns_search(),
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      hideInSearch: true,
      fixed: "left",
      render: (_, record, index) => {
        return index + 1;
      }
    },
    {
      title: "支付日期",
      dataIndex: "payDateStr",
      width: 100,
      hideInSearch: true,
      fixed: "left"
    },
    {
      title: "主播",
      dataIndex: "anchorName",
      width: 100,
      hideInSearch: true,
      fixed: "left"
    },
    {
      title: "商品ID",
      dataIndex: "itemId",
      width: 100,
      hideInSearch: true
    },
    {
      title: "商品信息",
      dataIndex: "itemInfo",
      width: 220,
      hideInSearch: true,
      render(_, record) {
        const { itemMainImg } = record;
        return (
          <div className="columnItemInfo">
            <Image width={50} height={50} src={returnItemImgUrl(itemMainImg)} />
            <div className="info">
              <div>{returnEllipsisTooltip({ title: record?.itemTitle })}</div>
              <div className="price">
                价格：
                <span className="value">{record?.itemPriceStr || NOVALUE}</span>
              </div>
            </div>
          </div>
        );
      }
    },
    {
      title: "店铺ID",
      dataIndex: "shopId",
      width: 100,
      hideInSearch: true
    },
    {
      title: "店铺名称",
      dataIndex: "shopName",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "供应商名称",
      dataIndex: "supplierName",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "大部门",
      dataIndex: "bigDepartment",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "我司合同主体",
      dataIndex: "companyName",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "合同号",
      dataIndex: "orderContractId",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },

    {
      title: "结算日期",
      dataIndex: "settleDateStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "结算金额",
      dataIndex: "settleAmountStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "结算件数",
      dataIndex: "settleVolume",
      width: 100,
      hideInSearch: true
    },
    {
      title: "结算订单数",
      dataIndex: "settleOrderCnt",
      width: 100,
      hideInSearch: true
    },
    {
      title: "线上佣金比率",
      dataIndex: "onlineCommisRateStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "平台计收累计佣金金额(元)",
      dataIndex: "platSettleCommissionAmountStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "分销者累计佣金金额(元)",
      dataIndex: "shopSettleCommissionAmountStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "主播红包补贴金额",
      dataIndex: "anchorRedPacketStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "辛选年框扣费金额",
      dataIndex: "xinxuanNiankuangAmtStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "团长类型",
      dataIndex: "leaderName",
      width: 100,
      hideInSearch: true
    },
    {
      title: "团长结算金额",
      dataIndex: "leaderCommissionAmountStr",
      width: 100,
      hideInSearch: true
    }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      ...item,
      align: "center",
      title: returnEllipsisTooltip({ title })
    };
  });
};
