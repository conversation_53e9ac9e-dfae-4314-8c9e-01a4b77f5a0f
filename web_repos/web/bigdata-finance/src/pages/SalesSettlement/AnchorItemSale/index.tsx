import React, { useState } from "react";
import { Tabs } from "antd";
import KeepAlive from "react-activation";
import AnchorItemSaleD from "./AnchorItemSaleD";
import AnchorItemSaleM from "./AnchorItemSaleM";
import { SIZE, tabs_tabBarStyle } from "@/utils/constant";
import useCommonOptions from "@/hooks/useCommonOptions";
const PageUrl = "/salesSettlement/anchorItemSale";
export const tab_day = "a";
export const tab_month = "b";
const AnchorItemSale = () => {
  const [curTab, setCurTab] = useState(tab_day);

  const [dataSourceOptions] = useCommonOptions({ dimName: "数据源" });
  const [isLiveOptions] = useCommonOptions({ dimName: "是否直播单" });
  const [, leaderFlagObj] = useCommonOptions({ dimName: "团长类型" });
  const tabItems = [
    {
      key: tab_day,
      label: "主播商品交易-日",
      children: <AnchorItemSaleD dataSourceOptions={dataSourceOptions} isLiveOptions={isLiveOptions} leaderFlagObj={leaderFlagObj} />
    },
    {
      key: tab_month,
      label: "主播商品交易-月",
      children: <AnchorItemSaleM dataSourceOptions={dataSourceOptions} isLiveOptions={isLiveOptions} />
    }
  ];
  return <Tabs activeKey={curTab} tabBarStyle={tabs_tabBarStyle} size={SIZE} onChange={setCurTab} items={tabItems} />;
};

const AliveAnchorItemSale = props => (
  <KeepAlive name={PageUrl}>
    <AnchorItemSale {...props} />
  </KeepAlive>
);

export default React.memo(AliveAnchorItemSale);
