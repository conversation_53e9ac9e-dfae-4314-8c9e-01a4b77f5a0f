import React, { useCallback } from "react";
import apis from "@/services/finance/anchorItemSale";
import BaseSelect from "@/pages/goods/components/base-select";

const Select = props => {
  const { params = {} } = props;
  const queryFun = useCallback(
    v =>
      apis.getListBrandNames({
        brandNameSearch: v,
        pageNo: 1,
        pageSize: 50,
        ...params
      }),
    [params]
  );

  const handleResFun = useCallback(r => {
    return r?.status ? (r.entry || []).map(i => ({ key: i, name: i })) : [];
  }, []);

  return <BaseSelect queryFun={queryFun} handleResFun={handleResFun} {...props} />;
};

export default Select;
