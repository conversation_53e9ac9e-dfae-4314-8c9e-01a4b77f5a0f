import { Select } from "antd";
import { filterOptionLabel, returnEllipsisTooltip } from "@/utils";
import { NOVALUE, SIZE } from "@/utils/constant";
import AnchorSelect from "@/components/AnchorSelect";
import DepartmentSelect from "@/components/DepartmentSelect";
import BrandDebounSelect from "@/components/BrandDebounSelect";
import SupplierNameSelect from "@/components/SupplierNameSelect";
const columns_SPU_search = ({ DataSourceOptions }: any) => {
  return [
    {
      title: "直播日期",
      dataIndex: "liveDate",
      hideInTable: true,
      valueType: "dateRange",
      fieldProps: {
        format: "YYYYMMDD"
      }
    },
    {
      title: "结算周期",
      dataIndex: "settleDate",
      hideInTable: true,
      valueType: "dateRange",
      fieldProps: {
        format: "YYYYMMDD"
      }
    },

    {
      title: "主播",
      dataIndex: "anchorNameList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem() {
        return <AnchorSelect showSearch allowClear maxTagCount="responsive" mode="multiple" />;
      }
    },
    {
      title: "主播ID",
      dataIndex: "anchorIdList",
      hideInSearch: false,
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },

    {
      title: "数据来源",
      dataIndex: "orderSourceList",
      hideInTable: true,
      renderFormItem: () => {
        return (
          <Select
            placeholder="请选择"
            style={{ minWidth: 120, textAlign: "left" }}
            size={SIZE}
            options={DataSourceOptions}
            filterOption={filterOptionLabel}
            showSearch
            allowClear
            maxTagCount="responsive"
            mode="multiple"
          />
        );
      }
    },

    {
      title: "商品名称",
      dataIndex: "itemName",
      hideInTable: true
    },
    {
      title: "商品id",
      dataIndex: "itemIdList",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "店铺名称",
      dataIndex: "shopName",
      hideInTable: true
    },
    {
      title: "大部门",
      dataIndex: "bigDepartmentList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return <DepartmentSelect key="bigDepartmentList" maxTagCount="responsive" mode="multiple" />;
      }
    },
    {
      title: "供应商名称",
      dataIndex: "supplierName",
      hideInTable: true,
      renderFormItem() {
        return <SupplierNameSelect />;
      }
    },
    {
      title: "我司合同主体",
      dataIndex: "companyName",
      hideInTable: true
    },
    {
      title: "品牌",
      dataIndex: "brandNameList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem() {
        return <BrandDebounSelect key="brandName" showSearch allowClear maxTagCount="responsive" mode="multiple" />;
      }
    }
  ];
};
export const columns_SPU = ({ DataSourceOptions, DataSourceOptionsObj, shopTypeObj, isRoomExplainObj }: any): Array<TableListItem> => {
  return [
    ...columns_SPU_search({ DataSourceOptions }),
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      hideInSearch: true,
      fixed: "left",
      render: (_, record, index) => {
        return index + 1;
      }
    },

    {
      title: "数据来源",
      dataIndex: "dataSource",
      width: 80,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: DataSourceOptionsObj[text] || NOVALUE });
      }
    },
    {
      title: "直播日期",
      dataIndex: "liveDate",
      width: 100,
      valueType: "date",
      hideInSearch: true
    },
    {
      title: "主播ID",
      dataIndex: "anchorId",
      width: 100,
      hideInSearch: true
    },
    {
      title: "主播",
      dataIndex: "anchorName",
      width: 80,
      hideInSearch: true
    },
    {
      title: "店铺ID",
      dataIndex: "shopId",
      width: 80,
      hideInSearch: true
    },
    {
      title: "店铺名称",
      dataIndex: "shopName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "店铺性质",
      dataIndex: "shopType",
      width: 80,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: shopTypeObj[text] || NOVALUE });
      }
    },
    {
      title: "品牌",
      dataIndex: "brandName",
      width: 80,
      hideInSearch: true
    },
    {
      title: "商品ID",
      dataIndex: "itemId",
      width: 100,
      hideInSearch: true
    },
    {
      title: "商品编码",
      dataIndex: "itemCode",
      width: 120,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品名称",
      dataIndex: "itemTitle",
      width: 200,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "是否讲解",
      dataIndex: "isRoomExplain",
      width: 80,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: isRoomExplainObj[text] || NOVALUE });
      }
    },
    {
      title: "结算周期",
      dataIndex: "settleDate",
      width: 80,
      hideInSearch: true
    },
    {
      title: "累计结算订单数",
      dataIndex: "settleSubOrderCntStr",
      width: 80,
      hideInSearch: true
    },

    {
      title: "累计结算件数",
      dataIndex: "settleItemVolumeStr",
      width: 80,
      hideInSearch: true
    },
    {
      title: "累计结算金额(元)",
      dataIndex: "settleItemAmountStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "累计结算佣金金额",
      dataIndex: "settleCommissionAmountStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "平台计收累计佣金金额(元)",
      dataIndex: "platSettleCommissionAmountStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "分销者累计佣金金额(元)",
      dataIndex: "shopSettleCommissionAmountStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "累计辛选年框扣费金额",
      dataIndex: "settleYearlyAmountStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "累计技术服务费(元)",
      dataIndex: "settleServiceAmountStr",
      width: 100,
      hideInSearch: true
    },

    {
      title: "累计结算技术服务费追回金额",
      dataIndex: "refundServiceAmountStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "累计售后订单数",
      dataIndex: "refundOrderNumStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "累计售后结算佣金",
      dataIndex: "refundCommissionAmountStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "累计维权数量",
      dataIndex: "refundItemNumStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "累计维权金额(元)",
      dataIndex: "refundAmountStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "线上佣金比例",
      dataIndex: "onlineCommissionRateStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "是否项目",
      dataIndex: "isProject",
      width: 80,
      hideInSearch: true
    },
    {
      title: "团长类型",
      dataIndex: "leaderName",
      width: 80,
      hideInSearch: true
    },
    {
      title: "团长结算金额",
      dataIndex: "leaderCommissionAmountStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "主播红包补贴金额",
      dataIndex: "anchorAllowAmountStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "支付转化率",
      dataIndex: "conversRateStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "退款率",
      dataIndex: "liveRefundRateStr",
      width: 80,
      hideInSearch: true
    },

    {
      title: "供应商名称",
      dataIndex: "supplierName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "大部门",
      dataIndex: "bigDepartment",
      width: 80,
      hideInSearch: true
    },
    {
      title: "直播价格",
      dataIndex: "livePriceStr",
      width: 80,
      hideInSearch: true
    },
    {
      title: "付款件数",
      dataIndex: "payItemVolumeStr",
      width: 80,
      hideInSearch: true
    },
    {
      title: "付款金额",
      dataIndex: "payItemAmountStr",
      width: 80,
      hideInSearch: true
    },
    {
      title: "待结算件数",
      dataIndex: "pendingSettleVolumeStr",
      width: 70,
      hideInSearch: true
    },
    {
      title: "待结算金额",
      dataIndex: "pendingSettleAmountStr",
      width: 70,
      hideInSearch: true
    },
    {
      title: "招商采购负责人",
      dataIndex: "purchasePrincipal",
      width: 80,
      hideInSearch: true
    },
    {
      title: "我司合同主体",
      dataIndex: "contractName",
      width: 140,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "合同号",
      dataIndex: "orderContractId",
      width: 80,
      hideInSearch: true
    }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      align: "center",
      ...item,
      title: returnEllipsisTooltip({ title })
    };
  });
};
const columns_distribution_search = ({ DataSourceOptions }: any) => {
  return [
    {
      title: "直播日期",
      dataIndex: "liveDate",
      hideInTable: true,
      valueType: "dateRange",
      fieldProps: {
        format: "YYYYMMDD"
      }
    },
    {
      title: "结算周期",
      dataIndex: "settleDate",
      hideInTable: true,
      valueType: "dateRange",
      fieldProps: {
        format: "YYYYMMDD"
      }
    },

    {
      title: "主播",
      dataIndex: "anchorNames",
      hideInTable: true,
      renderFormItem() {
        return <AnchorSelect showSearch allowClear maxTagCount="responsive" mode="multiple" />;
      }
    },

    {
      title: "数据来源",
      dataIndex: "orderSource",
      hideInTable: true,
      renderFormItem: () => {
        return (
          <Select
            placeholder="请选择"
            size={SIZE}
            options={DataSourceOptions}
            filterOption={filterOptionLabel}
            showSearch
            allowClear
            maxTagCount="responsive"
            mode="multiple"
          />
        );
      }
    },

    {
      title: "商品名称",
      dataIndex: "itemName",
      hideInTable: true
    },
    {
      title: "商品ID",
      dataIndex: "itemIds",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "店铺名称",
      dataIndex: "shopName",
      hideInTable: true
    },
    {
      title: "大部门",
      dataIndex: "bigDepartments",
      hideInTable: true,
      renderFormItem: () => {
        return <DepartmentSelect key="bigDepartmentList" maxTagCount="responsive" mode="multiple" />;
      }
    },
    {
      title: "供应商名称",
      dataIndex: "supplierName",
      hideInTable: true,
      renderFormItem() {
        return <SupplierNameSelect />;
      }
    }
  ];
};
export const columns_distribution = ({ DataSourceOptions, DataSourceOptionsObj }: any): Array<TableListItem> => {
  return [
    ...columns_distribution_search({ DataSourceOptions }),
    {
      title: "序号",
      dataIndex: "index",
      width: 60,
      fixed: "left",
      hideInSearch: true,
      render: (text: any, row: any, index: number) => {
        return index + 1;
      }
    },
    {
      title: "数据来源",
      dataIndex: "dataSource",
      width: 80,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: DataSourceOptionsObj[text] || NOVALUE });
      }
    },
    {
      title: "直播日期",
      dataIndex: "liveDate",
      width: 100,
      valueType: "date",
      hideInSearch: true
    },
    {
      title: "推广者pid",
      dataIndex: "pid",
      width: 80,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "主播",
      dataIndex: "anchorName",
      width: 80,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "店铺ID",
      dataIndex: "shopId",
      width: 80,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "店铺名称",
      dataIndex: "shopName",
      width: 80,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "店铺性质",
      dataIndex: "shopType",
      width: 80,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品ID",
      dataIndex: "itemId",
      width: 80,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品编码",
      dataIndex: "liveItemCode",
      width: 80,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品名称",
      dataIndex: "itemTitle",
      width: 200,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "是否挂链",
      dataIndex: "chainingState",
      width: 80,
      hideInSearch: true
    },
    {
      title: "结算周期",
      dataIndex: "settleDate",
      width: 80,
      hideInSearch: true
    },

    {
      title: "累计结算件数",
      dataIndex: "settleItemVolumeStr",
      width: 80,
      hideInSearch: true
    },
    {
      title: "累计结算金额(元)",
      dataIndex: "settleItemAmountStr",
      width: 80,
      hideInSearch: true
    },
    {
      title: "累计结算退款金额",
      dataIndex: "refundItemAmountStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "累计结算商品父订单数",
      dataIndex: "settleParentOrderCntStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "累计佣金金额(元)",
      dataIndex: "pubShareAmtStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "累计辛选店铺技术服务费",
      dataIndex: "shopTechServiceFeeStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "累计账单达人佣金金额",
      dataIndex: "billPidCommissionAmountStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "累计账单结算金额",
      dataIndex: "billSettleAmountStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "累计辛选年框扣费金额",
      dataIndex: "xinxuanNiankuangAmtStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "累计技术服务费(元)",
      dataIndex: "serviceAmtStr",
      width: 100,
      hideInSearch: true
    },

    {
      title: "累计预估专项服务费(元)",
      dataIndex: "commissionPreAmtStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "累计结算专项服务费(元)",
      dataIndex: "settleCommissionPreAmtStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "累计维权数量",
      dataIndex: "relationRefundVolumeStr",
      width: 80,
      hideInSearch: true
    },

    {
      title: "累计维权金额(元)",
      dataIndex: "relationRefundAmountStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "累计维权结算金额(元)",
      dataIndex: "relationRefundSettleAmountStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "累计维权应返商家金额(元)",
      dataIndex: "relationRefundReturnAmountStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "佣金率",
      dataIndex: "commissionRateStr",
      width: 80,
      hideInSearch: true
    },
    {
      title: "佣金率占比",
      dataIndex: "commissionRateMixStr",
      width: 90,
      hideInSearch: true
    },
    {
      title: "是否项目",
      dataIndex: "isOrganization",
      width: 80,
      hideInSearch: true
    },
    {
      title: "团长类型",
      dataIndex: "leaderName",
      width: 80,
      hideInSearch: true
    },
    {
      title: "团长结算金额",
      dataIndex: "leaderCommissionAmountStr",
      width: 80,
      hideInSearch: true
    },
    {
      title: "主播红包补贴金额",
      dataIndex: "anchorRedPacketStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "支付转化率",
      dataIndex: "conversRateStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "退款率",
      dataIndex: "liveRefundRateStr",
      width: 80,
      hideInSearch: true
    },
    {
      title: "佣金是否异常",
      dataIndex: "isNormal",
      width: 100,
      hideInSearch: true
    },
    {
      title: "供应商名称",
      dataIndex: "supplierName",
      width: 150,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "大部门",
      dataIndex: "bigDepartment",
      width: 80,
      hideInSearch: true
    },
    {
      title: "直播价格",
      dataIndex: "livePrice",
      width: 80,
      hideInSearch: true
    },
    {
      title: "付款件数",
      dataIndex: "payItemVolumeStr",
      width: 80,
      hideInSearch: true
    },
    {
      title: "付款金额",
      dataIndex: "payItemAmountStr",
      width: 80,
      hideInSearch: true
    },

    {
      title: "招商采购负责人",
      dataIndex: "purchasePrincipal",
      width: 80,
      hideInSearch: true
    },
    {
      title: "我司合同主体",
      dataIndex: "contractName",
      width: 140,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "合同号",
      dataIndex: "contraceNo",
      width: 80,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      align: "center",
      ...item,
      title: returnEllipsisTooltip({ title })
    };
  });
};
