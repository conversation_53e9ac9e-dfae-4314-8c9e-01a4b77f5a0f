import React, { useLayoutEffect, useState } from "react";
import { <PERSON><PERSON>, But<PERSON> } from "antd";
import KeepAlive from "react-activation";
import TableList from "@/components/TableList";
import { SIZE, tabs_tabBarStyle } from "@/utils/constant";
import { columns_SPU, columns_distribution } from "./variable";
import apis from "@/services/settlement/livesettlementday";
import dayjs from "dayjs";
import { pushExportHistory, splitSpaceComma } from "@/utils";
import useCommonOptions from "@/hooks/useCommonOptions";
import AuthButton, { useModelAuth } from "@/wrappers/authButton";
import { btn_auths, model_auths } from "./auths";
export const Finance_settlement_SPU = model_auths.model_livesettlementday_bill;
export const Finance_settlement_distribution = model_auths.model_livesettlementday_distribution;
export const PageUrl = "/salesSettlement/livesettlementday";
const DetailTable = () => {
  const authModel = useModelAuth({ pageUrl: PageUrl });
  const [DataSourceOptions, DataSourceOptionsObj] = useCommonOptions({ dimName: "数据来源" });
  const [leaderFlag, leaderFlagObj] = useCommonOptions({ dimName: "团长类型" });
  const [shopTypeOptions, shopTypeObj] = useCommonOptions({ dimName: "店铺性质" });
  const [isRoomExplainOptions, isRoomExplainObj] = useCommonOptions({ dimName: "是否讲解" });
  const [curTab, setCurTab] = useState("");
  const [SPUPageParams, setSPUPageParams] = useState({});
  const [distributionPageParams, setdistributionPageParams] = useState({});
  const columns_SPUProps = {
    DataSourceOptions,
    DataSourceOptionsObj,
    leaderFlag,
    leaderFlagObj,
    shopTypeOptions,
    shopTypeObj,
    isRoomExplainOptions,
    isRoomExplainObj
  };
  const columns_distributionProps = { DataSourceOptions, DataSourceOptionsObj };

  // 处理请求参数
  const handlePageParmas = (p: any) => {
    const { current, liveDate, settleDate, ...rest } = p;
    const params = {
      pageNo: current,
      ...rest,
      startTime: liveDate?.[0] ?? void 0,
      endTime: liveDate?.[1] ?? void 0,
      startSettleTime: settleDate?.[0] ?? void 0,
      endSettleTime: settleDate?.[1] ?? void 0,
      itemIdList: splitSpaceComma(rest?.itemIdList),
      anchorIdList: splitSpaceComma(rest?.anchorIdList)
    };
    return params;
  };
  const handledistributionPageParmas = (p: any) => {
    const { current, liveDate, settleDate, ...rest } = p;
    const params = {
      pageNo: current,
      ...rest,
      startTime: liveDate?.[0] ?? void 0,
      endTime: liveDate?.[1] ?? void 0,
      startSettleTime: settleDate?.[0] ?? void 0,
      endSettleTime: settleDate?.[1] ?? void 0,
      itemIds: splitSpaceComma(rest?.itemIds)
    };
    return params;
  };
  const handleExport = async (type: "bill" | "distribution") => {
    if (type === "bill") {
      const { status } = await apis.downloadLiveSettleAnchorItem(SPUPageParams);
      if (status) {
        pushExportHistory();
      }
      return;
    }
    if (type === "distribution") {
      const { status } = await apis.downloadSPUList(distributionPageParams);
      if (status) {
        pushExportHistory();
      }
      return;
    }
  };

  const tabItems = [
    {
      key: Finance_settlement_SPU,
      label: "账单维度结算",
      children: (
        <TableList
          columns={columns_SPU(columns_SPUProps)}
          scroll={{ y: "calc(100vh - 470px)" }}
          api={apis.getLiveSettleAnchorItemPage}
          form={{
            initialValues: {
              liveDate: [dayjs().endOf("day").subtract(30, "day").format("YYYYMMDD"), dayjs().startOf("day").format("YYYYMMDD")]
            }
          }}
          preFetch={handlePageParmas}
          paramsChange={setSPUPageParams}
          rowKey={record => {
            const { liveDate, pid, anchorame, itemId, settleDate } = record;
            return liveDate + pid + anchorame + itemId + settleDate;
          }}
          toolbar={{
            actions: [
              <AuthButton key="SPU-export" pageUrl={PageUrl} code={btn_auths.btn_livesettlementday_bill_export}>
                <Button onClick={() => handleExport("bill")} size={SIZE}>
                  导出
                </Button>
              </AuthButton>
            ]
          }}
        />
      )
    },
    {
      key: Finance_settlement_distribution,
      label: "分销维度结算",
      children: (
        <TableList
          columns={columns_distribution(columns_distributionProps)}
          scroll={{ y: "calc(100vh - 430px)" }}
          api={apis.getListForSPU}
          form={{
            initialValues: {
              liveDate: [dayjs().endOf("day").subtract(30, "day").format("YYYYMMDD"), dayjs().startOf("day").format("YYYYMMDD")]
            }
          }}
          preFetch={handledistributionPageParmas}
          paramsChange={setdistributionPageParams}
          rowKey={record => {
            const { liveDate, pid, anchorName, itemId } = record;
            return liveDate + pid + anchorName + itemId;
          }}
          toolbar={{
            actions: [
              <AuthButton key="SPU-export" pageUrl={PageUrl} code={btn_auths.btn_livesettlementday_distribution_export}>
                <Button onClick={() => handleExport("distribution")} size={SIZE}>
                  导出
                </Button>
              </AuthButton>
            ]
          }}
        />
      )
    }
  ].filter(item => {
    return authModel(item.key);
  });

  useLayoutEffect(() => {
    if (!curTab) {
      setCurTab(tabItems?.[0]?.key || "");
    }
  }, [tabItems, curTab]);
  return <Tabs activeKey={curTab} tabBarStyle={tabs_tabBarStyle} size={SIZE} onChange={setCurTab} items={tabItems} />;
};

const AliveRecord = () => (
  <KeepAlive name={PageUrl}>
    <DetailTable />
  </KeepAlive>
);
export default React.memo(AliveRecord);
