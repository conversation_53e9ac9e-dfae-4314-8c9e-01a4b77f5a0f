import React, { useContext } from "react";
import TableList from "@/components/TableList";
import { columns } from "./variable";
import apis from "@/services/settlement/settlementYearTotal";
import dayjs from "dayjs";
import styles from "../index.less";
import { FieldsContext } from "./../index";
const AnchorItemSaleD = () => {
  const { handlePageParmas } = useContext(FieldsContext);
  const SettlementYearProps = {};
  return (
    <>
      <TableList
        className={styles["table-width-detail"]}
        columns={columns(SettlementYearProps)}
        scroll={{ y: "calc(100vh - 440px)" }}
        form={{
          initialValues: {
            payMonth: [dayjs().startOf("year").month(0), dayjs()]
          }
        }}
        summaryApi={apis.detailPageSummary}
        api={apis.detailPage}
        downloadApi={apis.detailPageDownload}
        preFetch={handlePageParmas}
        rowKey="uuid"
      />
    </>
  );
};

export default React.memo(AnchorItemSaleD);
