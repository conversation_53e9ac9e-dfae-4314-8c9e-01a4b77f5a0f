import { formatYuanToAmount, millennialsNumber, returnEllipsisTitle, returnEllipsisTooltip } from "@/utils";
import { DatePicker } from "antd";
import dayjs from "dayjs";
// 年结算汇总-本年直播
const columns_search = ({}: any) => {
  return [
    {
      title: "支付月份",
      dataIndex: "payMonth",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return <DatePicker.RangePicker picker="month" allowClear maxDate={dayjs()} />;
      }
    }
  ];
};
export const columns = ({}: any): Array<TableListItem> => {
  return [
    ...columns_search({}),
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      hideInSearch: true,
      fixed: "left",
      render: (_, record, index) => {
        return index + 1;
      }
    },
    { title: "支付月份", dataIndex: "payMonth", valueType: "dateMonth", width: 80, hideInSearch: true },
    {
      title: "付款金额",
      dataIndex: "payAmount",
      width: 100,
      hideInSearch: true,
      summaryTooltip: {
        divisor: 100,
        hoverMillennials: true,
        millennialsOptions: { minimumFractionDigits: 2 }
      },
      render(text: any) {
        const to_yuan = text / 100 || 0;
        return returnEllipsisTitle({
          title: formatYuanToAmount(to_yuan),
          tooltip: millennialsNumber(to_yuan, { minimumFractionDigits: 2 })
        });
      }
    },
    {
      title: "结算金额",
      dataIndex: "actualSettleAmount",
      width: 100,
      hideInSearch: true,
      summaryTooltip: {
        divisor: 100,
        hoverMillennials: true,
        millennialsOptions: { minimumFractionDigits: 2 }
      },
      render(text: any) {
        const to_yuan = text / 100 || 0;
        return returnEllipsisTitle({
          title: formatYuanToAmount(to_yuan),
          tooltip: millennialsNumber(to_yuan, { minimumFractionDigits: 2 })
        });
      }
    },
    {
      title: "待结算金额",
      dataIndex: "preSettleAmount",
      width: 100,
      hideInSearch: true,
      summaryTooltip: {
        divisor: 100,
        hoverMillennials: true,
        millennialsOptions: { minimumFractionDigits: 2 }
      },
      render(text: any) {
        const to_yuan = text / 100 || 0;
        return returnEllipsisTitle({
          title: formatYuanToAmount(to_yuan),
          tooltip: millennialsNumber(to_yuan, { minimumFractionDigits: 2 })
        });
      }
    },
    {
      title: "退货退款率",
      dataIndex: "refundRateStr",
      width: 90,
      hideInSearch: true
    },
    {
      title: "已扣年框扣费",
      dataIndex: "acutalYearlyAmount",
      width: 100,
      hideInSearch: true,
      summaryTooltip: {
        divisor: 100,
        hoverMillennials: true,
        millennialsOptions: { minimumFractionDigits: 2 }
      },
      render(text: any) {
        const to_yuan = text / 100 || 0;
        return returnEllipsisTitle({
          title: formatYuanToAmount(to_yuan),
          tooltip: millennialsNumber(to_yuan, { minimumFractionDigits: 2 })
        });
      }
    }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      ...item,
      align: "center",
      title: returnEllipsisTooltip({ title })
    };
  });
};
