import React, { useContext } from "react";
import TableList from "@/components/TableList";
import { columns } from "./variable";
import apis from "@/services/settlement/settlementYearTotal";
import dayjs from "dayjs";
import styles from "../index.less";
import { FieldsContext } from "./../index";
const AnchorItemSaleD = () => {
  const { handlePageParmas } = useContext(FieldsContext);
  const SettlementYearProps = {};
  return (
    <>
      <TableList
        className={styles["table-width-total"]}
        columns={columns(SettlementYearProps)}
        scroll={{ y: "calc(100vh - 420px)" }}
        form={{
          initialValues: {
            payMonth: [dayjs().startOf("year").month(0), dayjs()]
          }
        }}
        summaryApi={apis.monthPageSummary}
        api={apis.monthPage}
        downloadApi={apis.monthPageDownload}
        preFetch={handlePageParmas}
        rowKey="uuid"
      />
    </>
  );
};

export default React.memo(AnchorItemSaleD);
