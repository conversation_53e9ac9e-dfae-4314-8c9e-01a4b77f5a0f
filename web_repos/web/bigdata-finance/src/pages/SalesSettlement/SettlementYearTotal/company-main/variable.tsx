import { filterOptionLabel, formatYuanToAmount, millennialsNumber, returnEllipsisTitle, returnEllipsisTooltip } from "@/utils";
import { DatePicker, Select, Radio } from "antd";
import { useState } from "react";
import dayjs from "dayjs";
import { SIZE } from "@/utils/constant";
import AnchorSelect from "@/components/AnchorSelect";
import CategorySelect from "@/components/CategorySelect";

export const Columns = ({ companyNameOptions }: any, groupTypeRef: any, paramsState: any): Array<TableListItem> => {
  const [groupType, setGroupType] = useState(2);
  const columns_search = [
    {
      title: (
        <Radio.Group
          style={{ minWidth: "151px" }}
          onChange={event => {
            setGroupType(event.target.value);
            groupTypeRef.current = event.target.value;
          }}
          defaultValue={groupType}
        >
          <Radio.Button value={2}>按入账月份</Radio.Button>
          <Radio.Button value={1}>按入账日期</Radio.Button>
        </Radio.Group>
      ),
      dataIndex: "settleDate",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return groupTypeRef.current === 1 ? (
          <DatePicker.RangePicker style={{ minWidth: "210px" }} allowClear maxDate={dayjs()} format="YYYY-MM-DD" />
        ) : (
          <DatePicker.RangePicker style={{ minWidth: "210px" }} picker="month" allowClear maxDate={dayjs()} format="YYYY-MM" />
        );
      }
    },
    {
      title: " ",
      dataIndex: `groupType${groupType}`,
      hideInSearch: true,
      hideInTable: true,
      renderFormItem: () => {
        return (
          <Radio.Group value={groupType}>
            <Radio.Button value={1}>入账日期</Radio.Button>
            <Radio.Button value={2}>入账月份</Radio.Button>
          </Radio.Group>
        );
      }
    },
    {
      title: "主播ID",
      dataIndex: "anchorIds",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "主播名称",
      dataIndex: "anchorNames",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem() {
        return <AnchorSelect params={{ type: 1 }} showSearch allowClear maxTagCount="responsive" mode="multiple" />;
      }
    },
    {
      title: "公司主体",
      dataIndex: "companyName",
      hideInTable: true,
      renderFormItem() {
        return <Select placeholder="请选择" options={companyNameOptions} filterOption={filterOptionLabel} showSearch allowClear />;
      }
    },
    {
      // 日区间
      title: <div style={{ minWidth: "151px" }}>直播日期</div>,
      dataIndex: "liveDate",
      hideInSearch: false,
      hideInTable: true,
      // valueType: "dateRange"
      renderFormItem: () => {
        return <DatePicker.RangePicker style={{ minWidth: "210px" }} allowClear maxDate={dayjs()} format="YYYY-MM-DD" />;
      }
    },
    {
      title: "商品ID",
      dataIndex: "itemIdList",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "商品品类",
      dataIndex: "itemCategory",
      hideInTable: true,
      renderFormItem() {
        return <CategorySelect returnFormat="name" />;
      }
    },
    {
      title: "是否黄金商品",
      dataIndex: "isGoldItem",
      hideInTable: true,
      renderFormItem: () => {
        return (
          <Select
            placeholder="请选择"
            style={{ minWidth: 120, textAlign: "left" }}
            size={SIZE}
            options={[
              { value: 1, label: "是" },
              { value: 0, label: "否" }
            ]}
            filterOption={filterOptionLabel}
            allowClear
            maxTagCount="responsive"
          />
        );
      }
    }
  ];
  return [
    ...columns_search,
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      hideInSearch: true,
      fixed: "left",
      render: (_, record, index) => {
        return index + 1;
      }
    },
    {
      title: paramsState?.groupType === 1 ? "入账日期" : "入账月份",
      dataIndex: "settlePeriod",
      valueType: paramsState?.groupType === 1 ? "date" : "dateMonth",
      width: 80,
      hideInSearch: true
    },
    {
      title: "主播ID",
      dataIndex: "anchorId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "主播名称",
      dataIndex: "anchorName",
      width: 80,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "直播日期", dataIndex: "liveDate", valueType: "date", width: 100, hideInSearch: true },
    {
      title: "店铺ID",
      dataIndex: "shopId",
      width: 140,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "店铺名称",
      dataIndex: "shopName",
      width: 160,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品ID",
      dataIndex: "itemId",
      width: 140,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品名称",
      dataIndex: "itemTitle",
      width: 200,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "已扣年框扣费",
      dataIndex: "actualYearlyAmount",
      width: 100,
      hideInSearch: true,
      summaryTooltip: {
        divisor: 100,
        hoverMillennials: true,
        millennialsOptions: { minimumFractionDigits: 2 }
      },
      render(text: any) {
        const to_yuan = text / 100 || 0;
        return returnEllipsisTitle({
          title: formatYuanToAmount(to_yuan),
          tooltip: millennialsNumber(to_yuan, { minimumFractionDigits: 2 })
        });
      }
    },
    {
      title: "结算金额",
      dataIndex: "settleAmount",
      width: 100,
      hideInSearch: true,
      summaryTooltip: {
        divisor: 100,
        hoverMillennials: true,
        millennialsOptions: { minimumFractionDigits: 2 }
      },
      render(text) {
        const to_yuan = text / 100 || 0;
        return returnEllipsisTitle({
          title: formatYuanToAmount(to_yuan),
          tooltip: millennialsNumber(to_yuan, { minimumFractionDigits: 2 })
        });
      }
    },
    {
      title: "公司主体",
      dataIndex: "companyName",
      width: 200,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品一级类目",
      dataIndex: "catLv1",
      width: 200,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品二级类目",
      dataIndex: "catLv2",
      width: 200,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品三级类目",
      dataIndex: "catLv3",
      width: 200,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品四级类目",
      dataIndex: "catLv4",
      width: 200,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品五级类目",
      dataIndex: "catLv5",
      width: 200,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "是否黄金商",
      dataIndex: "isGoldItem",
      hideInSearch: true,
      width: 100,
      render(text) {
        return returnEllipsisTooltip({ title: text === 0 ? "否" : "是" });
      }
    }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      ...item,
      align: "center",
      title: returnEllipsisTooltip({ title })
    };
  });
};
