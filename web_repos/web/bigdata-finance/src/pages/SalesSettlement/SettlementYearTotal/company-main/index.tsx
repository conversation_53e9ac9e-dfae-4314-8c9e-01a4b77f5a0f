import React, { useEffect, useState, useRef } from "react";
import TableList from "@/components/TableList";
import { Columns } from "./variable";
import apis from "@/services/settlement/settlementYearTotal";
import apis2 from "@/services/common";
import dayjs from "dayjs";
import styles from "../index.less";
import { returnOptions, splitSpaceComma } from "@/utils";
const AnchorItemSaleD = () => {
  const [companyNameOptions, setCompanyNameOptions] = useState<IOptions[]>([]);
  const SettlementYearProps = { companyNameOptions };
  const tableListRef = useRef<any>();
  const groupTypeRef = useRef<any>(2);
  const [paramsState, setParamsState] = useState<any>();
  const fetchCompanyName = async (params = {}) => {
    const { entry, status } = await apis2.getCompanyNameList(params);
    if (status) {
      setCompanyNameOptions(returnOptions(entry));
    } else {
      setCompanyNameOptions([]);
    }
  };
  const handlePreFect = (params: any): any => {
    const { current, itemCategory, settleDate, payMonth, liveDate, anchorIds, itemIdList, ...rest } = params;
    let groupTypeIndex = 2;
    tableListRef?.current?.rest.columns.forEach((item: any, index: number) => {
      if (item.dataIndex.includes("groupType")) {
        groupTypeIndex = index;
      }
    });
    rest.groupType = Number(tableListRef?.current?.rest.columns[groupTypeIndex].dataIndex.split("groupType")[1]);
    if (itemCategory) {
      Object.keys(itemCategory).forEach((item: string) => {
        switch (item) {
          case "stdCatLv1IdList":
            rest.catLv1List = itemCategory[item];
            break;
          case "stdCatLv2IdList":
            rest.catLv2List = itemCategory[item];
            break;
          case "stdCatLv3IdList":
            rest.catLv3List = itemCategory[item];
            break;
          case "stdCatLv4IdList":
            rest.catLv4List = itemCategory[item];
            break;
          case "stdCatLv5IdList":
            rest.catLv5List = itemCategory[item];
            break;
        }
      });
    }
    if (payMonth) {
      const [startPayMonth, endPayMonth] = payMonth;
      rest.startPayMonth = dayjs(startPayMonth).format("YYYYMM");
      rest.endPayMonth = dayjs(endPayMonth).format("YYYYMM");
    }
    if (liveDate) {
      const [liveDateStart, liveDateEnd] = liveDate;
      rest.liveDateStart = dayjs(liveDateStart).format("YYYYMMDD");
      rest.liveDateEnd = dayjs(liveDateEnd).format("YYYYMMDD");
    }
    if (settleDate) {
      const [settleDateStart, settleDateEnd] = settleDate;
      if (rest.groupType === 1) {
        rest.settleDateStart = dayjs(settleDateStart).format("YYYYMMDD");
        rest.settleDateEnd = dayjs(settleDateEnd).format("YYYYMMDD");
      } else {
        rest.settleDateStart = dayjs(settleDateStart).startOf("month").format("YYYYMMDD");
        rest.settleDateEnd = dayjs(settleDateEnd).endOf("month").format("YYYYMMDD");
      }
    }
    rest.anchorIds = splitSpaceComma(anchorIds);
    rest.itemIdList = splitSpaceComma(itemIdList);
    const paramsNew = { ...rest, groupType: groupTypeRef.current, pageNo: current };
    setParamsState(paramsNew);
    return paramsNew;
  };

  useEffect(() => {
    fetchCompanyName();
  }, []);

  return (
    <>
      <TableList
        className={styles["table-width-company"]}
        columns={Columns(SettlementYearProps, groupTypeRef, paramsState)}
        tableListRef={tableListRef}
        scroll={{ y: "calc(100vh - 440px)" }}
        form={{
          initialValues: {
            settleDate: [dayjs().startOf("year").month(0), dayjs()]
          }
        }}
        summaryApi={apis.companyPageSummary}
        api={apis.companyPage}
        downloadApi={apis.companyPageDownload}
        preFetch={handlePreFect}
        rowKey="uuid"
      />
    </>
  );
};

export default React.memo(AnchorItemSaleD);
