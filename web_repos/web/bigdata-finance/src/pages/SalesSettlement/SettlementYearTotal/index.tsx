import React, { useLayoutEffect, useState } from "react";
import { Tabs } from "antd";
import KeepAlive from "react-activation";
import { SIZE, tabs_tabBarStyle } from "@/utils/constant";
import AnchorSettlementDetail from "./anchor-settlement-detail";
import MonthSettlementTotal from "./month-settlement-total";
import CompanyMain from "./company-main";
import dayjs from "dayjs";
import { splitSpaceComma } from "@/utils";
import { model_auths } from "./auths";
import { useModelAuth } from "@/wrappers/authButton";
export const PageUrl = "/salesSettlement/settlementYearTotal";

export const FieldsContext = React.createContext<any>(null);
export const tab_month_total = model_auths.model_settlementYearTotal_month_tab;
export const tab_anchor_detail = model_auths.model_settlementYearTotal_anchor_tab;
export const tab_company_main = model_auths.model_settlementYearTotal_company_tab;
const AnchorItemSale = () => {
  const authModel = useModelAuth({ pageUrl: PageUrl });
  const [curTab, setCurTab] = useState("");
  const handlePageParmas = (params: any) => {
    const { current, payMonth, liveDate, anchorIds, itemIdList, ...rest } = params;
    if (payMonth) {
      const [startPayMonth, endPayMonth] = payMonth;
      rest.startPayMonth = dayjs(startPayMonth).format("YYYYMM");
      rest.endPayMonth = dayjs(endPayMonth).format("YYYYMM");
    }
    if (liveDate) {
      const [liveDateStart, liveDateEnd] = liveDate;
      rest.liveDateStart = dayjs(liveDateStart).format("YYYYMMDD");
      rest.liveDateEnd = dayjs(liveDateEnd).format("YYYYMMDD");
    }
    rest.anchorIds = splitSpaceComma(anchorIds);
    rest.itemIdList = splitSpaceComma(itemIdList);

    return { ...rest, pageNo: current };
  };
  const tabItems = [
    {
      key: tab_month_total,
      label: "月度年框汇总",
      children: <MonthSettlementTotal />
    },
    {
      key: tab_anchor_detail,
      label: "主播年框明细",
      children: <AnchorSettlementDetail />
    },
    {
      key: tab_company_main,
      label: "公司主体年框",
      children: <CompanyMain />
    }
  ].filter(item => {
    return authModel(item.key);
  });
  useLayoutEffect(() => {
    if (!curTab) {
      setCurTab(tabItems?.[0]?.key || "");
    }
  }, [tabItems, curTab]);
  return (
    <FieldsContext.Provider value={{ handlePageParmas }}>
      <Tabs activeKey={curTab} tabBarStyle={tabs_tabBarStyle} size={SIZE} onChange={setCurTab} items={tabItems} />
    </FieldsContext.Provider>
  );
};

const AliveAnchorItemSale = props => (
  <KeepAlive name={PageUrl}>
    <AnchorItemSale {...props} />
  </KeepAlive>
);

export default React.memo(AliveAnchorItemSale);
