import React, { useRef, useState, useEffect } from "react";
import { Button } from "antd";
import { columns, advanceColumns } from "./variable";
import { NOVALUE, Protable_FROM_CONFIG, SIZE } from "@/utils/constant";
import KeepAlive from "react-activation";
import { pushExportHistory, splitSpaceComma } from "@/utils";
import apis from "@/services/supplierInvoice/invoiceDetails";
import dayjs from "dayjs";
import apis2 from "@/services/settlement/billStatisticMonth";
import TableList from "@/components/TableList";
import useCommonOptions from "@/hooks/useCommonOptions";
const PageUrl = "/supplierInvoice/invoiceDetails";
const Res: React.FC = () => {
  const [settleModelOptions] = useCommonOptions({ dimName: "结算模式" });
  const proTableFormRef = useRef<any>();
  const actionRef = useRef<any>();
  const [pageParmas, setPageParmas] = useState<any>({});
  const [CompanyNameList, setCompanyNameList] = useState([]);
  const advanceColumnsProps: any = { settleModelOptions };

  const columnsProps = {
    CompanyNameList
  };
  const fetchCompanyName = async () => {
    const { entry, status } = await apis2.getCpsAnxinCompanyNameList();
    if (status) {
      setCompanyNameList(
        (entry || []).map((item: string) => {
          return {
            label: item,
            value: item
          };
        })
      );
    }
  };
  useEffect(() => {
    fetchCompanyName();
  }, []);
  const handleExport = async () => {
    const { status } = await apis.downloadCpsSettleMonthDetail(pageParmas);
    if (status) {
      pushExportHistory();
    }
  };
  // 处理请求参数
  const handlePageParmas = (params: any) => {
    const { current, settleMonth, liveDate, itemIdList, shopNameList, ...rest } = params;
    if (settleMonth) {
      const [settleMonthStart, settleMonthEnd] = settleMonth;
      rest.settleMonthStart = dayjs(settleMonthStart).format("YYYYMM");
      rest.settleMonthEnd = dayjs(settleMonthEnd).format("YYYYMM");
    }
    if (liveDate) {
      const [liveDateStart, liveDateEnd] = liveDate;
      rest.liveDateStart = dayjs(liveDateStart).format("YYYYMMDD");
      rest.liveDateEnd = dayjs(liveDateEnd).format("YYYYMMDD");
    }

    rest.itemIdList = splitSpaceComma(itemIdList);
    rest.shopNameList = splitSpaceComma(shopNameList);
    return { ...rest, pageNo: current };
  };

  return (
    <div>
      <TableList
        advanceColumns={advanceColumns(advanceColumnsProps)}
        formRef={proTableFormRef}
        actionRef={actionRef}
        columnEmptyText={NOVALUE}
        columns={columns(columnsProps)}
        scroll={{ y: "calc(100vh - 470px)" }}
        form={{
          ...Protable_FROM_CONFIG,
          initialValues: {}
        }}
        summaryApi={apis.getCpsSettleMonthDetailPageTotal}
        api={apis.getCpsSettleMonthDetailPage}
        preFetch={handlePageParmas}
        paramsChange={setPageParmas}
        toolbar={{
          actions: [
            <>
              <Button onClick={handleExport} size={SIZE}>
                导出
              </Button>
            </>
          ]
        }}
        rowKey={record => {
          const { settleMonth, anchorId, anchorName, liveDate, itemId, settleType } = record;
          return settleMonth + anchorId + anchorName + liveDate + itemId + settleType;
        }}
      />
    </div>
  );
};

const AliveRecord = () => (
  <KeepAlive name={PageUrl}>
    <Res />
  </KeepAlive>
);
export default React.memo(AliveRecord);
