import { Select, DatePicker } from "antd";
import { filterOptionLabel, returnEllipsisTooltip } from "@/utils/index";
import { NOVALUE, SIZE } from "@/utils/constant";
import AnchorSelect from "@/components/AnchorSelect";
import SupplierNameSelect from "@/components/SupplierNameSelect";
export const advanceColumns = ({ settleModelOptions }: any) => {
  return [
    {
      title: "基础信息",
      valueType: "group",
      colProps: { span: 24 },
      columns: [
        {
          title: <span>结算模式</span>,
          dataIndex: "settleModelList",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem: () => {
            return (
              <Select
                placeholder="请选择"
                size={SIZE}
                options={settleModelOptions}
                filterOption={filterOptionLabel}
                showSearch
                allowClear
                maxTagCount="responsive"
                mode="multiple"
              />
            );
          }
        },
        {
          title: <span>店铺名称</span>,
          dataIndex: "shopNameList",
          hideInSearch: false,
          hideInTable: true,
          fieldProps: {
            placeholder: "多个使用英文逗号/空格"
          }
        },
        {
          title: <span>供应商名称</span>,
          dataIndex: "supplierName",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem() {
            return <SupplierNameSelect />;
          }
        },
        {
          title: <span>我司主体</span>,
          dataIndex: "contractCompanyName",
          hideInSearch: false,
          hideInTable: true
        }
      ]
    }
  ];
};
const columns_search = ({ CompanyNameList }: any): Array<TableListItem> => {
  return [
    {
      title: "结算月份",
      dataIndex: "settleMonth",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return <DatePicker.RangePicker picker="month" allowClear />;
      }
    },
    {
      title: "直播日期",
      dataIndex: "liveDate",
      hideInSearch: false,
      hideInTable: true,
      valueType: "dateRange"
    },
    {
      title: "主播",
      dataIndex: "anchorNameList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem() {
        return <AnchorSelect showSearch allowClear maxTagCount="responsive" mode="multiple" />;
      }
    },
    {
      title: "商品ID",
      dataIndex: "itemIdList",
      hideInSearch: false,
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "商品名称",
      dataIndex: "itemTitle",
      hideInSearch: false,
      hideInTable: true
    },
    {
      title: "安心钱包主体名称",
      dataIndex: "cpsAnxinCompanyNameList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return (
          <Select
            placeholder="请选择"
            size={SIZE}
            options={CompanyNameList}
            filterOption={filterOptionLabel}
            showSearch
            allowClear
            maxTagCount="responsive"
            mode="multiple"
          />
        );
      }
    }
  ];
};
export const columns = ({ CompanyNameList }: any): Array<TableListItem> => {
  return [
    ...columns_search({ CompanyNameList }),
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      editable: false,
      hideInSearch: true,
      fixed: "left",
      render: (_, record, index) => {
        return index + 1;
      }
    },
    { title: "结算月份", dataIndex: "settleMonth", width: 100, hideInSearch: true },
    { title: "主播ID", dataIndex: "anchorId", width: 100, hideInSearch: true },
    {
      title: "主播名称",
      dataIndex: "anchorName",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "安心钱包主体名称",
      dataIndex: "cpsAnxinCompanyName",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "直播日期", dataIndex: "liveDate", width: 100, hideInSearch: true },
    {
      title: "商品信息",
      editable: false,
      dataIndex: "itemInfo",
      width: 300,
      hideInSearch: true,
      render(_, record) {
        return (
          <div className="columnItemInfo">
            <div className="info">
              <div className="id">
                <span>{returnEllipsisTooltip({ title: "ID:" + (record?.itemId || NOVALUE) })}</span>
              </div>
              <div>{returnEllipsisTooltip({ title: record?.itemTitle, rows: 1, width: 250 })}</div>
            </div>
          </div>
        );
      }
    },
    {
      title: "结算品类",
      dataIndex: "settleCategory",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "结算模式",
      dataIndex: "settleModel",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "一级模式",
      dataIndex: "modelNameLv1",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "二级模式",
      dataIndex: "modelNameLv2",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "店铺ID", dataIndex: "shopId", width: 100, hideInSearch: true },
    {
      title: "店铺名称",
      dataIndex: "shopName",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "店铺性质描述",
      dataIndex: "shopTypeDesc",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "一级类目",
      dataIndex: "catLv1Name",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "二级类目",
      dataIndex: "catLv2Name",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "三级类目",
      dataIndex: "catLv3Name",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "四级类目",
      dataIndex: "catLv4Name",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "五级类目",
      dataIndex: "catLv5Name",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "供应商名称",
      dataIndex: "supplierName",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "我司主体",
      dataIndex: "contractCompanyName",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "大部门",
      dataIndex: "bigDepartment",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "一级部门",
      dataIndex: "deptNameLv1",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "二级部门",
      dataIndex: "deptNameLv2",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "三级部门",
      dataIndex: "deptNameLv3",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "部门小组",
      dataIndex: "deptGroup",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "负责人",
      dataIndex: "purchasePrincipalName",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "订单渠道描述",
      dataIndex: "orderChannelDesc",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "结算类型描述",
      dataIndex: "settleTypeDesc",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "总付款金额", dataIndex: "totalAmountStr", width: 100, hideInSearch: true },
    { title: "结算金额", dataIndex: "settleAmountStr", width: 100, hideInSearch: true },
    { title: "主播补贴金额", dataIndex: "anchorAllowAmountStr", width: 100, hideInSearch: true },
    { title: "主播佣金金额", dataIndex: "settleCommissionAmountStr", width: 100, hideInSearch: true },
    { title: "安心钱包佣金金额", dataIndex: "anxinCommissionAmountStr", width: 100, hideInSearch: true },
    { title: "聚力钱包佣金金额", dataIndex: "juliCommissionAmountStr", width: 100, hideInSearch: true },
    { title: "微信佣金金额", dataIndex: "wechatCommissionAmountStr", width: 100, hideInSearch: true },
    { title: "支付宝佣金金额", dataIndex: "alipayCommissionAmountStr", width: 100, hideInSearch: true },
    { title: "安心钱包佣金金额返回", dataIndex: "backAnxinCommissionAmountStr", width: 100, hideInSearch: true },
    { title: "聚力钱包佣金金额返回", dataIndex: "backJuliCommissionAmountStr", width: 100, hideInSearch: true }
  ].map(item => {
    const { title } = item;
    return {
      ...item,
      align: "center",
      title: returnEllipsisTooltip({ title })
    };
  });
};
