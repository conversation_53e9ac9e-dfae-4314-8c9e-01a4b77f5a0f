import React, { useMemo, useState, useContext, useEffect } from "react";
import { Card, Select, Spin, Row, Col } from "antd";
import { EchartType } from "@/components/EchartsCard";
import { SIZE } from "@/utils/constant";
import { targetOptions, targetKeys } from "../../variable";
import apis from "@/services/AnchorOverview/overview";
import { anchorRank_columns } from "./variable";
import { FieldsContext } from "@/pages/AnchorOverview/Overview/index";
import { isEqual } from "lodash-es";
import { formatYuanToAmount, returnSortParams } from "@/utils";
const height = 300;
const Res: React.FC = () => {
  const { params } = useContext(FieldsContext);
  const [oldParams, setOldParams] = useState({});
  const [pageParams, setPageParams] = useState({ trendType: targetKeys.live_income });
  // 图表loading
  const [loading, setLoading] = useState(false);
  // 图表数据
  const [chartData, setChartData] = useState([]);
  const [tableParams, setTableParams] = useState({});
  // 榜单数据
  const fetchData = async params => {
    setLoading(true);
    const { entry, status } = await apis.monthlyTrends(params);
    if (status) {
      setChartData(
        entry.map(item => {
          const { gmv, gmvStr, settleMonth } = item;
          return {
            label: settleMonth,
            value: gmv / 100,
            labelValue: gmvStr
          };
        })
      );
    } else {
      setChartData([]);
    }
    setLoading(false);
  };

  const onSelectChange = e => {
    setPageParams({ ...pageParams, trendType: e });
  };
  const handleOption = options => {
    options.grid = {
      left: "12%",
      right: "10%",
      bottom: "36px",
      top: "10%",
      containLabel: false
    };

    options.xAxis = { ...options.xAxis, name: "月份" };
    options.yAxis = {
      ...options.yAxis,
      name: "金额",
      axisLabel: {
        ...options.yAxis.axisLabel,
        formatter: function (value) {
          return formatYuanToAmount(value, 2);
        }
      }
    };
    return options;
  };
  const handleParams = (p, sort) => {
    const { current, ...rest } = p;
    const sortParams = returnSortParams(sort);
    return {
      pageNo: current,
      ...rest,
      ...sortParams
    };
  };

  const extraNode = useMemo(() => {
    return <Select style={{ width: 140 }} value={pageParams.trendType} size={SIZE} options={targetOptions} onChange={onSelectChange}></Select>;
  }, [targetOptions, pageParams.trendType]);
  useEffect(() => {
    // 参数变化时 月度结算趋势 trendType字段需要切换到 默认值
    delete params.$_fetch;
    let _isEqual = isEqual(params, oldParams);
    if (!_isEqual) {
      setPageParams({ ...pageParams, trendType: targetKeys.live_income });
    }
    setOldParams(params);
  }, [params]);
  useEffect(() => {
    fetchData({ ...params, ...pageParams });
    setTableParams({ ...params });
  }, [params, pageParams]);
  return (
    <Row gutter={10}>
      <Col span={12}>
        <Card title="月度结算趋势" extra={extraNode}>
          <Spin spinning={loading}>
            <EchartType height={height} optionAfter={handleOption} data={chartData} type="bar" />
          </Spin>
        </Card>
      </Col>

      <Col span={12}>
        <Card title="主播榜">
          <EchartType
            type="table"
            height={height}
            tableProps={{
              rowKey: "uuid",
              params: tableParams,
              columns: anchorRank_columns({}),
              api: apis.ranks,
              preFetch: handleParams
            }}
          />
        </Card>
      </Col>
    </Row>
  );
};

export default React.memo(Res);
