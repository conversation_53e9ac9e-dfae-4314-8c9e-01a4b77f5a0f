import EditInputNumber from "@/components/EditInputNumber";
import { returnEllipsisTooltip, returnValueForNoValue } from "@/utils";
import { SIZE } from "@/utils/constant";
import { message } from "antd";

export const columns = ({}: any): TableListItem[] => {
  return [
    {
      title: "直播明细",
      children: [
        {
          title: "结算月份",
          fixed: "left",
          editable: false,
          valueType: "dateMonth",
          dataIndex: "settleMonth",
          width: 80
        },
        {
          title: "主播",
          fixed: "left",
          editable: false,
          dataIndex: "anchorName",
          width: 80
        },
        {
          title: "PID",
          fixed: "left",
          editable: false,
          dataIndex: "anchorId",
          width: 80
        },
        {
          title: "直播场次",
          fixed: "left",
          editable: false,
          dataIndex: "liveCnt",
          width: 80
        },
        {
          title: "目前粉丝数",
          fixed: "left",
          editable: false,
          dataIndex: "fansCnt",
          width: 100
        },
        {
          title: "GMV战报金额",
          fixed: "left",
          editable: false,
          dataIndex: "liveGmvStr",
          width: 120
        },
        {
          title: "结算金额",
          fixed: "left",
          editable: false,
          dataIndex: "liveSettleAmountStr",
          width: 80
        }
      ]
    },
    {
      title: "卖货收入-总",
      className: "table-cell-ff7875",
      children: [
        {
          title: "卖货收入",
          className: "table-cell-ff7875",
          editable: false,
          dataIndex: "liveAmountStr",
          width: 80
        },
        {
          title: "主播利润",
          className: "table-cell-ff7875",
          editable: false,
          dataIndex: "anchorLiveAmountStr",
          width: 80
        },
        {
          title: "公司利润",
          className: "table-cell-ff7875",
          editable: false,
          dataIndex: "companyLiveAmountStr",
          width: 80
        }
      ]
    },
    {
      title: "链接费收入",
      className: "table-cell-fff2e8",
      children: [
        {
          title: "主播收入",
          className: "table-cell-fff2e8",
          editable: false,
          dataIndex: "anchorLinkfeeAmountStr",
          width: 80
        },
        {
          title: "公司收入",
          className: "table-cell-fff2e8",
          editable: false,
          dataIndex: "companyLinkfeeAmountStr",
          width: 80
        }
      ]
    },
    {
      title: "辛选收入合计",
      className: "table-cell-ffd8bf",
      children: [
        {
          title: "主播收入",
          className: "table-cell-ffd8bf",
          editable: false,
          dataIndex: "anchorXinxuanTotalAmountStr",
          width: 80
        },
        {
          title: "公司收入",
          className: "table-cell-ffd8bf",
          editable: false,
          dataIndex: "companyXinxuanTotalAmountStr",
          width: 80
        }
      ]
    },
    {
      title: "辛选费用支出",
      className: "table-cell-fff7e6",
      children: [
        {
          title: "主播费用",
          className: "table-cell-fff7e6",
          editable: false,
          dataIndex: "anchorXinxuanFeeStr",
          width: 80
        },
        {
          title: "公司费用",
          className: "table-cell-fff7e6",
          editable: false,
          dataIndex: "companyXinxuanFeeStr",
          width: 80
        }
      ]
    },
    {
      title: "辛选费用合计",
      className: "table-cell-ffe7ba",
      children: [
        {
          title: "主播费用",
          className: "table-cell-ffe7ba",
          editable: false,
          dataIndex: "anchorXinxuanTotalFeeStr",
          width: 80
        },
        {
          title: "公司费用",
          className: "table-cell-ffe7ba",
          editable: false,
          dataIndex: "companyXinxuanTotalFeeStr",
          width: 80
        }
      ]
    },
    {
      title: "其他业务收入（推广收入）",
      className: "table-cell-fffbe6",
      children: [
        {
          title: "主播收入",
          className: "table-cell-fffbe6",
          editable: false,
          dataIndex: "anchorOtherAmountStr",
          width: 100
        },
        {
          title: "公司收入",
          className: "table-cell-fffbe6",
          editable: false,
          dataIndex: "companyOtherAmountStr",
          width: 100
        }
      ]
    },
    {
      title: "点关注返款（刷礼物发红包）",
      className: "table-cell-fff1b8",
      children: [
        {
          title: "主播收入",
          className: "table-cell-fff1b8",
          editable: false,
          dataIndex: "anchorBackAmountStr",
          width: 100
        },
        {
          title: "公司收入",
          className: "table-cell-fff1b8",
          editable: false,
          dataIndex: "companyBackAmountStr",
          width: 100
        }
      ]
    },
    {
      title: "对公打赏收入",
      className: "table-cell-feffe6",
      children: [
        {
          title: "主播收入",
          className: "table-cell-feffe6",
          editable: false,
          dataIndex: "anchorRewardAmountStr",
          width: 80
        },
        {
          title: "公司收入",
          className: "table-cell-feffe6",
          editable: false,
          dataIndex: "companyRewardAmountStr",
          width: 80
        }
      ]
    },
    {
      title: "巴伽收入合计",
      className: "table-cell-ffffb8",
      children: [
        {
          title: "主播收入",
          className: "table-cell-ffffb8",
          editable: false,
          dataIndex: "anchorBajiaTotalAmountStr",
          width: 80
        },
        {
          title: "公司收入",
          className: "table-cell-ffffb8",
          editable: false,
          dataIndex: "companyBajiaTotalAmountStr",
          width: 80
        }
      ]
    },
    {
      title: "费用1（快币充值）",
      className: "table-cell-fcffe6",
      children: [
        {
          title: "主播费用",
          className: "table-cell-fcffe6",
          editable: false,
          dataIndex: "anchorRechargeFee1Str",
          width: 80
        },
        {
          title: "公司费用",
          className: "table-cell-fcffe6",
          editable: false,
          dataIndex: "companyRechargeFee1Str",
          width: 80
        }
      ]
    },
    {
      title: "费用2（快币消耗锦鲤推广）",
      className: "table-cell-f4ffb8",
      children: [
        {
          title: "主播费用",
          className: "table-cell-f4ffb8",
          editable: false,
          dataIndex: "anchorPromotionFee2Str",
          width: 100
        },
        {
          title: "公司费用",
          className: "table-cell-f4ffb8",
          editable: false,
          dataIndex: "companyPromotionFee2Str",
          width: 100
        }
      ]
    },
    {
      title: "费用3(推广支出2)",
      className: "table-cell-f6ffed",
      children: [
        {
          title: "主播费用",
          className: "table-cell-f6ffed",
          editable: false,
          dataIndex: "anchorPromotionFee3Str",
          width: 80
        },
        {
          title: "公司费用",
          className: "table-cell-f6ffed",
          editable: false,
          dataIndex: "companyPromotionFee3Str",
          width: 80
        }
      ]
    },
    {
      title: "费用4(福利费)",
      className: "table-cell-d9f7be",
      children: [
        {
          title: "主播费用",
          className: "table-cell-d9f7be",
          editable: false,
          dataIndex: "anchorGiftFee4Str",
          width: 80
        },
        {
          title: "公司费用",
          className: "table-cell-d9f7be",
          editable: false,
          dataIndex: "companyGiftFee4Str",
          width: 80
        }
      ]
    },
    {
      title: "费用5(其他支出)",
      className: "table-cell-e6fffb",
      children: [
        {
          title: "主播费用",
          className: "table-cell-e6fffb",
          editable: false,
          dataIndex: "anchorOtherFee5Str",
          width: 80
        },
        {
          title: "公司费用",
          className: "table-cell-e6fffb",
          editable: false,
          dataIndex: "companyOtherFee5Str",
          width: 80
        }
      ]
    },
    {
      title: "费用6（快币充值-点关注）",
      className: "table-cell-b5f5ec",
      children: [
        {
          title: "主播费用",
          className: "table-cell-b5f5ec",
          editable: false,
          dataIndex: "anchorRechargeFee6Str",
          width: 100
        },
        {
          title: "公司费用",
          className: "table-cell-b5f5ec",
          editable: false,
          dataIndex: "companyRechargeFee6Str",
          width: 100
        }
      ]
    },
    {
      title: "费用7（搭建费）",
      className: "table-cell-e6f4ff",
      children: [
        {
          title: "主播费用",
          className: "table-cell-e6f4ff",
          editable: false,
          dataIndex: "anchorBuildFee7Str",
          width: 80
        },
        {
          title: "公司费用",
          className: "table-cell-e6f4ff",
          editable: false,
          dataIndex: "companyBuildFee7Str",
          width: 80
        }
      ]
    },
    {
      title: "费用8（大型活动差旅费）",
      className: "table-cell-bae0ff",
      children: [
        {
          title: "主播费用",
          className: "table-cell-bae0ff",
          editable: false,
          dataIndex: "anchorTicketFee8Str",
          width: 100
        },
        {
          title: "公司费用",
          className: "table-cell-bae0ff",
          editable: false,
          dataIndex: "companyTicketFee8Str",
          width: 100
        }
      ]
    },
    {
      title: "巴伽费用合计",
      className: "table-cell-f0f5ff",
      children: [
        {
          title: "主播费用",
          className: "table-cell-f0f5ff",
          editable: false,
          dataIndex: "anchorBajiaTotalFeeStr",
          width: 80
        },
        {
          title: "公司费用",
          className: "table-cell-f0f5ff",
          editable: false,
          dataIndex: "companyBajiaTotalFeeStr",
          width: 80
        }
      ]
    },
    {
      title: "应结算金额",
      className: "table-cell-d6e4ff",
      children: [
        {
          title: "主播可结算金额",
          className: "table-cell-d6e4ff",
          editable: false,
          dataIndex: "anchorSettleAmountStr",
          width: 80
        },
        {
          title: "公司可结算金额",
          className: "table-cell-d6e4ff",
          editable: false,
          dataIndex: "companySettleAmountStr",
          width: 80
        }
      ]
    },
    {
      title: "预结算款",
      className: "table-cell-f9f0ff",
      children: [
        {
          title: "主播已结算金额",
          className: "table-cell-f9f0ff",
          editable: false,
          dataIndex: "anchorPromiseSettleAmountStr",
          width: 80
        },
        {
          title: "代扣代缴税费",
          className: "table-cell-f9f0ff",
          editable: false,
          dataIndex: "anchorTaxAmountStr",
          width: 80
        }
      ]
    },
    {
      title: "个人（借支）",
      className: "table-cell-efdbff",
      editable: false,
      dataIndex: "anchorLoanAmountStr",
      width: 100
    },
    {
      title: "可结算金额",
      className: "table-cell-fff0f6",
      children: [
        {
          title: "主播可结算金额",
          className: "table-cell-fff0f6",
          editable: false,
          dataIndex: "anchorIncomeAmountStr",
          width: 80
        },
        {
          title: "公司可结算金额",
          className: "table-cell-fff0f6",
          editable: false,
          dataIndex: "companyIncomeAmountStr",
          width: 80
        }
      ]
    },
    {
      title: "年框扣费",
      className: "table-cell-ffd6e7",
      children: [
        {
          title: "主播金额",
          className: "table-cell-ffd6e7",
          editable: false,
          dataIndex: "anchorYearlyAmountStr",
          width: 80
        },
        {
          title: "公司金额",
          className: "table-cell-ffd6e7",
          editable: false,
          dataIndex: "companyYearlyAmountStr",
          width: 80
        }
      ]
    },
    {
      title: "其他",
      className: "table-cell-ffadd2",
      children: [
        {
          title: "公司可调整金额（主播）",
          className: "table-cell-ffadd2",
          editable: true,
          dataIndex: "anchorAdjustAmount",
          width: 120,
          render(_, record) {
            const { anchorAdjustAmountStr } = record;
            return returnValueForNoValue(anchorAdjustAmountStr);
          },
          renderFormItem: (_, { isEditable }) => {
            return isEditable && <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
          }
        },
        {
          title: "公司可调整金额（公司）",
          className: "table-cell-ffadd2",
          editable: true,
          dataIndex: "companyAdjustAmount",
          width: 120,
          render(_, record) {
            const { companyAdjustAmountStr } = record;
            return returnValueForNoValue(companyAdjustAmountStr);
          },
          renderFormItem: (_, { isEditable }) => {
            return isEditable && <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
          }
        },
        {
          title: "备注",
          className: "table-cell-ffadd2",
          editable: true,
          dataIndex: "remark",
          width: 120,
          fieldProps: { size: SIZE }
        }
      ]
    },
    {
      title: "操作",
      width: 80,
      valueType: "option",
      fixed: "right",
      showInGroup: false,
      render(_, record: Recordable<Record<string, any>>, index: number, action: any) {
        const { uuid } = record;
        return (
          <a
            onClick={() => {
              if (uuid) {
                action?.startEditable(uuid);
              } else {
                message.info("uuid 字段的没有可读可写权限，无法编辑！！！");
              }
            }}
          >
            编辑
          </a>
        );
      }
    }
  ].map(item => {
    const { children = [] } = item;
    return {
      align: "center",
      ...item,
      children: children?.map(item => {
        const { title } = item;
        return {
          align: "center",
          ...item,
          title: returnEllipsisTooltip({ title })
        };
      })
    };
  });
};
