import { returnEllipsisTooltip } from "@/utils";

export const columns = ({}: any): TableListItem[] => {
  return [
    {
      title: "直播明细",
      children: [
        {
          title: "主播",
          fixed: "left",
          dataIndex: "anchorName",
          width: 100
        },
        {
          title: "结算月份",
          fixed: "left",
          dataIndex: "settleMonth",
          valueType: "dateMonth",
          width: 100
        },
        {
          title: "直播场次",
          fixed: "left",
          dataIndex: "liveCnt",
          width: 100
        },
        {
          title: "GMV战报金额",
          fixed: "left",
          dataIndex: "liveGmvStr",
          width: 120
        },
        {
          title: "结算金额",
          fixed: "left",
          dataIndex: "liveSettleAmountStr",
          width: 100
        }
      ]
    },
    {
      title: "卖货收入",
      className: "table-cell-ff7875",
      children: [
        {
          title: "主播结算金额",
          className: "table-cell-ff7875",
          dataIndex: "anchorIncomeAmountStr",
          width: 120
        },
        {
          title: "公司结算金额",
          className: "table-cell-ff7875",
          dataIndex: "companyIncomeAmountStr",
          width: 120
        }
      ]
    },
    {
      title: "年框扣费金额",
      className: "table-cell-fff2e8",
      children: [
        {
          title: "主播承担金额",
          className: "table-cell-fff2e8",
          dataIndex: "anchorYearlyAmountStr",
          width: 120
        },
        {
          title: "公司承担金额",
          className: "table-cell-fff2e8",
          dataIndex: "companyYearlyAmountStr",
          width: 120
        }
      ]
    },
    {
      title: "应结算利润",
      className: "table-cell-ffd8bf",
      children: [
        {
          title: "主播应结算金额",
          className: "table-cell-ffd8bf",
          dataIndex: "anchorProfitAmountStr",
          width: 120
        },
        {
          title: "公司应结算金额",
          className: "table-cell-ffd8bf",
          dataIndex: "companyProfitAmountStr",
          width: 120
        }
      ]
    },
    {
      title: "事业部费用",
      className: "table-cell-fff7e6",
      children: [
        {
          title: "主播承担金额",
          className: "table-cell-fff7e6",
          dataIndex: "anchorExpensesAmountStr",
          width: 120
        },
        {
          title: "公司承担金额",
          className: "table-cell-fff7e6",
          dataIndex: "companyExpensesAmountStr",
          width: 120
        }
      ]
    },
    {
      title: "可结算利润",
      className: "table-cell-ffe7ba",
      children: [
        {
          title: "主播可结算金额",
          className: "table-cell-ffe7ba",
          dataIndex: "anchorNetProfitAmountStr",
          width: 140
        },
        {
          title: "公司可结算金额",
          className: "table-cell-ffe7ba",
          dataIndex: "companyNetProfitAmountStr",
          width: 140
        }
      ]
    }
  ].map(item => {
    const { children = [] } = item;
    return {
      align: "center",
      ...item,
      children: children?.map(item => {
        const { title } = item;
        return {
          align: "center",
          ...item,
          title: returnEllipsisTooltip({ title })
        };
      })
    };
  });
};
