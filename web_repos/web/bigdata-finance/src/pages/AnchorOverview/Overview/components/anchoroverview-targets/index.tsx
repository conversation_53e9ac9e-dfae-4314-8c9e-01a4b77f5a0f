import React, { useState, useContext, useEffect } from "react";
import { Spin } from "antd";
import { targetOptions } from "../../variable";
import TargetItem from "@/components/target-item";
import apis from "@/services/AnchorOverview/overview";
import styles from "./index.less";
import { FieldsContext } from "./../../index";
import { returnValueForNoValue } from "@/utils";
import { NOVALUE } from "@/utils/constant";
const Res: React.FC = () => {
  const { params } = useContext(FieldsContext);
  const [targets, setTargets] = useState<IOptions[]>(targetOptions);
  const [loading, setLoading] = useState(false);
  const fetchList = async () => {
    setLoading(true);
    const { status, entry } = await apis.indicators(params);
    if (status) {
      setTargets(
        targets.map(item => {
          const { name } = item;
          return {
            ...item,
            children: returnValueForNoValue(entry?.[name + "Str"] || entry?.[name])
          };
        })
      );
    } else {
      setTargets(
        targets.map(item => {
          return {
            ...item,
            children: NOVALUE
          };
        })
      );
    }
    setLoading(false);
  };
  useEffect(() => {
    fetchList();
  }, [params]);
  return (
    <Spin spinning={loading}>
      <div className={styles["anchoroverview-targets"]}>
        {targets.map(item => {
          const { value } = item;
          return <TargetItem key={value} {...item} />;
        })}
      </div>
    </Spin>
  );
};

export default React.memo(Res);
