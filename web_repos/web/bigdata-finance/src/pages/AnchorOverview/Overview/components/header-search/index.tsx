import React, { useEffect, useRef } from "react";
import { Form, Space, DatePicker, Button } from "antd";
import { ProForm } from "@ant-design/pro-components";
import type { ProFormInstance, SubmitterProps } from "@ant-design/pro-components";
import AnchorSelect from "@/components/AnchorSelect";
import { SIZE } from "@/utils/constant";
import dayjs from "dayjs";
interface IProps {
  onChange?: (val: Record<string, any>) => void;
}
const HeaderSearch: React.FC<IProps> = props => {
  const { onChange } = props;
  const formRef = useRef<ProFormInstance>(null);
  const initialValues = useRef({
    settleMonth: [dayjs().subtract(6, "month"), dayjs()]
  });
  const handleParams = val => {
    const { settleMonth = [], ...rest } = val;
    rest.$_fetch = +new Date();
    if (settleMonth.length) {
      const [settleMonthStart, settleMonthEnd] = settleMonth;
      rest.settleMonthStart = dayjs(settleMonthStart).format("YYYYMM");
      rest.settleMonthEnd = dayjs(settleMonthEnd).format("YYYYMM");
    }
    return rest;
  };
  const onFinish = val => {
    onChange?.(handleParams(val));
  };
  const onReset = () => {
    onChange?.(handleParams(initialValues.current));
  };
  const submitButtonRender = (props: SubmitterProps) => {
    return [
      <Button
        key="reset"
        onClick={() => {
          props?.form?.resetFields();
          onReset();
        }}
      >
        重置
      </Button>,
      <Button key="submit" type="primary" onClick={() => props?.form?.submit()}>
        查询
      </Button>
    ];
  };

  useEffect(() => {
    onChange?.(handleParams(initialValues.current));
  }, []);
  return (
    <ProForm
      layout="inline"
      initialValues={initialValues.current}
      formRef={formRef}
      size={SIZE}
      onFinish={onFinish}
      onReset={onReset}
      submitter={{ render: submitButtonRender }}
    >
      <Space wrap size={[2, 2]}>
        <Form.Item label="结算月份" name="settleMonth">
          <DatePicker.RangePicker picker="month"></DatePicker.RangePicker>
        </Form.Item>
        <Form.Item label="主播" name="anchorNameList">
          <AnchorSelect maxTagCount="responsive" mode="multiple" />
        </Form.Item>
      </Space>
    </ProForm>
  );
};
export default React.memo(HeaderSearch);
