import { returnEllipsisTooltip } from "@/utils";
export const columns = ({}: any): TableListItem[] => {
  return [
    {
      title: "直播明细",
      children: [
        {
          title: "结算月份",
          fixed: "left",
          valueType: "dateMonth",
          dataIndex: "settleMonth",
          width: 80
        },
        {
          title: "主播",
          fixed: "left",
          dataIndex: "anchorName",
          width: 80
        },
        {
          title: "PID",
          fixed: "left",
          dataIndex: "anchorId",
          width: 80
        },
        {
          title: "直播场次",
          fixed: "left",
          dataIndex: "liveCnt",
          width: 80
        },
        {
          title: "目前粉丝数",
          fixed: "left",
          dataIndex: "fansCntStr",
          width: 100
        },
        {
          title: "GMV战报金额",
          fixed: "left",
          dataIndex: "liveGmvStr",
          width: 120
        },
        {
          title: "结算金额",
          fixed: "left",
          dataIndex: "liveSettleAmountStr",
          width: 80
        }
      ]
    },
    {
      title: "卖货收入",
      className: "table-cell-ff7875",
      children: [
        {
          title: "卖货收入",
          className: "table-cell-ff7875",
          dataIndex: "liveAmountStr",
          width: 80
        },
        {
          title: "主播利润",
          className: "table-cell-ff7875",
          dataIndex: "anchorLiveAmountStr",
          width: 80
        },
        {
          title: "公司利润",
          className: "table-cell-ff7875",
          dataIndex: "companyLiveAmountStr",
          width: 80
        }
      ]
    },
    {
      title: "收入合计",
      className: "table-cell-fff2e8",
      children: [
        {
          title: "主播收入",
          className: "table-cell-fff2e8",
          dataIndex: "simpleAnchorIncomeAmountStr",
          width: 80
        },
        {
          title: "公司收入",
          className: "table-cell-fff2e8",
          dataIndex: "simpleCompanyIncomeAmountStr",
          width: 80
        }
      ]
    },
    {
      title: "费用合计",
      className: "table-cell-ffd8bf",
      children: [
        {
          title: "主播费用",
          className: "table-cell-ffd8bf",
          dataIndex: "simpleAnchorExpendAmountStr",
          width: 80
        },
        {
          title: "公司费用",
          className: "table-cell-ffd8bf",
          dataIndex: "simpleCompanyExpendAmountStr",
          width: 80
        }
      ]
    },
    {
      title: "应结算金额",
      className: "table-cell-fff7e6",
      children: [
        {
          title: "主播可结算金额",
          className: "table-cell-fff7e6",
          dataIndex: "simpleAnchorSettleAmountStr",
          width: 120
        },
        {
          title: "公司可结算金额",
          className: "table-cell-fff7e6",
          dataIndex: "simpleCompanySettleAmountStr",
          width: 120
        }
      ]
    }
  ].map(item => {
    const { children = [] } = item;
    return {
      align: "center",
      ...item,
      children: children?.map(item => {
        const { title } = item;
        return {
          align: "center",
          ...item,
          title: returnEllipsisTooltip({ title })
        };
      })
    };
  });
};
