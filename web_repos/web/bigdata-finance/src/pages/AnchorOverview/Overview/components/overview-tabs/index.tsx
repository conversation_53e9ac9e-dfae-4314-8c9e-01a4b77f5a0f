import React, { useMemo, useState } from "react";
import { <PERSON>, Tabs, Toolt<PERSON>, Button } from "antd";
import { InfoCircleOutlined } from "@ant-design/icons";
import ProfitAnalysisTotal from "./profit-analysis-total";
import ProfitAnalysisDetail from "./profit-analysis-detail";
import EstimatedSettlement from "./estimated-settlement";
import { SIZE, tabs_tabBarStyle } from "@/utils/constant";
import apis from "@/services/AnchorOverview/overview";
import { pushExportHistory } from "@/utils";

export const FieldsContext = React.createContext<any>(null);

export const tab_profit = "a";
export const tab_estimated = "b";
export const table_total = "total";
export const table_detail = "detail";
const Res = () => {
  const [curTab, setCurTab] = useState(tab_profit);
  const [curTable, setCurTable] = useState(table_total);
  const [pageParams, setPageParams] = useState({});
  const handleParams = p => {
    const { current, ...rest } = p;
    return { ...rest, pageNo: current };
  };
  const handleExport = async () => {
    switch (curTab) {
      case tab_profit: {
        const { status } = await apis.settleprofitsdwonload(pageParams);
        if (status) {
          pushExportHistory();
        }
        break;
      }
      case tab_estimated: {
        const { status } = await apis.estimatedsettleprofitsdownload(pageParams);
        if (status) {
          pushExportHistory();
        }
        break;
      }
      default:
        break;
    }
  };
  const tabItems = [
    {
      key: tab_profit,
      label: (
        <Space>
          结算利润分析
          <Tooltip title="基于实际结算数据汇总，每月11号左右产出上月结算数据">
            <InfoCircleOutlined />
          </Tooltip>
        </Space>
      ),
      children:
        curTable === table_total ? (
          <ProfitAnalysisTotal handleParams={handleParams} onParamsChange={setPageParams} />
        ) : (
          <ProfitAnalysisDetail handleParams={handleParams} onParamsChange={setPageParams} />
        )
    },
    {
      key: tab_estimated,
      label: (
        <Space>
          预估结算概览
          <Tooltip title="每月5号产出上月的预估结算数据">
            <InfoCircleOutlined />
          </Tooltip>
        </Space>
      ),
      children: <EstimatedSettlement handleParams={handleParams} onParamsChange={setPageParams} />
    }
  ];
  const tabbarExtra = useMemo(() => {
    return (
      <Space>
        {curTab === tab_profit ? (
          <>
            <Button size={SIZE} type={curTable === table_total ? "primary" : "default"} onClick={() => setCurTable(table_total)}>
              汇总
            </Button>
            <Button size={SIZE} type={curTable === table_detail ? "primary" : "default"} onClick={() => setCurTable(table_detail)}>
              明细
            </Button>
          </>
        ) : null}
        <Button size={SIZE} onClick={handleExport}>
          导出
        </Button>
      </Space>
    );
  }, [curTab, curTable, pageParams]);
  return (
    <FieldsContext.Provider value={{}}>
      <Tabs activeKey={curTab} tabBarStyle={tabs_tabBarStyle} size={SIZE} onChange={setCurTab} items={tabItems} tabBarExtraContent={tabbarExtra} />
    </FieldsContext.Provider>
  );
};

export default React.memo(Res);
