import React, { useRef, useContext } from "react";
import TableList from "@/components/TableList";
import { columns } from "./variable";
import apis from "@/services/AnchorOverview/overview";
import { FieldsContext } from "@/pages/AnchorOverview/Overview/index";
const Res: React.FC<any> = props => {
  const { handleParams, onParamsChange } = props;
  const { params } = useContext(FieldsContext);
  const formRef = useRef<any>();
  const actionRef = useRef<any>();
  return (
    <TableList
      formRef={formRef}
      actionRef={actionRef}
      columns={columns({})}
      search={false}
      options={false}
      scroll={{ x: "max-content", y: 420 }}
      params={params}
      preFetch={handleParams}
      paramsChange={onParamsChange}
      api={apis.estimatedSettleProfits}
    />
  );
};

export default React.memo(Res);
