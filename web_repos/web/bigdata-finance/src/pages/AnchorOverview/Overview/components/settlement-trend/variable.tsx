import { returnEllipsisTooltip } from "@/utils";

export const anchorRank_columns = ({}: any): TableListItem[] => {
  return [
    {
      title: "序号",
      dataIndex: "$index",
      width: 70,
      fixed: "left",
      render: (_, record, index) => {
        return index + 1;
      }
    },
    {
      title: "主播",
      dataIndex: "anchorName",
      width: 90,
      render(_, record) {
        const { anchorName } = record;
        return returnEllipsisTooltip({ title: anchorName });
      }
    },
    {
      title: "GMV战报金额",
      dataIndex: "gmv",
      sortDirections: ["descend", "ascend"],
      sorter: true,
      width: 140,
      render(_, record) {
        const { gmvStr } = record;
        return returnEllipsisTooltip({ title: gmvStr });
      }
    },
    {
      title: "订单结算金额",
      dataIndex: "settleAmount",
      sortDirections: ["descend", "ascend"],
      sorter: true,
      width: 140,
      render(_, record) {
        const { settleAmountStr } = record;
        return returnEllipsisTooltip({ title: settleAmountStr });
      }
    },
    {
      title: "总收入",
      dataIndex: "incomeAmount",
      sortDirections: ["descend", "ascend"],
      sorter: true,
      width: 90,
      render(_, record) {
        const { incomeAmountStr } = record;
        return returnEllipsisTooltip({ title: incomeAmountStr });
      }
    },
    {
      title: "总支出",
      dataIndex: "expendAmount",
      sortDirections: ["descend", "ascend"],
      sorter: true,
      width: 90,
      render(_, record) {
        const { expendAmountStr } = record;
        return returnEllipsisTooltip({ title: expendAmountStr });
      }
    },
    {
      title: "利润",
      dataIndex: "profitAmount",
      sortDirections: ["descend", "ascend"],
      sorter: true,
      width: 90,
      render(_, record) {
        const { profitAmountStr } = record;
        return returnEllipsisTooltip({ title: profitAmountStr });
      }
    }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      align: "center",
      ...item,
      title: returnEllipsisTooltip({ title })
    };
  });
};
