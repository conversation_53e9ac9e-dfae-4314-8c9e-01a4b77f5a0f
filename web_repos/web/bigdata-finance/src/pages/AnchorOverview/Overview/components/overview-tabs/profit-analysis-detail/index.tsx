import React, { useRef, useState, useContext } from "react";
import { Form, message } from "antd";
import TableList from "@/components/TableList";
import { columns } from "./variable";
import apis from "@/services/AnchorOverview/overview";
import { FieldsContext } from "@/pages/AnchorOverview/Overview/index";
const Res: React.FC<any> = props => {
  const { handleParams, onParamsChange } = props;
  const { params } = useContext(FieldsContext);
  const formRef = useRef<any>();
  const actionRef = useRef<any>();
  const [editForm] = Form.useForm();
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const editable: any = {
    type: "single",
    form: editForm,
    onSave: async (key, editData) => {
      const { status } = await apis.settleprofitsupdate(editData);
      editForm.resetFields([key]);
      if (status) {
        message.success("修改成功！");
      }
      actionRef.current?.reload();
    },
    editableKeys,
    onChange: setEditableRowKeys,
    actionRender: (row, config, { save, cancel }) => [save, cancel]
  };
  const handlePageParams = p => {
    setEditableRowKeys([]);
    return handleParams(p);
  };
  return (
    <TableList
      formRef={formRef}
      actionRef={actionRef}
      columns={columns({})}
      search={false}
      options={false}
      scroll={{ x: "max-content", y: 420 }}
      api={apis.settleProfits}
      params={params}
      preFetch={handlePageParams}
      paramsChange={onParamsChange}
      rowKey="uuid"
      editable={editable}
    />
  );
};

export default React.memo(Res);
