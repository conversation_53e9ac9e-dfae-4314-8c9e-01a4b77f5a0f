import React, { useState } from "react";
import { Row, Col } from "antd";
import KeepAlive from "react-activation";
import HeaderSearch from "./components/header-search";
import Targets from "./components/anchoroverview-targets";
import SettlementTrend from "./components/settlement-trend";
import OverviewTabs from "./components/overview-tabs";
import styles from "./index.less";
import classNames from "classnames";
import { ifEmptyObj } from "@/utils";

export const FieldsContext = React.createContext<any>(null);
export const PageUrl = "/anchoroverview/overview";
const Res: React.FC = () => {
  const [params, setParams] = useState({});
  const onParamsChange = val => {
    setParams(val);
  };
  return (
    <FieldsContext.Provider value={{ params }}>
      <div className={styles["anchoroverview"]}>
        <div className={classNames(styles["anchoroverview-search"], "header-search")}>
          <HeaderSearch onChange={onParamsChange} />
        </div>
        {ifEmptyObj(params) ? (
          <div className={styles["anchoroverview-content"]}>
            <Row gutter={[10, 10]}>
              <Col>
                <Targets />
              </Col>
              <Col>
                <SettlementTrend />
              </Col>
              <Col>
                <OverviewTabs />
              </Col>
            </Row>
          </div>
        ) : null}
      </div>
    </FieldsContext.Provider>
  );
};
const Alive = props => (
  <KeepAlive name={PageUrl}>
    <Res {...props} />
  </KeepAlive>
);

export default React.memo(Alive);
