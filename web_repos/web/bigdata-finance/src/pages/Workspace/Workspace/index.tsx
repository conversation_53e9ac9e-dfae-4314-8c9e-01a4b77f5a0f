import React from "react";
import { <PERSON>, Row, Col } from "antd";
import KeepAlive from "react-activation";
import styles from "./index.less";
import AnchorSettlement from "./components/anchor-settlement";
import Profitestim from "./components/profitestim";
import SupplierReconciliation from "./components/supplier-reconciliation";
export const PageUrl = "/workspace";
const Res: React.FC = () => {
  return (
    <div className={styles["workspace"]}>
      <Card className={styles["workspace-content"]} title="工作项">
        <Row gutter={[10, 10]}>
          <Col span={10}>
            <AnchorSettlement />
          </Col>
          <Col span={14}>
            <Profitestim />
          </Col>
          <Col span={24}>
            <SupplierReconciliation />
          </Col>
        </Row>
      </Card>
    </div>
  );
};
const AliveRecord = () => (
  <KeepAlive name={PageUrl}>
    <Res />
  </KeepAlive>
);
export default React.memo(AliveRecord);
