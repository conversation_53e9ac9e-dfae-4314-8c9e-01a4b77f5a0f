import { Tag } from "antd";
import { ClockCircleOutlined, CheckCircleOutlined, ExclamationCircleOutlined } from "@ant-design/icons";
import { returnEllipsisTooltip } from "@/utils";
import { NOVALUE } from "@/utils/constant";
import { history } from "@umijs/max";
import dayjs from "dayjs";
// 巴伽结算明细状态;
const bajiaStatus = {
  0: {
    label: "未开始",
    color: "default",
    icon: <ClockCircleOutlined />
  },
  1: {
    label: "已上传",
    color: "warning",
    icon: <ExclamationCircleOutlined />
  },
  2: {
    label: "已结束",
    color: "success",
    icon: <CheckCircleOutlined />
  }
};
// 辛选费用上传状态;
const expenditureAnchorStatus = {
  0: {
    label: "未开始",
    color: "default",
    icon: <ClockCircleOutlined />
  },
  1: {
    label: "已上传",
    color: "warning",
    icon: <ExclamationCircleOutlined />
  },
  2: {
    label: "已结束",
    color: "success",
    icon: <CheckCircleOutlined />
  }
};
// 金蝶成本状态;
const kingdeeCostStatus = {
  0: {
    label: "未开始",
    color: "default",
    icon: <ClockCircleOutlined />
  },
  1: {
    label: "部分上传",
    color: "warning",
    icon: <ExclamationCircleOutlined />
  },
  2: {
    label: "已完成",
    color: "success",
    icon: <CheckCircleOutlined />
  }
};
// 链接费台账状态;
const liveItemLinkfeeStatus = {
  0: {
    label: "未开始",
    color: "default",
    icon: <ClockCircleOutlined />
  },
  2: {
    label: "已结束",
    color: "success",
    icon: <CheckCircleOutlined />
  }
};
export const tableLists = [
  {
    name: "金蝶成本上传",
    statusObj: kingdeeCostStatus,
    statusKey: "kingdeeCostStatus",
    status: "",
    dateKey: "kingdeeCostDateStr",
    date: "",
    pathname: "/anchorstatement/jinDieUpload"
  },
  {
    name: "辛选费用上传",
    statusObj: expenditureAnchorStatus,
    statusKey: "expenditureAnchorStatus",
    status: "",
    dateKey: "expenditureAnchorDateStr",
    date: "",
    pathname: "/anchorstatement/xinxuancost"
  },
  {
    name: "巴伽结算明细",
    statusObj: bajiaStatus,
    statusKey: "bajiaStatus",
    status: "",
    dateKey: "bajiaDateStr",
    date: "",
    pathname: "/anchorstatement/bajiasettlement"
  },
  {
    name: "链接费台账确认",
    statusObj: liveItemLinkfeeStatus,
    statusKey: "liveItemLinkfeeStatus",
    status: "",
    dateKey: "liveItemLinkfeeDateStr",
    date: "",
    pathname: "/linkfee/ledger"
  }
];
export const columns = ({}: any): Array<TableListItem> => {
  return [
    {
      title: "事项",
      dataIndex: "name",
      width: 120,
      hideInSearch: true,
      render(text, record) {
        const { pathname, name } = record;
        return <a onClick={() => history.push({ pathname: pathname })}>{name}</a>;
      }
    },
    {
      title: "进度",
      dataIndex: "status",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { status, statusObj } = record;
        const curStatus = statusObj?.[status];
        if (curStatus) {
          return (
            <Tag icon={curStatus.icon} color={curStatus.color}>
              {curStatus.label}
            </Tag>
          );
        } else {
          return NOVALUE;
        }
      }
    },
    {
      title: "截止日期",
      dataIndex: "date",
      width: 100,
      hideInSearch: true,
      render(text) {
        if (text) {
          return dayjs(text).format("YYYY年MM月DD日");
        }
        return NOVALUE;
      }
    }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      align: "center",
      ...item,
      title: returnEllipsisTooltip({ title })
    };
  });
};
