import React from "react";
import { Card, Row, Col, CardProps } from "antd";
import { history } from "@umijs/max";
import queryString from "query-string";
interface IProps {
  targets: IOptions[];
  title?: CardProps["title"];
  params: Record<string, any>;
}
const Res: React.FC<IProps> = props => {
  const { targets, title, params } = props;

  return (
    <Card title={title} styles={{ body: { padding: "10px 0" } }}>
      <Row gutter={[30, 40]}>
        <>
          {targets.map(item => {
            const { label, value, pathname, scopeParams = {} } = item;
            return (
              <>
                <Col offset={4} span={8}>
                  {label}
                </Col>
                <Col offset={4} span={8}>
                  <a
                    onClick={() => {
                      history.push({ pathname, search: queryString.stringify({ ...scopeParams, ...params }) });
                    }}
                  >
                    {value}
                  </a>
                </Col>
              </>
            );
          })}
        </>
      </Row>
    </Card>
  );
};

export default React.memo(Res);
