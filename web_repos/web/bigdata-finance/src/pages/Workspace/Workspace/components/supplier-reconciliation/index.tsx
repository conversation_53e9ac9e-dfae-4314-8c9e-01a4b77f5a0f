import React, { useMemo, useState } from "react";
import { Card, Row, Col, Select, Space } from "antd";
import { SIZE } from "@/utils/constant";
import { RightOutlined } from "@ant-design/icons";
import ReconciliationProgress from "./reconciliation-progress";
import ReconciliationState from "./reconciliation-state";
import { history } from "@umijs/max";
import { PageUrl as SupplierOverviewPageUrl } from "@/pages/SupplierOverview/Overview";
export const select_my = 2;
export const select_all = 1;
const selectOptions = [
  { label: "我负责的", value: select_my },
  { label: "全部", value: select_all }
];
const height = 250;
const Res: React.FC = () => {
  const [params, setParams] = useState({ scopeType: select_my });
  const returnTitle = useMemo(() => {
    return (
      <Space>
        <span>供应商对账</span>
        <Select
          style={{ width: 150 }}
          defaultValue={params.scopeType}
          size={SIZE}
          options={selectOptions}
          onChange={e => setParams({ scopeType: e })}
        />
      </Space>
    );
  }, []);
  return (
    <Card
      title={returnTitle}
      styles={{ body: { padding: 10 } }}
      extra={
        <a
          onClick={() => {
            history.push({ pathname: SupplierOverviewPageUrl });
          }}
        >
          更多
          <RightOutlined />
        </a>
      }
    >
      <Row gutter={[10, 0]}>
        <Col span={12}>
          <ReconciliationProgress height={height} params={params} />
        </Col>
        <Col span={12}>
          <ReconciliationState height={height} params={params} />
        </Col>
      </Row>
    </Card>
  );
};

export default React.memo(Res);
