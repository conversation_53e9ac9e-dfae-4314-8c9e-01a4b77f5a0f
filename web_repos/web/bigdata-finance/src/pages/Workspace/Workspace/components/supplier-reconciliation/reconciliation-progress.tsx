import React, { useState, useEffect } from "react";
import { Card, Spin, Row, Col } from "antd";
import { EchartType } from "@/components/EchartsCard";
import styles from "../../index.less";
import { history } from "@umijs/max";
import queryString from "query-string";
import { PageUrl as chedulingPageUrl } from "@/pages/Supplierbill/Reconciliationcheduling";
import apis from "@/services/workspace/workspace";
import { handlePercentage } from "@/utils";
interface IProps {
  height?: number;
  params?: Record<string, any>;
}
const Res: React.FC<IProps> = ({ height = 200, params = {} }) => {
  const [loading, setLoading] = useState(false);
  // 进度
  const [progress, setProgress] = useState("0");
  const [itemList, setItemList] = useState<IOptions[]>(() => [
    {
      label: "未开始",
      key: "notStarted",
      pathname: chedulingPageUrl,
      scopeParams: {
        liveDate: false,
        reconcileProgress: 1
      },
      value: 0
    },
    {
      label: "对账中",
      key: "reconciling",
      pathname: chedulingPageUrl,
      scopeParams: {
        liveDate: false,
        reconcileProgress: 2
      },
      value: 0
    },
    {
      label: "已对账",
      key: "reconciled",
      pathname: chedulingPageUrl,
      scopeParams: {
        liveDate: false,
        reconcileProgress: 3
      },
      value: 0
    }
  ]);
  const fetchList = async (params = {}) => {
    setLoading(true);
    const { entry, status } = await apis.getStatementProgress(params);
    if (status) {
      let unComplete = 0;
      // 对账未完成的 key
      let unCompleteKeys = ["notStarted", "reconciling"];
      let total = itemList.reduce((t, cur) => {
        const val = entry?.[cur["key"]] || 0;
        if (unCompleteKeys.includes(cur["key"])) {
          unComplete += +val;
        }
        return +t + +val;
      }, 0);
      setProgress(handlePercentage(unComplete / total, 2) + "%");
      setItemList(
        itemList.map(item => {
          const { key } = item;
          let val = entry?.[key];
          return {
            ...item,
            value: val
          };
        })
      );
    }
    setLoading(false);
  };
  const optionAfter = (options: any) => {
    options.labelLine = {
      show: false
    };
    options.title = [
      {
        text: progress,
        textStyle: {
          fontSize: 16
        },
        textAlign: "center",
        x: "52%",
        y: "48%"
      }
    ];
    options.series = options.series.map((item: any) => {
      return {
        ...item,
        radius: ["40%", "60%"],
        avoidLabelOverlap: false,
        labelLine: {
          show: false
        },
        label: {
          show: false
        }
      };
    });
    return options;
  };
  useEffect(() => {
    fetchList(params);
  }, [params]);
  return (
    <Card title="对账进度" className={styles["supplier-reconciliation"]} styles={{ body: { height } }}>
      <Spin spinning={loading}>
        <Row>
          <Col span={10}>
            <EchartType type="pie" height={height - 10} data={itemList} optionAfter={optionAfter} />
          </Col>
          <Col span={14} className={styles["items"]}>
            {itemList.map(item => {
              const { label, value, key, pathname, scopeParams = {} } = item;
              return (
                <Col key={key} span={8}>
                  <div>{label}</div>
                  <a
                    className={styles["items-value"]}
                    onClick={() => {
                      history.push({ pathname, search: queryString.stringify({ ...scopeParams, ...params }) });
                    }}
                  >
                    {value}
                  </a>
                </Col>
              );
            })}
          </Col>
        </Row>
      </Spin>
    </Card>
  );
};

export default React.memo(Res);
