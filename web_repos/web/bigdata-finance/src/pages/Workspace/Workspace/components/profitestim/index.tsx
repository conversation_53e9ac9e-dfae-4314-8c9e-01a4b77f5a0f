import React, { useMemo, useEffect, useState } from "react";
import { Card, Row, Col, Select, Space, Spin } from "antd";
import { SIZE } from "@/utils/constant";
import apis from "@/services/workspace/workspace";
import CardTargets from "./card-targets";
import { PageUrl as liveProfitUrl, tab_profitestim } from "@/pages/Profitestim/LiveProfit";
import { PageUrl as videoProfitUrl, tab_video_profitestim } from "@/pages/Profitestim/ShortVideo";
export const select_my = 2;
export const select_all = 1;
const selectOptions = [
  { label: "我负责的", value: select_my },
  { label: "全部", value: select_all }
];
const Res: React.FC = () => {
  const [params, setParams] = useState({ scopeType: select_my });
  const [liveLoading, setLiveLoading] = useState(false);
  const [videoLoading, setVideoLoading] = useState(false);
  const [liveItems, setLiveItems] = useState<IOptions[]>(() => [
    {
      label: "模式缺失",
      name: "modeMissing",
      pathname: liveProfitUrl,
      scopeParams: {
        $_tab: tab_profitestim,
        modelLoss: true,
        liveDate: false
      },
      value: 0
    },
    {
      label: "无合同号",
      name: "noContractNumber",
      pathname: liveProfitUrl,
      scopeParams: {
        $_tab: tab_profitestim,
        tagIdList: "7",
        liveDate: false
      },
      value: 0
    },
    {
      label: "合同号变更",
      name: "contractNumberChanged",
      pathname: liveProfitUrl,
      scopeParams: {
        $_tab: tab_profitestim,
        tagIdList: "9",
        liveDate: false
      },
      value: 0
    }
  ]);
  const [videoItems, setVideoItems] = useState<IOptions[]>(() => [
    {
      label: "模式缺失",
      name: "modeMissing",
      pathname: videoProfitUrl,
      scopeParams: {
        $_tab: tab_video_profitestim,
        modelLoss: true,
        videoStartDate: false
      },
      value: 0
    },
    {
      label: "无合同号",
      name: "noContractNumber",
      pathname: videoProfitUrl,
      scopeParams: {
        $_tab: tab_video_profitestim,
        tagIdList: "7",
        videoStartDate: false
      },
      value: 0
    },
    {
      label: "合同号变更",
      name: "contractNumberChanged",
      pathname: videoProfitUrl,
      scopeParams: {
        $_tab: tab_video_profitestim,
        tagIdList: "9",
        videoStartDate: false
      },
      value: 0
    }
  ]);
  const fetchLive = async (params = {}) => {
    setLiveLoading(true);
    const { entry, status } = await apis.getLiveProfitCondition(params);
    if (status) {
      setLiveItems(
        liveItems.map(item => {
          const { name } = item;
          return {
            ...item,
            value: entry?.[name]
          };
        })
      );
    } else {
      setLiveItems(liveItems);
    }
    setLiveLoading(false);
  };
  const fetchVideo = async (params = {}) => {
    setVideoLoading(true);
    const { entry, status } = await apis.getShortVideoProfitCondition(params);
    if (status) {
      setVideoItems(
        videoItems.map(item => {
          const { name } = item;
          return {
            ...item,
            value: entry?.[name]
          };
        })
      );
    } else {
      setVideoItems(videoItems);
    }
    setVideoLoading(false);
  };
  useEffect(() => {
    fetchLive(params);
    fetchVideo(params);
  }, [params]);
  const returnTitle = useMemo(() => {
    return (
      <Space>
        <span>利润预估</span>
        <Select
          style={{ width: 150 }}
          defaultValue={params.scopeType}
          size={SIZE}
          options={selectOptions}
          onChange={e => setParams({ scopeType: e })}
        />
      </Space>
    );
  }, []);
  return (
    <Card title={returnTitle} styles={{ body: { padding: 10 } }}>
      <Row gutter={[10, 0]}>
        <Col span={12}>
          <Spin spinning={liveLoading}>
            <CardTargets targets={liveItems} title="直播" params={params} />
          </Spin>
        </Col>
        <Col span={12}>
          <Spin spinning={videoLoading}>
            <CardTargets targets={videoItems} title="短视频" params={params} />
          </Spin>
        </Col>
      </Row>
    </Card>
  );
};

export default React.memo(Res);
