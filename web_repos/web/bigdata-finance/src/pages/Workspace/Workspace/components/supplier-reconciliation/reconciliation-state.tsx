import React, { useState, useEffect } from "react";
import { Card, Row, Col, Spin } from "antd";
import styles from "../../index.less";
import { history } from "@umijs/max";
import queryString from "query-string";
import { PageUrl as billPageUrl, tab_reconcili_bill_detail } from "@/pages/Supplierbill/Reconciliationbill";
import apis from "@/services/workspace/workspace";
interface IProps {
  height?: number;
  params?: Record<string, any>;
}
const Res: React.FC<IProps> = ({ height = 200, params = {} }) => {
  const [loading, setLoading] = useState(false);
  const [itemList, setItemList] = useState<IOptions[]>(() => [
    {
      label: "待上传水单",
      key: "pendingUpload",
      pathname: billPageUrl,
      scopeParams: {
        $_tab: tab_reconcili_bill_detail,
        reconcileCondition: 1
      },
      value: 0
    },
    {
      label: "回款未完结",
      key: "unpaid",
      pathname: billPageUrl,
      scopeParams: {
        $_tab: tab_reconcili_bill_detail,
        reconcileCondition: 2
      },
      value: 0
    },
    {
      label: "超60天未回款",
      key: "overdue60Days",
      pathname: billPageUrl,
      scopeParams: {
        $_tab: tab_reconcili_bill_detail,
        reconcileCondition: 3
      },
      value: 0
    },
    {
      label: "需法务接入",
      key: "legalIntervention",
      pathname: billPageUrl,
      scopeParams: {
        $_tab: tab_reconcili_bill_detail,
        reconcileCondition: 4
      },
      value: 0
    }
  ]);
  const fetchProgress = async (params = {}) => {
    setLoading(true);
    const { entry, status } = await apis.getStatementCondition(params);
    if (status) {
      setItemList(
        itemList.map(item => {
          const { key } = item;
          let val = entry?.[key];
          return {
            ...item,
            value: val
          };
        })
      );
    }
    setLoading(false);
  };
  useEffect(() => {
    fetchProgress(params);
  }, [params]);
  return (
    <Card title="对账单情况" className={styles["supplier-state"]} styles={{ body: { height } }}>
      <Spin spinning={loading}>
        <Row align={"middle"} style={{ height: height - 10 }}>
          <Col className={styles["items"]}>
            {itemList.map(item => {
              const { label, value, key, pathname, scopeParams = {} } = item;
              return (
                <Col key={key} span={6}>
                  <div>{label}</div>
                  <a
                    className={styles["items-value"]}
                    onClick={() => {
                      history.push({ pathname, search: queryString.stringify({ ...scopeParams, ...params }) });
                    }}
                  >
                    {value}
                  </a>
                </Col>
              );
            })}
          </Col>
        </Row>
      </Spin>
    </Card>
  );
};

export default React.memo(Res);
