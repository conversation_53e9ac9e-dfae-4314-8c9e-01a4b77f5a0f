import React, { useEffect, useState } from "react";
import { Card, Space } from "antd";
import TableList from "@/components/TableList";
import { columns, tableLists } from "./variable";
import styles from "../../index.less";
import { RightOutlined } from "@ant-design/icons";
import { PageUrl as anchorstatementOverview } from "@/pages/AnchorOverview/Overview";
import { history } from "@umijs/max";
import apis from "@/services/workspace/workspace";
import { NOVALUE } from "@/utils/constant";
const Res: React.FC = () => {
  const [lists, setLists] = useState(() => tableLists);
  const [infos, setInfos] = useState(NOVALUE);
  const fetchList = async (params = {}) => {
    const { entry, status } = await apis.getAnchorSettleProcess(params);
    const { settleStr } = entry;
    setInfos(settleStr);
    if (status) {
      setLists(
        lists.map(item => {
          const { statusKey, dateKey } = item;
          return {
            ...item,
            status: entry?.[statusKey],
            date: entry?.[dateKey]
          };
        })
      );
    } else {
      setLists(tableLists);
    }
  };
  const returTitle = () => {
    return (
      <Space>
        <span>主播结算</span>
        <span className={styles["subtitle"]}>{infos}</span>
      </Space>
    );
  };
  useEffect(() => {
    fetchList();
  }, []);
  return (
    <Card
      className={styles["anchor-settlement"]}
      title={returTitle()}
      extra={
        <a
          onClick={() => {
            history.push({ pathname: anchorstatementOverview });
          }}
        >
          更多
          <RightOutlined />
        </a>
      }
    >
      <TableList search={false} options={false} pagination={false} columns={columns({})} dataSource={lists} />
    </Card>
  );
};

export default React.memo(Res);
