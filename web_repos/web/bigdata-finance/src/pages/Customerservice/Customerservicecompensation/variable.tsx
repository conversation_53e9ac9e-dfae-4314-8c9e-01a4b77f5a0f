import { Select } from "antd";
import { filterOptionLabel, returnEllipsisTooltip } from "@/utils";
import { NOVALUE, SIZE } from "@/utils/constant";
import AnchorSelect from "@/components/AnchorSelect";
const columns_search = ({ workerStatusOptions, approvalStatusOptions }: any) => {
  return [
    { title: "实际直播日期", dataIndex: "actualLiveDate", valueType: "dateRange", hideInTable: true },
    { title: "支付日期", dataIndex: "liveDate", valueType: "dateRange", hideInTable: true },
    { title: "打款成功时间", dataIndex: "remitTime", valueType: "dateRange", hideInTable: true },
    {
      title: "工单状态说明",
      dataIndex: "workerStatusList",
      hideInTable: true,
      renderFormItem: () => {
        return (
          <Select
            placeholder="请选择"
            style={{ minWidth: 120, textAlign: "left" }}
            size={SIZE}
            options={workerStatusOptions}
            filterOption={filterOptionLabel}
            showSearch
            allowClear
            maxTagCount="responsive"
            mode="multiple"
          />
        );
      }
    },
    {
      title: "订单编号",
      dataIndex: "bizOrderIdList",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "主播",
      dataIndex: "anchorNameList",
      hideInTable: true,
      renderFormItem() {
        return <AnchorSelect showSearch allowClear maxTagCount="responsive" mode="multiple" />;
      }
    },
    {
      title: "商品ID",
      dataIndex: "itemIdList",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },

    { title: "商品名称", dataIndex: "itemTitle", hideInTable: true },
    { title: "店铺名称", dataIndex: "shopName", hideInTable: true },
    {
      title: "赔付状态说明",
      dataIndex: "approvalStatusList",
      hideInTable: true,
      renderFormItem: () => {
        return (
          <Select
            placeholder="请选择"
            style={{ minWidth: 120, textAlign: "left" }}
            size={SIZE}
            options={approvalStatusOptions}
            filterOption={filterOptionLabel}
            showSearch
            allowClear
            maxTagCount="responsive"
            mode="multiple"
          />
        );
      }
    }
  ];
};

export const columns = ({ workerStatusOptions, isSettleAnchorObj, approvalStatusOptions }: any): Array<TableListItem> => {
  return [
    ...columns_search({ workerStatusOptions, approvalStatusOptions }),
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      hideInSearch: true,
      fixed: "left",
      render: (_, record, index) => {
        return index + 1;
      }
    },
    {
      title: "赔付单ID",
      dataIndex: "compensateId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "工单ID",
      dataIndex: "workerId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "工单状态说明", dataIndex: "workerStatusDesc", width: 100, hideInSearch: true },
    {
      title: "订单编号",
      dataIndex: "bizOrderId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "订单渠道描述", dataIndex: "orderChannelDesc", width: 100, hideInSearch: true },
    { title: "订单来源", dataIndex: "dataSourceStr", width: 100, hideInSearch: true },
    { title: "实际直播日期", dataIndex: "actualLiveDate", width: 100, hideInSearch: true },
    { title: "支付日期", dataIndex: "liveDate", width: 100, hideInSearch: true },
    {
      title: "主播名称",
      dataIndex: "anchorName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "主播ID", dataIndex: "anchorId", width: 100, hideInSearch: true },
    {
      title: "是否结算主播",
      dataIndex: "isSettleAnchor",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: isSettleAnchorObj[text] });
      }
    },
    {
      title: "商品信息",
      editable: false,
      dataIndex: "itemInfo",
      width: 300,
      hideInSearch: true,
      render(_, record) {
        return (
          <div className="columnItemInfo">
            <div className="info">
              <div className="id">
                <span>{returnEllipsisTooltip({ title: "ID:" + (record?.itemId || NOVALUE) })}</span>
              </div>
              <div>{returnEllipsisTooltip({ title: record?.itemTitle, rows: 1, width: 250 })}</div>
            </div>
          </div>
        );
      }
    },
    { title: "商品skuid", dataIndex: "outerSkuId", width: 100, hideInSearch: true },
    { title: "内部商品skuid", dataIndex: "innerSkuId", width: 100, hideInSearch: true },
    {
      title: "商品编码",
      dataIndex: "itemCode",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "店铺ID", dataIndex: "shopId", width: 100, hideInSearch: true },
    {
      title: "店铺名称",
      dataIndex: "shopName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "店铺类型说明", dataIndex: "sellerTypeDesc", width: 100, hideInSearch: true },
    {
      title: "问题详情",
      dataIndex: "problemDetail",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "发货仓", dataIndex: "warehouseName", width: 100, hideInSearch: true },
    {
      title: "物流公司",
      dataIndex: "logisticCompany",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "快递单号",
      dataIndex: "expressNo",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "工单一级类型", dataIndex: "woTypeNameLv1", width: 100, hideInSearch: true },
    { title: "工单二级类型", dataIndex: "woTypeNameLv2", width: 100, hideInSearch: true },
    { title: "工单三级类型", dataIndex: "woTypeNameLv3", width: 100, hideInSearch: true },
    { title: "赔付状态说明", dataIndex: "approvalStatusDesc", width: 100, hideInSearch: true },
    { title: "赔付优先级说明", dataIndex: "compensatePriorityTypeDesc", width: 100, hideInSearch: true },
    { title: "处理方式说明", dataIndex: "disposeModeDesc", width: 100, hideInSearch: true },
    {
      title: "买家账号",
      dataIndex: "buyerNick",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "支付宝账号",
      dataIndex: "aliAccount",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "支付宝账号加密ID",
      dataIndex: "aliAccountSid",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "支付宝姓名",
      dataIndex: "aliTrueName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "支付宝姓名加密ID",
      dataIndex: "aliTrueNameSid",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "支付时间", dataIndex: "payTime", width: 100, hideInSearch: true },
    { title: "购买数量", dataIndex: "itemNumStr", width: 100, hideInSearch: true },
    { title: "商品实付金额", dataIndex: "actualAmountStr", width: 100, hideInSearch: true },
    { title: "赔付金额", dataIndex: "compenseAmountStr", width: 100, hideInSearch: true },
    { title: "打款成功时间", dataIndex: "remitTime", width: 100, hideInSearch: true },
    { title: "赔付创建时间", dataIndex: "createTime", width: 100, hideInSearch: true },
    { title: "赔付修改时间", dataIndex: "modifiedTime", width: 100, hideInSearch: true },
    // {
    //   title: "凭证图片",
    //   dataIndex: "itemProofUrl",
    //   width: 100,
    //   hideInSearch: true,
    //   render(_, record) {
    //     const { itemProofUrl } = record;
    //     let imgs = itemProofUrl?.split(",");

    //     return (
    //       <div className="mutiImages">
    //         {Array.isArray(imgs) ? (
    //           imgs.map((item, index) => {
    //             return <Image width={50} height={50} src={returnItemImgUrl(item)} key={index} />;
    //           })
    //         ) : itemProofUrl ? (
    //           <Image width={50} height={50} src={itemProofUrl} />
    //         ) : (
    //           NOVALUE
    //         )}
    //       </div>
    //     );
    //   }
    // },
    {
      title: "提交赔付人",
      dataIndex: "approvalCreator",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "提交赔付人姓名", dataIndex: "approvalCreatorName", width: 100, hideInSearch: true },
    {
      title: "审批通过的人",
      dataIndex: "approvalPasser",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "审批通过人姓名", dataIndex: "approvalPasserName", width: 100, hideInSearch: true },
    {
      title: "工单处理人",
      dataIndex: "assignWorker",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "工单处理人姓名", dataIndex: "assignWorkerName", width: 100, hideInSearch: true },
    {
      title: "指派团队",
      dataIndex: "assignDeptId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "指派团队名称",
      dataIndex: "assignDeptName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "业务单据id",
      dataIndex: "businessNo",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "信息说明",
      dataIndex: "information",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      ...item,
      align: "center",
      title: returnEllipsisTooltip({ title })
    };
  });
};
