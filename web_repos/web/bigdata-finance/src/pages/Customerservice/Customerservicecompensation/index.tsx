import React, { useState, useRef } from "react";
import { But<PERSON> } from "antd";
import KeepAlive from "react-activation";
import TableList from "@/components/TableList";
import { SIZE } from "@/utils/constant";
import { columns } from "./variable";
import apis from "@/services/customerservice/customerservicecompensation";
import { pushExportHistory, splitSpaceComma } from "@/utils";
import useCommonOptions from "@/hooks/useCommonOptions";
import dayjs from "dayjs";
export const PageUrl = "/customerservice/customerservicecompensation";
const DetailTable = () => {
  const [workerStatusOptions, workerStatusObj] = useCommonOptions({ dimName: "工单状态说明" });
  const [approvalStatusOptions, approvalStatusObj] = useCommonOptions({ dimName: "赔付状态说明" });
  const [isSettleAnchorOptions, isSettleAnchorObj] = useCommonOptions({ dimName: "是否结算主播" });
  const subsidyActionRef = useRef<any>();
  const [SPUPageParams, setSPUPageParams] = useState({});

  const columnsProps = { workerStatusOptions, workerStatusObj, approvalStatusOptions, approvalStatusObj, isSettleAnchorOptions, isSettleAnchorObj };

  // 处理请求参数
  const handlePageParmas = (p: any) => {
    const { current, actualLiveDate, remitTime, liveDate, bizOrderIdList, itemIdList, ...rest } = p;
    if (actualLiveDate) {
      const [actualLiveDateStart, actualLiveDateEnd] = actualLiveDate;
      rest.actualLiveDateStart = dayjs(actualLiveDateStart).format("YYYYMMDD");
      rest.actualLiveDateEnd = dayjs(actualLiveDateEnd).format("YYYYMMDD");
    }
    if (remitTime) {
      const [remitTimeStart, remitTimeEnd] = remitTime;
      rest.remitTimeStart = dayjs(remitTimeStart).format("YYYY-MM-DD");
      rest.remitTimeEnd = dayjs(remitTimeEnd).format("YYYY-MM-DD");
    }
    if (liveDate) {
      const [liveDateStart, liveDateEnd] = liveDate;
      rest.liveDateStart = dayjs(liveDateStart).format("YYYYMMDD");
      rest.liveDateEnd = dayjs(liveDateEnd).format("YYYYMMDD");
    }
    rest.bizOrderIdList = splitSpaceComma(bizOrderIdList);
    rest.itemIdList = splitSpaceComma(itemIdList);
    const params = {
      pageNo: current,
      ...rest
    };
    return params;
  };
  const handleExport = async () => {
    const { status } = await apis.downloadCrmWorkOrderCompensate(SPUPageParams);
    if (status) {
      pushExportHistory();
    }
  };

  return (
    <>
      <TableList
        actionRef={subsidyActionRef}
        columns={columns(columnsProps)}
        scroll={{ y: "calc(100vh - 400px)" }}
        form={{
          initialValues: {
            liveDate: [dayjs().subtract(1, "month"), dayjs().subtract(1, "day")]
          }
        }}
        api={apis.getCrmWorkOrderCompensatePage}
        preFetch={handlePageParmas}
        paramsChange={setSPUPageParams}
        rowKey="compensateId"
        toolbar={{
          actions: [
            <Button key="export" onClick={() => handleExport()} size={SIZE}>
              导出
            </Button>
          ]
        }}
      />
    </>
  );
};

const AliveRecord = () => (
  <KeepAlive name={PageUrl}>
    <DetailTable />
  </KeepAlive>
);
export default React.memo(AliveRecord);
