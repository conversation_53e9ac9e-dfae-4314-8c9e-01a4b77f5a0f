import { Select, Image } from "antd";
import { filterOptionLabel, returnEllipsisTooltip, returnItemImgUrl } from "@/utils";
import { NOVALUE, SIZE } from "@/utils/constant";
import AnchorSelect from "@/components/AnchorSelect";
const columns_search = ({ payStatusOptions, orderChannelOptions, cashBackStatusOptions }: any) => {
  return [
    { title: "实际直播日期", dataIndex: "actualLiveDate", valueType: "dateRange", hideInTable: true },
    { title: "支付日期", dataIndex: "liveDate", valueType: "dateRange", hideInTable: true },
    { title: "返款时间", dataIndex: "cashBackTime", valueType: "dateRange", hideInTable: true },
    {
      title: "打款状态",
      dataIndex: "payStatusList",
      hideInTable: true,
      renderFormItem: () => {
        return (
          <Select
            placeholder="请选择"
            style={{ minWidth: 120, textAlign: "left" }}
            size={SIZE}
            options={payStatusOptions}
            filterOption={filterOptionLabel}
            showSearch
            allowClear
            maxTagCount="responsive"
            mode="multiple"
          />
        );
      }
    },
    {
      title: "返款单状态",
      dataIndex: "cashBackStatusList",
      hideInTable: true,
      renderFormItem: () => {
        return (
          <Select
            placeholder="请选择"
            style={{ minWidth: 120, textAlign: "left" }}
            size={SIZE}
            options={cashBackStatusOptions}
            filterOption={filterOptionLabel}
            showSearch
            allowClear
            maxTagCount="responsive"
            mode="multiple"
          />
        );
      }
    },
    {
      title: "订单编号",
      dataIndex: "bizOrderIdList",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "订单渠道",
      dataIndex: "orderChannelList",
      hideInTable: true,
      renderFormItem: () => {
        return (
          <Select
            placeholder="请选择"
            style={{ minWidth: 120, textAlign: "left" }}
            size={SIZE}
            options={orderChannelOptions}
            filterOption={filterOptionLabel}
            showSearch
            allowClear
            maxTagCount="responsive"
            mode="multiple"
          />
        );
      }
    },
    {
      title: "主播",
      dataIndex: "anchorNameList",
      hideInTable: true,
      renderFormItem() {
        return <AnchorSelect showSearch allowClear maxTagCount="responsive" mode="multiple" />;
      }
    },
    {
      title: "商品ID",
      dataIndex: "itemIdList",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    { title: "店铺名称", dataIndex: "shopName", hideInTable: true }
  ];
};

export const columns = ({ payStatusOptions, orderChannelOptions, cashBackStatusOptions }: any): Array<TableListItem> => {
  return [
    ...columns_search({ payStatusOptions, orderChannelOptions, cashBackStatusOptions }),
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      hideInSearch: true,
      fixed: "left",
      render: (_, record, index) => {
        return index + 1;
      }
    },
    { title: "返现单ID", dataIndex: "cashBackId", width: 100, hideInSearch: true },
    { title: "返款计划id", dataIndex: "planItemId", width: 100, hideInSearch: true },
    {
      title: "返款申请单号",
      dataIndex: "cashBackNo",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "返现账单单号",
      dataIndex: "billNo",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "支出账户（商户号）", dataIndex: "mchId", width: 100, hideInSearch: true },

    { title: "打款状态说明", dataIndex: "payStatusDesc", width: 100, hideInSearch: true },
    { title: "返款单状态说明", dataIndex: "cashBackStatusDesc", width: 100, hideInSearch: true },
    {
      title: "订单编号",
      dataIndex: "bizOrderId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },

    {
      title: "订单渠道描述",
      dataIndex: "orderChannelDesc",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "数据源", dataIndex: "dataSourceStr", width: 100, hideInSearch: true },
    { title: "实际直播日期", dataIndex: "actualLiveDate", width: 100, hideInSearch: true },
    { title: "支付日期", dataIndex: "liveDate", width: 100, hideInSearch: true },
    { title: "主播名称", dataIndex: "anchorName", width: 100, hideInSearch: true },
    { title: "主播ID", dataIndex: "anchorId", width: 100, hideInSearch: true },
    { title: "是否结算主播", dataIndex: "isSettleAnchorStr", width: 100, hideInSearch: true },
    {
      title: "商品信息",
      editable: false,
      dataIndex: "itemInfo",
      width: 300,
      hideInSearch: true,
      render(_, record) {
        const { itemImageUrl } = record;
        return (
          <div className="columnItemInfo">
            <Image width={50} height={50} src={returnItemImgUrl(itemImageUrl)} />
            <div className="info">
              <div className="id">
                <span>{returnEllipsisTooltip({ title: "ID:" + (record?.itemId || NOVALUE) })}</span>
              </div>
              <div>{returnEllipsisTooltip({ title: record?.itemTitle, rows: 1, width: 250 })}</div>
            </div>
          </div>
        );
      }
    },
    {
      title: "sku编码",
      dataIndex: "skuCode",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "sku描述",
      dataIndex: "skuDesc",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "商品价格", dataIndex: "itemPriceStr", width: 100, hideInSearch: true },
    { title: "下单数量", dataIndex: "itemNumStr", width: 100, hideInSearch: true },
    { title: "下单金额", dataIndex: "orderAmountStr", width: 100, hideInSearch: true },
    { title: "店铺ID", dataIndex: "shopId", width: 100, hideInSearch: true },
    {
      title: "店铺名称",
      dataIndex: "shopName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "单品返款金额", dataIndex: "itemCashbackAmountStr", width: 100, hideInSearch: true },
    { title: "申请返款数量", dataIndex: "itemCashbackNumStr", width: 100, hideInSearch: true },
    { title: "返款总金额", dataIndex: "totalCashbackAmountStr", width: 100, hideInSearch: true },
    { title: "手续费（暂无）", dataIndex: "commissionStr", width: 100, hideInSearch: true },
    { title: "应付款金额", dataIndex: "payableAmountStr", width: 100, hideInSearch: true },
    { title: "线上打款金额", dataIndex: "onlineAmountStr", width: 100, hideInSearch: true },
    { title: "线下打款金额", dataIndex: "offlineAmountStr", width: 100, hideInSearch: true },
    { title: "线下返款支付凭证", dataIndex: "paymentVoucher", width: 100, hideInSearch: true },
    { title: "实际返现金额", dataIndex: "actualPaidAmountStr", width: 100, hideInSearch: true },
    { title: "支付方式", dataIndex: "paymentWay", width: 100, hideInSearch: true },
    {
      title: "打款方式/转账类型说明",
      dataIndex: "paymentTypeDesc",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "微信商户号", dataIndex: "merchantNo", width: 100, hideInSearch: true },
    {
      title: "收款账户id",
      dataIndex: "payeeAccount",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "收款账户类型",
      dataIndex: "collectionAccount",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "收款人姓名",
      dataIndex: "payeeName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "第三方用户id",
      dataIndex: "userId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "三方用户昵称",
      dataIndex: "userNickname",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "第三方用户手机号", dataIndex: "userPhone", width: 100, hideInSearch: true },
    {
      title: "订单截图",
      dataIndex: "imageUrl",
      width: 140,
      hideInSearch: true,
      render(_, record) {
        const { imageUrl } = record;
        let imgs = imageUrl?.split(",");

        return (
          <div className="mutiImages">
            {Array.isArray(imgs) ? (
              imgs.map((item, index) => {
                return <Image width={50} height={50} src={returnItemImgUrl(item)} key={index} />;
              })
            ) : imageUrl ? (
              <Image width={50} height={50} src={imageUrl} />
            ) : (
              NOVALUE
            )}
          </div>
        );
      }
    },
    { title: "创建渠道说明", dataIndex: "channelDesc", width: 100, hideInSearch: true },
    { title: "创建人姓名", dataIndex: "createorName", width: 100, hideInSearch: true },
    { title: "创建时间", dataIndex: "gmtCreate", width: 100, hideInSearch: true },
    { title: "修改时间", dataIndex: "gmtModified", width: 100, hideInSearch: true },
    { title: "申请时间", dataIndex: "applyTime", width: 100, hideInSearch: true },
    { title: "下单时间", dataIndex: "orderCreateTime", width: 100, hideInSearch: true },
    { title: "返款时间", dataIndex: "cashBackTime", width: 100, hideInSearch: true },
    { title: "关单时间", dataIndex: "closeTime", width: 100, hideInSearch: true },
    { title: "关单操作人", dataIndex: "closeOperator", width: 100, hideInSearch: true },
    { title: "关单类型说明", dataIndex: "closeTypeDesc", width: 100, hideInSearch: true },
    { title: "流水号", dataIndex: "paymentNo", width: 100, hideInSearch: true }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      ...item,
      align: "center",
      title: returnEllipsisTooltip({ title })
    };
  });
};
