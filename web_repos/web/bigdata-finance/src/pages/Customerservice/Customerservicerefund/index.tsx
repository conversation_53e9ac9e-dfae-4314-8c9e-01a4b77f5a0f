import React, { useState, useRef } from "react";
import { But<PERSON> } from "antd";
import KeepAlive from "react-activation";
import TableList from "@/components/TableList";
import { SIZE } from "@/utils/constant";
import { columns } from "./variable";
import apis from "@/services/customerservice/customerservicerefund";
import { pushExportHistory, splitSpaceComma } from "@/utils";
import useCommonOptions from "@/hooks/useCommonOptions";
import dayjs from "dayjs";
export const PageUrl = "/customerservice/customerservicerefund";
const DetailTable = () => {
  const [payStatusOptions, payStatusObj] = useCommonOptions({ dimName: "打款状态" });
  const [orderChannelOptions, orderChannelObj] = useCommonOptions({ dimName: "订单渠道" });
  const [cashBackStatusOptions, cashBackStatusObj] = useCommonOptions({ dimName: "返款单状态" });
  const subsidyActionRef = useRef<any>();
  const [SPUPageParams, setSPUPageParams] = useState({});

  const columnsProps = { payStatusOptions, payStatusObj, orderChannelOptions, orderChannelObj, cashBackStatusOptions, cashBackStatusObj };

  // 处理请求参数
  const handlePageParmas = (p: any) => {
    const { current, actualLiveDate, cashBackTime, liveDate, bizOrderIdList, itemIdList, ...rest } = p;
    if (actualLiveDate) {
      const [actualLiveDateStart, actualLiveDateEnd] = actualLiveDate;
      rest.actualLiveDateStart = dayjs(actualLiveDateStart).format("YYYYMMDD");
      rest.actualLiveDateEnd = dayjs(actualLiveDateEnd).format("YYYYMMDD");
    }
    if (cashBackTime) {
      const [cashBackTimeStart, cashBackTimeEnd] = cashBackTime;
      rest.cashBackTimeStart = dayjs(cashBackTimeStart).format("YYYY-MM-DD");
      rest.cashBackTimeEnd = dayjs(cashBackTimeEnd).format("YYYY-MM-DD");
    }
    if (liveDate) {
      const [liveDateStart, liveDateEnd] = liveDate;
      rest.liveDateStart = dayjs(liveDateStart).format("YYYYMMDD");
      rest.liveDateEnd = dayjs(liveDateEnd).format("YYYYMMDD");
    }
    rest.bizOrderIdList = splitSpaceComma(bizOrderIdList);
    rest.itemIdList = splitSpaceComma(itemIdList);
    const params = {
      pageNo: current,
      ...rest
    };
    return params;
  };
  const handleExport = async () => {
    const { status } = await apis.downloadCrmCashBackOrder(SPUPageParams);
    if (status) {
      pushExportHistory();
    }
  };

  return (
    <>
      <TableList
        actionRef={subsidyActionRef}
        columns={columns(columnsProps)}
        scroll={{ y: "calc(100vh - 400px)" }}
        form={{
          initialValues: {
            liveDate: [dayjs().subtract(1, "month"), dayjs().subtract(1, "day")]
          }
        }}
        api={apis.getCrmCashBackOrderPage}
        preFetch={handlePageParmas}
        paramsChange={setSPUPageParams}
        rowKey="cashBackId"
        toolbar={{
          actions: [
            <Button key="export" onClick={() => handleExport()} size={SIZE}>
              导出
            </Button>
          ]
        }}
      />
    </>
  );
};

const AliveRecord = () => (
  <KeepAlive name={PageUrl}>
    <DetailTable />
  </KeepAlive>
);
export default React.memo(AliveRecord);
