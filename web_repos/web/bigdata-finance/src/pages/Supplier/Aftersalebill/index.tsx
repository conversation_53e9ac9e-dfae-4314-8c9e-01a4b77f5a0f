import React, { useRef, useState, useMemo } from "react";
import { Button } from "antd";
import { columns } from "./variable";
import { NOVALUE, Protable_FROM_CONFIG } from "@/utils/constant";
import KeepAlive from "react-activation";
import { splitSpaceComma } from "@/utils";
import UploadModal from "@/components/UploadModal";
import apis from "@/services/supplier/aftersalebill";
import dayjs from "dayjs";
import TableList from "@/components/TableList";
import useCommonOptions from "@/hooks/useCommonOptions";
const PageUrl = "/supplier/aftersalebill";
const Res: React.FC = () => {
  const [recoverOptions, recoverObj] = useCommonOptions({ dimName: "供应商对账-是否追款" });
  const proTableFormRef = useRef<any>();
  const actionRef = useRef<any>();
  const [pageParmas, setPageParmas] = useState<any>({});
  const [showExcelUpload, setshowExcelUpload] = useState(false);

  const [editProps, setEditProps] = useState<any>({
    open: false,
    onClose: () => {
      setEditProps({ ...editProps, open: false });
      actionRef.current?.reload();
    }
  });

  const columnsProps = {
    recoverOptions,
    recoverObj
  };
  // 上传excel modal属性
  const UploadExcelModalProps = useMemo(() => {
    return {
      title: "上传超售后账单",
      visible: showExcelUpload,
      targetType: 0,
      api: apis.addAfterSalesFromExcel,
      message: (
        <>
          <span>1. 请参考模板中数据格式进行上传</span>
          <br />
          <span>2. 同一订单多次上传，取最新上传订单</span>
          <br />
        </>
      ),
      tempUrl: "https://s.xinc818.com/files/webcilxbm3zgw1iau0h/商家-超售后账单上传模板.xlsx",
      fresh: () => {},
      close: () => {
        setshowExcelUpload(false);
      }
    };
  }, [showExcelUpload, pageParmas]);

  // 处理请求参数
  const handlePageParmas = (params: any) => {
    const { current, gmtRefundSuccessTime, gmtTradeSuccessTime, orderIdList, refundIdList, ...rest } = params;
    if (gmtRefundSuccessTime) {
      const [gmtRefundSuccessTimeStart, gmtRefundSuccessTimeEnd] = gmtRefundSuccessTime;
      rest.gmtRefundSuccessTimeStart = dayjs(gmtRefundSuccessTimeStart).format("YYYY-MM-DD");
      rest.gmtRefundSuccessTimeEnd = dayjs(gmtRefundSuccessTimeEnd).format("YYYY-MM-DD");
    }
    if (gmtTradeSuccessTime) {
      const [gmtTradeSuccessTimeStart, gmtTradeSuccessTimeEnd] = gmtTradeSuccessTime;
      rest.gmtTradeSuccessTimeStart = dayjs(gmtTradeSuccessTimeStart).format("YYYY-MM-DD");
      rest.gmtTradeSuccessTimeEnd = dayjs(gmtTradeSuccessTimeEnd).format("YYYY-MM-DD");
    }
    rest.orderIdList = splitSpaceComma(orderIdList);
    rest.refundIdList = splitSpaceComma(refundIdList);
    return { ...rest, pageNo: current };
  };

  return (
    <div>
      <TableList
        headerTitle="超售后账单"
        formRef={proTableFormRef}
        actionRef={actionRef}
        columnEmptyText={NOVALUE}
        columns={columns(columnsProps)}
        scroll={{ x: "max-content", y: "calc(100vh - 350px)" }}
        form={{
          ...Protable_FROM_CONFIG,
          initialValues: {}
        }}
        api={apis.getFinanceSupplierAfterSalesUploadPage}
        downloadApi={apis.downloadAfterSales}
        preFetch={handlePageParmas}
        paramsChange={setPageParmas}
        toolbar={{
          actions: [
            <>
              <Button type="primary" onClick={() => setshowExcelUpload(true)} size="small">
                上传
              </Button>
            </>
          ]
        }}
        rowKey={"refundId"}
      />
      {showExcelUpload && <UploadModal {...UploadExcelModalProps} />}
    </div>
  );
};

const AliveRecord = () => (
  <KeepAlive name={PageUrl}>
    <Res />
  </KeepAlive>
);
export default React.memo(AliveRecord);
