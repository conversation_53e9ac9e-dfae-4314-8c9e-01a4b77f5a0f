import { Select } from "antd";
import { filterOptionLabel, returnEllipsisTooltip } from "@/utils/index";
import { NOVALUE, SIZE } from "@/utils/constant";

const columns_search = ({ recoverOptions }: any): Array<TableListItem> => {
  return [
    {
      title: "退款成功时间",
      dataIndex: "gmtRefundSuccessTime",
      valueType: "dateRange",
      fieldProps: {
        allowClear: true
      },
      hideInTable: true
    },
    {
      title: "交易完成时间",
      dataIndex: "gmtTradeSuccessTime",
      valueType: "dateRange",
      fieldProps: {
        allowClear: true
      },
      hideInTable: true
    },

    {
      title: "订单号",
      dataIndex: "orderIdList",
      hideInSearch: false,
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "商品名称",
      dataIndex: "itemTitle",
      hideInSearch: false,
      hideInTable: true
    },
    {
      title: "退款单号",
      dataIndex: "refundIdList",
      hideInSearch: false,
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "是否追款",
      dataIndex: "recover",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return (
          <Select
            placeholder="请选择"
            style={{ minWidth: 120, textAlign: "left" }}
            size={SIZE}
            options={recoverOptions}
            filterOption={filterOptionLabel}
            showSearch
            allowClear
          />
        );
      }
    }
  ];
};
export const columns = ({ recoverOptions, recoverObj }: any): Array<TableListItem> => {
  return [
    ...columns_search({ recoverOptions }),
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      editable: false,
      hideInSearch: true,
      fixed: "left",
      render: (_, record, index) => {
        return index + 1;
      }
    },
    { title: "订单号", dataIndex: "orderId", width: 100, hideInSearch: true },
    {
      title: "商品名称",
      dataIndex: "itemTitle",
      width: 160,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "商品数量", dataIndex: "itemNumStr", width: 100, hideInSearch: true },
    {
      title: "退款单号",
      dataIndex: "refundId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "退款申请时间", dataIndex: "gmtRefundSubmitTime", width: 100, hideInSearch: true },
    { title: "退款成功时间", dataIndex: "gmtRefundSuccessTime", width: 100, hideInSearch: true },
    { title: "交易完成时间", dataIndex: "gmtTradeSuccessTime", width: 100, hideInSearch: true },
    {
      title: "是否追款",
      dataIndex: "recover",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: recoverObj[text] || NOVALUE });
      }
    },
    { title: "用户实付退款", dataIndex: "actualPayAmountStr", width: 100, hideInSearch: true },
    { title: "平台补贴退款", dataIndex: "platformAllowanceRefundAmountStr", width: 100, hideInSearch: true },
    { title: "达人佣金追回", dataIndex: "anchorHongBaoAmountStr", width: 100, hideInSearch: true },
    { title: "达人佣金追回时间", dataIndex: "gmtAnchorHongBaoComplete", width: 100, hideInSearch: true },
    { title: "团长佣金追回", dataIndex: "activityUserAmountStr", width: 100, hideInSearch: true },
    { title: "团长佣金追回时间", dataIndex: "gmtActivityUserComplete", width: 100, hideInSearch: true },
    { title: "快赚客佣金追回", dataIndex: "kzkAmountStr", width: 100, hideInSearch: true },
    { title: "快赚客佣金追回时间", dataIndex: "gmtKzkComplete", width: 100, hideInSearch: true },
    { title: "服务商佣金追回", dataIndex: "serviceAmountStr", width: 100, hideInSearch: true },
    { title: "服务商佣金追回时间", dataIndex: "gmtServiceComplete", width: 100, hideInSearch: true },
    { title: "平台技术服务费追回", dataIndex: "platformAmountStr", width: 100, hideInSearch: true },
    { title: "平台技术服务费追回时间", dataIndex: "gmtPlatformComplete", width: 100, hideInSearch: true },
    { title: "二创作者追回金额", dataIndex: "secondCreatorsAmountStr", width: 100, hideInSearch: true },
    { title: "二创作者追回状态", dataIndex: "secondCreatorsState", width: 100, hideInSearch: true },
    { title: "二创作者追回时间", dataIndex: "gmtSecondCreators", width: 100, hideInSearch: true },
    { title: "二创机构追回金额", dataIndex: "secondOrganicAmountStr", width: 100, hideInSearch: true },
    { title: "二创机构追回状态", dataIndex: "secondOrganicState", width: 100, hideInSearch: true },
    { title: "二创机构追回时间", dataIndex: "gmtSecondOrganic", width: 100, hideInSearch: true },
    { title: "二级团长追回金额", dataIndex: "secondHeadAmountStr", width: 100, hideInSearch: true },
    { title: "二级团长追回状态", dataIndex: "secondHeadState", width: 100, hideInSearch: true },
    { title: "二级团长追回时间", dataIndex: "gmtSecondHead", width: 100, hideInSearch: true },
    { title: "MCN机构ID", dataIndex: "mcnOrgId", width: 100, hideInSearch: true },
    { title: "MCN机构追回金额", dataIndex: "mcnOrgAmountStr", width: 100, hideInSearch: true },
    { title: "MCN机构追回状态", dataIndex: "mcnOrgState", width: 100, hideInSearch: true },
    { title: "MCN机构追回时间", dataIndex: "gmtMcnOrg", width: 100, hideInSearch: true }
  ].map(item => {
    const { title } = item;
    return {
      ...item,
      align: "center",
      title: returnEllipsisTooltip({ title })
    };
  });
};
