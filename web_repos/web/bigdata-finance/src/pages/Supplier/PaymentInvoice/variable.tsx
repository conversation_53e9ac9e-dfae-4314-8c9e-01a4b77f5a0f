import { returnEllipsisTooltip } from "@/utils/index";
export const advance_columns_search = ({}: any): TableListItem[] => {
  return [
    {
      title: "基本信息",
      valueType: "group",
      colProps: { span: 24 },
      columns: [
        {
          title: "MCN机构ID",
          dataIndex: "mcnIdList",
          hideInSearch: false,
          hideInTable: true,
          fieldProps: {
            placeholder: "多个使用英文逗号/空格"
          }
        }
      ]
    }
  ];
};
const columns_search = ({}: any): Array<TableListItem> => {
  return [
    {
      title: "订单创建时间",
      dataIndex: "gmtOrderCreate",
      hideInSearch: false,
      hideInTable: true,
      valueType: "dateRange",
      fieldProps: {
        allowClear: true
      }
    },
    {
      title: "实际结算时间",
      dataIndex: "gmtActualSettlement",
      hideInSearch: false,
      hideInTable: true,
      valueType: "dateRange",
      fieldProps: {
        allowClear: true
      }
    },
    {
      title: "商家ID",
      dataIndex: "sellerIdList",
      hideInSearch: false,
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "订单号",
      dataIndex: "orderIdList",
      hideInSearch: false,
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "商品ID",
      dataIndex: "itemIdList",
      hideInSearch: false,
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "商品名称",
      dataIndex: "itemTitle",
      hideInSearch: false,
      hideInTable: true
    },

    {
      title: "达人ID",
      dataIndex: "pidList",
      hideInSearch: false,
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    }
  ];
};
export const columns = ({}: any): Array<TableListItem> => {
  return [
    ...columns_search({}),
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      editable: false,
      hideInSearch: true,
      fixed: "left",
      render: (_, record, index) => {
        return index + 1;
      }
    },
    { title: "商家ID", dataIndex: "sellerId", width: 100, hideInSearch: true },
    { title: "订单号", dataIndex: "orderId", width: 100, hideInSearch: true },
    { title: "商品ID", dataIndex: "itemId", width: 100, hideInSearch: true },
    {
      title: "商品名称",
      dataIndex: "itemTitle",
      width: 160,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "商品数量", dataIndex: "itemNumStr", width: 100, hideInSearch: true },
    { title: "订单创建时间", dataIndex: "gmtOrderCreate", width: 130, hideInSearch: true },
    { title: "订单实付(元)", dataIndex: "actualAmountStr", width: 100, hideInSearch: true },
    { title: "政府补贴", dataIndex: "governmentSubsidyStr", width: 100, hideInSearch: true },
    { title: "支付营销补贴", dataIndex: "paymentMarketingSubsidyStr", width: 100, hideInSearch: true },
    { title: "平台补贴", dataIndex: "platformSubsidyAmtStr", width: 100, hideInSearch: true },
    { title: "主播补贴", dataIndex: "anchorSubsidyAmtStr", width: 100, hideInSearch: true },
    {
      title: "主播补贴明细",
      dataIndex: "anchorSubsidyDetailAmt",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "合计收入(元)", dataIndex: "totalIncomeStr", width: 100, hideInSearch: true },
    { title: "订单退款(元)", dataIndex: "refundAmtStr", width: 100, hideInSearch: true },
    { title: "支付营销回退(元)", dataIndex: "paymentMarketingRefundStr", width: 100, hideInSearch: true },
    { title: "技术服务费(元)", dataIndex: "technicalServiceFeeStr", width: 120, hideInSearch: true },
    { title: "达人ID", dataIndex: "pid", width: 100, hideInSearch: true },
    { title: "MCN机构ID", dataIndex: "mcnOrgId", width: 100, hideInSearch: true },
    { title: "达人佣金(元)", dataIndex: "pidCommissionStr", width: 100, hideInSearch: true },
    { title: "团长ID", dataIndex: "leaderId", width: 100, hideInSearch: true },
    { title: "团长佣金(元)", dataIndex: "leaderCommissionStr", width: 100, hideInSearch: true },
    {
      title: "佣金模式",
      dataIndex: "commissionModel",
      width: 100,
      hideInSearch: true
    },
    { title: "快赚客ID", dataIndex: "fastUserId", width: 100, hideInSearch: true },
    { title: "快赚客佣金(元)", dataIndex: "fastUserCommissionStr", width: 120, hideInSearch: true },
    { title: "服务商ID", dataIndex: "facilitatorId", width: 100, hideInSearch: true },
    { title: "服务商佣金(元)", dataIndex: "facilitatorCommissionStr", width: 120, hideInSearch: true },
    { title: "分账基数", dataIndex: "routingWikidata", width: 100, hideInSearch: true },
    { title: "其他收费", dataIndex: "otherChargesStr", width: 100, hideInSearch: true },
    {
      title: "其他收费明细",
      dataIndex: "otherChargesDetail",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "合计支出(元)", dataIndex: "totalExpenditureStr", width: 100, hideInSearch: true },
    { title: "实际结算金额(元)", dataIndex: "actualSettlementAmountStr", width: 140, hideInSearch: true },
    { title: "实际结算时间", dataIndex: "gmtActualSettlement", width: 130, hideInSearch: true },
    {
      title: "结算规则",
      dataIndex: "settlementRules",
      width: 110,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "资金渠道",
      dataIndex: "moneyChannel",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "账户名称", dataIndex: "accountName", width: 120, hideInSearch: true },
    {
      title: "结算商户号",
      dataIndex: "settlementAccountNum",
      width: 130,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "备注",
      dataIndex: "remark",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "消费金信息",
      dataIndex: "cosumerAmtInfo",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    }
  ].map(item => {
    const { title } = item;
    return {
      ...item,
      align: "center",
      title: returnEllipsisTooltip({ title })
    };
  });
};
