import React, { useRef, useState, useMemo } from "react";
import { Button } from "antd";
import { columns, advance_columns_search } from "./variable";
import { NOVALUE, Protable_FROM_CONFIG } from "@/utils/constant";
import KeepAlive from "react-activation";
import { splitSpaceComma } from "@/utils";
import UploadModal from "@/components/UploadModal";
import apis from "@/services/supplier/paymentinvoice";
import TableList from "@/components/TableList";
import dayjs from "dayjs";
import useCommonOptions from "@/hooks/useCommonOptions";
const PageUrl = "/supplier/paymentinvoice";
const Res: React.FC = () => {
  const [commissionModelOptions, commissionModelObj] = useCommonOptions({ dimName: "供应商对账-佣金模式" });
  const proTableFormRef = useRef<any>();
  const actionRef = useRef<any>();
  const [pageParmas, setPageParmas] = useState<any>({});
  const [showExcelUpload, setshowExcelUpload] = useState(false);

  const columnsProps = {
    commissionModelOptions,
    commissionModelObj
  };
  const advanceColumnsProps = {
    commissionModelOptions
  };
  // 上传excel modal属性
  const UploadExcelModalProps = useMemo(() => {
    return {
      title: "上传货款账单",
      visible: showExcelUpload,
      targetType: 0,
      api: apis.addSupplierPaymentFromExcel,
      message: (
        <>
          <span>1. 模板中黄色背景字段为必填项</span>
          <br />
          <span>2. 同一订单多次上传，取最新上传订单</span>
          <br />
        </>
      ),
      tempUrl: "https://s.xinc818.com/files/webcim71ky79xbzbhdh/商家-货款账单上传模板.xlsx",
      fresh: () => {},
      close: () => {
        setshowExcelUpload(false);
      }
    };
  }, [showExcelUpload, pageParmas]);

  // 处理请求参数
  const handlePageParmas = (params: any) => {
    const { current, gmtOrderCreate, gmtActualSettlement, sellerIdList, orderIdList, itemIdList, pidList, mcnIdList, ...rest } = params;
    if (gmtOrderCreate) {
      const [gmtOrderCreateStart, gmtOrderCreateEnd] = gmtOrderCreate;
      rest.gmtOrderCreateStart = dayjs(gmtOrderCreateStart).format("YYYY-MM-DD");
      rest.gmtOrderCreateEnd = dayjs(gmtOrderCreateEnd).format("YYYY-MM-DD");
    }
    if (gmtActualSettlement) {
      const [gmtActualSettlementStart, gmtActualSettlementEnd] = gmtActualSettlement;
      rest.gmtActualSettlementStart = dayjs(gmtActualSettlementStart).format("YYYY-MM-DD");
      rest.gmtActualSettlementEnd = dayjs(gmtActualSettlementEnd).format("YYYY-MM-DD");
    }
    rest.sellerIdList = splitSpaceComma(sellerIdList);
    rest.orderIdList = splitSpaceComma(orderIdList);
    rest.itemIdList = splitSpaceComma(itemIdList);
    rest.pidList = splitSpaceComma(pidList);
    rest.mcnIdList = splitSpaceComma(mcnIdList);
    return { ...rest, pageNo: current };
  };

  return (
    <div>
      <TableList
        headerTitle="货款账单"
        formRef={proTableFormRef}
        actionRef={actionRef}
        columnEmptyText={NOVALUE}
        advanceColumns={advance_columns_search(advanceColumnsProps)}
        columns={columns(columnsProps)}
        scroll={{ x: "max-content", y: "calc(100vh - 330px)" }}
        form={{
          ...Protable_FROM_CONFIG,
          initialValues: {}
        }}
        api={apis.getFinanceSupplierPaymentStatementUploadPage}
        downloadApi={apis.downloadSupplierPaymentUpload}
        preFetch={handlePageParmas}
        paramsChange={setPageParmas}
        toolbar={{
          actions: [
            <>
              <Button type="primary" onClick={() => setshowExcelUpload(true)} size="small">
                上传
              </Button>
            </>
          ]
        }}
        rowKey={"orderId"}
      />
      {showExcelUpload && <UploadModal {...UploadExcelModalProps} />}
    </div>
  );
};

const AliveRecord = () => (
  <KeepAlive name={PageUrl}>
    <Res />
  </KeepAlive>
);
export default React.memo(AliveRecord);
