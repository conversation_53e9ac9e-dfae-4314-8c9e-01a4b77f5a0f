import React, { useRef, useState, useMemo } from "react";
import { Button } from "antd";
import { columns, advance_columns_search } from "./variable";
import { NOVALUE, Protable_FROM_CONFIG } from "@/utils/constant";
import KeepAlive from "react-activation";
import { splitSpaceComma } from "@/utils";
import UploadModal from "@/components/UploadModal";
import apis from "@/services/supplier/sale-detail";
import dayjs from "dayjs";
import TableList from "@/components/TableList";
import useCommonOptions from "@/hooks/useCommonOptions";
const PageUrl = "/supplier/saledetail";
const Res: React.FC = () => {
  const proTableFormRef = useRef<any>();
  const actionRef = useRef<any>();
  const [channelListOptions] = useCommonOptions({ dimName: "供应商交易明细-渠道" });
  const [pageParmas, setPageParmas] = useState<any>({});
  const [showExcelUpload, setshowExcelUpload] = useState(false);

  const [editProps, setEditProps] = useState<any>({
    open: false,
    onClose: () => {
      setEditProps({ ...editProps, open: false });
      actionRef.current?.reload();
    }
  });

  const columnsProps = {};
  const advanceColumnsProps = { channelListOptions };

  // 上传excel modal属性
  const UploadExcelModalProps = useMemo(() => {
    return {
      title: "上传交易明细",
      visible: showExcelUpload,
      targetType: 0,
      api: apis.addSupplierTranDetailFromExcel,
      message: (
        <>
          <span>1. 请参考模板中数据格式进行上传</span>
          <br />
          <span>2. 同一订单多次上传，取最新上传订单</span>
          <br />
        </>
      ),
      tempUrl: "https://s.xinc818.com/files/webcim3o0i0gl78d2lm/交易明细上传模板.xlsx",
      fresh: () => {},
      close: () => {
        setshowExcelUpload(false);
      }
    };
  }, [showExcelUpload, pageParmas]);

  // 处理请求参数
  const handlePageParmas = (params: any) => {
    const { current, orderCreationTime, orderPaymentTime, orderNumberList, itemIdList, cpsInfluencerIdList, channelList, ...rest } = params;
    if (orderCreationTime) {
      const [orderCreationTimeStart, orderCreationTimeEnd] = orderCreationTime;
      rest.orderCreationTimeStart = dayjs(orderCreationTimeStart).format("YYYY-MM-DD");
      rest.orderCreationTimeEnd = dayjs(orderCreationTimeEnd).format("YYYY-MM-DD");
    }
    if (orderPaymentTime) {
      const [orderPaymentTimeStart, orderPaymentTimeEnd] = orderPaymentTime;
      rest.orderPaymentTimeStart = dayjs(orderPaymentTimeStart).format("YYYY-MM-DD");
      rest.orderPaymentTimeEnd = dayjs(orderPaymentTimeEnd).format("YYYY-MM-DD");
    }
    if (channelList) {
      rest.channelList = [channelList];
    }
    rest.orderNumberList = splitSpaceComma(orderNumberList);
    rest.itemIdList = splitSpaceComma(itemIdList);
    rest.cpsInfluencerIdList = splitSpaceComma(cpsInfluencerIdList);
    return { ...rest, pageNo: current };
  };

  return (
    <div>
      <TableList
        headerTitle="交易明细"
        formRef={proTableFormRef}
        actionRef={actionRef}
        columnEmptyText={NOVALUE}
        advanceColumns={advance_columns_search(advanceColumnsProps)}
        columns={columns(columnsProps)}
        scroll={{ x: "max-content", y: "calc(100vh - 350px)" }}
        form={{
          ...Protable_FROM_CONFIG,
          initialValues: {}
        }}
        api={apis.getSupplierTranDetailUploadPage}
        downloadApi={apis.downloadSupplierTranDetailUpload}
        preFetch={handlePageParmas}
        paramsChange={setPageParmas}
        toolbar={{
          actions: [
            <>
              <Button type="primary" onClick={() => setshowExcelUpload(true)} size="small">
                上传
              </Button>
            </>
          ]
        }}
        rowKey={"refundId"}
      />
      {showExcelUpload && <UploadModal {...UploadExcelModalProps} />}
    </div>
  );
};

const AliveRecord = () => (
  <KeepAlive name={PageUrl}>
    <Res />
  </KeepAlive>
);
export default React.memo(AliveRecord);
