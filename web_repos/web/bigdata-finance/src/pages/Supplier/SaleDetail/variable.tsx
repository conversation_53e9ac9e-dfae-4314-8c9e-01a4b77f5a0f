import { Select } from "antd";
import { filterOptionLabel, returnEllipsisTooltip } from "@/utils/index";
import { SIZE } from "@/utils/constant";
export const advance_columns_search = ({ channelListOptions }: any): TableListItem[] => {
  return [
    {
      title: "基本信息",
      valueType: "group",
      colProps: { span: 24 },
      columns: [
        {
          title: "渠道",
          dataIndex: "channelList",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem: () => {
            return <Select placeholder="请选择" size={SIZE} options={channelListOptions} filterOption={filterOptionLabel} showSearch allowClear />;
          }
        }
      ]
    }
  ];
};
const columns_search = ({}: any): Array<TableListItem> => {
  return [
    {
      title: "订单创建时间",
      dataIndex: "orderCreationTime",
      valueType: "dateRange",
      fieldProps: {
        allowClear: true
      },
      hideInTable: true
    },
    {
      title: "订单支付时间",
      dataIndex: "orderPaymentTime",
      valueType: "dateRange",
      fieldProps: {
        allowClear: true
      },
      hideInTable: true
    },

    {
      title: "订单号",
      dataIndex: "orderNumberList",
      hideInSearch: false,
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "商品ID",
      dataIndex: "itemIdList",
      hideInSearch: false,
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "商品名称",
      dataIndex: "itemName",
      hideInSearch: false,
      hideInTable: true
    },
    {
      title: "CPS达人ID",
      dataIndex: "cpsInfluencerIdList",
      hideInSearch: false,
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "CPS达人昵称",
      dataIndex: "cpsInfluencerNickname",
      hideInSearch: false,
      hideInTable: true
    }
  ];
};
export const columns = ({ recoverOptions }: any): Array<TableListItem> => {
  return [
    ...columns_search({ recoverOptions }),
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      editable: false,
      hideInSearch: true,
      fixed: "left",
      render: (_, record, index) => {
        return index + 1;
      }
    },
    {
      title: "订单号",
      dataIndex: "orderNumber",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "订单创建时间", dataIndex: "orderCreationTime", width: 100, hideInSearch: true },
    { title: "订单支付时间", dataIndex: "orderPaymentTime", width: 100, hideInSearch: true },
    { title: "预售定金支付时间", dataIndex: "preSaleDepositPaymentTime", width: 100, hideInSearch: true },
    {
      title: "订单状态",
      dataIndex: "orderStatus",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "实付款", dataIndex: "actualPaymentStr", width: 100, hideInSearch: true },
    { title: "快递费", dataIndex: "shippingFeeStr", width: 100, hideInSearch: true },
    { title: "店铺优惠", dataIndex: "storeDiscountStr", width: 100, hideInSearch: true },
    { title: "平台补贴", dataIndex: "platformSubsidyStr", width: 100, hideInSearch: true },
    { title: "主播补贴", dataIndex: "anchorSubsidyStr", width: 100, hideInSearch: true },
    {
      title: "渠道",
      dataIndex: "channel",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "支付方式",
      dataIndex: "paymentMethod",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "成交数量", dataIndex: "transactionQuantityStr", width: 100, hideInSearch: true },
    {
      title: "买家留言",
      dataIndex: "buyerMessage",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "账号类型",
      dataIndex: "accountType",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "账号明细",
      dataIndex: "accountDetails",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "订单备注",
      dataIndex: "orderRemarks",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "旗帜颜色",
      dataIndex: "flagColor",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "售后状态",
      dataIndex: "afterSalesStatus",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "活动订单",
      dataIndex: "activityOrder",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "预售/承诺发货时间", dataIndex: "preSalePromisedDeliveryTime", width: 100, hideInSearch: true },
    {
      title: "商品名称",
      dataIndex: "itemName",
      width: 160,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品ID",
      dataIndex: "itemId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品规格",
      dataIndex: "itemSpec",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "SKU编码",
      dataIndex: "skuCode",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "商品单价", dataIndex: "itemUnitPriceStr", width: 100, hideInSearch: true },
    {
      title: "CPS达人ID",
      dataIndex: "cpsInfluencerId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "CPS达人昵称",
      dataIndex: "cpsInfluencerNickname",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "预估推广佣金", dataIndex: "estimatedPromotionCommissionStr", width: 100, hideInSearch: true },
    { title: "预估推广者分佣比例", dataIndex: "estimatedPromoterCommissionRatio", width: 100, hideInSearch: true },
    {
      title: "团长ID",
      dataIndex: "groupLeaderId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "团长昵称",
      dataIndex: "groupLeaderNickname",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "快赚客ID",
      dataIndex: "quickEarnerId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "快赚客昵称",
      dataIndex: "quickEarnerNickname",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "授权推广者ID",
      dataIndex: "authorizedPromoterId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "授权推广者昵称",
      dataIndex: "authorizedPromoterNickname",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "收货人姓名",
      dataIndex: "consigneeName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "收货人电话",
      dataIndex: "consigneePhone",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "收货地址-省",
      dataIndex: "consigneeProvince",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "收货地址-市",
      dataIndex: "consigneeCity",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "收货地址-区",
      dataIndex: "consigneeDistrict",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "收货地址-街道",
      dataIndex: "consigneeStreet",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "收货地址",
      dataIndex: "consigneeAddress",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "发货时间", dataIndex: "deliveryTime", width: 100, hideInSearch: true },
    {
      title: "快递公司",
      dataIndex: "courierCompany",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "快递单号",
      dataIndex: "courierTrackingNumber",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "物流信息",
      dataIndex: "logisticsInformation",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "可配送物流公司",
      dataIndex: "deliverableLogisticsCompanies",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "买家催发货",
      dataIndex: "buyerUrgeDelivery",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "超时未发货",
      dataIndex: "overdueUnshipped",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "延迟发货报备类型",
      dataIndex: "delayedDeliveryReportType",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "实名姓名",
      dataIndex: "realName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "门店ID",
      dataIndex: "storeId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "门店名称",
      dataIndex: "payDateStr",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "门店地址",
      dataIndex: "storeName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "车型信息",
      dataIndex: "vehicleInformation",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    }
  ].map(item => {
    const { title } = item;
    return {
      ...item,
      align: "center",
      title: returnEllipsisTooltip({ title })
    };
  });
};
