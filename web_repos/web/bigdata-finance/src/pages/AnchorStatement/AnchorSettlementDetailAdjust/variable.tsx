import { filterOptionLabel, returnEllipsisTooltip } from "@/utils";
import { Select, DatePicker, Space, Button, message, Popconfirm } from "antd";
import { ProFormDigitRange } from "@ant-design/pro-components";
import AnchorSelect from "@/components/AnchorSelect";
import CategorySelect from "../AnchorSettlementDetail/components/CategorySelect";
import CategoryNameSelect from "../AnchorSettlementDetail/components/CategoryNameSelect";
import { NOVALUE, SIZE } from "@/utils/constant";
import EditInputNumber from "@/components/EditInputNumber";
import SupplierNameSelect from "@/components/SupplierNameSelect";
const columns_search = ({ dataTypeOptions, shopTypeOptions, modelNameLv1Options }: any) => {
  return [
    {
      // 日区间
      title: "直播日期",
      dataIndex: "liveDate",
      hideInSearch: false,
      hideInTable: true,
      valueType: "dateRange"
    },
    {
      // 月份区间
      title: "财务结算月份",
      dataIndex: "expectSettleMonth",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return <DatePicker.RangePicker picker="month" allowClear />;
      }
    },
    {
      // 月份区间
      title: "订单结算月份",
      dataIndex: "settleMonth",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return <DatePicker.RangePicker picker="month" allowClear />;
      }
    },
    {
      // 输入+下拉
      title: "主播",
      dataIndex: "anchorName",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem() {
        return <AnchorSelect showSearch allowClear />;
      }
    },
    {
      // 下拉
      title: "模式",
      dataIndex: "modelNameLv1",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem() {
        return <Select placeholder="请选择" options={modelNameLv1Options} filterOption={filterOptionLabel} showSearch allowClear />;
      }
    },
    {
      // 输入搜索，支持逗号分隔搜索多个
      title: "商品ID",
      dataIndex: "itemIdList",
      hideInSearch: false,
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      // 输入
      title: "商品名称",
      dataIndex: "itemName",
      hideInSearch: false,
      hideInTable: true
    },
    {
      // 输入+下拉
      title: "品类",
      dataIndex: "categoryName",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem() {
        return <CategoryNameSelect placeholder="请选择" showSearch allowClear />;
      }
    },
    {
      // 输入+下拉
      title: "一级类目",
      dataIndex: "catLv1Name",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem() {
        return <CategorySelect placeholder="请选择" showSearch allowClear />;
      }
    },
    {
      // 输入
      title: "品牌",
      dataIndex: "brandName",
      hideInSearch: false,
      hideInTable: true
    },
    {
      // 输入
      title: "店铺ID",
      dataIndex: "shopId",
      hideInSearch: false,
      hideInTable: true
    },
    {
      // 输入
      title: "店铺名称",
      dataIndex: "shopName",
      hideInSearch: false,
      hideInTable: true
    },
    {
      // 下拉
      title: "店铺性质",
      dataIndex: "shopTypeList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem() {
        return (
          <Select
            placeholder="请选择"
            options={shopTypeOptions}
            filterOption={filterOptionLabel}
            showSearch
            allowClear
            maxTagCount="responsive"
            mode="multiple"
          />
        );
      }
    },
    {
      title: "供应商名称",
      dataIndex: "supplierName",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem() {
        return <SupplierNameSelect />;
      }
    },
    {
      // 输入
      title: "我司合同主体",
      dataIndex: "companyName",
      hideInSearch: false,
      hideInTable: true
    },
    {
      // 范围筛选
      title: "结算商品数量",
      dataIndex: "settleItemNum",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem() {
        return <ProFormDigitRange fieldProps={{ min: 0, controls: false }} separator="-" placeholder={["最小值", "最大值"]} />;
      }
    },
    {
      // 下拉
      title: "数据状态",
      dataIndex: "dataType",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem() {
        return (
          <Select
            placeholder="请选择"
            options={dataTypeOptions}
            filterOption={filterOptionLabel}
            showSearch
            allowClear
            // maxTagCount="responsive"
            // mode="multiple"
          />
        );
      }
    }
  ];
};
export const columns = ({
  handleDelete,
  isNeedRedPunchObj,
  shopTypeOptions,
  shopTypeObj,
  platTypeObj,
  modelNameLv1Options,
  dataTypeObj,
  dataTypeOptions
}: any): Array<TableListItem> => {
  return [
    ...columns_search({
      shopTypeOptions,
      modelNameLv1Options,
      dataTypeOptions
    }),
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      hideInSearch: true,
      fixed: "left",
      editable: false,
      render: (_, record, index) => {
        return index + 1;
      }
    },
    { title: "财务结算月份", editable: false, dataIndex: "expectSettleMonth", width: 100, hideInSearch: true },
    { title: "订单结算月份", editable: false, dataIndex: "settleMonth", width: 100, hideInSearch: true },
    { title: "主播ID", editable: false, dataIndex: "anchorId", width: 100, hideInSearch: true },
    { title: "主播", editable: false, dataIndex: "anchorName", width: 100, hideInSearch: true },
    { title: "直播日期", editable: false, dataIndex: "liveDate", width: 100, hideInSearch: true },
    { title: "模式", editable: false, dataIndex: "modelNameLv1", width: 100, hideInSearch: true },
    { title: "二级模式", editable: false, dataIndex: "modelNameLv2", width: 100, hideInSearch: true },
    { title: "商品ID", editable: false, dataIndex: "itemId", width: 100, hideInSearch: true },
    {
      title: "商品名称",
      editable: false,
      dataIndex: "itemName",
      width: 200,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "平台",
      editable: false,
      dataIndex: "platType",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: platTypeObj[text] || NOVALUE });
      }
    },
    { title: "品类", editable: false, dataIndex: "categoryName", width: 100, hideInSearch: true },
    {
      title: "一级类目",
      editable: false,
      dataIndex: "catLv1Name",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "二级类目",
      editable: false,
      dataIndex: "catLv2Name",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "三级类目",
      editable: false,
      dataIndex: "catLv3Name",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "四级类目",
      editable: false,
      dataIndex: "catLv4Name",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "品牌",
      editable: false,
      dataIndex: "brandName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "店铺ID", editable: false, dataIndex: "shopId", width: 100, hideInSearch: true },
    {
      title: "店铺名称",
      editable: false,
      dataIndex: "shopName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "店铺性质",
      editable: false,
      dataIndex: "shopType",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: shopTypeObj[text] || NOVALUE });
      }
    },
    {
      title: "供应商名称",
      editable: false,
      dataIndex: "supplierName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "我司合同主体",
      editable: false,
      dataIndex: "companyName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品编码",
      editable: false,
      dataIndex: "styleCode",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "线上佣金比例", editable: false, dataIndex: "onlineCommissionRate", width: 100, hideInSearch: true },
    {
      title: "发货内容",
      editable: false,
      dataIndex: "deliveryContent",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "结算商品数量",
      editable: true,
      dataIndex: "settleItemNum",
      width: 100,
      hideInSearch: true,
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={1} precision={0} style={{ width: "100%" }} />;
      }
    },
    {
      title: "结算订单数",
      editable: true,
      dataIndex: "settleOrderNum",
      width: 100,
      hideInSearch: true,
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={1} precision={0} style={{ width: "100%" }} />;
      }
    },
    // 分
    {
      title: "结算金额",
      editable: true,
      dataIndex: "settleAmount",
      width: 100,
      hideInSearch: true,
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
      },
      render(_, record) {
        const { settleAmountStr } = record;
        return returnEllipsisTooltip({ title: settleAmountStr });
      }
    },
    {
      title: "超售后数量",
      editable: true,
      dataIndex: "refundItemNum",
      width: 100,
      hideInSearch: true,
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={1} precision={0} style={{ width: "100%" }} />;
      }
    },
    {
      title: "超售后订单数",
      editable: true,
      dataIndex: "refundOrderNum",
      width: 100,
      hideInSearch: true,
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={1} precision={0} style={{ width: "100%" }} />;
      }
    },
    // 分
    {
      title: "超售后金额",
      editable: true,
      dataIndex: "refundAmount",
      width: 100,
      hideInSearch: true,
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
      },
      render(_, record) {
        const { refundAmountStr } = record;
        return returnEllipsisTooltip({ title: refundAmountStr });
      }
    },
    // 分
    {
      title: "线上佣金金额",
      editable: true,
      dataIndex: "onlineCommissionAmount",
      width: 100,
      hideInSearch: true,
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
      },
      render(_, record) {
        const { onlineCommissionAmountStr } = record;
        return returnEllipsisTooltip({ title: onlineCommissionAmountStr });
      }
    },
    // 毫
    {
      title: "主品成本单价",
      editable: true,
      dataIndex: "mainCostPrice",
      width: 100,
      hideInSearch: true,
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
      },
      render(_, record) {
        const { mainCostPriceStr } = record;
        return returnEllipsisTooltip({ title: mainCostPriceStr });
      }
    },
    // 分
    {
      title: "主品成本总金额",
      editable: true,
      dataIndex: "mainCostAmount",
      width: 100,
      hideInSearch: true,
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
      },
      render(_, record) {
        const { mainCostAmountStr } = record;
        return returnEllipsisTooltip({ title: mainCostAmountStr });
      }
    },
    // 毫
    {
      title: "赠品成本单价",
      editable: true,
      dataIndex: "giftCostPrice",
      width: 100,
      hideInSearch: true,
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
      },
      render(_, record) {
        const { giftCostPriceStr } = record;
        return returnEllipsisTooltip({ title: giftCostPriceStr });
      }
    },
    // 分
    {
      title: "赠品成本总金额",
      editable: true,
      dataIndex: "giftCostAmount",
      width: 100,
      hideInSearch: true,
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
      },
      render(_, record) {
        const { giftCostAmountStr } = record;
        return returnEllipsisTooltip({ title: giftCostAmountStr });
      }
    },
    { title: "总成本金额(主品+赠品)", editable: false, dataIndex: "totalCostAmount", width: 100, hideInSearch: true },
    {
      title: "发货快递单数量",
      editable: true,
      dataIndex: "expressNum",
      width: 100,
      hideInSearch: true,
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={1} precision={0} style={{ width: "100%" }} />;
      }
    },
    // 毫
    {
      title: "发货费用单价",
      editable: true,
      dataIndex: "expressPrice",
      width: 100,
      hideInSearch: true,
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
      },
      render(_, record) {
        const { expressPriceStr } = record;
        return returnEllipsisTooltip({ title: expressPriceStr });
      }
    },
    { title: "发货费用合计", editable: false, dataIndex: "expressAmount", width: 100, hideInSearch: true },
    {
      title: "退款订单数量",
      editable: true,
      dataIndex: "refundOperateOrderNum",
      width: 100,
      hideInSearch: true,
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={1} precision={0} style={{ width: "100%" }} />;
      }
    },
    // 分
    {
      title: "销退操作费用单价",
      editable: true,
      dataIndex: "refundOperatePrice",
      width: 100,
      hideInSearch: true,
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
      },
      render(_, record) {
        const { refundOperatePriceStr } = record;
        return returnEllipsisTooltip({ title: refundOperatePriceStr });
      }
    },
    { title: "销退费用合计", editable: false, dataIndex: "refundOperateAmount", width: 100, hideInSearch: true },
    { title: "总发货费用", editable: false, dataIndex: "totalExpressAmount", width: 100, hideInSearch: true },
    {
      title: "达人佣金追回",
      editable: true,
      dataIndex: "backCommissionAmount",
      width: 100,
      hideInSearch: true,
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={100} precision={4} style={{ width: "100%" }} />;
      },
      render(_, record) {
        const { backCommissionAmountStr } = record;
        return returnEllipsisTooltip({ title: backCommissionAmountStr });
      }
    },
    // 分
    {
      title: "达人佣金未追回",
      editable: true,
      dataIndex: "unbackCommissionAmount",
      width: 100,
      hideInSearch: true,
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
      },
      render(_, record) {
        const { unbackCommissionAmountStr } = record;
        return returnEllipsisTooltip({ title: unbackCommissionAmountStr });
      }
    },
    { title: "线下收/返佣比例", editable: false, dataIndex: "offlineCommissionRate", width: 100, hideInSearch: true },
    { title: "线下收/返佣金额", editable: false, dataIndex: "offlineCommissionAmount", width: 100, hideInSearch: true },
    { title: "实际佣金", editable: false, dataIndex: "totalCommissionAmount", width: 100, hideInSearch: true },
    // 分
    {
      title: "快手技术服务费",
      editable: true,
      dataIndex: "settleServiceAmount",
      width: 100,
      hideInSearch: true,
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
      },
      render(_, record) {
        const { settleServiceAmountStr } = record;
        return returnEllipsisTooltip({ title: settleServiceAmountStr });
      }
    },
    // 分
    {
      title: "快手技术服务费追回",
      editable: true,
      dataIndex: "refundServiceAmount",
      width: 100,
      hideInSearch: true,
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
      },
      render(_, record) {
        const { refundServiceAmountStr } = record;
        return returnEllipsisTooltip({ title: refundServiceAmountStr });
      }
    },
    {
      title: "线下赔付金额",
      editable: true,
      dataIndex: "compensateAmount",
      width: 100,
      hideInSearch: true,
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={100} precision={4} style={{ width: "100%" }} />;
      },
      render(_, record) {
        const { compensateAmountStr } = record;
        return returnEllipsisTooltip({ title: compensateAmountStr });
      }
    },
    {
      title: "线下返款金额",
      editable: true,
      dataIndex: "backAmount",
      width: 100,
      hideInSearch: true,
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={100} precision={4} style={{ width: "100%" }} />;
      },
      render(_, record) {
        const { backAmountStr } = record;
        return returnEllipsisTooltip({ title: backAmountStr });
      }
    },
    {
      title: "返款承担方",
      editable: false,
      dataIndex: "backFlag",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "利润", editable: false, dataIndex: "profitAmount", width: 100, hideInSearch: true },
    { title: "线上利润", editable: false, dataIndex: "onlineProfitAmount", width: 100, hideInSearch: true },
    { title: "线下利润", editable: false, dataIndex: "offlineProfitAmount", width: 100, hideInSearch: true },
    // 分
    {
      title: "年框扣费金额",
      editable: true,
      dataIndex: "yearlyDeductAmount",
      width: 100,
      hideInSearch: true,
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
      },
      render(_, record) {
        const { yearlyDeductAmountStr } = record;
        return returnEllipsisTooltip({ title: yearlyDeductAmountStr });
      }
    },
    { title: "净利润", editable: false, dataIndex: "grossProfitAmount", width: 100, hideInSearch: true },
    { title: "公司利润", editable: false, dataIndex: "companyGrossAmount", width: 100, hideInSearch: true },
    { title: "主播利润", editable: false, dataIndex: "anchorGrossAmount", width: 100, hideInSearch: true },
    { title: "主播净利润分成比例", editable: false, dataIndex: "anchorProfitRate", width: 100, hideInSearch: true },
    { title: "商家利润分成比例", editable: false, dataIndex: "shopProfitRate", width: 100, hideInSearch: true },
    {
      title: "备注",
      editable: true,
      fieldProps: { size: SIZE },
      dataIndex: "remark",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "合同ID",
      editable: false,
      dataIndex: "contractId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "是否红冲",
      editable: false,
      dataIndex: "redInkType",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: isNeedRedPunchObj[text] || NOVALUE });
      }
    },
    { title: "红冲原月份", editable: false, dataIndex: "redInkOriginalMonth", width: 100, hideInSearch: true },
    {
      title: "数据状态",
      editable: false,
      dataIndex: "dataType",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: dataTypeObj[text] || NOVALUE });
      }
    },
    { title: "更新时间", editable: false, dataIndex: "modificationTime", width: 100, hideInSearch: true },
    { title: "更新人", editable: false, dataIndex: "modificationBy", width: 100, hideInSearch: true },
    {
      title: "操作",
      width: 100,
      valueType: "option",
      fixed: "right",
      render: (text: string, record: any, index: number, action: any) => {
        const { id, confirmationTime } = record;
        return (
          <Space>
            <Button
              type="link"
              key="edit"
              size={SIZE}
              disabled={!!confirmationTime}
              onClick={() => {
                if (id) {
                  action?.startEditable(id);
                } else {
                  message.info("ID 字段的没有可读可写权限，无法编辑！！！");
                }
              }}
            >
              编辑
            </Button>
            <Popconfirm
              title="是否确认删除?"
              key="delete"
              onConfirm={() => handleDelete(record)}
              onCancel={() => {}}
              okButtonProps={{ size: SIZE }}
              cancelButtonProps={{ size: SIZE }}
            >
              <Button type="link" size={SIZE} disabled={!!confirmationTime}>
                删除
              </Button>
            </Popconfirm>
          </Space>
        );
      }
    }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      ...item,
      align: "center",
      title: returnEllipsisTooltip({ title })
    };
  });
};
