import React, { useState, useRef } from "react";
import { Button, message, Form } from "antd";
import KeepAlive from "react-activation";
import TableList from "@/components/TableList";
import { SIZE } from "@/utils/constant";
import { columns } from "./variable";
import apis from "@/services/anchorStatement/anchorSettlementDetailAdjust";
import { pushExportHistory, splitSpaceComma } from "@/utils";
import useCommonOptions from "@/hooks/useCommonOptions";
import dayjs from "dayjs";
import UploadExcelModal from "@/components/UploadExcelModal";
export const PageUrl = "/anchorstatement/anchorsettlementdetailadjust";
const DetailTable = () => {
  const [editForm] = Form.useForm();

  const [isNeedRedPunchOptions, isNeedRedPunchObj] = useCommonOptions({ dimName: "主播结算明细-是否红冲" });
  const [dataTypeOptions, dataTypeObj] = useCommonOptions({ dimName: "主播结算明细调账-数据状态" });
  const [shopTypeOptions, shopTypeObj] = useCommonOptions({ dimName: "店铺性质" });
  const [platTypeOptions, platTypeObj] = useCommonOptions({ dimName: "数据来源" });
  const [modelNameLv1Options, modelNameLv1Obj] = useCommonOptions({ dimName: "模式" });
  const [confirmStatusOptions, confirmStatusObj] = useCommonOptions({ dimName: "确认状态" });
  const [pageParams, setpageParams] = useState({});
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const [showExcelUpload, setshowExcelUpload] = useState(false);
  const actionRef = useRef<any>();
  const handleDelete = async record => {
    await apis.delete({ ids: [record.id] });
    actionRef.current?.reload();
  };
  const columnsProps = {
    handleDelete,
    isNeedRedPunchOptions,
    isNeedRedPunchObj,
    shopTypeOptions,
    shopTypeObj,
    platTypeOptions,
    platTypeObj,
    modelNameLv1Options,
    modelNameLv1Obj,
    confirmStatusOptions,
    confirmStatusObj,
    dataTypeOptions,
    dataTypeObj
  };
  const editable: any = {
    type: "single",
    form: editForm,
    onSave: async (key, editData) => {
      const { status } = await apis.update(editData);
      editForm.resetFields([key]);
      if (status) {
        message.success("修改成功！");
      }
      actionRef.current?.reload();
    },
    editableKeys,
    onChange: setEditableRowKeys,
    actionRender: (row, config, { save, cancel }) => [save, cancel]
  };

  // 处理请求参数
  const handlePageParmas = (params: any) => {
    const { liveDate, current, settleItemNum, modelNameLv1, itemIdList, expectSettleMonth, settleMonth, ...rest } = params;

    if (liveDate) {
      const [startLiveDate, endLiveDate] = liveDate;
      rest.startLiveDate = dayjs(startLiveDate).format("YYYYMMDD");
      rest.endLiveDate = dayjs(endLiveDate).format("YYYYMMDD");
    }
    if (settleItemNum) {
      const [settleItemNumMin, settleItemNumMax] = settleItemNum;
      rest.settleItemNumMax = settleItemNumMax;
      rest.settleItemNumMin = settleItemNumMin;
    }
    // 结算月份
    if (expectSettleMonth) {
      const [startExpectSettleMonth, endExpectSettleMonth] = expectSettleMonth;
      rest.startExpectSettleMonth = dayjs(startExpectSettleMonth).format("YYYYMM");
      rest.endExpectSettleMonth = dayjs(endExpectSettleMonth).format("YYYYMM");
    }
    if (settleMonth) {
      const [startSettleMonth, endSettleMonth] = settleMonth;
      rest.startSettleMonth = dayjs(startSettleMonth).format("YYYYMM");
      rest.endSettleMonth = dayjs(endSettleMonth).format("YYYYMM");
    }
    // 模式
    if (modelNameLv1 !== undefined) {
      rest.modelNameLv1 = modelNameLv1Obj[modelNameLv1];
    }
    rest.itemIds = splitSpaceComma(itemIdList);
    return { ...rest, pageNo: current };
  };

  const handleExport = async () => {
    const { status } = await apis.download(pageParams);
    if (status) {
      pushExportHistory();
    }
  };
  // 上传excel modal属性
  const UploadExcelModalProps = {
    title: "上传调账明细",
    visible: showExcelUpload,
    api: apis.upload,
    tempUrl: "https://s.xinc818.com/files/webcilxcwphha1ghj2x/主播结算明细调账模板.xlsx",
    infos: [
      <span key="line1">模板中黄色背景字段为必填项</span>,
      <span key="line2" style={{ color: "red" }}>
        只需要填写需要调整的字段
      </span>,
      <span key="line3" style={{ color: "red" }}>
        仅可对未确认的上月数据进行调整
      </span>,
      <span key="line4" style={{ color: "red" }}>
        同一订单结算月同一商品，取最新上传的调账数据
      </span>
    ],
    fresh: () => {
      actionRef.current?.reload();
    },
    close: () => {
      setshowExcelUpload(false);
    }
  };
  return (
    <>
      <TableList
        actionRef={actionRef}
        columns={columns(columnsProps)}
        api={apis.getList}
        scroll={{ y: "calc(100vh - 500px)" }}
        preFetch={handlePageParmas}
        paramsChange={setpageParams}
        rowKey="id"
        editable={editable}
        toolbar={{
          actions: [
            <Button key="upload" type="primary" onClick={() => setshowExcelUpload(true)} size={SIZE}>
              上传
            </Button>,
            <Button key="export" onClick={() => handleExport()} size={SIZE}>
              导出
            </Button>
          ]
        }}
      />

      {showExcelUpload ? <UploadExcelModal {...UploadExcelModalProps} /> : null}
    </>
  );
};

const AliveRecord = () => (
  <KeepAlive name={PageUrl}>
    <DetailTable />
  </KeepAlive>
);
export default React.memo(AliveRecord);
