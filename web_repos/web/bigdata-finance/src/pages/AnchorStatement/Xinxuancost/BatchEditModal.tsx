import React, { useState, useEffect } from "react";
import { Modal, Form, Select } from "antd";
import apis from "@/services/anchorStatement/xinxuancost";
import { SIZE } from "@/utils/constant";
import AnchorSelect from "@/components/AnchorSelect";
import { filterOptionLabel, returnEllipsisTooltip } from "@/utils";
const formLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 }
};
interface IProps {
  batchRows: any[];
  batchIds: any[];
  // 显示隐藏 modal
  visible: boolean;
  feeTypeOptions: IOptions[];
  includeAnchorOptions: IOptions[];
  // 关闭modal
  close: () => void;
  // 关闭 modal后 刷新页面
  fresh?: () => void;
  // 标题
  title: React.ReactNode;
}
const UploadExcelModal = (props: IProps) => {
  const { visible, close, fresh, title, batchIds, feeTypeOptions, includeAnchorOptions } = props;
  const [form] = Form.useForm();
  const [disables, setDisables] = useState({
    anchorName: false,
    included: false,
    expenseCategory: false
  });
  const [formValues, setformValues] = useState({
    included: void 0,
    expenseCategory: void 0,
    anchorName: void 0
  });
  const handleExcelCancel = () => {
    close?.();
  };

  const handleExcelOk = () => {
    form.validateFields().then(async values => {
      const { status } = await apis.updateBatch({
        idList: batchIds,
        ...values
      });
      if (status) {
        close?.();
        fresh?.();
      }
    });
  };
  const onChange = (name: string, val: any) => {
    setformValues({
      ...formValues,
      [name]: val
    });
  };
  useEffect(() => {
    if (formValues.anchorName) {
      setDisables({
        anchorName: false,
        included: true,
        expenseCategory: true
      });
      return;
    }
    if (formValues.included) {
      setDisables({
        anchorName: true,
        included: false,
        expenseCategory: true
      });
      return;
    }
    if (formValues.expenseCategory) {
      setDisables({
        anchorName: true,
        included: true,
        expenseCategory: false
      });
      return;
    }
    setDisables({
      anchorName: false,
      included: false,
      expenseCategory: false
    });
    return;
  }, [formValues]);
  return (
    <Modal
      centered={true}
      title={title}
      open={visible}
      onOk={() => handleExcelOk()}
      onCancel={() => handleExcelCancel()}
      maskClosable={false}
      okButtonProps={{
        size: SIZE,
        disabled: !Object.values(formValues).some(item => item)
      }}
      cancelButtonProps={{
        size: SIZE
      }}
    >
      <Form size={SIZE} form={form} {...formLayout}>
        <Form.Item label={returnEllipsisTooltip({ title: "主播" })} name="anchorName">
          <AnchorSelect
            style={{ width: "100%" }}
            disabled={disables.anchorName}
            onChange={val => {
              onChange("anchorName", val);
            }}
          />
        </Form.Item>
        <Form.Item label={returnEllipsisTooltip({ title: "是否计入主播" })} name="included">
          <Select
            options={includeAnchorOptions}
            disabled={disables.included}
            placeholder="请选择"
            filterOption={filterOptionLabel}
            showSearch
            allowClear
            onChange={val => {
              onChange("included", val);
            }}
          ></Select>
        </Form.Item>
        <Form.Item label={returnEllipsisTooltip({ title: "费用分类" })} name="expenseCategory">
          <Select
            placeholder="请选择"
            disabled={disables.expenseCategory}
            options={feeTypeOptions}
            filterOption={filterOptionLabel}
            showSearch
            allowClear
            onChange={val => {
              onChange("expenseCategory", val);
            }}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};
export default React.memo(UploadExcelModal);
