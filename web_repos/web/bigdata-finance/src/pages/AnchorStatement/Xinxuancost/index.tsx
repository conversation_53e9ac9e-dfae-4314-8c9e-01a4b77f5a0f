import { useRef, useState, useEffect } from "react";
import TableList from "@/components/TableList";
import { TableRowSelection } from "antd/es/table/interface";
import { splitSpaceComma, returnOptions } from "@/utils";
import { SIZE, Protable_FROM_CONFIG } from "@/utils/constant";
import { columns, advance_columns_search } from "./variable";
import KeepAlive from "react-activation";
import { Button, message, Form } from "antd";
import dayjs from "dayjs";
import apis from "@/services/anchorStatement/xinxuancost";
import UploadExcelModal from "./UploadExcelModal";
import SettlementDataModal, { HandleType } from "./SettlementDataModal";
import BatchEditModal from "./BatchEditModal";
import useCommonOptions from "@/hooks/useCommonOptions";
import AuthButton from "@/wrappers/authButton";
import { btn_auths } from "./auths";
export const PageUrl = `/anchorstatement/xinxuancost`;
const Res = () => {
  const [includeAnchorOptions, includeAnchorOptionsObj] = useCommonOptions({ dimName: "是否计入主播" });
  const [feeTypeOptions] = useCommonOptions({ dimName: "费用分类" });
  const formRef = useRef<any>();
  const [editForm] = Form.useForm();

  const actionRef = useRef<any>();
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const [showExcelUpload, setshowExcelUpload] = useState(false);
  const [showSettlementModal, setshowSettlementModal] = useState(false);
  const [settlementHandle, setsettlementHandle] = useState<HandleType>();
  const [showbatchEditModal, setshowbatchEditModal] = useState(false);
  const [shopProject, setshopProject] = useState<IOptions[]>([]);
  const [accountName, setaccountName] = useState<IOptions[]>([]);
  const [accountingBook, setaccountingBook] = useState<IOptions[]>([]);

  const [selectedKeys, setSelectedKeys] = useState([]);
  const [selectedRows, setselectedRows] = useState([]);
  const handleDelete = async record => {
    await apis.delete({ id: record.id });
    actionRef.current?.reload();
  };
  const columnsProps = {
    handleDelete,
    includeAnchorOptions,
    includeAnchorOptionsObj,
    accountName,
    accountingBook
  };
  const advanceColumnsProps = {
    shopProject,
    feeTypeOptions
  };
  const getshopProject = async () => {
    const { status, entry } = await apis.shopProject();
    if (status) {
      setshopProject(returnOptions(entry));
    } else {
      setshopProject([]);
    }
  };
  const getaccountName = async () => {
    const { status, entry } = await apis.accountName();
    if (status) {
      setaccountName(returnOptions(entry));
    } else {
      setaccountName([]);
    }
  };
  const getaccountingBook = async () => {
    const { status, entry } = await apis.accountingBook();
    if (status) {
      setaccountingBook(returnOptions(entry));
    } else {
      setaccountingBook([]);
    }
  };
  useEffect(() => {
    getshopProject();
    getaccountName();
    getaccountingBook();
  }, []);
  const editable: any = {
    type: "single",
    form: editForm,

    onSave: async (key, editData) => {
      const { id, included, expenseCategory, anchorName } = editData;
      const params = {
        id: id,
        included: included,
        expenseCategory: expenseCategory,
        anchorName: anchorName
      };

      const { status } = await apis.edit(params);
      editForm.resetFields([key]);
      if (status) {
        message.info("修改成功！");
      }
      actionRef.current?.reload();
    },
    editableKeys,
    onChange: setEditableRowKeys,
    actionRender: (row, config, { save, cancel }) => [save, cancel]
  };
  // 重置批量选择
  const resetSelected = () => {
    setSelectedKeys([]);
    setselectedRows([]);
  };
  const handlePageParmas = params => {
    const { settleMonth, periodMonth, current, accountCodeList, voucherNumberList, ...rest } = params;
    // 单独处理 结算月份
    if (settleMonth) {
      const [startSettleMonth, endSettleMonth] = settleMonth;
      rest.startSettleMonth = dayjs(startSettleMonth).format("YYYYMM");
      rest.endSettleMonth = dayjs(endSettleMonth).format("YYYYMM");
    }
    if (periodMonth) {
      const [startPeriodMonth, endPeriodMonth] = periodMonth;
      rest.startPeriodMonth = dayjs(startPeriodMonth).format("YYYYMM");
      rest.endPeriodMonth = dayjs(endPeriodMonth).format("YYYYMM");
    }
    resetSelected();
    setEditableRowKeys([]);
    rest.accountCodeList = splitSpaceComma(accountCodeList);
    rest.voucherNumberList = splitSpaceComma(voucherNumberList);
    return { ...rest, pageNo: current };
  };
  const handleUpload = async () => {
    setshowExcelUpload(true);
  };
  const toolbarActions = () => {
    return [
      <AuthButton key="cancelconfirm" code={btn_auths.btn_xinxuancost_index_cancelconfirm} pageUrl={PageUrl}>
        <Button
          size={SIZE}
          onClick={() => {
            setsettlementHandle("delete");
            setshowSettlementModal(true);
          }}
        >
          取消确认
        </Button>
      </AuthButton>,
      <AuthButton key="cancelconfirm" code={btn_auths.btn_xinxuancost_index_confirm} pageUrl={PageUrl}>
        <Button
          type="primary"
          size={SIZE}
          danger
          onClick={() => {
            setsettlementHandle("add");
            setshowSettlementModal(true);
          }}
        >
          确认
        </Button>
      </AuthButton>,
      <AuthButton key="batchedit" code={btn_auths.btn_xinxuancost_index_batchedit} pageUrl={PageUrl}>
        <Button
          type="primary"
          size={SIZE}
          disabled={!selectedKeys.length}
          onClick={() => {
            setshowbatchEditModal(true);
          }}
        >
          批量修改
        </Button>
      </AuthButton>,
      <AuthButton key="upload" code={btn_auths.btn_xinxuancost_index_upload} pageUrl={PageUrl}>
        <Button type="primary" size={SIZE} onClick={handleUpload}>
          上传
        </Button>
      </AuthButton>
    ];
  };

  // 上传excel modal属性
  const UploadExcelModalProps = {
    title: "上传辛选费用支出",
    visible: showExcelUpload,
    api: apis.addAnchorLiveExpendFromExcel,
    tempUrl: "https://s.xinc818.com/files/webcim4m6rmo9qhfak1/辛选费用分摊.xlsx",
    message: (
      <>
        <span>1. 同一结算月份数据以最新上传的为准，已确认的结算月份数据无法上传</span>
        <br />
        <span> 2. 模板中黄色背景字段为必填项</span>
      </>
    ),
    fresh: () => {
      actionRef?.current?.reload();
    },
    close: () => {
      setshowExcelUpload(false);
    }
  };
  const SettlementDataModalProps = {
    title: (settlementHandle === "add" ? "" : "取消") + "确认结算数据",
    info: settlementHandle === "add" ? "确认后，该结算月数据不可再新增、修改或删除" : "取消确认后，该结算月数据可进行新增、修改或删除",
    visible: showSettlementModal,
    handleType: settlementHandle,
    message: settlementHandle === "add" ? "确认后，该结算月数据不可再新增、修改或删除" : "取消确认后，该结算月数据可进行新增、修改或删除",
    fresh: () => {
      actionRef?.current?.reload();
    },
    close: () => {
      setshowSettlementModal(false);
    }
  };
  const BatchEditModalProps = {
    title: "批量修改",
    includeAnchorOptions,
    feeTypeOptions,
    visible: showbatchEditModal,
    batchRows: selectedRows,
    batchIds: selectedKeys,
    fresh: () => {
      actionRef?.current?.reload();
    },
    close: () => {
      setshowbatchEditModal(false);
    }
  };
  const rowSelection: TableRowSelection<any> = {
    selectedRowKeys: selectedKeys,
    columnWidth: 50,
    getCheckboxProps: record => {
      return {
        disabled: record.confirmationTime
      };
    },
    onChange(selected, selectedRows) {
      setSelectedKeys(selected);
      setselectedRows(selectedRows);
    }
  };
  return (
    <div>
      <TableList
        headerTitle="辛选费用支出"
        formRef={formRef}
        actionRef={actionRef}
        advanceColumns={advance_columns_search(advanceColumnsProps)}
        columns={columns(columnsProps)}
        scroll={{ x: "max-content", y: "calc(100vh - 340px)" }}
        form={{
          ...Protable_FROM_CONFIG,
          initialValues: {
            settleMonth: [dayjs().subtract(1, "month"), dayjs()]
          }
        }}
        toolbar={{
          actions: toolbarActions()
        }}
        api={apis.getList}
        downloadApi={apis.downloadList}
        preFetch={handlePageParmas}
        rowKey="id"
        rowSelection={rowSelection}
        editable={editable}
      />
      {/* 上传 */}
      {UploadExcelModalProps.visible ? <UploadExcelModal {...UploadExcelModalProps} /> : null}
      {/* 确认 取消确认 */}
      {SettlementDataModalProps.visible ? <SettlementDataModal {...SettlementDataModalProps} /> : null}
      {/* 批量修改 */}
      {BatchEditModalProps.visible ? <BatchEditModal {...BatchEditModalProps} /> : null}
    </div>
  );
};
const AliveRecord = () => (
  <KeepAlive name={PageUrl}>
    <Res />
  </KeepAlive>
);
export default AliveRecord;
