import React, { useState, useEffect } from "react";
import { Modal, Form, Button, Select, Space, Statistic, Alert } from "antd";
import RSSUpload from "@/components/RSS/RSSUpload";
import styles from "./index.less";
import { pushExportHistory, pushImportHistory, returnEllipsisTooltip } from "@/utils";
import { UploadModalProps } from "@/components/UploadModal";
import apis from "@/services/anchorStatement/xinxuancost";
import { SIZE, SettlementStartTime } from "@/utils/constant";
import dayjs from "dayjs";
const { Countdown } = Statistic;

const UploadExcelModal = (props: UploadModalProps) => {
  const { visible, close, fresh, title, tempUrl, api, message } = props;
  const [form] = Form.useForm();
  const [uploadParams, setUploadParams] = useState(() => ({
    filePath: "",
    fileName: "",
    targetType: 0
  }));
  const [modalOkDisable, setModalOkDisable] = useState<boolean>(true);
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);
  const [settleMonth, setSettleMonth] = useState([]);
  // 获取结算月份
  const fetchSettleMonth = async () => {
    // 获取当前时间
    const now = dayjs();
    // 获取起始时间
    const startTime = dayjs(SettlementStartTime);
    // 获取时间差，即相差的月份
    const months = now.diff(startTime, "month");
    const wholeMonths = [];
    // 循环获取每个月份
    let current = startTime.startOf("month");
    for (let i = 0; i <= months; i++) {
      wholeMonths.push(current.format("YYYYMM"));
      current = current.add(1, "month");
    }
    const { entry, status } = await apis.settleMonth({ settleMonthType: 1 });
    if (status) {
      // 剔除已结算的月份
      const result = wholeMonths.filter(mon => !entry?.includes(mon))?.map(item => ({ label: item, value: item }));
      setSettleMonth(result || []);
    }
  };
  useEffect(() => {
    fetchSettleMonth();
  }, []);
  useEffect(() => {
    setModalOkDisable(uploadParams.filePath ? false : true);
  }, [uploadParams]);

  const handleExcelCancel = () => {
    Modal.destroyAll();
    close?.();
  };
  const handleDownloadUploaded = async params => {
    const { status } = await apis.downloadList(params);
    if (status) {
      handleExcelCancel();
      fresh?.();
      pushExportHistory();
    }
  };

  const handleExcelOk = async () => {
    form.validateFields().then(async values => {
      setConfirmLoading(true);
      const { status, entry = {} } = await api?.({ ...uploadParams, ...values });
      const { success, successRow } = entry;
      if (status) {
        Modal.confirm({
          centered: true,
          title: <div style={{ textAlign: "center" }}>上传辛选费用支出</div>,
          icon: null,
          content: (
            <div style={{ textAlign: "center" }}>
              <Space direction="vertical">
                <span>{`已解析 ${successRow || 0}/${successRow || 0} 条`}</span>
                {!success ? (
                  <Space>
                    <span style={{ color: "red" }}>导入出错</span>，
                    <Countdown
                      format="s"
                      value={Date.now() + 4 * 1000}
                      onFinish={() => {
                        new Promise(reslove => {
                          handleExcelCancel();
                          fresh?.();
                          reslove(true);
                        }).finally(() => {
                          setTimeout(() => {
                            pushImportHistory();
                          }, 500);
                        });
                      }}
                    />
                    秒后自动跳转到导入页面
                  </Space>
                ) : null}
                <Button
                  type="link"
                  disabled={!successRow}
                  onClick={() => handleDownloadUploaded({ endSettleMonth: values?.settleMonth, startSettleMonth: values?.settleMonth })}
                >
                  下载解析后文件
                </Button>
              </Space>
            </div>
          ),
          okButtonProps: {
            size: SIZE
          },
          cancelButtonProps: {
            size: SIZE
          },
          onCancel() {
            handleExcelCancel();
          },
          onOk() {
            handleExcelCancel();
            fresh?.();
          }
        });
      }
    });
  };

  const fileChangeHandle = (url: string, fileName: string) => {
    setUploadParams({ ...uploadParams, fileName, filePath: url });
  };
  const onRemoveHandle = () => {
    setUploadParams({ ...uploadParams, fileName: "", filePath: "" });
  };
  // 下载导入商品Excel模板
  const handleDownloadExample = (e: React.BaseSyntheticEvent) => {
    e.stopPropagation();
    window.open(tempUrl);
  };
  return (
    <Modal
      centered={true}
      className={styles.uploadExcelModal}
      title={title}
      open={visible}
      onOk={() => handleExcelOk()}
      onCancel={() => handleExcelCancel()}
      maskClosable={false}
      confirmLoading={confirmLoading}
      okButtonProps={{
        size: SIZE,
        disabled: modalOkDisable
      }}
      cancelButtonProps={{
        size: SIZE
      }}
    >
      <Form size={SIZE} form={form}>
        <Form.Item>
          <Alert message={message} type="info" showIcon></Alert>
        </Form.Item>
        <Form.Item label={returnEllipsisTooltip({ title: "结算月份" })} name="settleMonth" rules={[{ required: true }]}>
          <Select options={settleMonth} placeholder="请选择"></Select>
        </Form.Item>
        <Form.Item>
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              flexDirection: "column",
              alignItems: "center"
            }}
          >
            <RSSUpload
              accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
              fileChange={(ossResultUrl: string, file: any, fileName: string) => fileChangeHandle(ossResultUrl, fileName)}
              fileRemove={() => onRemoveHandle()}
              onRemove={false}
              size={30 * 1024 * 1024}
              type={["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "application/vnd.ms-exce", "text/csv"]}
              fileLength={1}
              fileList={uploadParams.filePath ? [uploadParams.filePath] : []}
            />
            <Form.Item>
              <div
                style={{
                  display: "flex",
                  justifyContent: "center",
                  flexDirection: "column",
                  alignItems: "center"
                }}
              >
                <Button type="link" onClick={handleDownloadExample}>
                  [下载模板]
                </Button>
              </div>
            </Form.Item>
          </div>
        </Form.Item>
      </Form>
    </Modal>
  );
};
export default React.memo(UploadExcelModal);
