import React, { useState, useEffect } from "react";
import { Modal, Form, Select, Alert } from "antd";
import apis from "@/services/anchorStatement/xinxuancost";
import { SIZE } from "@/utils/constant";
import { returnEllipsisTooltip } from "@/utils";
export type HandleType = "add" | "delete" | void;

interface IProps {
  // 显示隐藏 modal
  visible: boolean;
  // 关闭modal
  close: () => void;
  // 关闭 modal后 刷新页面
  fresh?: () => void;
  handleType: HandleType;
  // 标题
  title: React.ReactNode;
  message: string;
}
const UploadExcelModal = (props: IProps) => {
  const { visible, close, fresh, title, handleType, message } = props;
  const [form] = Form.useForm();

  const [settleMonth, setSettleMonth] = useState([]);
  // 获取结算月份
  const fetchSettleMonth = async () => {
    const { entry, status } = await apis.settleMonth({ settleMonthType: handleType === "add" ? 0 : 1 });
    if (status) {
      setSettleMonth((entry || [])?.map(item => ({ label: item, value: item })));
    }
  };
  useEffect(() => {
    fetchSettleMonth();
  }, []);

  const handleExcelCancel = () => {
    close?.();
  };

  const handleExcelOk = () => {
    form.validateFields().then(async values => {
      const { status } = await apis.batchConfirmed({ ...values, confirmedType: handleType === "add" ? 1 : 0 });
      if (status) {
        close?.();
        fresh?.();
      }
    });
  };

  return (
    <Modal
      centered={true}
      title={title}
      open={visible}
      onOk={() => handleExcelOk()}
      onCancel={() => handleExcelCancel()}
      maskClosable={false}
      okButtonProps={{
        size: SIZE
      }}
      cancelButtonProps={{
        size: SIZE
      }}
    >
      <Form size={SIZE} form={form}>
        {message ? (
          <Form.Item>
            <Alert message={message} type="warning" showIcon></Alert>
          </Form.Item>
        ) : null}
        <Form.Item label={returnEllipsisTooltip({ title: "结算月份" })} name="settleMonth" rules={[{ required: true, message: "请选择结算月份" }]}>
          <Select options={settleMonth} placeholder="请选择"></Select>
        </Form.Item>
      </Form>
    </Modal>
  );
};
export default React.memo(UploadExcelModal);
