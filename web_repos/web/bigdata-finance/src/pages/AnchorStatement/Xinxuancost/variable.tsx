import { Popconfirm, Select, Space, message, DatePicker, Button } from "antd";
import { NOVALUE, SIZE } from "@/utils/constant";
import { filterOptionLabel, returnEllipsisTooltip } from "@/utils";
import AnchorSelect from "@/components/AnchorSelect";
import AuthButton from "@/wrappers/authButton";
import { btn_auths } from "./auths";
import { PageUrl } from "./index";
import EditSelect from "@/components/EditSelect";
export const advance_columns_search = ({ shopProject, feeTypeOptions }: any): TableListItem[] => {
  return [
    {
      title: "基本信息",
      valueType: "group",
      colProps: { span: 24 },
      columns: [
        {
          title: "店铺项目",
          dataIndex: "shopProjectList",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem: () => {
            return (
              <Select
                placeholder="请选择"
                style={{ minWidth: 120, textAlign: "left" }}
                size={SIZE}
                options={shopProject}
                filterOption={filterOptionLabel}
                showSearch
                allowClear
                maxTagCount="responsive"
                mode="multiple"
              />
            );
          }
        },
        {
          title: "凭证号",
          dataIndex: "voucherNumberList",
          hideInSearch: false,
          hideInTable: true,
          fieldProps: {
            placeholder: "多个使用英文逗号/空格"
          }
        },
        {
          title: "费用分类",
          dataIndex: "expenseCategoryList",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem: () => {
            return (
              <Select
                placeholder="请选择"
                style={{ minWidth: 120, textAlign: "left" }}
                size={SIZE}
                options={feeTypeOptions}
                filterOption={filterOptionLabel}
                showSearch
                allowClear
                maxTagCount="responsive"
                mode="multiple"
              />
            );
          }
        }
      ]
    }
  ];
};
const columns_search = ({ includeAnchorOptions, accountName, accountingBook }: any) => {
  return [
    {
      title: "结算月份",
      dataIndex: "settleMonth",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return <DatePicker.RangePicker picker="month" allowClear />;
      }
    },
    {
      title: "期间",
      dataIndex: "periodMonth",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return <DatePicker.RangePicker picker="month" allowClear />;
      }
    },
    {
      title: "账套",
      dataIndex: "accountingBookList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return (
          <Select
            placeholder="请选择"
            style={{ minWidth: 120, textAlign: "left" }}
            size={SIZE}
            options={accountingBook}
            filterOption={filterOptionLabel}
            showSearch
            allowClear
            maxTagCount="responsive"
            mode="multiple"
          />
        );
      }
    },

    {
      title: "科目代码",
      dataIndex: "accountCodeList",
      hideInSearch: false,
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "科目名称",
      dataIndex: "accountNameList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return (
          <Select
            placeholder="请选择"
            style={{ minWidth: 120, textAlign: "left" }}
            size={SIZE}
            options={accountName}
            filterOption={filterOptionLabel}
            showSearch
            allowClear
            maxTagCount="responsive"
            mode="multiple"
          />
        );
      }
    },
    {
      title: "主播名称",
      dataIndex: "anchorNameList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return <AnchorSelect maxTagCount="responsive" mode="multiple" />;
      }
    },
    {
      title: "是否计入主播",
      dataIndex: "includedList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return (
          <Select
            placeholder="请选择"
            style={{ minWidth: 120, textAlign: "left" }}
            size={SIZE}
            options={includeAnchorOptions}
            filterOption={filterOptionLabel}
            showSearch
            allowClear
            maxTagCount="responsive"
            mode="multiple"
          />
        );
      }
    }
  ];
};
export const columns = ({
  handleDelete,
  includeAnchorOptions,
  includeAnchorOptionsObj,
  feeTypeOptions,
  shopProject,
  accountName,
  accountingBook
}: any): Array<TableListItem> => {
  return [
    ...columns_search({ includeAnchorOptions, feeTypeOptions, shopProject, accountName, accountingBook }),
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      hideInSearch: true,
      fixed: "left",
      editable: false,
      render: (_, record, index) => {
        return index + 1;
      }
    },
    {
      title: "结算月份",
      dataIndex: "settleMonthStr",
      hideInSearch: true,
      editable: false,
      width: 100
    },
    {
      title: "账套",
      dataIndex: "accountingBook",
      hideInSearch: true,
      editable: false,
      width: 100,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "店铺项目",
      dataIndex: "shopProject",
      hideInSearch: true,
      editable: false,
      width: 120,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "期间",
      dataIndex: "periodMonthStr",
      hideInSearch: true,
      editable: false,
      width: 100
    },
    {
      title: "科目代码",
      dataIndex: "accountCode",
      hideInSearch: true,
      editable: false,
      width: 100,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "科目名称",
      dataIndex: "accountName",
      hideInSearch: true,
      editable: false,
      width: 100,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "凭证号",
      dataIndex: "voucherNumber",
      hideInSearch: true,
      editable: false,
      width: 100,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "凭证摘要",
      dataIndex: "voucherSummary",
      hideInSearch: true,
      editable: false,
      width: 120,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "借方发生额",
      dataIndex: "debitAmountStr",
      hideInSearch: true,
      editable: false,
      width: 100
    },
    {
      title: "主播名称",
      dataIndex: "anchorName",
      hideInSearch: true,
      width: 120,
      renderFormItem: (_, { isEditable, defaultRender }: any) => {
        return isEditable ? <AnchorSelect /> : { defaultRender };
      }
    },
    {
      title: "是否计入主播",
      dataIndex: "included",
      hideInSearch: true,
      width: 120,
      render(_, record) {
        const { included } = record;
        return includeAnchorOptionsObj?.[included] ?? NOVALUE;
      },
      renderFormItem: (_, { isEditable, defaultRender }: any) => {
        return isEditable ? <EditSelect placeholder="请选择" options={includeAnchorOptions} size={SIZE} showSearch /> : <>{defaultRender()}</>;
      }
    },
    {
      title: "费用分类",
      dataIndex: "expenseCategory",
      hideInSearch: true,
      width: 120,
      renderFormItem: (_, { isEditable, defaultRender }: any) => {
        return isEditable ? (
          <Select
            placeholder="请选择"
            style={{ minWidth: 120, textAlign: "left" }}
            size={SIZE}
            options={feeTypeOptions}
            filterOption={filterOptionLabel}
            showSearch
            allowClear
          />
        ) : (
          <>{defaultRender()}</>
        );
      }
    },
    {
      title: "是否主播核算",
      dataIndex: "anchorCalculateStr",
      hideInSearch: true,
      editable: false,
      width: 100
    },
    {
      title: "分成比例",
      dataIndex: "anchorRateStr",
      hideInSearch: true,
      editable: false,
      width: 100
    },
    {
      title: "公司承担费用",
      dataIndex: "companyExpenditureStr",
      hideInSearch: true,
      editable: false,
      width: 100
    },
    {
      title: "主播承担费用",
      dataIndex: "anchorExpenditureStr",
      hideInSearch: true,
      editable: false,
      width: 100
    },
    {
      title: "确认时间",
      dataIndex: "confirmationTime",
      hideInSearch: true,
      editable: false,
      width: 100
    },
    {
      title: "确认人",
      dataIndex: "confirmedBy",
      hideInSearch: true,
      editable: false,
      width: 100
    },
    {
      title: "操作",
      width: 100,
      valueType: "option",
      fixed: "right",
      render: (text: string, record: any, index: number, action: any) => {
        const { id, confirmationTime } = record;
        return (
          <Space>
            <AuthButton key="edit" code={btn_auths.btn_xinxuancost_index_edit} pageUrl={PageUrl}>
              <Button
                type="link"
                size={SIZE}
                disabled={confirmationTime}
                onClick={() => {
                  if (id) {
                    action?.startEditable(id);
                  } else {
                    message.info("ID 字段的没有可读可写权限，无法编辑！！！");
                  }
                }}
              >
                编辑
              </Button>
            </AuthButton>
            <AuthButton key="delete" code={btn_auths.btn_xinxuancost_index_delete} pageUrl={PageUrl}>
              <Popconfirm
                title="是否确认删除?"
                onConfirm={() => handleDelete(record)}
                onCancel={() => {}}
                okButtonProps={{ size: SIZE }}
                cancelButtonProps={{ size: SIZE }}
              >
                <Button type="link" disabled={confirmationTime} size={SIZE}>
                  删除
                </Button>
              </Popconfirm>
            </AuthButton>
          </Space>
        );
      }
    }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      ...item,
      align: "center",
      title: returnEllipsisTooltip({ title })
    };
  });
};
