import { Popconfirm, Button, Space, DatePicker } from "antd";
import EditInputNumber from "@/components/EditInputNumber";
import { SIZE } from "@/utils/constant";
import { returnEllipsisTooltip } from "@/utils";
const columns_search = ({}): Array<TableListItem> => {
  return [
    {
      title: "结算月份",
      dataIndex: "month",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return <DatePicker.RangePicker picker="month" allowClear />;
      }
    },
    {
      title: "款式编码",
      dataIndex: "styleCode",
      editable: true,
      hideInTable: true
    },
    {
      title: "商品编码",
      dataIndex: "itemCode",
      editable: true,
      hideInTable: true
    },
    {
      title: "商品名称",
      dataIndex: "itemName",
      editable: true,
      hideInTable: true
    }
  ];
};
export const columns = ({ handleDelete }): Array<TableListItem> => {
  return [
    ...columns_search({}),
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      editable: false,
      hideInSearch: true,
      fixed: "left",
      render: (_, record, index) => {
        return index + 1;
      }
    },
    {
      title: "结算月份",
      dataIndex: "settleMonth",
      width: 100,
      editable: false,
      hideInSearch: true
    },
    {
      title: "款式编码",
      dataIndex: "styleCode",
      width: 100,
      editable: true,
      hideInSearch: true,
      fieldProps: { size: SIZE },
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品编码",
      dataIndex: "itemCode",
      width: 120,
      editable: true,
      fieldProps: { size: SIZE },
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品名称",
      dataIndex: "itemName",
      width: 140,
      editable: true,
      hideInSearch: true,
      fieldProps: { size: SIZE },
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "公司主体",
      dataIndex: "companyName",
      width: 100,
      editable: true,
      hideInSearch: true,
      fieldProps: { size: SIZE },
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "分类",
      dataIndex: "itemType",
      width: 100,
      editable: true,
      hideInSearch: true,
      fieldProps: { size: SIZE },
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "整合成本价(含税)",
      dataIndex: "costPrice",
      width: 140,
      editable: true,
      hideInSearch: true,
      render(_, record) {
        const { costPriceStr } = record;
        return returnEllipsisTooltip({ title: costPriceStr });
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
      }
    },
    {
      title: "操作",
      width: 100,
      valueType: "option",
      fixed: "right",
      render: (text: string, record: any, index: number, action: any) => {
        const { id, confirmationTime } = record;
        return (
          <Space>
            <Button
              key="edit"
              type="link"
              size={SIZE}
              disabled={confirmationTime}
              onClick={() => {
                if (id) {
                  action?.startEditable(id);
                }
              }}
            >
              编辑
            </Button>
            <Popconfirm
              key="delete"
              title="是否确认删除?"
              onConfirm={() => handleDelete(record)}
              onCancel={() => {}}
              okButtonProps={{ size: SIZE }}
              cancelButtonProps={{ size: SIZE }}
            >
              <Button type="link" disabled={confirmationTime} size={SIZE}>
                删除
              </Button>
            </Popconfirm>
          </Space>
        );
      }
    }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      ...item,
      align: "center",
      title: returnEllipsisTooltip({ title })
    };
  });
};
