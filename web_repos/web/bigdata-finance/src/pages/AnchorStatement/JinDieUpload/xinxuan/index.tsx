import React, { useContext, useRef, useState } from "react";
import { columns } from "./variable";
import TableList from "@/components/TableList";
import { message, Button, Form, Modal } from "antd";
import apis from "@/services/anchorStatement/kingdeeCost";
import UploadExcelModal from "../UploadExcelModal";
import dayjs from "dayjs";
import { FieldsContext } from "../index";
import styles from "../index.less";
import { SIZE } from "@/utils/constant";
const Res = () => {
  const { handlePageParmas } = useContext(FieldsContext);
  const actions = useRef<any>();
  const formRef = useRef<any>();
  const [showExcelUpload, setshowExcelUpload] = useState(false);
  const [editForm] = Form.useForm();
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);

  const handleDelete = async record => {
    const { id } = record;
    const { status } = await apis.delete({ ids: [id] });
    if (status) {
      message.success("删除成功！");
      actions?.current?.reload();
    }
  };
  const handleParams = (p, sort) => {
    setEditableRowKeys([]);
    const res = handlePageParmas?.(p, sort);
    return res;
  };
  const columnsProps = { handleDelete };
  const editable: any = {
    type: "single",
    form: editForm,
    onSave: async (key, editData) => {
      const params = {
        ...editData,
        billType: 1
      };
      const { status, entry } = await apis.update(params);
      editForm.resetFields([key]);
      if (entry) {
        Modal.info({
          centered: true,
          title: "提示",
          icon: null,
          content: <div style={{ textAlign: "center", color: "red" }}>{entry}</div>,
          okButtonProps: {
            size: SIZE
          },
          cancelButtonProps: {
            size: SIZE
          },
          onCancel() {},
          onOk() {}
        });
      } else if (status) {
        message.success("修改成功！");
      }
      actions.current?.reload();
    },
    editableKeys,
    onChange: setEditableRowKeys,
    actionRender: (row, config, { save, cancel }) => [save, cancel]
  };

  // 上传excel modal属性
  const UploadExcelModalProps = {
    title: "上传辛选网络金蝶成本",
    visible: showExcelUpload,
    api: apis.upload,
    params: {
      billType: 1
    },
    tempUrl: "https://s.xinc818.com/files/webcim1yx6y2scl81sl/金蝶成本上传模板.xlsx",
    fresh: () => {
      actions?.current?.reload();
    },
    close: () => {
      setshowExcelUpload(false);
    }
  };
  const toolbarActions = () => {
    return [
      <Button key="upload" type="primary" onClick={() => setshowExcelUpload(true)} size={SIZE}>
        上传
      </Button>
    ];
  };
  return (
    <div>
      <TableList
        className={styles["table-xinxuan"]}
        actionRef={actions}
        formRef={formRef}
        form={{
          initialValues: {
            month: [dayjs().subtract(2, "month"), dayjs()]
          }
        }}
        columns={columns(columnsProps)}
        api={apis.getList}
        params={{ billType: 1 }}
        scroll={{ y: "calc(100vh - 380px)" }}
        preFetch={handleParams}
        rowKey="id"
        editable={editable}
        toolbar={{
          actions: toolbarActions()
        }}
      />

      {showExcelUpload ? <UploadExcelModal {...UploadExcelModalProps} /> : null}
    </div>
  );
};

export default React.memo(Res);
