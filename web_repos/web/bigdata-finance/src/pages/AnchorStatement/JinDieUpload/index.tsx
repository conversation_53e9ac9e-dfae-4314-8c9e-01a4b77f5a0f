import React, { useLayoutEffect, useState } from "react";
import { Tabs } from "antd";
import KeepAlive from "react-activation";
import Xinxuan from "./xinxuan";
import HainanMaikos from "./hainan-maikos";
import XinrunMain from "./xinrun_main";
import { SIZE, tabs_tabBarStyle } from "@/utils/constant";
import dayjs from "dayjs";
import { model_auths } from "./auths";
import { useModelAuth } from "@/wrappers/authButton";
export const FieldsContext = React.createContext<any>(null);
export const PageUrl = "/anchorstatement/jinDieUpload";
export const tab_xinxuan = model_auths.model_jinDieUpload_xinxuan;
export const tab_hainan_maikos = model_auths.model_jinDieUpload_hainan_maikos;
export const tab_xinrun_main = model_auths.model_jinDieUpload_xinrun_main;
const Res = () => {
  const authModel = useModelAuth({ pageUrl: PageUrl });
  const [curTab, setCurTab] = useState("");
  // 处理请求参数
  const handlePageParmas = (params: any) => {
    const { current, month, ...rest } = params;
    if (month) {
      rest.startMonth = dayjs(month[0]).format("YYYYMM");
      rest.endMonth = dayjs(month[1]).format("YYYYMM");
    }
    return { ...rest, pageNo: current };
  };
  const tabItems = [
    {
      key: tab_xinxuan,
      label: "辛选网络",
      children: <Xinxuan />
    },
    {
      key: tab_hainan_maikos,
      label: "海南迈科斯",
      children: <HainanMaikos />
    },
    {
      key: tab_xinrun_main,
      label: "辛润主体",
      children: <XinrunMain />
    }
  ].filter(item => {
    return authModel(item.key);
  });
  useLayoutEffect(() => {
    if (!curTab) {
      setCurTab(tabItems?.[0]?.key || "");
    }
  }, [tabItems, curTab]);
  return (
    <FieldsContext.Provider value={{ handlePageParmas }}>
      <Tabs activeKey={curTab} tabBarStyle={tabs_tabBarStyle} size={SIZE} onChange={setCurTab} items={tabItems} />
    </FieldsContext.Provider>
  );
};
const AliveRecord = props => {
  return (
    <KeepAlive name={PageUrl}>
      <Res {...props} />
    </KeepAlive>
  );
};

export default React.memo(AliveRecord);
