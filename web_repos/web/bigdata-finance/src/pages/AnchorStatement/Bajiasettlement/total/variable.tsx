import { returnEllipsisTitle, returnEllipsisTooltip } from "@/utils";
import { DatePicker } from "antd";
import AnchorSelect from "@/components/AnchorSelect";
import { millennialsNumber } from "@/utils";
import { statusOptions, confirmStatus_column } from "@/pages/AnchorStatement/Settlement/variable";
const columns_search = ({}: any) => {
  return [
    {
      title: "对账月份",
      dataIndex: "month",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return <DatePicker.RangePicker picker="month" allowClear={false} />;
      }
    },
    {
      title: "主播",
      dataIndex: "anchorNameList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem() {
        return <AnchorSelect showSearch allowClear maxTagCount="responsive" mode="multiple" />;
      }
    },
    {
      title: "入账状态",
      dataIndex: "confirmStatusList",
      hideInSearch: false,
      hideInTable: true,
      valueType: "checkbox",
      fieldProps: {
        options: statusOptions,
        style: { minWidth: "200px" }
      }
    }
  ];
};
export const columns = ({ mainBodyOptions, subjectOptions }: any): Array<TableListItem> => {
  return [
    ...columns_search({ mainBodyOptions, subjectOptions }),
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      hideInSearch: true,
      editable: false,
      fixed: "left",
      render: (_, record, index) => {
        const { level } = record;
        return level === 0 ? index + 1 : "";
      }
    },
    {
      title: "对账月份",
      dataIndex: "settleMonth",
      fixed: "left",
      width: 90,
      hideInSearch: true,
      valueType: "dateMonth"
    },
    {
      title: "主播",
      dataIndex: "anchorName",
      fixed: "left",
      hideInSearch: true,
      width: 80
    },
    {
      title: "费用主体",
      dataIndex: "mainBody",
      hideInSearch: true,
      width: 90
    },

    {
      title: "项目内容",
      dataIndex: "subject",
      hideInSearch: true,
      width: 90,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },

    {
      title: "总金额",
      dataIndex: "amount",
      hideInSearch: true,
      width: 130,
      summaryTooltip: {
        divisor: 10000,
        hoverMillennialsRenderMillennials: true,
        millennialsOptions: { minimumFractionDigits: 4 }
      },
      render(_, record) {
        const { amount = 0, amountStr = "" } = record;
        return returnEllipsisTitle({ title: amountStr, tooltip: millennialsNumber(amount / 10000, { minimumFractionDigits: 4 }) });
      }
    },

    {
      title: "主播金额",
      dataIndex: "anchorAmount",
      hideInSearch: true,
      width: 130,
      summaryTooltip: {
        divisor: 10000,
        hoverMillennialsRenderMillennials: true,
        millennialsOptions: { minimumFractionDigits: 4 }
      },
      render(_, record) {
        const { anchorAmount = 0, anchorAmountStr = "" } = record;
        return returnEllipsisTitle({ title: anchorAmountStr, tooltip: millennialsNumber(anchorAmount / 10000, { minimumFractionDigits: 4 }) });
      }
    },
    {
      title: "公司金额",
      dataIndex: "companyAmount",
      hideInSearch: true,
      width: 130,
      summaryTooltip: {
        divisor: 10000,
        hoverMillennialsRenderMillennials: true,
        millennialsOptions: { minimumFractionDigits: 4 }
      },
      render(_, record) {
        const { companyAmount = 0, companyAmountStr = "" } = record;
        return returnEllipsisTitle({ title: companyAmountStr, tooltip: millennialsNumber(companyAmount / 10000, { minimumFractionDigits: 4 }) });
      }
    },

    {
      title: "备注（占比）",
      dataIndex: "remark",
      hideInSearch: true,
      width: 140,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    confirmStatus_column()
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      align: "center",
      ...item,
      title: returnEllipsisTooltip({ title })
    };
  });
};
