import React, { useRef } from "react";
import TableList from "@/components/TableList";
import { Protable_FROM_CONFIG } from "@/utils/constant";
import { columns } from "./variable";
import apis from "@/services/anchorStatement/bajiasettlement";
import dayjs from "dayjs";
import styles from "../index.less";
const DetailTable = () => {
  const tableActionRef = useRef<any>();
  const columnsProps = {};
  // 处理请求参数
  const handlePageParmas = (params: any) => {
    const { current, month, ...rest } = params;
    if (month) {
      const [startMonth, endMonth] = month;
      rest.startMonth = dayjs(startMonth).format("YYYYMM");
      rest.endMonth = dayjs(endMonth).format("YYYYMM");
    }
    return { ...rest, pageNo: current };
  };

  return (
    <>
      <TableList
        className={styles["table-total"]}
        hasChildren={true}
        defaultExpandAll={{ deep: 0 }}
        form={{
          ...Protable_FROM_CONFIG,
          initialValues: {
            month: [dayjs().subtract(1, "month"), dayjs()],
            confirmStatusList: [1]
          }
        }}
        actionRef={tableActionRef}
        columns={columns(columnsProps)}
        scroll={{ y: "calc(100vh - 330px)" }}
        api={apis.getCollectList}
        summaryApi={apis.getCollectListTotal}
        downloadApi={apis.downloadCollectList}
        preFetch={handlePageParmas}
        rowKey={"unionPrimaryKey"}
      />
    </>
  );
};

export default React.memo(DetailTable);
