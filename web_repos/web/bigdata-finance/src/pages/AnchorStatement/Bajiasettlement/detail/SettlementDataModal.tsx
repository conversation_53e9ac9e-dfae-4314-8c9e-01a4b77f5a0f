import React, { useState, useEffect } from "react";
import { Modal, Form, Alert } from "antd";
import apis from "@/services/anchorStatement/bajiasettlement";
import { confirm_columns } from "./variable";
import TableList from "@/components/TableList";
import { SIZE } from "@/utils/constant";
import dayjs from "dayjs";

interface IProps {
  // 显示隐藏 modal
  visible: boolean;
  // 关闭modal
  close: () => void;
  // 关闭 modal后 刷新页面
  fresh?: () => void;
  settleMonth: Record<string, string>;
  // 标题
  title: React.ReactNode;
}
const UploadExcelModal = (props: IProps) => {
  const { visible, close, fresh, title, settleMonth } = props;
  const [pendingConfirmList, setpendingConfirmList] = useState([]);

  const [okDisable, setokDisable] = useState(false);
  const fetchList = async () => {
    const { entry, status } = await apis.getPreConfirmSummaryList({ settleMonth: settleMonth.nextSettleMonth });
    if (status) {
      setpendingConfirmList(entry);
    } else {
      setpendingConfirmList([]);
    }
  };
  useEffect(() => {
    fetchList();
  }, []);
  const handleExcelCancel = () => {
    close?.();
    fresh?.();
  };

  const handleExcelOk = async () => {
    setokDisable(true);
    const { status } = await apis.confirm({ settleMonth: settleMonth.nextSettleMonth });
    if (status) {
      close?.();
      fresh?.();
    }
    setokDisable(false);
  };
  const info = `当前结算月份为【${dayjs(settleMonth?.nextSettleMonth).format("YYYY-MM")}】，确认后数据不可修改`;
  return (
    <Modal
      centered={true}
      title={title}
      open={visible}
      onOk={() => handleExcelOk()}
      onCancel={() => handleExcelCancel()}
      maskClosable={false}
      okButtonProps={{
        size: SIZE,
        loading: okDisable
      }}
      cancelButtonProps={{
        size: SIZE
      }}
    >
      <Form size={SIZE}>
        <Form.Item>
          <Alert message={info} type="info" showIcon></Alert>
        </Form.Item>
        <Form.Item>
          <TableList
            size={SIZE}
            columns={confirm_columns({})}
            scroll={{ x: "max-content", y: "350px" }}
            dataSource={pendingConfirmList}
            options={false}
            pagination={false}
            rowKey={record => {
              return JSON.stringify(record);
            }}
            search={false}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};
export default React.memo(UploadExcelModal);
