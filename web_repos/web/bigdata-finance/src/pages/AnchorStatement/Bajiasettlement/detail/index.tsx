import React, { useState, useEffect, useRef } from "react";
import { Button, message, Form, Modal } from "antd";
import TableList from "@/components/TableList";
import { Protable_FROM_CONFIG, SIZE } from "@/utils/constant";
import { Bajia_detail_Columns, advance_columns_search } from "./variable";
import apis from "@/services/anchorStatement/bajiasettlement";
import UploadExcelModal from "../UploadExcelModal";
import SettlementDataModal from "./SettlementDataModal";
import dayjs from "dayjs";
import AuthButton from "@/wrappers/authButton";
import { btn_auths } from "../auths";
import { PageUrl } from "../index";
import { splitSpaceComma } from "@/utils";
export const Bajia_detail = "Bajia_detail";
export const Confirm_list = "Confirm_list";
const DetailTable = () => {
  const [editForm] = Form.useForm();
  // const [selectedKeys, setSelectedKeys] = useState([]);
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const [showExcelUpload, setshowExcelUpload] = useState(false);
  const [showSettlementModal, setshowSettlementModal] = useState(false);
  const [settleMonth, setsettleMonth] = useState({ latestSettleMonth: "", nextSettleMonth: "" });

  const [mainBodyOptions, setmainBodyOptions] = useState([]);
  const [subjectOptions, setsubjectOptions] = useState([]);
  // const [selectedRows, setSelectedRows] = useState([]);
  const incomeDetailactionRef = useRef<any>();

  const fetchgetMainBodyList = async () => {
    const { entry, status } = await apis.getMainBodyList();
    if (status) {
      setmainBodyOptions(entry.map(item => ({ label: item, value: item })));
    } else {
      setmainBodyOptions([]);
    }
  };
  const fetchgetSubjectList = async () => {
    const { entry, status } = await apis.getSubjectList();
    if (status) {
      setsubjectOptions(entry.map(item => ({ label: item, value: item })));
    } else {
      setsubjectOptions([]);
    }
  };
  const fetchgetSettleMonth = async () => {
    const { entry, status } = await apis.getSettleMonth();
    if (status) {
      setsettleMonth(entry);
    } else {
      setsettleMonth({});
    }
  };
  useEffect(() => {
    fetchgetMainBodyList();
    fetchgetSubjectList();
    fetchgetSettleMonth();
  }, []);
  const Bajia_detail_ColumnsProps = { mainBodyOptions, subjectOptions };
  const advanceColumnsProps = {};

  // 排序字段 枚举值
  const sortObj: Record<string, Record<string, number>> = {
    settleMonth: {
      ascend: 1,
      descend: 2
    },
    dateOccurrence: {
      ascend: 3,
      descend: 4
    },
    amount: {
      ascend: 5,
      descend: 6
    },
    anchorAmount: {
      ascend: 7,
      descend: 8
    },
    companyAmount: {
      ascend: 9,
      descend: 10
    }
  };
  // 处理请求参数
  const handlePageParmas = (params: any, sort) => {
    setEditableRowKeys([]);
    const { current, month, time, amount, personRate, anchorAmount, companyAmount, mainBodyList, ...rest } = params;
    if (Object.keys(sort).length) {
      let key = Object.keys(sort)[0];
      rest["sortOrder"] = sortObj[key]?.[sort[key]];
    }
    if (month) {
      const [startMonth, endMonth] = month;
      rest.startMonth = dayjs(startMonth).format("YYYYMM");
      rest.endMonth = dayjs(endMonth).format("YYYYMM");
    }
    if (amount) {
      const [minAmount, maxAmount] = amount;
      if (typeof minAmount !== "undefined") {
        rest.minAmount = Math.floor(minAmount * 10000);
      }
      if (typeof maxAmount !== "undefined") {
        rest.maxAmount = Math.ceil(maxAmount * 10000);
      }
    }
    if (personRate) {
      const [minPersonRate, maxPersonRate] = personRate;
      if (typeof minPersonRate !== "undefined") {
        rest.minPersonRate = minPersonRate / 100;
      }
      if (typeof maxPersonRate !== "undefined") {
        rest.maxPersonRate = maxPersonRate / 100;
      }
    }
    if (anchorAmount) {
      const [minAnchorAmount, maxAnchorAmount] = anchorAmount;
      if (typeof minAnchorAmount !== "undefined") {
        rest.minAnchorAmount = Math.floor(minAnchorAmount * 10000);
      }
      if (typeof maxAnchorAmount !== "undefined") {
        rest.maxAnchorAmount = Math.ceil(maxAnchorAmount * 10000);
      }
    }
    if (companyAmount) {
      const [minCompanyAmount, maxCompanyAmount] = companyAmount;
      if (typeof minCompanyAmount !== "undefined") {
        rest.minCompanyAmount = Math.floor(minCompanyAmount * 10000);
      }
      if (typeof maxCompanyAmount !== "undefined") {
        rest.maxCompanyAmount = Math.ceil(maxCompanyAmount * 10000);
      }
    }
    if (time) {
      const [startTime, endTime] = time;
      rest.startTime = dayjs(startTime).format("YYYYMMDD");
      rest.endTime = dayjs(endTime).format("YYYYMMDD");
    }
    rest.mainBodyList = splitSpaceComma(mainBodyList);
    return { ...rest, pageNo: current };
  };

  const SettlementDataModalProps = {
    title: "确认结算数据",
    visible: showSettlementModal,
    settleMonth,
    fresh: () => {
      fetchgetSettleMonth();
      setshowExcelUpload(false);
      incomeDetailactionRef?.current?.reload();
    },
    close: () => {
      setshowSettlementModal(false);
      setshowExcelUpload(false);
    }
  };
  const handleCancelConfirm = () => {
    Modal.confirm({
      title: "取消确认",
      content: `取消结算月份【${dayjs(settleMonth?.latestSettleMonth).format("YYYY-MM")}】的数据确认状态，取消后可对该月数据进行修改调整`,
      centered: true,
      okButtonProps: {
        size: SIZE
      },
      cancelButtonProps: {
        size: SIZE
      },
      onOk: async () => {
        const { status } = await apis.unConfirm({ settleMonth: settleMonth?.latestSettleMonth });
        if (status) {
          fetchgetSettleMonth();
          incomeDetailactionRef?.current?.reload();
        }
      },
      onCancel: () => {}
    });
  };
  const bajiaActions = () => {
    return [
      <AuthButton key="cancel-confirm" pageUrl={PageUrl} code={btn_auths.btn_bajiasettlement_index_cancelConfirm}>
        <Button onClick={handleCancelConfirm} size={SIZE}>
          取消确认
        </Button>
      </AuthButton>,
      <AuthButton key="confirm" pageUrl={PageUrl} code={btn_auths.btn_bajiasettlement_index_confirm}>
        <Button
          type="primary"
          onClick={() => {
            setshowSettlementModal(true);
          }}
          size={SIZE}
        >
          确认
        </Button>
      </AuthButton>,
      <AuthButton key="upload" pageUrl={PageUrl} code={btn_auths.btn_bajiasettlement_index_upload}>
        <Button type="primary" onClick={() => setshowExcelUpload(true)} size={SIZE}>
          上传
        </Button>
      </AuthButton>
    ];
  };
  const editable: any = {
    type: "single",
    form: editForm,
    onSave: async (key, editData) => {
      const { status } = await apis.update(editData);
      editForm.resetFields([key]);
      if (status) {
        message.success("修改成功！");
      }
      incomeDetailactionRef?.current?.reload();
    },
    editableKeys,
    onChange: setEditableRowKeys,
    actionRender: (row, config, { save, cancel }) => [save, cancel]
  };
  // 上传excel modal属性
  const UploadExcelModalProps = {
    title: "上传巴伽明细",
    visible: showExcelUpload,
    targetType: 0,
    settleMonth,
    setshowSettlementModal,
    api: apis.uploadFinancePromotion,
    tempUrl: "https://s.xinc818.com/files/webcim1hae2n2806w6p/巴伽明细上传模板.xlsx",
    fresh: () => {
      fetchgetMainBodyList();
      fetchgetSubjectList();
    },
    close: () => {
      setshowExcelUpload(false);
    }
  };
  return (
    <>
      <TableList
        form={{
          ...Protable_FROM_CONFIG,
          initialValues: {
            month: [dayjs().subtract(1, "month"), dayjs()]
          }
        }}
        advanceColumns={advance_columns_search(advanceColumnsProps)}
        actionRef={incomeDetailactionRef}
        columns={Bajia_detail_Columns(Bajia_detail_ColumnsProps)}
        scroll={{ y: "calc(100vh - 400px)" }}
        api={apis.getList}
        summaryApi={apis.getListSummary}
        downloadApi={apis.downloadList}
        preFetch={handlePageParmas}
        editable={editable}
        toolbar={{
          actions: bajiaActions()
        }}
      />
      {/* 确认*/}
      {SettlementDataModalProps.visible ? <SettlementDataModal {...SettlementDataModalProps} /> : null}
      {showExcelUpload && <UploadExcelModal {...UploadExcelModalProps} />}
    </>
  );
};

export default React.memo(DetailTable);
