import { filterOptionLabel, returnEllipsisTitle, returnEllipsisTooltip } from "@/utils";
import { SIZE } from "@/utils/constant";
import { DatePicker, Space, Button, Select } from "antd";
import { ProFormDigitRange } from "@ant-design/pro-components";
import AnchorSelect from "@/components/AnchorSelect";
import { millennialsNumber, DeleteBtn } from "@/utils";
import apis from "@/services/anchorStatement/bajiasettlement";
import EditInputNumberPercent from "@/components/EditInputNumberPercent";
import AuthButton from "@/wrappers/authButton";
import { btn_auths } from "../auths";
import { PageUrl } from "../index";
import EditInputNumber from "@/components/EditInputNumber";

export const advance_columns_search = ({}: any): TableListItem[] => {
  return [
    {
      title: "基础信息",
      valueType: "group",
      colProps: { span: 24 },
      columns: [
        {
          title: "个人比例",
          dataIndex: "personRate",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem() {
            return <ProFormDigitRange noStyle fieldProps={{ controls: false, suffix: "%" }} separator="-" placeholder={["最小值", "最大值"]} />;
          }
        },
        {
          title: "备注",
          dataIndex: "remark",
          hideInSearch: false,
          hideInTable: true
        },
        {
          title: "备注2",
          dataIndex: "remarkTwo",
          hideInSearch: false,
          hideInTable: true
        },
        {
          title: "主播金额",
          dataIndex: "anchorAmount",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem() {
            return <ProFormDigitRange noStyle fieldProps={{ controls: false }} separator="-" placeholder={["最小值", "最大值"]} />;
          }
        },
        {
          title: "公司金额",
          dataIndex: "companyAmount",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem() {
            return <ProFormDigitRange noStyle fieldProps={{ controls: false }} separator="-" placeholder={["最小值", "最大值"]} />;
          }
        }
      ]
    }
  ];
};
const Bajia_detail_columns_search = ({ mainBodyOptions, subjectOptions }: any) => {
  return [
    {
      title: "对账月份",
      dataIndex: "month",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return <DatePicker.RangePicker picker="month" allowClear />;
      }
    },
    {
      title: "日期",
      dataIndex: "time",
      hideInSearch: false,
      hideInTable: true,
      valueType: "dateRange"
    },
    {
      title: "主播",
      dataIndex: "anchorNameList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem() {
        return <AnchorSelect showSearch allowClear maxTagCount="responsive" mode="multiple" />;
      }
    },
    {
      title: "费用主体",
      dataIndex: "mainBodyList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem() {
        return (
          <Select
            placeholder="请选择"
            options={mainBodyOptions}
            filterOption={filterOptionLabel}
            showSearch
            allowClear
            // maxTagCount="responsive"
            // mode="multiple"
          />
        );
      }
    },
    {
      title: "科目",
      dataIndex: "subjectList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem() {
        return (
          <Select
            placeholder="请选择"
            options={subjectOptions}
            filterOption={filterOptionLabel}
            showSearch
            allowClear
            maxTagCount="responsive"
            mode="multiple"
          />
        );
      }
    },
    {
      title: "摘要",
      dataIndex: "digest",
      hideInSearch: false,
      hideInTable: true
    },
    {
      title: "金额",
      dataIndex: "amount",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem() {
        return <ProFormDigitRange fieldProps={{ controls: false }} separator="-" placeholder={["最小值", "最大值"]} />;
      }
    }
  ];
};
// 巴伽-支出-明细
export const Bajia_detail_Columns = ({ mainBodyOptions, subjectOptions }: any): Array<TableListItem> => {
  return [
    ...Bajia_detail_columns_search({ mainBodyOptions, subjectOptions }),
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      hideInSearch: true,
      editable: false,
      fixed: "left",
      render: (_, record, index) => {
        return index + 1;
      }
    },
    {
      title: "对账月份",
      dataIndex: "settleMonth",
      fixed: "left",
      width: 90,
      hideInSearch: true,
      editable: false,
      sortDirections: ["descend", "ascend"],
      sorter: true,
      valueType: "dateMonth",
      fieldProps: { size: SIZE, picker: "month" }
    },
    {
      title: "主播",
      dataIndex: "anchorName",
      fixed: "left",
      hideInSearch: true,
      width: 80,
      editable: false
    },
    {
      title: "费用主体",
      dataIndex: "mainBody",
      hideInSearch: true,
      width: 100,
      editable: false
    },
    {
      title: "日期",
      dataIndex: "dateOccurrence",
      hideInSearch: true,
      width: 100,
      sortDirections: ["descend", "ascend"],
      sorter: true,
      editable: false,
      valueType: "date"
    },
    {
      title: "科目",
      dataIndex: "subject",
      hideInSearch: true,
      width: 90,
      editable: false,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "摘要",
      dataIndex: "digest",
      hideInSearch: true,
      width: 160,
      valueType: "text",
      fieldProps: { size: SIZE },
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "金额",
      dataIndex: "amount",
      hideInSearch: true,
      width: 130,
      sortDirections: ["descend", "ascend"],
      sorter: true,
      formItemProps: () => {
        return {
          rules: [{ required: true, message: "此项为必填项" }]
        };
      },
      summaryTooltip: {
        divisor: 10000,
        hoverMillennialsRenderMillennials: true
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
      },
      render(_, record) {
        const { amount, amountStr = "" } = record;
        return returnEllipsisTitle({ title: amountStr, tooltip: millennialsNumber(amount / 10000, { minimumFractionDigits: 4 }) });
      }
    },
    {
      title: "个人比例",
      dataIndex: "personRate",
      valueType: "text",
      hideInSearch: true,
      width: 80,
      formItemProps: () => {
        return {
          rules: [{ required: true, message: "此项为必填项" }]
        };
      },
      render(_, record) {
        const { personRate = 0, personRateStr = "" } = record;
        return returnEllipsisTitle({ title: personRateStr, tooltip: millennialsNumber(personRate) });
      },
      renderFormItem(_, { isEditable }) {
        return (
          isEditable && (
            <EditInputNumberPercent formatter={value => `${value}%`} style={{ width: "100%" }} max={100} min={-100} step={1} placeholder="个人比例" />
          )
        );
      }
    },
    {
      title: "备注",
      dataIndex: "remark",
      hideInSearch: true,
      width: 120,
      valueType: "text",
      fieldProps: { size: SIZE },
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "备注2",
      dataIndex: "remarkTwo",
      hideInSearch: true,
      width: 120,
      valueType: "text",
      fieldProps: { size: SIZE },
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },

    {
      title: "主播金额",
      dataIndex: "anchorAmount",
      hideInSearch: true,
      width: 130,
      sortDirections: ["descend", "ascend"],
      sorter: true,
      formItemProps: () => {
        return {
          rules: [{ required: true, message: "此项为必填项" }]
        };
      },
      summaryTooltip: {
        divisor: 10000,
        hoverMillennialsRenderMillennials: true
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
      },
      render(_, record) {
        const { anchorAmount = 0, anchorAmountStr = "" } = record;
        return returnEllipsisTitle({ title: anchorAmountStr, tooltip: millennialsNumber(anchorAmount / 10000, { minimumFractionDigits: 4 }) });
      }
    },
    {
      title: "公司金额",
      dataIndex: "companyAmount",
      hideInSearch: true,
      width: 130,
      sortDirections: ["descend", "ascend"],
      sorter: true,
      formItemProps: () => {
        return {
          rules: [{ required: true, message: "此项为必填项" }]
        };
      },
      summaryTooltip: {
        divisor: 10000,
        hoverMillennialsRenderMillennials: true
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
      },
      render(_, record) {
        const { companyAmount = 0, companyAmountStr = "" } = record;
        return returnEllipsisTitle({ title: companyAmountStr, tooltip: millennialsNumber(companyAmount / 10000, { minimumFractionDigits: 4 }) });
      }
    },
    {
      title: "直播场次",
      dataIndex: "liveBroadcast",
      hideInSearch: true,
      width: 100,
      editable: false,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "确认时间",
      dataIndex: "confirmedTime",
      hideInSearch: true,
      width: 100,
      editable: false,
      fieldProps: { size: SIZE },
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "确认人",
      dataIndex: "confirmedBy",
      hideInSearch: true,
      width: 80,
      editable: false,
      fieldProps: { size: SIZE },
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "操作",
      valueType: "option",
      fixed: "right",
      width: 100,
      render: (_, record, index, action) => {
        const { id, confirmedTime } = record;
        return (
          <Space size={SIZE}>
            <AuthButton pageUrl={PageUrl} code={btn_auths.btn_bajiasettlement_index_edit}>
              <Button
                size={SIZE}
                disabled={!!confirmedTime}
                type="link"
                onClick={() => {
                  action?.startEditable?.(id);
                }}
              >
                编辑
              </Button>
            </AuthButton>
            <AuthButton pageUrl={PageUrl} code={btn_auths.btn_bajiasettlement_index_delete}>
              <DeleteBtn btnProps={{ disabled: !!confirmedTime, type: "link" }} params={{ ids: [id] }} action={action} api={apis.delete} />
            </AuthButton>
          </Space>
        );
      }
    }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      align: "center",
      ...item,
      title: returnEllipsisTooltip({ title })
    };
  });
};

export const confirm_columns = ({}): Array<TableListItem> => {
  return [
    { title: "结算月份", dataIndex: "settleMonth", valueType: "dateMonth", width: 100, hideInSearch: true },
    { title: "主播", dataIndex: "anchorName", width: 100, hideInSearch: true },
    { title: "收入金额", dataIndex: "inTotalAmountStr", width: 100, hideInSearch: true },
    { title: "支出总金额", dataIndex: "outTotalAmountStr", width: 100, hideInSearch: true }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      align: "center",
      ...item,
      title: returnEllipsisTooltip({ title })
    };
  });
};
