import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, Form, Button, Alert } from "antd";
import RSSUpload from "@/components/RSS/RSSUpload";
import styles from "@/components/UploadExcelModal/index.less";
import { UploadExcelModalProps } from "@/components/UploadExcelModal";
import { SIZE } from "@/utils/constant";
import { pushImportHistory } from "@/utils";
import dayjs from "dayjs";
interface IProps extends UploadExcelModalProps {
  settleMonth: Record<string, string>;
  setshowSettlementModal: any;
}
const UploadExcelModal = (props: IProps) => {
  const { visible, close, title, tempUrl, api, settleMonth, setshowSettlementModal } = props;
  const [form] = Form.useForm();
  const [uploadParams, setUploadParams] = useState(() => ({ fileUrl: "", fileName: "", settleMonth: settleMonth?.nextSettleMonth }));
  const [modalOkDisable, setModalOkDisable] = useState<boolean>(true);
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);

  useEffect(() => {
    setModalOkDisable(uploadParams.fileUrl ? false : true);
  }, [uploadParams]);

  const handleExcelCancel = () => {
    Modal.destroyAll();
    close?.();
  };

  const handleExcelOk = async () => {
    form.validateFields().then(async values => {
      setConfirmLoading(true);
      const { status } = await api?.({ ...uploadParams, ...values });
      setConfirmLoading(false);
      if (status) {
        setshowSettlementModal(true);
      } else {
        close?.();
        pushImportHistory();
      }
    });
  };

  const fileChangeHandle = (url: string, fileName: string) => {
    setUploadParams({ ...uploadParams, fileName, fileUrl: url });
  };
  const onRemoveHandle = () => {
    setUploadParams({ ...uploadParams, fileName: "", fileUrl: "" });
  };
  // 下载导入商品Excel模板
  const handleDownloadExample = (e: React.BaseSyntheticEvent) => {
    e.stopPropagation();
    window.open(tempUrl);
  };
  return (
    <Modal
      centered={true}
      className={styles.uploadExcelModal}
      title={title}
      open={visible}
      onOk={() => handleExcelOk()}
      onCancel={() => handleExcelCancel()}
      maskClosable={false}
      confirmLoading={confirmLoading}
      okButtonProps={{
        size: SIZE,
        disabled: modalOkDisable
      }}
      cancelButtonProps={{
        size: SIZE
      }}
    >
      <Form size={SIZE} form={form}>
        <Form.Item>
          <Alert message="同一结算月份数据以最新上传的为准，已确认的结算月份数据无法上传。" type="info" showIcon></Alert>
        </Form.Item>
        <Form.Item>
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center"
            }}
          >
            结算月份 <span>{dayjs(uploadParams.settleMonth).format("YYYY-MM")}</span>
          </div>
        </Form.Item>
        <Form.Item>
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              flexDirection: "column",
              alignItems: "center"
            }}
          >
            <RSSUpload
              accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
              fileChange={(ossResultUrl: string, file: any, fileName: string) => fileChangeHandle(ossResultUrl, fileName)}
              fileRemove={() => onRemoveHandle()}
              onRemove={false}
              size={30 * 1024 * 1024}
              type={["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "application/vnd.ms-exce", "text/csv"]}
              fileLength={1}
              fileList={uploadParams.fileUrl ? [uploadParams.fileUrl] : []}
            />
          </div>
        </Form.Item>
        <Form.Item>
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              flexDirection: "column",
              alignItems: "center"
            }}
          >
            <Button type="link" onClick={handleDownloadExample}>
              [下载模板]
            </Button>
          </div>
        </Form.Item>
      </Form>
    </Modal>
  );
};
export default React.memo(UploadExcelModal);
