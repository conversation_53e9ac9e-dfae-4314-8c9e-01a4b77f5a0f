import React, { useState } from "react";
import { Tabs } from "antd";
import KeepAlive from "react-activation";
import Detail from "./detail";
import Total from "./total";
import { SIZE, tabs_tabBarStyle } from "@/utils/constant";
export const FieldsContext = React.createContext<any>(null);
export const PageUrl = "/anchorstatement/bajiasettlement";

export const tab_detail = "a";
export const tab_total = "b";
const Res = () => {
  const [curTab, setCurTab] = useState(tab_detail);

  const tabItems = [
    {
      key: tab_detail,
      label: "明细",
      children: <Detail />
    },
    {
      key: tab_total,
      label: "汇总",
      children: <Total />
    }
  ];
  return (
    <FieldsContext.Provider value={{}}>
      <Tabs activeKey={curTab} tabBarStyle={tabs_tabBarStyle} size={SIZE} onChange={setCurTab} items={tabItems} />
    </FieldsContext.Provider>
  );
};
const AliveRecord = props => (
  <KeepAlive name={PageUrl}>
    <Res {...props} />
  </KeepAlive>
);

export default React.memo(AliveRecord);
