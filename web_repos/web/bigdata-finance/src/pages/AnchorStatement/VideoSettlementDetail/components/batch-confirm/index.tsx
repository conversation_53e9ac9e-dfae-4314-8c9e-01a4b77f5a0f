import React, { useEffect, useState } from "react";
import { Modal, Form, message, Alert, DatePicker } from "antd";
import { SIZE } from "@/utils/constant";
import apis from "@/services/anchorStatement/videosettlementdetail";
import api2 from "@/services/anchorStatement/anchorsettlementdetail";
import dayjs from "dayjs";
interface IProps {
  // 显示隐藏 modal
  visible: boolean;
  // 关闭modal
  close: () => void;
  // 关闭 modal后 刷新页面
  fresh?: () => void;
  selectedRows: any[];
  // 标题
  title: React.ReactNode;
}
const Res = (props: IProps) => {
  const { visible, close, fresh, title, selectedRows } = props;
  const [form] = Form.useForm();
  const [okDisable, setokDisable] = useState(false);
  const [settleMonth, setSettleMonth] = useState({
    latestSettleMonth: "",
    nextSettleMonth: ""
  });
  const fetchSettleMonth = async () => {
    const { entry, status } = await api2.getSettleMonth();
    if (status) {
      setSettleMonth(entry);
    }
  };
  const handleCancel = () => {
    form.resetFields();
    close?.();
  };

  const handleOk = () => {
    form.validateFields().then(async values => {
      const { expectSettleMonth } = values;
      setokDisable(true);
      if (selectedRows?.length) {
        const items = selectedRows.map(item => {
          const { anchorName, dataType, expectSettleMonth, itemId, publishDate, modelNameLv1Str, settleMonth, skuId } = item;
          return {
            anchorName,
            dataType,
            expectSettleMonth,
            itemId,
            publishDate,
            modelNameLv1: modelNameLv1Str,
            settleMonth,
            skuId
          };
        });
        const { status } = await apis.batchConfirm({ items, expectSettleMonth: dayjs(expectSettleMonth).format("YYYYMM") });
        if (status) {
          fresh?.();
          close?.();
        }
      } else {
        message.info("所选数据没有需要确认的数据！");
      }
      setokDisable(false);
    });
  };
  useEffect(() => {
    fetchSettleMonth();
  }, []);
  return (
    <Modal
      centered={true}
      title={title}
      open={visible}
      onOk={() => handleOk()}
      onCancel={() => handleCancel()}
      maskClosable={false}
      okButtonProps={{
        size: SIZE,
        loading: okDisable
      }}
      cancelButtonProps={{
        size: SIZE
      }}
    >
      <Form size={SIZE} form={form}>
        <Form.Item>
          <Alert message="调整财务结算月份保存后，会同步将数据登记到调账记录中，后续可在调账记录中操作" type="info" showIcon></Alert>
        </Form.Item>
        <Form.Item label="财务结算月份：" name="expectSettleMonth" labelCol={{ span: 10 }}>
          <DatePicker picker="month" minDate={dayjs(settleMonth.nextSettleMonth)}></DatePicker>
        </Form.Item>
      </Form>
    </Modal>
  );
};
export default React.memo(Res);
