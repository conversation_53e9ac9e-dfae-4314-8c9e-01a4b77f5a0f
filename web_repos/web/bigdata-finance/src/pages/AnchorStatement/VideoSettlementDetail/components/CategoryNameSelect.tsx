import React, { useEffect, useState } from "react";
import { SelectProps, Select } from "antd";
import apis from "@/services/anchorStatement/videosettlementdetail";
import { filterOptionLabel } from "@/utils";
type IProps = SelectProps;
const Res = (props: IProps) => {
  const [lists, setList] = useState<any>([]);
  const getList = async () => {
    let { entry = [], status } = await apis.categoryName();
    if (status && entry?.length) {
      setList(entry.map(item => ({ label: item, value: item })));
    }
  };
  useEffect(() => {
    getList();
  }, []);
  return <Select style={{ minWidth: 120 }} placeholder="请选择" options={lists} filterOption={filterOptionLabel} showSearch allowClear {...props} />;
};
export default React.memo(Res);
