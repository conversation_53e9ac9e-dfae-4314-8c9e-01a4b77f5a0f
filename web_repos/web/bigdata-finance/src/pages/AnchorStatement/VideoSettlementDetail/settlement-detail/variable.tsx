import { Select, DateP<PERSON>, Button, Space } from "antd";
import { DeleteBtn, filterOptionLabel, returnEllipsisTooltip } from "@/utils";
import AnchorSelect from "@/components/AnchorSelect";
import CategoryNameSelect from "../components/CategoryNameSelect";
import CategorySelect from "../components/CategorySelect";
import SupplierNameSelect from "@/components/SupplierNameSelect";
import { menuKeys } from "./index";
import apis from "@/services/anchorStatement/anchorsettlementdetail";
import { SIZE } from "@/utils/constant";
import EditInputNumberPercent from "@/components/EditInputNumberPercent";
import EditInputNumber from "@/components/EditInputNumber";
import EditSelect from "@/components/EditSelect";
export const advance_columns_search = ({ shopTypeOptions, modelNameLv1Options }: any): TableListItem[] => {
  return [
    {
      title: "基本信息",
      valueType: "group",
      colProps: { span: 24 },
      columns: [
        {
          title: "SKU规格",
          dataIndex: "skuName",
          hideInSearch: false,
          hideInTable: true
        }
      ]
    },
    {
      title: "合同信息",
      valueType: "group",
      colProps: { span: 24 },
      columns: [
        {
          title: "模式",
          dataIndex: "modelNameLv1s",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem() {
            return (
              <Select
                placeholder="请选择"
                options={modelNameLv1Options}
                filterOption={filterOptionLabel}
                showSearch
                allowClear
                maxTagCount="responsive"
                mode="multiple"
              />
            );
          }
        },
        {
          title: "二级模式",
          dataIndex: "modelNameLv2",
          hideInSearch: false,
          hideInTable: true
        },
        {
          title: "供应商名称",
          dataIndex: "supplierName",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem() {
            return <SupplierNameSelect />;
          }
        },
        {
          title: "我司合同主体",
          dataIndex: "companyName",
          hideInSearch: false,
          hideInTable: true
        },
        {
          title: "合同ID",
          dataIndex: "contractId",
          hideInSearch: false,
          hideInTable: true
        }
      ]
    },
    {
      title: "商品信息",
      valueType: "group",
      colProps: { span: 24 },
      columns: [
        {
          title: "品类",
          dataIndex: "categoryName",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem() {
            return <CategoryNameSelect placeholder="请选择" showSearch allowClear />;
          }
        },
        {
          title: "一级类目",
          dataIndex: "catLv1Name",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem() {
            return <CategorySelect placeholder="请选择" showSearch allowClear />;
          }
        },
        {
          title: "品牌",
          dataIndex: "brandName",
          hideInSearch: false,
          hideInTable: true
        },
        {
          title: "店铺ID",
          dataIndex: "shopId",
          hideInSearch: false,
          hideInTable: true
        },
        {
          title: "店铺名称",
          dataIndex: "shopName",
          hideInSearch: false,
          hideInTable: true
        },
        {
          title: "店铺性质",
          dataIndex: "shopType",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem() {
            return <Select placeholder="请选择" options={shopTypeOptions} filterOption={filterOptionLabel} showSearch allowClear />;
          }
        }
      ]
    }
  ];
};
const columns_search = ({ isNeedRedPunchOptions }: any): Array<TableListItem> => {
  return [
    {
      // 月份区间
      title: "财务结算月份",
      dataIndex: "expectSettleMonth",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return <DatePicker.RangePicker picker="month" allowClear />;
      }
    },
    {
      // 月份区间
      title: "订单结算月份",
      dataIndex: "settleMonth",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return <DatePicker.RangePicker picker="month" allowClear />;
      }
    },
    {
      // 日区间
      title: "发布日期",
      dataIndex: "publishDate",
      hideInSearch: false,
      hideInTable: true,
      valueType: "dateRange"
    },
    {
      // 输入+下拉
      title: "主播名称",
      dataIndex: "anchorNames",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem() {
        return <AnchorSelect showSearch allowClear maxTagCount="responsive" mode="multiple" />;
      }
    },

    {
      title: "商品ID",
      dataIndex: "itemIds",
      hideInSearch: false,
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      // 输入
      title: "商品名称",
      dataIndex: "itemName",
      hideInSearch: false,
      hideInTable: true
    },
    {
      title: "是否红冲",
      dataIndex: "redInkTypeList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem() {
        return (
          <Select
            placeholder="请选择"
            options={isNeedRedPunchOptions}
            filterOption={filterOptionLabel}
            showSearch
            allowClear
            maxTagCount="responsive"
            mode="multiple"
          />
        );
      }
    }
  ];
};
export const columns = ({
  canRedInk,
  canEditDelete,
  handleDownload,
  handleRedpunch,
  isNeedRedPunchOptions,
  isNeedRedPunchObj,
  shopTypeOptions,
  activeMenu,
  modelNameLv1Options,
  platTypeOptions,
  backFlagOptions
}: any): Array<TableListItem> => {
  return [
    ...columns_search({ isNeedRedPunchOptions, isNeedRedPunchObj }),
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      hideInSearch: true,
      fixed: "left",
      editable: false,
      render: (_, record, index) => {
        return index + 1;
      }
    },
    { title: "财务结算月份", editable: false, dataIndex: "expectSettleMonth", fixed: "left", width: 100, valueType: "dateMonth", hideInSearch: true },
    {
      title: "订单结算月份",
      editable: true,
      dataIndex: "settleMonth",
      fixed: "left",
      width: 100,
      hideInSearch: true,
      valueType: "dateMonth",
      fieldProps: { size: SIZE, picker: "month" }
    },
    { title: "主播ID", editable: true, dataIndex: "anchorId", width: 100, hideInSearch: true, fieldProps: { size: SIZE } },
    {
      title: "主播名称",
      editable: true,
      dataIndex: "anchorName",
      fixed: "left",
      width: 100,
      hideInSearch: true,
      fieldProps: { size: SIZE },
      renderFormItem: (_, { isEditable }) => {
        return isEditable ? <AnchorSelect showSearch allowClear /> : null;
      }
    },
    {
      title: "发布日期",
      editable: true,
      dataIndex: "publishDate",
      fixed: "left",
      width: 100,
      hideInSearch: true,
      fieldProps: { size: SIZE },
      valueType: "date"
    },

    {
      title: "模式",
      editable: true,
      dataIndex: "modelNameLv1",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { modelNameLv1Str } = record;
        return returnEllipsisTooltip({ title: modelNameLv1Str });
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable ? (
          <EditSelect size={SIZE} placeholder="请选择" options={modelNameLv1Options} filterOption={filterOptionLabel} showSearch allowClear />
        ) : null;
      }
    },
    { title: "二级模式", editable: true, dataIndex: "modelNameLv2", width: 100, hideInSearch: true, fieldProps: { size: SIZE } },
    { title: "商品ID", editable: true, dataIndex: "itemId", width: 100, hideInSearch: true, fieldProps: { size: SIZE } },
    {
      title: "商品名称",
      editable: true,
      dataIndex: "itemName",
      fixed: "left",
      width: 200,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      },
      fieldProps: { size: SIZE }
    },
    {
      title: "平台",
      editable: true,
      dataIndex: "platType",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { platTypeStr } = record;
        return returnEllipsisTooltip({ title: platTypeStr });
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable ? (
          <EditSelect size={SIZE} placeholder="请选择" options={platTypeOptions} filterOption={filterOptionLabel} showSearch allowClear />
        ) : null;
      }
    },

    {
      title: "品类",
      editable: true,
      dataIndex: "categoryName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable ? <CategoryNameSelect size={SIZE} placeholder="请选择" showSearch allowClear /> : null;
      }
    },
    {
      title: "一级类目",
      editable: true,
      dataIndex: "catLv1Name",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable ? <CategorySelect size={SIZE} placeholder="请选择" showSearch allowClear /> : null;
      }
    },
    {
      title: "二级类目",
      editable: true,
      dataIndex: "catLv2Name",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      },
      fieldProps: { size: SIZE }
    },
    {
      title: "三级类目",
      editable: true,
      dataIndex: "catLv3Name",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      },
      fieldProps: { size: SIZE }
    },
    {
      title: "四级类目",
      editable: true,
      dataIndex: "catLv4Name",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      },
      fieldProps: { size: SIZE }
    },
    {
      title: "品牌",
      editable: true,
      dataIndex: "brandName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      },
      fieldProps: { size: SIZE }
    },
    { title: "店铺ID", editable: true, dataIndex: "shopId", width: 100, hideInSearch: true, fieldProps: { size: SIZE } },
    {
      title: "店铺名称",
      editable: true,
      dataIndex: "shopName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      },
      fieldProps: { size: SIZE }
    },
    {
      title: "店铺性质",
      editable: true,
      dataIndex: "shopType",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { shopTypeStr } = record;
        return returnEllipsisTooltip({ title: shopTypeStr });
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable ? (
          <EditSelect size={SIZE} placeholder="请选择" options={shopTypeOptions} filterOption={filterOptionLabel} showSearch allowClear />
        ) : null;
      }
    },
    {
      title: "供应商名称",
      editable: true,
      dataIndex: "supplierName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable ? <SupplierNameSelect /> : null;
      }
    },
    {
      title: "我司合同主体",
      editable: true,
      dataIndex: "companyName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      },
      fieldProps: { size: SIZE }
    },
    {
      title: "商品编码",
      editable: true,
      dataIndex: "styleCode",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      },
      fieldProps: { size: SIZE }
    },
    {
      title: "SKU_ID",
      editable: true,
      dataIndex: "skuId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      },
      fieldProps: { size: SIZE }
    },
    {
      title: "SKU规格",
      editable: true,
      dataIndex: "skuName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      },
      fieldProps: { size: SIZE }
    },
    {
      title: "线上佣金比例",
      editable: true,
      dataIndex: "onlineCommissionRate",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { onlineCommissionRateStr } = record;
        return returnEllipsisTooltip({ title: onlineCommissionRateStr });
      },
      renderFormItem: (_, { isEditable }) => {
        return (
          isEditable && (
            <EditInputNumberPercent formatter={value => `${value}%`} style={{ width: "100%" }} max={100} step={1} placeholder="线上佣金比例" />
          )
        );
      }
    },
    {
      title: "发货内容",
      editable: true,
      dataIndex: "deliveryContent",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      },
      fieldProps: { size: SIZE }
    },
    {
      title: "结算商品数量",
      editable: true,
      dataIndex: "settleItemNum",
      width: 100,
      hideInSearch: true,
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={1} precision={0} style={{ width: "100%" }} />;
      }
    },
    {
      title: "结算订单数",
      editable: true,
      dataIndex: "settleOrderNum",
      width: 100,
      hideInSearch: true,
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={1} precision={0} style={{ width: "100%" }} />;
      }
    },
    {
      title: "结算金额",
      editable: true,
      dataIndex: "settleAmount",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { settleAmountStr } = record;
        return returnEllipsisTooltip({ title: settleAmountStr });
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
      }
    },
    {
      title: "超售后数量",
      editable: true,
      dataIndex: "refundItemNum",
      width: 100,
      hideInSearch: true,
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={1} precision={0} style={{ width: "100%" }} />;
      }
    },
    {
      title: "超售后订单数",
      editable: true,
      dataIndex: "refundOrderNum",
      width: 100,
      hideInSearch: true,
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={1} precision={0} style={{ width: "100%" }} />;
      }
    },
    {
      title: "超售后金额",
      editable: true,
      dataIndex: "refundAmount",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { refundAmountStr } = record;
        return returnEllipsisTooltip({ title: refundAmountStr });
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
      }
    },
    {
      title: "线上佣金金额",
      editable: true,
      dataIndex: "onlineCommissionAmount",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { onlineCommissionAmountStr } = record;
        return returnEllipsisTooltip({ title: onlineCommissionAmountStr });
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
      }
    },
    {
      title: "主品成本单价",
      editable: true,
      dataIndex: "mainCostPrice",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { mainCostPriceStr } = record;
        return returnEllipsisTooltip({ title: mainCostPriceStr });
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
      }
    },
    {
      title: "主品成本总金额",
      editable: true,
      dataIndex: "mainCostAmount",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { mainCostAmountStr } = record;
        return returnEllipsisTooltip({ title: mainCostAmountStr });
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
      }
    },
    {
      title: "赠品成本单价",
      editable: true,
      dataIndex: "giftCostPrice",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { giftCostPriceStr } = record;
        return returnEllipsisTooltip({ title: giftCostPriceStr });
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
      }
    },
    {
      title: "赠品成本总金额",
      editable: true,
      dataIndex: "giftCostAmount",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { giftCostAmountStr } = record;
        return returnEllipsisTooltip({ title: giftCostAmountStr });
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
      }
    },
    {
      title: "总成本金额(主品+赠品)",
      editable: true,
      dataIndex: "totalCostAmount",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { totalCostAmountStr } = record;
        return returnEllipsisTooltip({ title: totalCostAmountStr });
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
      }
    },
    {
      title: "加成成本单价",
      tooltip: "历史账单兼容字段",
      editable: true,
      dataIndex: "markupCostUnitPrice",
      width: 100,
      hideInSearch: true,
      className: "danger-target",
      render(_, record) {
        const { markupCostUnitPriceStr } = record;
        return returnEllipsisTooltip({ title: markupCostUnitPriceStr });
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
      }
    },
    {
      title: "加成成本金额",
      tooltip: "历史账单兼容字段",
      editable: true,
      dataIndex: "markupCostAmount",
      width: 100,
      hideInSearch: true,
      className: "danger-target",
      render(_, record) {
        const { markupCostAmountStr } = record;
        return returnEllipsisTooltip({ title: markupCostAmountStr });
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
      }
    },
    {
      title: "发货快递单数量",
      editable: true,
      dataIndex: "expressNum",
      width: 100,
      hideInSearch: true,
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={1} precision={0} style={{ width: "100%" }} />;
      }
    },
    {
      title: "发货费用单价",
      editable: true,
      dataIndex: "expressPrice",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { expressPriceStr } = record;
        return returnEllipsisTooltip({ title: expressPriceStr });
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
      }
    },
    {
      title: "发货费用合计",
      editable: true,
      dataIndex: "expressAmount",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { expressAmountStr } = record;
        return returnEllipsisTooltip({ title: expressAmountStr });
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
      }
    },
    {
      title: "退款订单数量",
      editable: true,
      dataIndex: "refundOperateOrderNum",
      width: 100,
      hideInSearch: true,
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={1} precision={0} style={{ width: "100%" }} />;
      }
    },
    {
      title: "销退操作费用单价",
      editable: true,
      dataIndex: "refundOperatePrice",
      width: 100,
      hideInSearch: true,
      fieldProps: { size: SIZE },
      render(_, record) {
        const { refundOperatePriceStr } = record;
        return returnEllipsisTooltip({ title: refundOperatePriceStr });
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
      }
    },
    {
      title: "销退费用合计",
      editable: true,
      dataIndex: "refundOperateAmount",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { refundOperateAmountStr } = record;
        return returnEllipsisTooltip({ title: refundOperateAmountStr });
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
      }
    },
    {
      title: "总发货费用",
      editable: true,
      dataIndex: "totalExpressAmount",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { totalExpressAmountStr } = record;
        return returnEllipsisTooltip({ title: totalExpressAmountStr });
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
      }
    },
    {
      title: "达人佣金追回",
      editable: true,
      dataIndex: "backCommissionAmount",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { backCommissionAmountStr } = record;
        return returnEllipsisTooltip({ title: backCommissionAmountStr });
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
      }
    },
    {
      title: "达人佣金未追回",
      editable: true,
      dataIndex: "unbackCommissionAmount",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { unbackCommissionAmountStr } = record;
        return returnEllipsisTooltip({ title: unbackCommissionAmountStr });
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
      }
    },
    {
      title: "线下收/返佣比例",
      editable: true,
      dataIndex: "offlineCommissionRate",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { offlineCommissionRateStr } = record;
        return returnEllipsisTooltip({ title: offlineCommissionRateStr });
      },
      renderFormItem: (_, { isEditable }) => {
        return (
          isEditable && (
            <EditInputNumberPercent formatter={value => `${value}%`} style={{ width: "100%" }} max={100} step={1} placeholder="线下收/返佣比例" />
          )
        );
      }
    },
    {
      title: "线下收/返佣金额",
      editable: true,
      dataIndex: "offlineCommissionAmount",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { offlineCommissionAmountStr } = record;
        return returnEllipsisTooltip({ title: offlineCommissionAmountStr });
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
      }
    },
    {
      title: "实际佣金",
      editable: true,
      dataIndex: "totalCommissionAmount",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { totalCommissionAmountStr } = record;
        return returnEllipsisTooltip({ title: totalCommissionAmountStr });
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
      }
    },
    {
      title: "全佣金金额（佣金+预估专项）",
      tooltip: "历史账单兼容字段",
      editable: true,
      dataIndex: "fullCommissionAmount",
      width: 100,
      hideInSearch: true,
      className: "danger-target",
      render(_, record) {
        const { fullCommissionAmountStr } = record;
        return returnEllipsisTooltip({ title: fullCommissionAmountStr });
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
      }
    },
    {
      title: "淘客技术服务费",
      tooltip: "历史账单兼容字段",
      editable: true,
      dataIndex: "tkTechnicalFee",
      width: 100,
      hideInSearch: true,
      className: "danger-target",
      render(_, record) {
        const { tkTechnicalFeeStr } = record;
        return returnEllipsisTooltip({ title: tkTechnicalFeeStr });
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
      }
    },
    {
      title: "淘客预估专项服务费",
      tooltip: "历史账单兼容字段",
      editable: true,
      dataIndex: "tkSpecialFee",
      width: 100,
      hideInSearch: true,
      className: "danger-target",
      render(_, record) {
        const { tkSpecialFeeStr } = record;
        return returnEllipsisTooltip({ title: tkSpecialFeeStr });
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
      }
    },
    {
      title: "淘客结算专项服务费",
      tooltip: "历史账单兼容字段",
      editable: true,
      dataIndex: "tkSettleFee",
      width: 100,
      hideInSearch: true,
      className: "danger-target",
      render(_, record) {
        const { tkSettleFeeStr } = record;
        return returnEllipsisTooltip({ title: tkSettleFeeStr });
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
      }
    },
    {
      title: "淘客支付宝费用汇总",
      tooltip: "历史账单兼容字段",
      editable: true,
      dataIndex: "tkPayFee",
      width: 100,
      hideInSearch: true,
      className: "danger-target",
      render(_, record) {
        const { tkPayFeeStr } = record;
        return returnEllipsisTooltip({ title: tkPayFeeStr });
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
      }
    },
    {
      title: "有赞技术服务费10%",
      tooltip: "历史账单兼容字段",
      editable: true,
      dataIndex: "yzTechnicalFee",
      width: 100,
      hideInSearch: true,
      className: "danger-target",
      render(_, record) {
        const { yzTechnicalFeeStr } = record;
        return returnEllipsisTooltip({ title: yzTechnicalFeeStr });
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
      }
    },
    {
      title: "有赞渠道服务费5%",
      tooltip: "历史账单兼容字段",
      editable: true,
      dataIndex: "yzChannelFee",
      width: 100,
      hideInSearch: true,
      className: "danger-target",
      render(_, record) {
        const { yzChannelFeeStr } = record;
        return returnEllipsisTooltip({ title: yzChannelFeeStr });
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
      }
    },
    {
      title: "有赞平台交易扣费汇总",
      tooltip: "历史账单兼容字段",
      editable: true,
      dataIndex: "yzTransactionFee",
      width: 100,
      hideInSearch: true,
      className: "danger-target",
      render(_, record) {
        const { yzTransactionFeeStr } = record;
        return returnEllipsisTooltip({ title: yzTransactionFeeStr });
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
      }
    },
    {
      title: "快手技术服务费",
      editable: true,
      dataIndex: "settleServiceAmount",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { settleServiceAmountStr } = record;
        return returnEllipsisTooltip({ title: settleServiceAmountStr });
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
      }
    },
    {
      title: "快手技术服务费追回",
      editable: true,
      dataIndex: "refundServiceAmount",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { refundServiceAmountStr } = record;
        return returnEllipsisTooltip({ title: refundServiceAmountStr });
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
      }
    },
    {
      title: "线下赔付金额",
      editable: true,
      dataIndex: "compensateAmount",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { compensateAmountStr } = record;
        return returnEllipsisTooltip({ title: compensateAmountStr });
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
      }
    },
    {
      title: "线下返款金额",
      editable: true,
      dataIndex: "backAmount",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { backAmountStr } = record;
        return returnEllipsisTooltip({ title: backAmountStr });
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
      }
    },
    {
      title: "返款承担方",
      editable: true,
      dataIndex: "backFlag",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable ? (
          <EditSelect size={SIZE} placeholder="请选择" options={backFlagOptions} filterOption={filterOptionLabel} showSearch allowClear />
        ) : null;
      }
    },
    {
      title: "利润",
      editable: true,
      dataIndex: "profitAmount",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { profitAmountStr } = record;
        return returnEllipsisTooltip({ title: profitAmountStr });
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
      }
    },
    {
      title: "线上利润",
      editable: true,
      dataIndex: "onlineProfitAmount",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { onlineProfitAmountStr } = record;
        return returnEllipsisTooltip({ title: onlineProfitAmountStr });
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
      }
    },
    {
      title: "线下利润",
      editable: true,
      dataIndex: "offlineProfitAmount",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { offlineProfitAmountStr } = record;
        return returnEllipsisTooltip({ title: offlineProfitAmountStr });
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
      }
    },
    {
      title: "对公综合税费",
      tooltip: "历史账单兼容字段",
      editable: true,
      dataIndex: "corporateComprehensiveTax",
      width: 100,
      hideInSearch: true,
      className: "danger-target",
      render(_, record) {
        const { corporateComprehensiveTaxStr } = record;
        return returnEllipsisTooltip({ title: corporateComprehensiveTaxStr });
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
      }
    },
    {
      title: "对公综合税费（线上）",
      tooltip: "历史账单兼容字段",
      editable: true,
      dataIndex: "corporateComprehensiveTaxOnline",
      width: 100,
      hideInSearch: true,
      className: "danger-target",
      render(_, record) {
        const { corporateComprehensiveTaxOnlineStr } = record;
        return returnEllipsisTooltip({ title: corporateComprehensiveTaxOnlineStr });
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
      }
    },
    {
      title: "对公综合税费（线下）",
      tooltip: "历史账单兼容字段",
      editable: true,
      dataIndex: "corporateComprehensiveTaxOffline",
      width: 100,
      hideInSearch: true,
      className: "danger-target",
      render(_, record) {
        const { corporateComprehensiveTaxOfflineStr } = record;
        return returnEllipsisTooltip({ title: corporateComprehensiveTaxOfflineStr });
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
      }
    },
    {
      title: "年框扣费金额",
      editable: true,
      dataIndex: "yearlyDeductAmount",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { yearlyDeductAmountStr } = record;
        return returnEllipsisTooltip({ title: yearlyDeductAmountStr });
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
      }
    },
    {
      title: "净利润",
      editable: true,
      dataIndex: "grossProfitAmount",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { grossProfitAmountStr } = record;
        return returnEllipsisTooltip({ title: grossProfitAmountStr });
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
      }
    },
    {
      title: "公司利润",
      editable: true,
      dataIndex: "companyGrossAmount",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { companyGrossAmountStr } = record;
        return returnEllipsisTooltip({ title: companyGrossAmountStr });
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
      }
    },
    {
      title: "主播利润",
      editable: true,
      dataIndex: "anchorGrossAmount",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { anchorGrossAmountStr } = record;
        return returnEllipsisTooltip({ title: anchorGrossAmountStr });
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
      }
    },
    {
      title: "税点",
      tooltip: "历史账单兼容字段",
      editable: true,
      dataIndex: "taxPointAmount",
      width: 100,
      hideInSearch: true,
      className: "danger-target",
      render(_, record) {
        const { taxPointAmountStr } = record;
        return returnEllipsisTooltip({ title: taxPointAmountStr });
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
      }
    },
    {
      title: "主播净利润分成比例",
      editable: true,
      dataIndex: "anchorProfitRate",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { anchorProfitRateStr } = record;
        return returnEllipsisTooltip({ title: anchorProfitRateStr });
      },
      renderFormItem: (_, { isEditable }) => {
        return (
          isEditable && (
            <EditInputNumberPercent formatter={value => `${value}%`} style={{ width: "100%" }} max={100} step={1} placeholder="主播净利润分成比例" />
          )
        );
      }
    },
    {
      title: "商家利润分成比例",
      editable: true,
      dataIndex: "shopProfitRate",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { shopProfitRateStr } = record;
        return returnEllipsisTooltip({ title: shopProfitRateStr });
      },
      renderFormItem: (_, { isEditable }) => {
        return (
          isEditable && (
            <EditInputNumberPercent formatter={value => `${value}%`} style={{ width: "100%" }} max={100} step={1} placeholder="商家利润分成比例" />
          )
        );
      }
    },
    {
      title: "备注",
      editable: true,
      dataIndex: "remark",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      },
      fieldProps: { size: SIZE }
    },
    {
      title: "合同ID",
      editable: true,
      dataIndex: "contractId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      },
      fieldProps: { size: SIZE }
    },
    {
      title: "是否红冲",
      editable: true,
      dataIndex: "redInkType",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: isNeedRedPunchObj[text] });
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable ? (
          <EditSelect size={SIZE} placeholder="请选择" options={isNeedRedPunchOptions} filterOption={filterOptionLabel} showSearch allowClear />
        ) : null;
      }
    },
    {
      title: "红冲原月份",
      editable: true,
      dataIndex: "redInkOriginalMonth",
      width: 100,
      hideInSearch: true,
      fieldProps: { size: SIZE },
      valueType: "dateMonth"
    },
    {
      title: "操作",
      valueType: "option",
      hideInTable: activeMenu === menuKeys.adjustRecord || activeMenu === menuKeys.all || activeMenu === menuKeys.hasconfirm ? false : true,
      fixed: "right",
      editable: true,
      width: 100,
      render: (text: string, record: any, index: number, action: any) => {
        const { id, uuid } = record;
        return (
          <>
            {activeMenu === menuKeys.adjustRecord && canEditDelete(record) ? (
              <Space>
                <a
                  onClick={() => {
                    action?.startEditable(uuid);
                  }}
                >
                  编辑
                </a>
                <DeleteBtn params={{ ids: [id] }} btnProps={{ type: "link" }} action={action} api={apis.delete} />
              </Space>
            ) : null}
            {activeMenu === menuKeys.all ? (
              <Button type="link" onClick={() => handleDownload(record)}>
                下载明细
              </Button>
            ) : null}
            {activeMenu === menuKeys.hasconfirm ? (
              <>
                <Button type="link" disabled={canRedInk(record)} onClick={() => handleRedpunch(record)}>
                  红冲
                </Button>
              </>
            ) : null}
          </>
        );
      }
    }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      align: "center",
      ...item,
      title: returnEllipsisTooltip({ title })
    };
  });
};
