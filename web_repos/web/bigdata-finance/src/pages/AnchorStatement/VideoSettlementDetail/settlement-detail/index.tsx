import React, { useState, useRef, useMemo, useContext, useEffect } from "react";
import { Button, Form, message } from "antd";
import TableList from "@/components/TableList";
import { columns, advance_columns_search } from "./variable";
import apis from "@/services/anchorStatement/videosettlementdetail";
import dayjs from "dayjs";
import BatchRedpunch from "../components/batch-redpunch";
import UploadModal from "@/components/UploadModal";
import { SIZE } from "@/utils/constant";
import useCommonOptions from "@/hooks/useCommonOptions";
import { ifEmptyObj, pushExportHistory, splitSpaceComma } from "@/utils";
import { FieldsContext } from "../index";
import { TableRowSelection } from "antd/es/table/interface";
import BatchConfirm from "../components/batch-confirm";

export const menuKeys = {
  redpunch: "0",
  hasredpunch: "1",
  unconfirm: "2",
  hasconfirm: "3",
  adjustRecord: "4",
  all: "5"
};
const Res = () => {
  const [editForm] = Form.useForm();
  const actionRef = useRef<any>();
  const tableFormRef = useRef<any>();
  const { urlParams } = useContext(FieldsContext);
  const [isNeedRedPunchOptions, isNeedRedPunchObj] = useCommonOptions({ dimName: "主播结算明细-是否红冲" });
  const [platTypeOptions, platTypeObj] = useCommonOptions({ dimName: "主播结算明细-平台" });
  const [backFlagOptions, backFlagObj] = useCommonOptions({ dimName: "返款承担方" });
  const [shopTypeOptions, shopTypeObj] = useCommonOptions({ dimName: "店铺性质" });
  const [modelNameLv1Options, modelNameLv1Obj] = useCommonOptions({ dimName: "模式" });
  const [activeMenu, setActiveMenu] = useState(menuKeys.redpunch);
  const [showBatchRedpunch, setShowBatchRedpunch] = useState(false);
  const [batchConfirm, setBatchConfirm] = useState(false);
  const [showUpload, setShowUpload] = useState(false);
  const [selectedRows, setSelectedRows] = useState([]);
  const [selectedKeys, setSelectedKeys] = useState([]);
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const handleDownload = async record => {
    const { anchorId, anchorName, expectSettleMonth, itemId, publishDate, modelNameLv1Str, settleMonth, skuId } = record;
    const { status } = await apis.downloadDetail({
      anchorId,
      anchorName,
      expectSettleMonth,
      itemId,
      publishDate,
      modelNameLv1: modelNameLv1Str,
      settleMonth,
      skuId
    });
    if (status) {
      pushExportHistory();
    }
  };
  const handleRedpunch = async record => {
    setShowBatchRedpunch(true);
    setSelectedRows([record]);
  };
  const canRedInk = record => {
    // 以下是 针对已确认tab,需要禁用掉 redInkType 1/5 的数据，不能选择
    const { redInkType, dataType } = record;
    return redInkType === 1 || redInkType === 5 || dataType !== "system";
  };
  const canEditDelete = record => {
    const { confirm } = record;
    return confirm !== "1";
  };
  const columns_props = {
    canRedInk,
    canEditDelete,
    handleDownload,
    handleRedpunch,
    isNeedRedPunchOptions,
    isNeedRedPunchObj,
    shopTypeObj,
    modelNameLv1Obj,
    activeMenu,
    modelNameLv1Options,
    shopTypeOptions,
    platTypeOptions,
    platTypeObj,
    backFlagOptions,
    backFlagObj
  };
  const advanceColumnsProps = { shopTypeOptions, modelNameLv1Options };
  // 重置批量选择
  const resetSelected = () => {
    setSelectedKeys([]);
    setSelectedRows([]);
  };
  // 处理请求参数
  const handlePageParmas = (p: any) => {
    resetSelected();
    setEditableRowKeys([]);
    const params = tableFormRef.current.getFieldsValue();
    const finnalP = { ...p, ...params };

    const { publishDate, current, settleItemNum, itemIds, expectSettleMonth, settleMonth, modelNameLv1s, ...rest } = finnalP;
    if (publishDate) {
      const [startPublishDate, endPublishDate] = publishDate;
      rest.startPublishDate = dayjs(startPublishDate).format("YYYYMMDD");
      rest.endPublishDate = dayjs(endPublishDate).format("YYYYMMDD");
    }
    if (settleItemNum) {
      const [settleItemNumMin, settleItemNumMax] = settleItemNum;
      rest.settleItemNumMax = settleItemNumMax;
      rest.settleItemNumMin = settleItemNumMin;
    }
    // 结算月份
    if (expectSettleMonth) {
      const [startExpectSettleMonth, endExpectSettleMonth] = expectSettleMonth;
      rest.startExpectSettleMonth = dayjs(startExpectSettleMonth).format("YYYYMM");
      rest.endExpectSettleMonth = dayjs(endExpectSettleMonth).format("YYYYMM");
    }
    if (settleMonth) {
      const [startSettleMonth, endSettleMonth] = settleMonth;
      rest.startSettleMonth = dayjs(startSettleMonth).format("YYYYMM");
      rest.endSettleMonth = dayjs(endSettleMonth).format("YYYYMM");
    }
    // 模式
    if (modelNameLv1s?.length) {
      rest.modelNameLv1s = modelNameLv1s.map(item => modelNameLv1Obj[item]);
    }
    rest.itemIds = splitSpaceComma(itemIds);
    return { ...rest, pageNo: current };
  };
  const handleUrlParamsChange = p => {
    const params = tableFormRef.current?.getFieldsValue() || {};
    const res = handlePageParmas?.({ ...p, ...params });
    return res;
  };
  const batchDelete = async () => {
    const ids = selectedRows.map(row => row.id);
    const { status } = await apis.delete({ ids: ids });
    if (status) {
      actionRef.current?.reload();
      message.success("删除成功！");
    }
  };

  const reload = () => {
    actionRef.current?.reload();
  };

  const rowSelection: TableRowSelection<any> = {
    selectedRowKeys: selectedKeys,
    columnWidth: 50,
    getCheckboxProps: record => {
      // 建议红冲tab 数据已进行过滤
      return {
        disabled: canRedInk(record)
      };
    },
    onChange(selected, selectedRows) {
      setSelectedKeys(selected);
      setSelectedRows(selectedRows);
    }
  };
  const rowSelectionAdjust: TableRowSelection<any> = {
    selectedRowKeys: selectedKeys,
    columnWidth: 50,
    getCheckboxProps: record => {
      return {
        disabled: !canEditDelete(record)
      };
    },
    onChange(selected, selectedRows) {
      setSelectedKeys(selected);
      setSelectedRows(selectedRows);
    }
  };
  const rowSelectionUnconfirm: TableRowSelection<any> = {
    selectedRowKeys: selectedKeys,
    columnWidth: 50,
    getCheckboxProps: record => {
      const { dataType } = record;
      return {
        disabled: dataType !== "system"
      };
    },
    onChange(selected, selectedRows) {
      setSelectedKeys(selected);
      setSelectedRows(selectedRows);
    }
  };

  // 返回菜单
  const returnMenuItems = useMemo(() => {
    return [
      {
        key: menuKeys.redpunch,
        label: "建议红冲"
      },
      {
        key: menuKeys.hasredpunch,
        label: "已红冲"
      },
      {
        key: menuKeys.unconfirm,
        label: "未确认"
      },
      {
        key: menuKeys.hasconfirm,
        label: "已确认"
      },
      {
        key: menuKeys.adjustRecord,
        label: "调账记录"
      },
      {
        key: menuKeys.all,
        label: "全部"
      }
    ];
  }, []);
  // 返回操作按钮
  const returnActions = () => {
    return [
      <>
        {activeMenu === menuKeys.unconfirm ? (
          <Button type="primary" disabled={selectedRows.length === 0} size={SIZE} onClick={() => setBatchConfirm(true)}>
            批量确认
          </Button>
        ) : null}
      </>,
      <>
        {activeMenu === menuKeys.redpunch || activeMenu === menuKeys.hasconfirm ? (
          <Button type="primary" disabled={selectedRows.length === 0} size={SIZE} onClick={() => setShowBatchRedpunch(true)}>
            批量红冲
          </Button>
        ) : null}
      </>,
      <>
        {activeMenu === menuKeys.adjustRecord ? (
          <>
            <Button type="primary" size={SIZE} onClick={() => setShowUpload(true)}>
              上传调账
            </Button>
            <Button type="primary" size={SIZE} disabled={selectedRows.length === 0} onClick={batchDelete}>
              批量删除
            </Button>
          </>
        ) : null}
      </>
    ];
  };
  const returnRowSelections = () => {
    if (activeMenu === menuKeys.unconfirm) {
      return rowSelectionUnconfirm;
    }
    if (activeMenu === menuKeys.adjustRecord) {
      return rowSelectionAdjust;
    }
    if (activeMenu === menuKeys.redpunch || activeMenu === menuKeys.hasconfirm) {
      return rowSelection;
    }
    return false;
  };
  const batchRedpunchProps = {
    title: "批量红冲",
    visible: showBatchRedpunch,
    selectedRows,
    fresh: () => {
      reload();
    },
    close: () => {
      setShowBatchRedpunch(false);
    }
  };
  const batchConfirmProps = {
    title: "批量确认",
    visible: batchConfirm,
    selectedRows,
    fresh: () => {
      reload();
    },
    close: () => {
      setBatchConfirm(false);
    }
  };

  const uploadAdjustProps = {
    title: "上传调账记录",
    visible: showUpload,
    message: (
      <>
        <span>1. 黄色字段为必填项</span>
        <br />
        <span>2. 调账记录会入账到当前做账月份，不影响历史已完成账单</span>
        <br />
        <span>3. 所有字段均可编辑，尽量信息填写完整</span>
        <br />
        <span>4. 同一订单结算月份，最细支持到不同SKU调账</span>
        <br />
      </>
    ),
    tempUrl: "https://s.xinc818.com/files/webcim5gd210ysfzv08/短视频结算明细调账模板.xlsx",
    api: apis.upload,
    fresh: () => {
      reload();
    },
    close: () => {
      setShowUpload(false);
    }
  };
  const editable: any = {
    type: "single",
    form: editForm,
    onSave: async (key, editData) => {
      const { modelNameLv1, publishDate, settleMonth, redInkOriginalMonth, ...rest } = editData;
      const params = {
        ...rest,
        modelNameLv1: modelNameLv1Obj[modelNameLv1] ?? modelNameLv1
      };
      if (publishDate) {
        params.publishDate = dayjs(publishDate).format("YYYYMMDD");
      }
      if (settleMonth) {
        params.settleMonth = dayjs(settleMonth).format("YYYYMM");
      }
      if (redInkOriginalMonth) {
        params.redInkOriginalMonth = dayjs(redInkOriginalMonth).format("YYYYMM");
      }
      const { status } = await apis.update(params);
      editForm.resetFields([key]);
      if (status) {
        message.success("修改成功！");
      }
      actionRef.current?.reload();
    },
    editableKeys,
    onChange: setEditableRowKeys,
    actionRender: (row, config, { save, cancel }) => [save, cancel]
  };
  const returnSummaryApi = useMemo(() => {
    return activeMenu === menuKeys.redpunch || activeMenu === menuKeys.hasredpunch ? false : apis.getListSummary;
  }, [activeMenu]);
  useEffect(() => {
    if (ifEmptyObj(urlParams)) {
      tableFormRef.current.setFieldsValue(urlParams);
      const { confirmStatus } = urlParams;
      if (confirmStatus === "1") {
        setActiveMenu(menuKeys.hasconfirm);
      } else if (confirmStatus === "0") {
        setActiveMenu(menuKeys.unconfirm);
      }
    }
  }, [urlParams]);
  return (
    <>
      <TableList
        form={{
          initialValues: {
            expectSettleMonth: [dayjs().subtract(1, "month"), dayjs()]
          }
        }}
        formRef={tableFormRef}
        actionRef={actionRef}
        scroll={{ y: "calc(100vh - 400px)" }}
        preFetch={handleUrlParamsChange}
        advanceColumns={advance_columns_search(advanceColumnsProps)}
        columns={columns(columns_props)}
        params={{ tab: +activeMenu }}
        api={apis.getList}
        summaryApi={returnSummaryApi}
        rowKey="uuid"
        editable={editable}
        rowSelection={returnRowSelections()}
        downloadApi={apis.downloadListAll}
        toolbar={{
          menu: {
            type: "tab",
            activeKey: activeMenu,
            items: returnMenuItems,
            onChange: key => {
              setActiveMenu(key as string);
            }
          },
          actions: returnActions()
        }}
      />
      {batchRedpunchProps.visible ? <BatchRedpunch {...batchRedpunchProps} /> : null}
      {batchConfirmProps.visible ? <BatchConfirm {...batchConfirmProps} /> : null}
      {uploadAdjustProps.visible ? <UploadModal {...uploadAdjustProps} /> : null}
    </>
  );
};

export default React.memo(Res);
