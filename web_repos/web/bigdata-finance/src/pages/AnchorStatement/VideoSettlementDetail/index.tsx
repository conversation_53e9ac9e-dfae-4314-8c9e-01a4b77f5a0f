import React from "react";
import KeepAlive from "react-activation";
import SettlementDetail from "./settlement-detail";

export const FieldsContext = React.createContext<any>(null);
export const PageUrl = "/anchorstatement/videosettlementdetail";

const Res: React.FC = () => {
  return (
    <FieldsContext.Provider value={{}}>
      <SettlementDetail />
    </FieldsContext.Provider>
  );
};

const AliveRecord = () => {
  return (
    <KeepAlive name={PageUrl}>
      <Res />
    </KeepAlive>
  );
};
export default React.memo(AliveRecord);
