import React, { useRef, useState, useContext, useEffect } from "react";
import TableList from "@/components/TableList";
import { columns } from "./variable";
import apis from "@/services/anchorStatement/statement";
import { pushExportHistory } from "@/utils";
import { FieldsContext } from "./../index";
import { tabMap, tab_bajia_expend } from "../variable";
import dayjs from "dayjs";
import styles from "../index.less";
const curTab = tabMap[tab_bajia_expend];
const menu_api: Record<string, Record<string, any>> = {
  [curTab.menu_total]: {
    api: apis.getBaJiaExpendCollectPage,
    summaryApi: apis.getBaJiaExpendCollectPageTotal,
    defaultExpandAll: { deep: 0 },
    hasChildren: true,
    className: styles["table-bajia-expend-total"]
    // rowKey: record => {
    //   const { settleMonth, anchorName, mainExpenditure, subject, confirmStatus, level } = record;
    //   return settleMonth + anchorName + mainExpenditure + subject + confirmStatus + level;
    // }
  },
  [curTab.menu_detail]: {
    api: apis.getBaJiaExpendDetailPage,
    summaryApi: apis.getBaJiaExpendDetailPageTotal,
    className: styles["table-bajia-expend-detail"]
    // rowKey: "id"
  }
};
const Res = props => {
  const { handlePageParmas, tabInfo, returnParams, setPageParams } = useContext(FieldsContext);
  const { tableProps } = props;
  const actionRef = useRef<any>();
  const tableFormRef = useRef<any>();

  const [activeMenu, setActiveMenu] = useState(tabInfo.menu || curTab.menu_total);
  const [resetFlag, setResetFlag] = useState(false);
  const [menuApi, setMenuApi] = useState(() => menu_api[activeMenu]);

  const handleTotalDetail = async record => {
    const { anchorName, confirmStatus, settleMonth } = record;
    setActiveMenu(curTab.menu_detail);
    setMenuApi(menu_api[curTab.menu_detail]);
    tableFormRef.current?.setFieldsValue({
      anchorNameList: [anchorName],
      settleMonth: [dayjs(settleMonth), dayjs(settleMonth)],
      entryStatusList: [confirmStatus]
    });
  };
  const handleTotalExport = async record => {
    const { anchorName, confirmStatus, settleMonth } = record;
    const { status } = await apis.downloadBaJiaExpendCollect({ anchorName, confirmStatus, settleMonth });
    if (status) {
      pushExportHistory();
    }
  };
  const handleDatas = data => {
    return data.map(item => {
      return {
        ...item,
        $_unionPrimaryKey: JSON.stringify(item)
      };
    });
  };
  const handleParams = (p, sort) => {
    const params = tableFormRef.current.getFieldsValue();
    const res = handlePageParmas?.({ ...p, ...params }, sort);
    setPageParams(res);
    return res;
  };
  const toolbar_menus = [
    {
      key: curTab.menu_total,
      label: <span>汇总</span>
    },
    {
      key: curTab.menu_detail,
      label: <span>明细</span>
    }
  ];
  const columns_props = {
    menu: activeMenu,
    handleTotalDetail,
    handleTotalExport
  };
  useEffect(() => {
    if (tabInfo?.tab === tab_bajia_expend) {
      // 由结算汇总跳转进入
      tableFormRef.current?.setFieldsValue(returnParams());
      actionRef.current?.reload();
    }
  }, [tableFormRef, tabInfo?.tab]);
  return (
    <>
      <TableList
        formRef={tableFormRef}
        actionRef={actionRef}
        scroll={{ y: "calc(100vh - 370px)" }}
        columns={columns(columns_props)}
        {...menuApi}
        // className={menuApi.className}
        // api={menuApi.api}
        // summaryApi={menuApi.summaryApi}
        // defaultExpandAll={menuApi.defaultExpandAll}
        // hasChildren={menuApi.hasChildren}
        rowKey="$_unionPrimaryKey"
        handleDatas={handleDatas}
        preFetch={handleParams}
        params={{
          resetFlag,
          $_curTab: activeMenu
        }}
        onReset={() => setResetFlag(!resetFlag)}
        toolbar={{
          menu: {
            type: "tab",
            activeKey: activeMenu,
            items: toolbar_menus,
            onChange: (key: string) => {
              setActiveMenu(key as string);
              setMenuApi(menu_api[key]);
              console.log(22222, menu_api[key]);
            }
          }
        }}
        {...tableProps}
      />
    </>
  );
};

export default React.memo(Res);
