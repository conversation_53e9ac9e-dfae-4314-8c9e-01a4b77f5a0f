import { Space, Button } from "antd";
import { millennialsNumber, returnEllipsisTooltip } from "@/utils";
import { columns_search, confirmStatus_column } from "../variable";
import { tabMap, tab_bajia_income } from "../variable";
import { SIZE } from "@/utils/constant";
const { menu_detail, menu_total } = tabMap[tab_bajia_income];
export const columns = ({ menu, handleTotalDetail, handleTotalExport }: any): Array<TableListItem> => {
  // 汇总
  const total_columns = [
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      hideInSearch: true,
      fixed: "left",
      render: (_, record, index) => {
        const { level } = record;
        return level === 0 ? index + 1 : "";
      }
    },
    {
      title: "对账月份",
      dataIndex: "settleMonthStr",
      hideInSearch: true,
      fixed: "left",
      width: 80
    },
    {
      title: "主播",
      dataIndex: "anchorName",
      hideInSearch: true,
      fixed: "left",
      width: 80
    },
    {
      title: "收入主体",
      dataIndex: "mainBody",
      hideInSearch: true,
      width: 80
    },
    {
      title: "项目内容",
      dataIndex: "subject",
      hideInSearch: true,
      width: 120
    },
    {
      title: "收入总额",
      dataIndex: "amount",
      hideInSearch: true,
      width: 110,
      summaryTooltip: {
        divisor: 10000,
        millennials: true,
        millennialsOptions: { minimumFractionDigits: 4 }
      },
      render(text: number) {
        return millennialsNumber(text / 10000, { minimumFractionDigits: 4 });
      }
    },

    {
      title: "公司金额",
      dataIndex: "companyAmount",
      hideInSearch: true,
      width: 110,
      summaryTooltip: {
        divisor: 10000,
        millennials: true,
        millennialsOptions: { minimumFractionDigits: 4 }
      },
      render(text: number) {
        return millennialsNumber(text / 10000, { minimumFractionDigits: 4 });
      }
    },
    {
      title: "主播金额",
      dataIndex: "anchorAmount",
      hideInSearch: true,
      width: 110,
      summaryTooltip: {
        divisor: 10000,
        millennials: true,
        millennialsOptions: { minimumFractionDigits: 4 }
      },
      render(text: number) {
        return millennialsNumber(text / 10000, { minimumFractionDigits: 4 });
      }
    },
    {
      title: "分成比例",
      dataIndex: "personRateStr",
      hideInSearch: true,
      width: 80
    },

    confirmStatus_column(),
    {
      title: "操作",
      valueType: "option",
      fixed: "right",
      width: 100,
      render: (_, record) => {
        const { level, confirmStatus } = record;
        return (
          <Space>
            {level === 0 ? (
              <>
                <Button size={SIZE} type="link" onClick={() => handleTotalDetail(record)}>
                  详情
                </Button>
                {confirmStatus === 1 ? (
                  <Button size={SIZE} type="link" onClick={() => handleTotalExport(record)}>
                    导出
                  </Button>
                ) : null}
              </>
            ) : null}
          </Space>
        );
      }
    }
  ];
  // 明细
  const detail_columns = [
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      hideInSearch: true,
      fixed: "left",
      render: (_, record, index) => {
        return index + 1;
      }
    },
    {
      title: "对账月份",
      dataIndex: "settleMonthStr",
      hideInSearch: true,
      fixed: "left",
      width: 80
    },
    {
      title: "主播",
      dataIndex: "anchorName",
      hideInSearch: true,
      fixed: "left",
      width: 80,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "费用主体",
      dataIndex: "mainBody",
      hideInSearch: true,
      width: 80,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "日期",
      dataIndex: "dateOccurrenceStr",
      hideInSearch: true,
      sortDirections: ["descend", "ascend"],
      sorter: true,
      width: 80
    },
    {
      title: "科目",
      dataIndex: "subject",
      hideInSearch: true,
      width: 120,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "摘要",
      dataIndex: "digest",
      hideInSearch: true,
      width: 180,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "金额",
      dataIndex: "amount",
      hideInSearch: true,
      width: 100,
      sortDirections: ["descend", "ascend"],
      sorter: true,
      summaryTooltip: {
        divisor: 10000,
        millennials: true,
        millennialsOptions: { minimumFractionDigits: 4 }
      },
      render(text: number) {
        return millennialsNumber(text / 10000, { minimumFractionDigits: 4 });
      }
    },
    {
      title: "个人比例",
      dataIndex: "personRateStr",
      hideInSearch: true,
      width: 80
    },
    {
      title: "备注",
      dataIndex: "remark",
      hideInSearch: true,
      width: 100,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "备注2",
      dataIndex: "remarkTwo",
      hideInSearch: true,
      width: 100,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "公司金额",
      dataIndex: "companyAmount",
      hideInSearch: true,
      width: 110,
      sortDirections: ["descend", "ascend"],
      sorter: true,
      summaryTooltip: {
        divisor: 10000,
        millennials: true,
        millennialsOptions: { minimumFractionDigits: 4 }
      },
      render(text: number) {
        return millennialsNumber(text / 10000, { minimumFractionDigits: 4 });
      }
    },
    {
      title: "主播金额",
      dataIndex: "anchorAmount",
      hideInSearch: true,
      width: 110,
      sortDirections: ["descend", "ascend"],
      sorter: true,
      summaryTooltip: {
        divisor: 10000,
        millennials: true,
        millennialsOptions: { minimumFractionDigits: 4 }
      },
      render(text: number) {
        return millennialsNumber(text / 10000, { minimumFractionDigits: 4 });
      }
    },

    confirmStatus_column()
  ];
  const return_columns: Record<string, TableListItem[]> = {
    [menu_total]: total_columns,
    [menu_detail]: detail_columns
  };

  return [...columns_search({}), ...return_columns[menu]].map((item: TableListItem) => {
    const { title } = item;
    return {
      align: "center",
      ...item,
      title: returnEllipsisTooltip({ title })
    };
  });
};
