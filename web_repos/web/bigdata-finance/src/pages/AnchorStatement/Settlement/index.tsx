import { Tabs } from "antd";
import React, { useState } from "react";
import KeepAlive from "react-activation";
import { SIZE, tabs_tabBarStyle } from "@/utils/constant";
import JSHZ from "./JSHZ";
import BajiaExpend from "./bajia-expend";
import BajiaIncome from "./bajia-income";
import XinxuanExpend from "./xinxuan-expend";
import LiveRevenue from "./live-revenue";
import dayjs from "dayjs";
import styles from "./index.less";
import { tab_JSHZ, tab_live_revenue, tab_bajia_income, tab_xinxuan_expend, tab_bajia_expend } from "./variable";
import { ifEmptyObj, returnSortParams } from "@/utils";

export const FieldsContext = React.createContext<any>(null);

export const PageUrl = "/anchorstatement/index";
interface ITabInfo {
  tab: string;
  menu: string;
  params: Record<string, any>;
}
const Res = () => {
  const [tabInfo, setTabInfo] = useState<ITabInfo>({ tab: tab_JSHZ, menu: "", params: {} });
  const initialValues = {
    settleMonth: [dayjs().subtract(1, "month"), dayjs()],
    entryStatusList: [1]
  };
  const [pageParams, setPageParams] = useState(() => initialValues);
  const returnParams = () => {
    const params = ifEmptyObj(pageParams) ? pageParams : initialValues;
    return params;
  };
  // 处理请求参数
  const handlePageParmas = (p: any, sort: Record<string, any> = {}) => {
    const { current, ...rest } = p;
    const sortParams = returnSortParams(sort);
    if (p.settleMonth) {
      const [settleMonthStart, settleMonthEnd] = p.settleMonth;
      rest.settleMonthStart = dayjs(settleMonthStart).format("YYYYMM");
      rest.settleMonthEnd = dayjs(settleMonthEnd).format("YYYYMM");
    }

    const params = {
      pageNo: current,
      ...rest,
      ...sortParams
    };
    return params;
  };
  const tabItems = [
    {
      key: tab_JSHZ,
      label: "结算汇总",
      children: (
        <JSHZ
          tableProps={{
            className: styles["table-jshz"],
            form: {
              initialValues: {
                ...initialValues
              }
            }
          }}
        />
      )
    },
    {
      key: tab_live_revenue,
      label: "直播收入",
      children: (
        <LiveRevenue
          tableProps={{
            className: styles["table-live-revenue"],
            form: {
              initialValues: {
                ...initialValues
              }
            }
          }}
        />
      )
    },
    {
      key: tab_bajia_income,
      label: "巴伽推广打赏收入",
      children: (
        <BajiaIncome
          tableProps={{
            form: {
              initialValues: {
                ...initialValues
              }
            }
          }}
        />
      )
    },
    {
      key: tab_xinxuan_expend,
      label: "辛选费用支出",
      children: (
        <XinxuanExpend
          tableProps={{
            form: {
              initialValues: {
                ...initialValues
              }
            }
          }}
        />
      )
    },
    {
      key: tab_bajia_expend,
      label: "巴伽费用支出",
      children: (
        <BajiaExpend
          tableProps={{
            form: {
              initialValues: {
                ...initialValues
              }
            }
          }}
        />
      )
    }
  ];
  return (
    <FieldsContext.Provider value={{ handlePageParmas, tabInfo, setTabInfo, returnParams, setPageParams }}>
      <Tabs
        activeKey={tabInfo.tab}
        tabBarStyle={tabs_tabBarStyle}
        size={SIZE}
        onChange={val => {
          setTabInfo({ ...tabInfo, tab: val, params: pageParams });
        }}
        items={tabItems}
      />
    </FieldsContext.Provider>
  );
};

const AliveAnchorItemSale = props => (
  <KeepAlive name={PageUrl}>
    <Res {...props} />
  </KeepAlive>
);

export default React.memo(AliveAnchorItemSale);
