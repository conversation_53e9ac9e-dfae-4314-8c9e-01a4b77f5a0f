const models = [
  { name: "巴伽-推广打赏收入", code: "anchorstatement_BJTGDS_tab", description: null, status: 1, type: 2 },
  { name: "巴伽-推广打赏收入-明细", code: "anchorstatement_BJTGDS_DETAIL_tab", description: null, status: 1, type: 2 },
  { name: "巴伽-推广打赏收入-汇总", code: "anchorstatement_BJTGDS_SUMMARY_tab", description: null, status: 1, type: 2 },
  { name: "巴伽-支出", code: "anchorstatement_BJZC_tab", description: null, status: 1, type: 2 },
  { name: "巴伽-支出-明细", code: "anchorstatement_BJZC_DETAIL_tab", description: null, status: 1, type: 2 },
  { name: "巴伽-支出-汇总", code: "anchorstatement_BJZC_SUMMARY_tab", description: null, status: 1, type: 2 },
  { name: "直播收入", code: "anchorstatement_ZBSR_tab", description: null, status: 1, type: 2 },
  { name: "直播收入-汇总", code: "anchorstatement_ZBSR_SUMMARY_tab", description: null, status: 1, type: 2 },
  { name: "结算汇总", code: "anchorstatement_JSHZ_tab", description: null, status: 1, type: 2 },
  { name: "结算汇总-汇总", code: "anchorstatement_JSHZ_SUMMARY_tab", description: null, status: 1, type: 2 },
  { name: "费用支出", code: "anchorstatement_FYZC_tab", description: null, status: 1, type: 2 },
  { name: "费用支出-明细", code: "anchorstatement_FYZC_DETAIL_tab", description: null, status: 1, type: 2 },
  { name: "费用支出-汇总", code: "anchorstatement_FYZC_SUMMARY_tab", description: null, status: 1, type: 2 }
];

// 巴伽-推广打赏收入 汇总
const btn_BJTGDS_SUMMARY = [{ name: "导出", code: "anchorstatement_BJTGDS_SUMMARY_export", description: null, status: 1, type: 3 }];
// 巴伽-推广打赏收入 明细
const btn_BJTGDS_DETAIL = [
  { name: "导出", code: "anchorstatement_BJTGDS_DETAIL_export", description: null, status: 1, type: 3 },
  { name: "批量删除", code: "anchorstatement_BJTGDS_DETAIL_batchdelete", description: null, status: 1, type: 3 },
  { name: "上传", code: "anchorstatement_BJTGDS_DETAIL_upload", description: null, status: 1, type: 3 },
  { name: "编辑", code: "anchorstatement_BJTGDS_DETAIL_edit", description: null, status: 1, type: 3 },
  { name: "删除", code: "anchorstatement_BJTGDS_DETAIL_delete", description: null, status: 1, type: 3 }
];
// 巴伽-支出 汇总
const btn_BJZC_SUMMARY = [{ name: "导出", code: "anchorstatement_BJZC_SUMMARY_export", description: null, status: 1, type: 3 }];
// 巴伽-支出 明细
const btn_BJZC_DETAIL = [
  { name: "导出", code: "anchorstatement_BJZC_DETAIL_export", description: null, status: 1, type: 3 },
  { name: "批量删除", code: "anchorstatement_BJZC_DETAIL_batchdelete", description: null, status: 1, type: 3 },
  { name: "上传", code: "anchorstatement_BJZC_DETAIL_upload", description: null, status: 1, type: 3 },
  { name: "编辑", code: "anchorstatement_BJZC_DETAIL_edit", description: null, status: 1, type: 3 },
  { name: "删除", code: "anchorstatement_BJZC_DETAIL_delete", description: null, status: 1, type: 3 }
];
// 直播收入 汇总
const btn_ZBSR_SUMMARY = [{ name: "导出", code: "anchorstatement_ZBSR_SUMMARY_export", description: null, status: 1, type: 3 }];
// 结算汇总 汇总
const btn_JSHZ_SUMMARY = [
  { name: "导出", code: "anchorstatement_JSHZ_SUMMARY_export", description: null, status: 1, type: 3 },
  { name: "整体导出", code: "anchorstatement_JSHZ_SUMMARY_batchexport", description: null, status: 1, type: 3 }
];
// 费用支出 汇总
const btn_FYZC_SUMMARY = [{ name: "导出", code: "anchorstatement_FYZC_SUMMARY_export", description: null, status: 1, type: 3 }];
// 费用支出 明细
const btn_FYZC_DETAIL = [{ name: "导出", code: "anchorstatement_FYZC_DETAIL_export", description: null, status: 1, type: 3 }];
