import React, { useContext, useState, useRef, useEffect } from "react";
import TableList from "@/components/TableList";
import { columns } from "./variable";
import apis from "@/services/anchorStatement/statement";
import { pushExportHistory } from "@/utils";
import { FieldsContext } from "./../index";
import { tabMap, tab_JSHZ, tab_xinxuan_expend } from "../variable";
import dayjs from "dayjs";
const Res = props => {
  const { handlePageParmas, tabInfo, setTabInfo, returnParams, setPageParams } = useContext(FieldsContext);
  const { tableProps } = props;
  const actionRef = useRef<any>();
  const tableFormRef = useRef<any>();
  const [resetFlag, setResetFlag] = useState(false);
  const [downloadParams, setDownloadParams] = useState({});
  const handleExport = async record => {
    const { status } = await apis.downloadAnchorSettle({ ...downloadParams, settleExportList: [record] });
    if (status) {
      pushExportHistory();
    }
  };
  const handleDetail = async record => {
    const { project, settleMonth, anchorName, confirmStatus } = record;
    const jump_tab = tabMap[tab_xinxuan_expend];
    setPageParams({ anchorNameList: [anchorName], settleMonth: [dayjs(settleMonth), dayjs(settleMonth)], entryStatusList: [confirmStatus] });
    setTabInfo({
      tab: project,
      menu: jump_tab.menu_total
    });
  };
  const handleDownLoadParams = p => {
    const { entryStatusList, ...rest } = p;
    if (entryStatusList?.includes(1) || entryStatusList?.length === 0) {
      rest.entryStatusList = ["1"];
    }
    return rest;
  };
  const handleParams = p => {
    const params = tableFormRef.current.getFieldsValue();
    const res = handlePageParmas?.({ ...p, ...params });
    setPageParams(res);
    return res;
  };
  const columns_props = {
    handleExport,
    handleDetail
  };
  useEffect(() => {
    if (tabInfo?.tab === tab_JSHZ) {
      // 由结算汇总跳转进入
      tableFormRef.current?.setFieldsValue(returnParams());
      // actionRef.current?.reload();
    }
  }, [tableFormRef, tabInfo]);
  return (
    <>
      <TableList
        manualRequest={true}
        formRef={tableFormRef}
        actionRef={actionRef}
        defaultExpandAll={{ deep: 0 }}
        hasChildren={true}
        scroll={{ y: "calc(100vh - 330px)" }}
        columns={columns(columns_props)}
        api={apis.getCollectCollectPage}
        summaryApi={apis.getCollectCollectPageTotal}
        downloadApi={apis.downloadAnchorSettle}
        downloadBtn={{
          btnProps: { disabled: downloadParams?.entryStatusList?.join() === "0" ? true : false },
          tooltipProps: { title: downloadParams?.entryStatusList?.join() === "0" ? "仅支持导出已入账数据" : "" }
        }}
        handleDownLoadParams={handleDownLoadParams}
        paramsChange={setDownloadParams}
        params={{
          resetFlag
        }}
        onReset={() => setResetFlag(!resetFlag)}
        rowKey="unionPrimaryKey"
        preFetch={handleParams}
        {...tableProps}
      />
    </>
  );
};

export default React.memo(Res);
