import { Button } from "antd";
import { millennialsNumber, returnEllipsisTooltip } from "@/utils";
import { columns_search, confirmStatus_column } from "../variable";
import { SIZE } from "@/utils/constant";
export const columns = ({ handleDetail, handleExport }: any): Array<TableListItem> => {
  return [
    ...columns_search({}),
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      hideInSearch: true,
      fixed: "left",
      render: (_, record, index) => {
        const { level } = record;
        return level === 0 ? index + 1 : "";
      }
    },
    {
      title: "对账月份",
      dataIndex: "settleMonthStr",
      hideInSearch: true,
      fixed: "left",
      width: 80
    },
    {
      title: "主播",
      dataIndex: "anchorName",
      hideInSearch: true,
      fixed: "left",
      width: 80
    },
    {
      title: "项目内容",
      dataIndex: "project",
      hideInSearch: true,
      width: 120,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "GMV战报金额",
      dataIndex: "gmv",
      hideInSearch: true,
      width: 100,
      summaryTooltip: {
        divisor: 10000,
        millennials: true,
        millennialsOptions: { minimumFractionDigits: 4 }
      },
      render(text: number) {
        return millennialsNumber(text / 10000, { minimumFractionDigits: 4 });
      }
    },
    {
      title: "订单结算金额",
      dataIndex: "settleAmount",
      hideInSearch: true,
      width: 100,
      summaryTooltip: {
        divisor: 10000,
        millennials: true,
        millennialsOptions: { minimumFractionDigits: 4 }
      },
      render(text: number) {
        return millennialsNumber(text / 10000, { minimumFractionDigits: 4 });
      }
    },
    {
      title: "总金额",
      dataIndex: "grossProfitAmount",
      hideInSearch: true,
      width: 100,
      summaryTooltip: {
        divisor: 10000,
        millennials: true,
        millennialsOptions: { minimumFractionDigits: 4 }
      },
      render(text: number) {
        return millennialsNumber(text / 10000, { minimumFractionDigits: 4 });
      }
    },
    {
      title: "公司金额",
      dataIndex: "companyGrossAmount",
      hideInSearch: true,
      width: 100,
      summaryTooltip: {
        divisor: 10000,
        millennials: true,
        millennialsOptions: { minimumFractionDigits: 4 }
      },
      render(text: number) {
        return millennialsNumber(text / 10000, { minimumFractionDigits: 4 });
      }
    },
    {
      title: "主播金额",
      dataIndex: "anchorGrossAmount",
      hideInSearch: true,
      width: 100,
      summaryTooltip: {
        divisor: 10000,
        millennials: true,
        millennialsOptions: { minimumFractionDigits: 4 }
      },
      render(text: number) {
        return millennialsNumber(text / 10000, { minimumFractionDigits: 4 });
      }
    },
    confirmStatus_column(),
    {
      title: "操作",
      valueType: "option",
      fixed: "right",
      width: 100,
      render: (_, record) => {
        const { level, confirmStatus } = record;
        return (
          <>
            {level === 0 && confirmStatus === 1 ? (
              <Button size={SIZE} type="link" onClick={() => handleExport(record)}>
                导出
              </Button>
            ) : null}
            {level === 1 ? (
              <Button size={SIZE} type="link" onClick={() => handleDetail(record)}>
                详情
              </Button>
            ) : null}
          </>
        );
      }
    }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      align: "center",
      ...item,
      title: returnEllipsisTooltip({ title })
    };
  });
};
