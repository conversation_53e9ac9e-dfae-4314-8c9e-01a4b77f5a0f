import { Space, Button } from "antd";
import { millennialsNumber, returnEllipsisTooltip } from "@/utils";
import { columns_search, confirmStatus_column } from "../variable";
import { tabMap, tab_xinxuan_expend } from "../variable";
import { SIZE } from "@/utils/constant";
const { menu_detail, menu_total } = tabMap[tab_xinxuan_expend];
export const columns = ({ menu, handleTotalDetail, handleTotalExport }: any): Array<TableListItem> => {
  // 汇总
  const total_columns = [
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      hideInSearch: true,
      fixed: "left",
      render: (_, record, index) => {
        const { level } = record;
        return level === 0 ? index + 1 : "";
      }
    },
    {
      title: "对账月份",
      dataIndex: "settleMonthStr",
      hideInSearch: true,
      fixed: "left",
      width: 80
    },
    {
      title: "主播",
      dataIndex: "anchorName",
      hideInSearch: true,
      fixed: "left",
      width: 80,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "项目内容",
      dataIndex: "expenseCategory",
      hideInSearch: true,
      width: 120,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "支出总额",
      dataIndex: "debitAmount",
      hideInSearch: true,
      width: 110,
      summaryTooltip: {
        divisor: 10000,
        millennials: true,
        millennialsOptions: { minimumFractionDigits: 4 }
      },
      render(text: number) {
        return millennialsNumber(text / 10000, { minimumFractionDigits: 4 });
      }
    },
    {
      title: "公司金额",
      dataIndex: "companyExpenditure",
      hideInSearch: true,
      width: 110,
      summaryTooltip: {
        divisor: 10000,
        millennials: true,
        millennialsOptions: { minimumFractionDigits: 4 }
      },
      render(text: number) {
        return millennialsNumber(text / 10000, { minimumFractionDigits: 4 });
      }
    },
    {
      title: "主播金额",
      dataIndex: "anchorExpenditure",
      hideInSearch: true,
      width: 110,
      summaryTooltip: {
        divisor: 10000,
        millennials: true,
        millennialsOptions: { minimumFractionDigits: 4 }
      },
      render(text: number) {
        return millennialsNumber(text / 10000, { minimumFractionDigits: 4 });
      }
    },
    {
      title: "分成比例",
      dataIndex: "anchorRateStr",
      hideInSearch: true,
      width: 80
    },
    confirmStatus_column(),
    {
      title: "操作",
      valueType: "option",
      fixed: "right",
      width: 100,
      render: (_, record) => {
        const { level, confirmStatus } = record;
        return (
          <Space>
            {level === 0 ? (
              <>
                <Button size={SIZE} type="link" onClick={() => handleTotalDetail(record)}>
                  详情
                </Button>
                {confirmStatus === 1 ? (
                  <Button size={SIZE} type="link" onClick={() => handleTotalExport(record)}>
                    导出
                  </Button>
                ) : null}
              </>
            ) : null}
          </Space>
        );
      }
    }
  ];
  // 明细
  const detail_columns = [
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      hideInSearch: true,
      fixed: "left",
      render: (_, record, index) => {
        return index + 1;
      }
    },
    {
      title: "对账月份",
      dataIndex: "settleMonthStr",
      hideInSearch: true,
      fixed: "left",
      width: 80
    },
    {
      title: "主播",
      dataIndex: "anchorName",
      hideInSearch: true,
      fixed: "left",
      width: 80,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "分类",
      dataIndex: "expenseCategory",
      hideInSearch: true,
      width: 120,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "账套",
      dataIndex: "accountingBook",
      hideInSearch: true,
      width: 150,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "日期",
      dataIndex: "periodMonth",
      hideInSearch: true,
      width: 80,
      sortDirections: ["descend", "ascend"],
      sorter: true
    },
    {
      title: "科目",
      dataIndex: "accountName",
      hideInSearch: true,
      width: 120,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "摘要",
      dataIndex: "voucherSummary",
      hideInSearch: true,
      width: 180,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "金额",
      dataIndex: "debitAmount",
      hideInSearch: true,
      width: 110,
      sortDirections: ["descend", "ascend"],
      sorter: true,
      summaryTooltip: {
        divisor: 10000,
        millennials: true,
        millennialsOptions: { minimumFractionDigits: 4 }
      },
      render(text: number) {
        return millennialsNumber(text / 10000, { minimumFractionDigits: 4 });
      }
    },
    {
      title: "公司金额",
      dataIndex: "companyExpenditure",
      hideInSearch: true,
      width: 110,
      sortDirections: ["descend", "ascend"],
      sorter: true,
      summaryTooltip: {
        divisor: 10000,
        millennials: true,
        millennialsOptions: { minimumFractionDigits: 4 }
      },
      render(text: number) {
        return millennialsNumber(text / 10000, { minimumFractionDigits: 4 });
      }
    },
    {
      title: "主播金额",
      dataIndex: "anchorExpenditure",
      hideInSearch: true,
      width: 110,
      sortDirections: ["descend", "ascend"],
      sorter: true,
      summaryTooltip: {
        divisor: 10000,
        millennials: true,
        millennialsOptions: { minimumFractionDigits: 4 }
      },
      render(text: number) {
        return millennialsNumber(text / 10000, { minimumFractionDigits: 4 });
      }
    },
    {
      title: "分成比例",
      dataIndex: "anchorRateStr",
      hideInSearch: true,
      width: 110
    },

    confirmStatus_column()
  ];
  const return_columns: Record<string, TableListItem[]> = {
    [menu_total]: total_columns,
    [menu_detail]: detail_columns
  };
  return [...columns_search({}), ...return_columns[menu]].map((item: TableListItem) => {
    const { title } = item;
    return {
      align: "center",
      ...item,
      title: returnEllipsisTooltip({ title })
    };
  });
};
