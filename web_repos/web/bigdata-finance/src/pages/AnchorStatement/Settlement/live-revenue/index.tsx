import React, { useContext, useEffect, useRef, useState } from "react";
import TableList from "@/components/TableList";
import { columns } from "./variable";
import apis from "@/services/anchorStatement/statement";
import { FieldsContext } from "./../index";
import { tab_live_revenue } from "../variable";
import { pushExportHistory } from "@/utils";
import { history } from "@umijs/max";
import queryString from "query-string";
const Res = props => {
  const { handlePageParmas, tabInfo, returnParams, setPageParams } = useContext(FieldsContext);
  const { tableProps } = props;
  const actionRef = useRef<any>();
  const tableFormRef = useRef<any>();
  const [resetFlag, setResetFlag] = useState(false);
  const handleExport = async record => {
    const { anchorName, confirmStatus, settleMonth } = record;
    const { status } = await apis.downloadLiveIncomeCollect({ anchorName, confirmStatus, settleMonth });
    if (status) {
      pushExportHistory();
    }
  };
  const handleDetail = async record => {
    const { settleMonth, anchorName, confirmStatus } = record;
    history.push({
      pathname: "/anchorstatement/anchorsettlementdetail",
      search: queryString.stringify({
        settleMonth,
        anchorName,
        confirmStatus
      })
    });
  };
  const columns_props = {
    handleDetail,
    handleExport
  };
  const handleParams = p => {
    const params = tableFormRef.current.getFieldsValue();
    const res = handlePageParmas?.({ ...p, ...params });
    setPageParams(res);
    return res;
  };
  useEffect(() => {
    if (tabInfo?.tab === tab_live_revenue) {
      // 由结算汇总跳转进入
      tableFormRef.current?.setFieldsValue(returnParams());
      // actionRef.current?.reload();
    }
  }, [tableFormRef, tabInfo?.tab]);
  return (
    <>
      <TableList
        manualRequest={true}
        formRef={tableFormRef}
        actionRef={actionRef}
        scroll={{ y: "calc(100vh - 330px)" }}
        defaultExpandAll={{ deep: 0 }}
        hasChildren={true}
        columns={columns(columns_props)}
        api={apis.getLiveIncomeCollectPage}
        summaryApi={apis.getLiveIncomeCollectPageTotal}
        params={{
          resetFlag
        }}
        onReset={() => setResetFlag(!resetFlag)}
        rowKey="unionPrimaryKey"
        preFetch={handleParams}
        {...tableProps}
      />
    </>
  );
};

export default React.memo(Res);
