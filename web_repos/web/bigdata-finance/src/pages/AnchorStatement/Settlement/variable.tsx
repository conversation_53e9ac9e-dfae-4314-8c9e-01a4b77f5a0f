import { DatePicker, Tag } from "antd";
import AnchorSelect from "@/components/AnchorSelect";
import { returnTargetObject } from "@/utils";
export const statusOptions = [
  { label: "已入账", value: 1 },
  { label: "未入账", value: 0 }
];
export const statusOptionsObj = returnTargetObject(statusOptions);
export const columns_search = ({}: any) => {
  return [
    {
      title: "结算月份",
      dataIndex: "settleMonth",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return <DatePicker.RangePicker picker="month" allowClear />;
      }
    },
    {
      title: "主播",
      dataIndex: "anchorNameList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return <AnchorSelect showSearch allowClear maxTagCount="responsive" mode="multiple" />;
      }
    },
    {
      title: "入账状态",
      dataIndex: "entryStatusList",
      hideInSearch: false,
      hideInTable: true,
      valueType: "checkbox",
      fieldProps: {
        options: statusOptions,
        style: { minWidth: "200px" }
      }
    }
  ];
};
export const confirmStatus_column = () => {
  return {
    title: "入账状态",
    dataIndex: "confirmStatus",
    hideInSearch: true,
    width: 70,
    render(text: number) {
      return <Tag color={text ? "success" : "error"}>{statusOptionsObj[text]}</Tag>;
    }
  };
};
export const tab_JSHZ = "结算汇总";
export const tab_live_revenue = "直播收入";
export const tab_bajia_income = "推广、打赏收入";
export const tab_xinxuan_expend = "辛选费用支出";
export const tab_bajia_expend = "巴伽费用支出";
export const tabMap = {
  [tab_JSHZ]: {},
  [tab_live_revenue]: {},
  [tab_bajia_income]: {
    menu_total: "a",
    menu_detail: "b"
  },
  [tab_xinxuan_expend]: {
    menu_total: "a",
    menu_detail: "b"
  },
  [tab_bajia_expend]: {
    menu_total: "a",
    menu_detail: "b"
  }
};
