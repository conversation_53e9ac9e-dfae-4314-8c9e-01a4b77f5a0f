export const model_auths = {
  model_anchorstatement_BJTGDS_tab: "anchorstatement_BJTGDS_tab",
  model_anchorstatement_BJTGDS_DETAIL_tab: "anchorstatement_BJTGDS_DETAIL_tab",
  model_anchorstatement_BJTGDS_SUMMARY_tab: "anchorstatement_BJTGDS_SUMMARY_tab",
  model_anchorstatement_BJZC_tab: "anchorstatement_BJZC_tab",
  model_anchorstatement_BJZC_DETAIL_tab: "anchorstatement_BJZC_DETAIL_tab",
  model_anchorstatement_BJZC_SUMMARY_tab: "anchorstatement_BJZC_SUMMARY_tab",
  model_anchorstatement_ZBSR_tab: "anchorstatement_ZBSR_tab",
  model_anchorstatement_ZBSR_SUMMARY_tab: "anchorstatement_ZBSR_SUMMARY_tab",
  model_anchorstatement_JSHZ_tab: "anchorstatement_JSHZ_tab",
  model_anchorstatement_JSHZ_SUMMARY_tab: "anchorstatement_JSHZ_SUMMARY_tab",
  model_anchorstatement_FYZC_tab: "anchorstatement_FYZC_tab",
  model_anchorstatement_FYZC_DETAIL_tab: "anchorstatement_FYZC_DETAIL_tab",
  model_anchorstatement_FYZC_SUMMARY_tab: "anchorstatement_FYZC_SUMMARY_tab"
};

export const btn_auths = {
  btn_anchorstatement_BJTGDS_DETAIL_export: "anchorstatement_BJTGDS_DETAIL_export",
  btn_anchorstatement_BJTGDS_SUMMARY_export: "anchorstatement_BJTGDS_SUMMARY_export",
  btn_anchorstatement_BJTGDS_DETAIL_batchdelete: "anchorstatement_BJTGDS_DETAIL_batchdelete",
  btn_anchorstatement_BJTGDS_DETAIL_upload: "anchorstatement_BJTGDS_DETAIL_upload",
  btn_anchorstatement_BJTGDS_DETAIL_edit: "anchorstatement_BJTGDS_DETAIL_edit",
  btn_anchorstatement_BJTGDS_DETAIL_delete: "anchorstatement_BJTGDS_DETAIL_delete",

  btn_anchorstatement_BJZC_DETAIL_export: "anchorstatement_BJZC_DETAIL_export",
  btn_anchorstatement_BJZC_SUMMARY_export: "anchorstatement_BJZC_SUMMARY_export",
  btn_anchorstatement_BJZC_DETAIL_batchdelete: "anchorstatement_BJZC_DETAIL_batchdelete",
  btn_anchorstatement_BJZC_DETAIL_upload: "anchorstatement_BJZC_DETAIL_upload",
  btn_anchorstatement_BJZC_DETAIL_edit: "anchorstatement_BJZC_DETAIL_edit",
  btn_anchorstatement_BJZC_DETAIL_delete: "anchorstatement_BJZC_DETAIL_delete",

  btn_anchorstatement_ZBSR_SUMMARY_export: "anchorstatement_ZBSR_SUMMARY_export",
  btn_anchorstatement_JSHZ_SUMMARY_export: "anchorstatement_JSHZ_SUMMARY_export",
  btn_anchorstatement_JSHZ_SUMMARY_batchexport: "anchorstatement_JSHZ_SUMMARY_batchexport",
  btn_anchorstatement_FYZC_DETAIL_export: "anchorstatement_FYZC_DETAIL_export",
  btn_anchorstatement_FYZC_SUMMARY_export: "anchorstatement_FYZC_SUMMARY_export"
};
