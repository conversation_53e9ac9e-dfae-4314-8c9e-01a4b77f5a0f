import React, { useEffect, useState } from "react";
import { Modal, Form, message, Alert } from "antd";
import { SIZE } from "@/utils/constant";
import apis from "@/services/anchorStatement/anchorsettlementdetail";
import dayjs from "dayjs";
interface IProps {
  // 显示隐藏 modal
  visible: boolean;
  // 关闭modal
  close: () => void;
  // 关闭 modal后 刷新页面
  fresh?: () => void;
  selectedRows: any[];
  // 标题
  title: React.ReactNode;
}
const UploadExcelModal = (props: IProps) => {
  const { visible, close, fresh, title, selectedRows } = props;
  const [form] = Form.useForm();
  const [okDisable, setokDisable] = useState(false);
  const [settleMonth, setSettleMonth] = useState({
    latestSettleMonth: "",
    nextSettleMonth: ""
  });
  const fetchSettleMonth = async () => {
    const { entry, status } = await apis.getSettleMonth();
    if (status) {
      setSettleMonth(entry);
    }
  };
  const handleExcelCancel = () => {
    form.resetFields();
    close?.();
  };

  const handleExcelOk = () => {
    form.validateFields().then(async () => {
      setokDisable(true);
      if (selectedRows?.length) {
        const ids = selectedRows.map(item => item.id);
        const { status } = await apis.redInk({ ids: ids, expectSettleMonth: settleMonth.nextSettleMonth });
        if (status) {
          fresh?.();
          close?.();
        }
      } else {
        message.info("所选数据没有需要红冲数据！");
      }
      setokDisable(false);
    });
  };
  useEffect(() => {
    fetchSettleMonth();
  }, []);
  return (
    <Modal
      centered={true}
      title={title}
      open={visible}
      onOk={() => handleExcelOk()}
      onCancel={() => handleExcelCancel()}
      maskClosable={false}
      okButtonProps={{
        size: SIZE,
        loading: okDisable
      }}
      cancelButtonProps={{
        size: SIZE
      }}
    >
      <Form size={SIZE} form={form}>
        <Form.Item>
          <Alert
            message="对当前选中记录进行批量红冲，确定后会对该商品上条结算数据进行红冲，同时按该商品最新模式重新计算生成调账记录"
            type="info"
            showIcon
          ></Alert>
        </Form.Item>
        <Form.Item name="settleMonth">
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center"
            }}
          >
            财务结算月份：<span>{dayjs(settleMonth?.nextSettleMonth).format("YYYY-MM")}</span>
          </div>
        </Form.Item>
      </Form>
    </Modal>
  );
};
export default React.memo(UploadExcelModal);
