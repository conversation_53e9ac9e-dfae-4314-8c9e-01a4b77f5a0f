import React, { useRef } from "react";
import TableList from "@/components/TableList";
import { columns } from "./variable";
import apis from "@/services/anchorStatement/anchorsettlementdetail";
import dayjs from "dayjs";
import useCommonOptions from "@/hooks/useCommonOptions";

const Res = () => {
  const actionRef = useRef<any>();
  const tableFormRef = useRef<any>();
  const [modelNameLv1Options, modelNameLv1Obj] = useCommonOptions({ dimName: "模式" });

  const columns_props = {
    modelNameLv1Options,
    modelNameLv1Obj
  };

  // 处理请求参数
  const handlePageParmas = (p: any) => {
    const { current, settleDate, modelNameLv1, ...rest } = p;
    // 结算月份
    if (settleDate) {
      const [startExpectSettleMonth, endExpectSettleMonth] = settleDate;
      rest.startExpectSettleMonth = dayjs(startExpectSettleMonth).format("YYYYMM");
      rest.endExpectSettleMonth = dayjs(endExpectSettleMonth).format("YYYYMM");
    }
    // 模式
    if (modelNameLv1 !== undefined) {
      rest.modelNameLv1 = modelNameLv1Obj[modelNameLv1];
    }
    return { ...rest, pageNo: current };
  };

  return (
    <>
      <TableList
        formRef={tableFormRef}
        actionRef={actionRef}
        columns={columns(columns_props)}
        api={apis.getCategoryList}
        scroll={{ y: "calc(100vh - 330px)" }}
        preFetch={handlePageParmas}
        rowKey={record => {
          const { expectSettleMonth, anchorName, modelNameLv1, categoryName, backFlag } = record;
          return expectSettleMonth + anchorName + modelNameLv1 + categoryName + backFlag;
        }}
      />
    </>
  );
};

export default React.memo(Res);
