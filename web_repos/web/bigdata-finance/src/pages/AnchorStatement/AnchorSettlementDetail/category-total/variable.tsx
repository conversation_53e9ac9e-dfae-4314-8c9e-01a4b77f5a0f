import { Select, DatePicker } from "antd";
import { filterOptionLabel, returnEllipsisTooltip } from "@/utils";
import AnchorSelect from "@/components/AnchorSelect";
const columns_search = ({ modelNameLv1Options }: any) => {
  return [
    {
      title: "结算月份",
      dataIndex: "settleDate",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return <DatePicker.RangePicker picker="month" allowClear />;
      }
    },
    {
      title: "主播",
      dataIndex: "anchorName",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem() {
        return <AnchorSelect showSearch allowClear />;
      }
    },
    {
      title: "模式",
      dataIndex: "modelNameLv1",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem() {
        return <Select placeholder="请选择" options={modelNameLv1Options} filterOption={filterOptionLabel} showSearch allowClear />;
      }
    },
    {
      title: "品类",
      dataIndex: "categoryName",
      hideInSearch: false,
      hideInTable: true
    }
  ];
};
export const columns = ({ modelNameLv1Options }: any): Array<TableListItem> => {
  return [
    ...columns_search({ modelNameLv1Options }),
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      hideInSearch: true,
      fixed: "left",
      render: (_, record, index) => {
        return index + 1;
      }
    },
    { title: "结算月份", dataIndex: "expectSettleMonth", fixed: "left", width: 100, hideInSearch: true },
    { title: "主播", dataIndex: "anchorName", width: 100, fixed: "left", hideInSearch: true },
    { title: "模式", dataIndex: "modelNameLv1", width: 100, fixed: "left", hideInSearch: true },
    { title: "品类", dataIndex: "categoryName", width: 100, fixed: "left", hideInSearch: true },
    { title: "结算金额", dataIndex: "settleAmount", width: 100, hideInSearch: true },
    { title: "超售后金额", dataIndex: "refundAmount", width: 100, hideInSearch: true },
    { title: "线上佣金金额", dataIndex: "onlineCommissionAmount", width: 100, hideInSearch: true },
    { title: "总成本金额(主品+赠品)", dataIndex: "totalCostAmount", width: 100, hideInSearch: true },
    { title: "总发货费用", dataIndex: "totalExpressAmount", width: 100, hideInSearch: true },
    { title: "达人佣金追回", dataIndex: "backCommissionAmount", width: 100, hideInSearch: true },
    { title: "达人佣金未追回", dataIndex: "unbackCommissionAmount", width: 100, hideInSearch: true },
    { title: "线下收/返佣金额", dataIndex: "offlineCommissionAmount", width: 100, hideInSearch: true },
    { title: "实际佣金", dataIndex: "totalCommissionAmount", width: 100, hideInSearch: true },
    { title: "快手技术服务费", dataIndex: "settleServiceAmount", width: 100, hideInSearch: true },
    { title: "快手技术服务费追回", dataIndex: "refundServiceAmount", width: 100, hideInSearch: true },
    { title: "线下赔付金额", dataIndex: "compensateAmount", width: 100, hideInSearch: true },
    { title: "线下返款金额", dataIndex: "backAmount", width: 100, hideInSearch: true },
    { title: "返款承担方", dataIndex: "backFlag", width: 100, hideInSearch: true },
    { title: "利润", dataIndex: "profitAmount", width: 100, hideInSearch: true },
    { title: "线上利润", dataIndex: "onlineProfitAmount", width: 100, hideInSearch: true },
    { title: "线下利润", dataIndex: "offlineProfitAmount", width: 100, hideInSearch: true },
    { title: "年框扣费金额", dataIndex: "yearlyDeductAmount", width: 100, hideInSearch: true },
    { title: "净利润", dataIndex: "grossProfitAmount", width: 100, hideInSearch: true },
    { title: "公司利润", dataIndex: "companyGrossAmount", width: 100, hideInSearch: true },
    { title: "主播利润", dataIndex: "anchorGrossAmount", width: 100, hideInSearch: true }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      align: "center",
      ...item,
      title: returnEllipsisTooltip({ title })
    };
  });
};
