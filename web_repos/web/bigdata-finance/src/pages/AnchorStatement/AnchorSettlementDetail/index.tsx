import React, { useEffect, useState } from "react";
import { Tabs } from "antd";
import KeepAlive, { useAliveController } from "react-activation";
import { SIZE, tabs_tabBarStyle } from "@/utils/constant";
import { useSearchParams, history } from "@umijs/max";
import { tab_settlement_detail, tab_category_total } from "./variable";
import SettlementDetail from "./settlement-detail";
import CategoryTotal from "./category-total";
import { ifEmptyObj } from "@/utils";
import dayjs from "dayjs";

export const FieldsContext = React.createContext<any>(null);
export const PageUrl = "/anchorstatement/anchorsettlementdetail";
interface IProps {
  urlParams: Record<string, any>;
}
const DetailTable: React.FC<IProps> = props => {
  const { urlParams } = props;
  const [curTab, setCurTab] = useState(tab_settlement_detail);
  const [treatedUrlParams, setTreatedUrlParams] = useState({});
  const tabItems = [
    {
      key: tab_settlement_detail,
      label: "主播结算明细",
      children: <SettlementDetail />
    },

    {
      key: tab_category_total,
      label: "主播结算类目汇总",
      children: <CategoryTotal />
    }
  ];
  useEffect(() => {
    if (ifEmptyObj(urlParams)) {
      const { settleMonth, anchorName, confirmStatus } = urlParams;
      setTreatedUrlParams({
        expectSettleMonth: [dayjs(settleMonth), dayjs(settleMonth)],
        anchorNames: [anchorName],
        confirmStatus
      });
    }
  }, [urlParams]);
  return (
    <FieldsContext.Provider value={{ urlParams: treatedUrlParams }}>
      <Tabs activeKey={curTab} tabBarStyle={tabs_tabBarStyle} size={SIZE} onChange={setCurTab} items={tabItems} />
    </FieldsContext.Provider>
  );
};

const AliveRecord = () => {
  const [searchParams] = useSearchParams();
  const hasSearch = searchParams.size > 0;
  let urlParams = {};
  if (hasSearch) {
    const anchorName = searchParams.get("anchorName");
    const confirmStatus = searchParams.get("confirmStatus");
    const settleMonth = searchParams.get("settleMonth");
    urlParams = {
      anchorName,
      confirmStatus,
      settleMonth
    };
  }
  let aliveCont = useAliveController();
  // 当 url 上携带参数时，刷新 页面缓存
  if (history.action === "PUSH" && hasSearch && history.location.pathname === PageUrl) {
    aliveCont.refreshScope(PageUrl);
  }
  return (
    <KeepAlive name={PageUrl}>
      <DetailTable urlParams={urlParams} />
    </KeepAlive>
  );
};
export default React.memo(AliveRecord);
