import React, { useState, useRef } from "react";
import { Button } from "antd";
import KeepAlive from "react-activation";
import TableList from "@/components/TableList";
import { SIZE } from "@/utils/constant";
import { columns, advance_columns_search } from "./variable";
import apis from "@/services/downloadcenter/KSDistrBill";
import { pushExportHistory, splitSpaceComma } from "@/utils";
import dayjs from "dayjs";
import useCommonOptions from "@/hooks/useCommonOptions";
export const PageUrl = "/upload/KSDistrBill";
const DetailTable = () => {
  const [accountChannelOptions] = useCommonOptions({ dimName: "收款渠道" });
  const popActionRef = useRef<any>();
  const [SPUPageParams, setSPUPageParams] = useState({});
  const columnsProps = {};

  // 处理请求参数
  const handlePageParmas = (params: any) => {
    const { current, anchorSettleTime, liveDate, bizOrderIdList, itemIdList, shopIdList, anchorIdList, orderTime, anchorAllowAmount, ...rest } =
      params;
    if (liveDate) {
      const [liveDateStart, liveDateEnd] = liveDate;
      rest.liveDateStart = dayjs(liveDateStart).format("YYYYMMDD");
      rest.liveDateEnd = dayjs(liveDateEnd).format("YYYYMMDD");
    }
    if (anchorSettleTime) {
      const [anchorSettleTimeStart, anchorSettleTimeEnd] = anchorSettleTime;
      rest.anchorSettleTimeStart = dayjs(anchorSettleTimeStart).format("YYYY-MM-DD");
      rest.anchorSettleTimeEnd = dayjs(anchorSettleTimeEnd).format("YYYY-MM-DD");
    }
    if (orderTime) {
      const [orderTimeStart, orderTimeEnd] = orderTime;
      rest.orderTimeStart = dayjs(orderTimeStart).format("YYYY-MM-DD");
      rest.orderTimeEnd = dayjs(orderTimeEnd).format("YYYY-MM-DD");
    }
    if (anchorAllowAmount) {
      const [anchorAllowAmountStart, anchorAllowAmountEnd] = anchorAllowAmount;
      if (typeof anchorAllowAmountStart !== "undefined") {
        rest.anchorAllowAmountStart = anchorAllowAmountStart * 100;
      }
      if (typeof anchorAllowAmountEnd !== "undefined") {
        rest.anchorAllowAmountEnd = anchorAllowAmountEnd * 100;
      }
    }

    rest.bizOrderIdList = splitSpaceComma(bizOrderIdList);
    rest.itemIdList = splitSpaceComma(itemIdList);
    rest.shopIdList = splitSpaceComma(shopIdList);
    rest.anchorIdList = splitSpaceComma(anchorIdList);
    return { ...rest, pageNo: current };
  };
  const handleExport = async () => {
    const { status } = await apis.downloadCpsSettleOrder(SPUPageParams);
    if (status) {
      pushExportHistory();
    }
  };
  const advanceColumnsProps = { accountChannelOptions };
  return (
    <>
      <TableList
        actionRef={popActionRef}
        columns={columns(columnsProps)}
        scroll={{ y: "calc(100vh - 370px)" }}
        form={{
          initialValues: {
            liveDate: [dayjs().subtract(6, "day"), dayjs().subtract(0, "day")]
          }
        }}
        advanceColumns={advance_columns_search(advanceColumnsProps)}
        api={apis.getCpsSettleOrderPage}
        preFetch={handlePageParmas}
        paramsChange={setSPUPageParams}
        rowKey="bizOrderId"
        toolbar={{
          actions: [
            <Button key="export" onClick={() => handleExport()} size={SIZE}>
              导出
            </Button>
          ]
        }}
      />
    </>
  );
};

const AliveRecord = () => (
  <KeepAlive name={PageUrl}>
    <DetailTable />
  </KeepAlive>
);
export default React.memo(AliveRecord);
