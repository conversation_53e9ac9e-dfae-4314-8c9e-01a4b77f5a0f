import { filterOptionLabel, returnEllipsisTooltip } from "@/utils";
import AnchorSelect from "@/components/AnchorSelect";
import { ProFormDigitRange } from "@ant-design/pro-components";
import { Select } from "antd";
export const advance_columns_search = ({ accountChannelOptions }: any): TableListItem[] => {
  return [
    {
      title: "基本信息",
      valueType: "group",
      colProps: { span: 24 },
      columns: [
        {
          title: "主播ID",
          dataIndex: "anchorIdList",
          hideInTable: true,
          fieldProps: {
            placeholder: "多个使用英文逗号/空格"
          }
        },
        {
          title: "店铺ID",
          dataIndex: "shopIdList",
          hideInTable: true,
          fieldProps: {
            placeholder: "多个使用英文逗号/空格"
          }
        },
        {
          title: "收款渠道",
          dataIndex: "accountChannelList",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem() {
            return (
              <Select
                placeholder="请选择"
                options={accountChannelOptions}
                filterOption={filterOptionLabel}
                showSearch
                allowClear
                maxTagCount="responsive"
                mode="multiple"
              />
            );
          }
        },
        { title: "店铺名称", dataIndex: "shopName", hideInTable: true },
        {
          title: "主播补贴金额",
          dataIndex: "anchorAllowAmount",
          hideInTable: true,
          renderFormItem() {
            return <ProFormDigitRange noStyle fieldProps={{ controls: false }} separator="-" placeholder={["最小值", "最大值"]} />;
          }
        }
      ]
    }
  ];
};
const columns_search = ({}: any) => {
  return [
    { title: "直播日期", dataIndex: "liveDate", valueType: "dateRange", hideInTable: true },
    {
      title: "订单号",
      dataIndex: "bizOrderIdList",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },

    {
      title: "主播名称",
      dataIndex: "anchorNameList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem() {
        return <AnchorSelect params={{ type: 1 }} showSearch allowClear maxTagCount="responsive" mode="multiple" />;
      }
    },

    {
      title: "商品ID",
      dataIndex: "itemIdList",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    { title: "商品名称", dataIndex: "itemTitle", hideInTable: true },
    { title: "订单创建时间", dataIndex: "orderTime", valueType: "dateRange", hideInTable: true },
    { title: "结算时间", dataIndex: "anchorSettleTime", valueType: "dateRange", hideInTable: true }
  ];
};

export const columns = ({}: any): Array<TableListItem> => {
  return [
    ...columns_search({}),
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      hideInSearch: true,
      fixed: "left",
      render: (_, record, index) => {
        return index + 1;
      }
    },
    {
      title: "订单号",
      dataIndex: "bizOrderId",
      width: 120,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "直播日期", dataIndex: "liveDate", width: 100, hideInSearch: true },
    { title: "主播ID", dataIndex: "anchorId", width: 100, hideInSearch: true },
    {
      title: "主播名称",
      dataIndex: "anchorName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "店铺ID",
      dataIndex: "shopId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "店铺名称",
      dataIndex: "shopName",
      width: 140,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "店铺性质",
      dataIndex: "shopTypeDesc",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品ID",
      dataIndex: "itemId",
      width: 100,
      hideInSearch: true
    },
    {
      title: "商品名称",
      dataIndex: "itemName",
      width: 200,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "订单渠道",
      dataIndex: "orderChannelDesc",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "结算业务类型",
      dataIndex: "settleTypeDesc",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "收款渠道",
      dataIndex: "accountChannel",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "收款身份",
      dataIndex: "accountType",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "是否超售后", dataIndex: "isOverRefundStr", width: 100, hideInSearch: true },
    {
      title: "订单创建时间",
      dataIndex: "orderTime",
      width: 130,
      hideInSearch: true
    },
    {
      title: "结算时间",
      dataIndex: "anchorSettleTime",
      width: 130,
      hideInSearch: true
    },
    { title: "结算金额", dataIndex: "settleAmountStr", width: 100, hideInSearch: true },
    { title: "结算商品数量", dataIndex: "settleItemNumStr", width: 100, hideInSearch: true },
    { title: "订单实付金额", dataIndex: "orderPayAmountStr", width: 100, hideInSearch: true },
    { title: "主播补贴金额", dataIndex: "anchorAllowAmountStr", width: 100, hideInSearch: true },
    {
      title: "主播宠爱红包补贴金额",
      dataIndex: "petAnchorAllowAmountStr",
      width: 140,
      hideInSearch: true
    },
    {
      title: "主播红包补贴金额",
      dataIndex: "otherAnchorAllowAmountStr",
      width: 120,
      hideInSearch: true
    },
    { title: "平台补贴金额", dataIndex: "platAllowAmountStr", width: 100, hideInSearch: true },
    { title: "技术服务费用", dataIndex: "platServiceAmountStr", width: 100, hideInSearch: true },
    { title: "达人佣金比例", dataIndex: "onlineCommissionRateStr", width: 100, hideInSearch: true },
    { title: "达人佣金金额", dataIndex: "onlineCommissionAmountStr", width: 100, hideInSearch: true },
    {
      title: "政府补贴",
      dataIndex: "governmentAllowAmountStr",
      width: 80,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "备注",
      dataIndex: "remark",
      width: 80,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      ...item,
      align: "center",
      title: returnEllipsisTooltip({ title })
    };
  });
};
