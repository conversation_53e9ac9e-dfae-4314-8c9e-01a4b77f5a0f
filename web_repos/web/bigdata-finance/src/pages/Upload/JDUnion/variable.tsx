import { Select } from "antd";
import { filterOptionLabel, returnEllipsisTooltip } from "@/utils";
import { NOVALUE } from "@/utils/constant";
const columns_POP_search = ({ promotionPlatformOptions }: any) => {
  return [
    { title: "下单时间", dataIndex: "gmtCreateOrder", valueType: "dateRange", hideInTable: true },
    { title: "完成时间", dataIndex: "gmtCompleteOrder", valueType: "dateRange", hideInTable: true },
    {
      title: "订单号",
      dataIndex: "orderIdList",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "商品ID",
      dataIndex: "itemIdList",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "联盟商品ID",
      dataIndex: "allianceItemIdList",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    { title: "商品名称", dataIndex: "itemTitle", hideInTable: true },
    { title: "达人昵称", dataIndex: "masterNickname", hideInTable: true },
    {
      title: "推广平台",
      dataIndex: "promotionPlatformList",
      hideInTable: true,
      renderFormItem() {
        return (
          <Select
            placeholder="请选择"
            options={promotionPlatformOptions}
            filterOption={filterOptionLabel}
            showSearch
            allowClear
            maxTagCount="responsive"
            mode="multiple"
          />
        );
      }
    },
    {
      title: "抖快外部订单号",
      dataIndex: "tiktokFastExternalOrderIdList",
      hideInTable: true,
      tooltip: "快手订单号",
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    }
  ];
};

export const columns_POP = ({ promotionPlatformOptions, promotionPlatformObj }: any): Array<TableListItem> => {
  return [
    ...columns_POP_search({ promotionPlatformOptions }),
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      hideInSearch: true,
      fixed: "left",
      render: (_, record, index) => {
        return index + 1;
      }
    },
    { title: "下单时间", dataIndex: "gmtCreateOrder", width: 100, hideInSearch: true },
    { title: "完成时间", dataIndex: "gmtCompleteOrder", width: 100, hideInSearch: true },
    { title: "订单号", dataIndex: "orderId", width: 100, hideInSearch: true },
    { title: "商品ID", dataIndex: "itemId", width: 100, hideInSearch: true },
    {
      title: "联盟商品ID",
      dataIndex: "allianceItemId",
      width: 140,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品名称",
      dataIndex: "itemTitle",
      width: 140,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "状态变化时间(逆向发生时间)", dataIndex: "gmtStateChange", width: 100, hideInSearch: true },
    { title: "商品数量", dataIndex: "itemNumStr", width: 100, hideInSearch: true },
    { title: "计佣金额", dataIndex: "commissionAmountStr", width: 100, hideInSearch: true },
    { title: "佣金变化(增／减)", dataIndex: "commissionChangeStr", width: 140, hideInSearch: true },
    {
      title: "达人昵称",
      dataIndex: "masterNickname",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "推广平台",
      dataIndex: "promotionPlatform",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: promotionPlatformObj[text] || NOVALUE });
      }
    },
    {
      title: "抖快外部订单号",
      dataIndex: "tiktokFastExternalOrderId",
      width: 140,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      ...item,
      align: "center",
      title: returnEllipsisTooltip({ title })
    };
  });
};
const columns_subsidy_search = ({ promotionPlatformOptions }: any) => {
  return [
    { title: "下单时间", dataIndex: "gmtCreateOrder", valueType: "dateRange", hideInTable: true },
    { title: "完成时间", dataIndex: "gmtCompleteOrder", valueType: "dateRange", hideInTable: true },
    {
      title: "订单号",
      dataIndex: "orderIdList",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "商品ID",
      dataIndex: "itemIdList",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "联盟商品ID",
      dataIndex: "allianceItemIdList",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    { title: "商品名称", dataIndex: "itemTitle", hideInTable: true },
    { title: "达人昵称", dataIndex: "masterNickname", hideInTable: true },
    {
      title: "推广平台",
      dataIndex: "promotionPlatformList",
      hideInTable: true,
      renderFormItem() {
        return (
          <Select
            placeholder="请选择"
            options={promotionPlatformOptions}
            filterOption={filterOptionLabel}
            showSearch
            allowClear
            maxTagCount="responsive"
            mode="multiple"
          />
        );
      }
    },
    {
      title: "抖快外部订单号",
      dataIndex: "tiktokFastExternalOrderIdList",
      hideInTable: true,
      tooltip: "快手订单号",
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    }
  ];
};

export const columns_subsidy = ({ promotionPlatformOptions, promotionPlatformObj }: any): Array<TableListItem> => {
  return [
    ...columns_subsidy_search({ promotionPlatformOptions }),
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      hideInSearch: true,
      fixed: "left",
      render: (_, record, index) => {
        return index + 1;
      }
    },
    { title: "下单时间", dataIndex: "gmtCreateOrder", width: 100, hideInSearch: true },
    { title: "完成时间", dataIndex: "gmtCompleteOrder", width: 100, hideInSearch: true },
    {
      title: "订单号",
      dataIndex: "orderId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "商品ID", dataIndex: "itemId", width: 100, hideInSearch: true },
    {
      title: "联盟商品ID",
      dataIndex: "allianceItemId",
      width: 140,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品名称",
      dataIndex: "itemTitle",
      width: 140,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "状态变化时间(逆向发生时间)", dataIndex: "gmtStateChange", width: 100, hideInSearch: true },
    { title: "商品数量", dataIndex: "itemNumStr", width: 100, hideInSearch: true },
    { title: "计佣金额", dataIndex: "commissionAmountStr", width: 100, hideInSearch: true },
    { title: "佣金变化(增／减)", dataIndex: "commissionChangeStr", width: 140, hideInSearch: true },
    {
      title: "达人昵称",
      dataIndex: "masterNickname",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "推广平台",
      dataIndex: "promotionPlatform",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: promotionPlatformObj[text] || NOVALUE });
      }
    },
    {
      title: "抖快外部订单号",
      dataIndex: "tiktokFastExternalOrderId",
      width: 140,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      ...item,
      align: "center",
      title: returnEllipsisTooltip({ title })
    };
  });
};
