import React, { useState, useRef } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "antd";
import KeepAlive from "react-activation";
import TableList from "@/components/TableList";
import { SIZE, tabs_tabBarStyle } from "@/utils/constant";
import { columns_POP, columns_subsidy } from "./variable";
import apis from "@/services/downloadcenter/JDUnion";
import { pushExportHistory, splitSpaceComma } from "@/utils";
import UploadExcelModal from "@/components/UploadExcelModal";
import useCommonOptions from "@/hooks/useCommonOptions";
export const JD_POP = "JD_POP";
export const JD_subsidy = "JD_subsidy";
export const PageUrl = "/records/JDUnion";
const DetailTable = () => {
  const [promotionPlatformOptions, promotionPlatformObj] = useCommonOptions({ dimName: "推广平台" });
  const popActionRef = useRef<any>();
  const subsidyActionRef = useRef<any>();
  const [curTab, setCurTab] = useState(JD_POP);
  const [SPUPageParams, setSPUPageParams] = useState({});
  const [showExcelUpload, setshowExcelUpload] = useState(false);
  const columns_POPProps = {
    promotionPlatformOptions,
    promotionPlatformObj
  };
  const columns_subsidyProps = {
    promotionPlatformOptions,
    promotionPlatformObj
  };

  // 处理请求参数
  const handlePageParmas = (p: any) => {
    const { current, gmtCreateOrder, gmtCompleteOrder, ...rest } = p;
    const params = {
      pageNo: current,
      ...rest,
      gmtCreateOrderStart: gmtCreateOrder?.[0] ?? void 0,
      gmtCreateOrderEnd: gmtCreateOrder?.[1] ?? void 0,
      gmtCompleteOrderStart: gmtCompleteOrder?.[0] ?? void 0,
      gmtCompleteOrderEnd: gmtCompleteOrder?.[1] ?? void 0,
      itemIdList: splitSpaceComma(rest?.itemIdList),
      orderIdList: splitSpaceComma(rest?.orderIdList),
      allianceItemIdList: splitSpaceComma(rest?.allianceItemIdList),
      tiktokFastExternalOrderIdList: splitSpaceComma(rest?.tiktokFastExternalOrderIdList)
    };
    return params;
  };
  const handleExport = async (type: "pop" | "subsidy") => {
    if (type === "pop") {
      const { status } = await apis.downloadJdPopMerchant(SPUPageParams);
      if (status) {
        pushExportHistory();
      }
      return;
    }
    if (type === "subsidy") {
      const { status } = await apis.downloadJdPlatformSubsidy(SPUPageParams);
      if (status) {
        pushExportHistory();
      }
      return;
    }
  };
  const uploadObj: Record<string, any> = {
    [JD_POP]: {
      title: "Excel导入POP商家",
      targetType: 0,
      api: apis.addJdPopMerchantFromExcel,
      actionRef: popActionRef.current,
      tempUrl: "https://s.xinc818.com/files/webcilxb7yvwhcys5sc/京东联盟-POP商家上传模板.xlsx"
    },
    [JD_subsidy]: {
      title: "Excel导入平台补贴",
      targetType: 0,
      api: apis.addJdPlatformSubsidyFromExcel,
      actionRef: subsidyActionRef.current,
      tempUrl: "https://s.xinc818.com/files/webcilxb7z7hp0wiorq/京东联盟-平台补贴上传模板.xlsx"
    }
  };
  // 上传excel modal属性
  const UploadExcelModalProps = {
    title: uploadObj[curTab]?.title,
    visible: showExcelUpload,
    targetType: uploadObj[curTab]?.targetType,
    api: uploadObj[curTab]?.api,
    infos: [
      <span key="line1" style={{ color: "red" }}>
        上传时，必须严格按照模板格式填写，否则会上传失败
      </span>
    ],
    tempUrl: uploadObj[curTab]?.tempUrl,
    fresh: () => {
      uploadObj[curTab]?.actionRef?.reload();
    },
    close: () => {
      setshowExcelUpload(false);
    }
  };

  const tabItems = [
    {
      key: JD_POP,
      label: "POP商家",
      children: (
        <TableList
          actionRef={popActionRef}
          columns={columns_POP(columns_POPProps)}
          scroll={{ y: "calc(100vh - 430px)" }}
          api={apis.getJdPopMerchantPage}
          preFetch={handlePageParmas}
          paramsChange={setSPUPageParams}
          rowKey="id"
          toolbar={{
            actions: [
              <Button
                key="upload"
                type="primary"
                onClick={() => {
                  setshowExcelUpload(true);
                }}
                size={SIZE}
              >
                上传
              </Button>,
              <Button key="export" onClick={() => handleExport("pop")} size={SIZE}>
                导出
              </Button>
            ]
          }}
        />
      )
    },
    {
      key: JD_subsidy,
      label: "平台补贴",
      children: (
        <TableList
          actionRef={subsidyActionRef}
          columns={columns_subsidy(columns_subsidyProps)}
          scroll={{ y: "calc(100vh - 440px)" }}
          api={apis.getJdPlatformSubsidyPage}
          preFetch={handlePageParmas}
          paramsChange={setSPUPageParams}
          rowKey="id"
          toolbar={{
            actions: [
              <Button
                key="upload"
                type="primary"
                onClick={() => {
                  setshowExcelUpload(true);
                }}
                size={SIZE}
              >
                上传
              </Button>,
              <Button key="export" onClick={() => handleExport("subsidy")} size={SIZE}>
                导出
              </Button>
            ]
          }}
        />
      )
    }
  ];
  // ].filter(item => {
  //   return authModel(item.key);
  // });

  // useLayoutEffect(() => {
  //   const res = tabItems.filter(item => {
  //     return authModel(item.key);
  //   });
  //   if (!curTab) {
  //     setCurTab(res?.[0]?.key || "");
  //   }
  // }, [tabItems, curTab]);
  return (
    <>
      <Tabs activeKey={curTab} tabBarStyle={tabs_tabBarStyle} size={SIZE} onChange={setCurTab} items={tabItems} />

      {showExcelUpload && <UploadExcelModal {...UploadExcelModalProps} />}
    </>
  );
};

const AliveRecord = () => (
  <KeepAlive name={PageUrl}>
    <DetailTable />
  </KeepAlive>
);
export default React.memo(AliveRecord);
