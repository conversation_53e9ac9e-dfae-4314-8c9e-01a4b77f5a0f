import { Select } from "antd";
import { filterOptionLabel, returnEllipsisTooltip } from "@/utils";
import AnchorSelect from "@/components/AnchorSelect";
export const advance_columns_search = (props: any): TableListItem[] => {
  return [
    {
      title: "基本信息",
      valueType: "group",
      colProps: { span: 24 },
      columns: [
        {
          title: "订单创建时间",
          dataIndex: "orderTime",
          valueType: "dateRange",
          fieldProps: {
            style: { width: "100%" }
          },
          hideInTable: true
        },
        {
          title: "达人ID",
          dataIndex: "anchorIdList",
          hideInTable: true,
          fieldProps: {
            placeholder: "多个使用英文逗号/空格"
          }
        },
        {
          title: "MCN机构ID",
          dataIndex: "mcnIdList",
          hideInTable: true,
          fieldProps: {
            placeholder: "多个使用英文逗号/空格"
          }
        },
        {
          title: "佣金模式",
          dataIndex: "settleComisModeList",
          hideInTable: true,
          renderFormItem() {
            return <Select placeholder="请选择" options={props.settleComisModeOptions} filterOption={filterOptionLabel} showSearch allowClear />;
          }
        },
        {
          title: "实际结算时间",
          dataIndex: "shopSettleTime",
          valueType: "dateRange",
          fieldProps: {
            style: { width: "100%" }
          },
          hideInTable: true
        }
      ]
    }
  ];
};
const columns_search = ({}: any) => {
  return [
    { title: "直播日期", dataIndex: "liveDate", valueType: "dateRange", hideInTable: true },
    {
      title: "订单号",
      dataIndex: "bizOrderIdList",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "主播名称",
      dataIndex: "anchorNameList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem() {
        return <AnchorSelect params={{ type: 1 }} showSearch allowClear maxTagCount="responsive" mode="multiple" />;
      }
    },
    {
      title: "商家ID",
      dataIndex: "shopIdList",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    { title: "店铺名称", dataIndex: "shopName", hideInTable: true },
    {
      title: "商品ID",
      dataIndex: "itemIdList",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    { title: "商品名称", dataIndex: "itemTitle", hideInTable: true }
  ];
};

export const columns = ({}: any): Array<TableListItem> => {
  return [
    ...columns_search({}),
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      hideInSearch: true,
      fixed: "left",
      render: (_, record, index) => {
        return index + 1;
      }
    },
    {
      title: "订单号",
      dataIndex: "bizOrderId",
      width: 130,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "直播日期", dataIndex: "liveDate", width: 100, hideInSearch: true },
    {
      title: "主播名称",
      dataIndex: "anchorName",
      width: 110,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商家ID",
      dataIndex: "shopId",
      width: 110,
      hideInSearch: true
    },
    {
      title: "店铺名称",
      dataIndex: "shopName",
      width: 130,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "店铺性质",
      dataIndex: "shopTypeDesc",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品ID",
      dataIndex: "itemId",
      width: 110,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品名称",
      dataIndex: "itemTitle",
      width: 200,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "品牌名称",
      dataIndex: "brandName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "商品数量", dataIndex: "itemNumStr", width: 100, hideInSearch: true },
    { title: "订单创建时间", dataIndex: "orderTime", width: 130, hideInSearch: true },
    {
      title: "订单类型",
      dataIndex: "orderType",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "订单是否结算", dataIndex: "isSettleStr", width: 100, hideInSearch: true },
    {
      title: "订单渠道",
      dataIndex: "orderChannelDesc",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "订单实付（元）", dataIndex: "orderPayAmountStr", width: 100, hideInSearch: true },
    {
      title: "政府补贴",
      dataIndex: "governmentAllowAmountStr",
      width: 100,
      hideInSearch: true
    },
    { title: "支付营销补贴", dataIndex: "payMarketAmountStr", width: 100, hideInSearch: true },
    { title: "平台补贴", dataIndex: "platAllowAmountStr", width: 100, hideInSearch: true },
    { title: "主播补贴", dataIndex: "anchorAllowAmountStr", width: 100, hideInSearch: true },
    {
      title: "主播补贴明细",
      dataIndex: "anchorAllowDetail",
      width: 100,
      hideInSearch: true
    },
    { title: "合计收入（元）", dataIndex: "totalIncomeStr", width: 100, hideInSearch: true },
    { title: "订单退款（元）", dataIndex: "orderRefundAmountStr", width: 100, hideInSearch: true },
    { title: "技术服务费（元）", dataIndex: "shopServiceAmountStr", width: 100, hideInSearch: true },
    {
      title: "达人ID",
      dataIndex: "anchorId",
      width: 100,
      hideInSearch: true
    },
    {
      title: "MCN机构ID",
      dataIndex: "mcnId",
      width: 100,
      hideInSearch: true
    },
    { title: "达人佣金（元）", dataIndex: "onlineCommissionAmountStr", width: 100, hideInSearch: true },
    {
      title: "团长ID",
      dataIndex: "leaderId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "团长佣金（元）", dataIndex: "leaderCommissionAmountStr", width: 100, hideInSearch: true },
    {
      title: "佣金模式",
      dataIndex: "settleComisMode",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "快赚客ID",
      dataIndex: "kzkId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "快赚客佣金（元）", dataIndex: "kzkCommissionAmountStr", width: 100, hideInSearch: true },
    {
      title: "服务商ID",
      dataIndex: "serviceId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "服务商佣金（元）", dataIndex: "serviceCommissionAmountStr", width: 100, hideInSearch: true },
    {
      title: "分账基数",
      dataIndex: "serviceCommissionRole",
      width: 100,
      hideInSearch: true
    },
    { title: "其他收费", dataIndex: "otherAmountStr", width: 100, hideInSearch: true },
    {
      title: "其他收费明细",
      dataIndex: "otherAmountDetail",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "合计支出（元）", dataIndex: "totalDisbursementStr", width: 100, hideInSearch: true },
    { title: "实际结算金额（元）", dataIndex: "settleAmountStr", width: 100, hideInSearch: true },
    { title: "实际结算时间", dataIndex: "shopSettleTime", width: 130, hideInSearch: true },
    {
      title: "结算规则",
      dataIndex: "settleRuleDesc",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "资金渠道",
      dataIndex: "settleChannel",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "账户名称",
      dataIndex: "accountName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "结算商户号",
      dataIndex: "accountMerchantId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "备注",
      dataIndex: "remark",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "消费金信息",
      dataIndex: "consumeDetail",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      ...item,
      align: "center",
      title: returnEllipsisTooltip({ title })
    };
  });
};
