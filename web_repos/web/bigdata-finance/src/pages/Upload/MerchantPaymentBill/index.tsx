import React, { useState, useRef } from "react";
import { But<PERSON> } from "antd";
import KeepAlive from "react-activation";
import TableList from "@/components/TableList";
import { SIZE } from "@/utils/constant";
import { columns, advance_columns_search } from "./variable";
import apis from "@/services/downloadcenter/merchantpaymentbill";
import { pushExportHistory, splitSpaceComma } from "@/utils";
import useCommonOptions from "@/hooks/useCommonOptions";
import dayjs from "dayjs";
export const PageUrl = "/upload/merchantpaymentbill";
const DetailTable = () => {
  const [settleComisModeOptions] = useCommonOptions({ dimName: "结算佣金模式" });
  const popActionRef = useRef<any>();
  const [SPUPageParams, setSPUPageParams] = useState({});

  // 处理请求参数
  const handlePageParmas = (params: any) => {
    const {
      current,
      shopSettleTime,
      liveDate,
      mcnIdList,
      orderTime,
      anchorIdList,
      bizOrderIdList,
      itemIdList,
      shopIdList,
      settleComisModeList,
      ...rest
    } = params;
    if (liveDate) {
      const [liveDateStart, liveDateEnd] = liveDate;
      rest.liveDateStart = dayjs(liveDateStart).format("YYYYMMDD");
      rest.liveDateEnd = dayjs(liveDateEnd).format("YYYYMMDD");
    }
    if (shopSettleTime) {
      const [shopSettleTimeStart, shopSettleTimeEnd] = shopSettleTime;
      rest.shopSettleTimeStart = dayjs(shopSettleTimeStart).format("YYYY-MM-DD");
      rest.shopSettleTimeEnd = dayjs(shopSettleTimeEnd).format("YYYY-MM-DD");
    }
    if (orderTime) {
      const [orderTimeStart, orderTimeEnd] = orderTime;
      rest.orderTimeStart = dayjs(orderTimeStart).format("YYYY-MM-DD");
      rest.orderTimeEnd = dayjs(orderTimeEnd).format("YYYY-MM-DD");
    }
    if (typeof settleComisModeList !== "undefined") {
      rest.settleComisModeList = [settleComisModeList];
    }

    rest.mcnIdList = splitSpaceComma(mcnIdList);
    rest.anchorIdList = splitSpaceComma(anchorIdList);
    rest.bizOrderIdList = splitSpaceComma(bizOrderIdList);
    rest.itemIdList = splitSpaceComma(itemIdList);
    rest.shopIdList = splitSpaceComma(shopIdList);
    return { ...rest, pageNo: current };
  };
  const handleExport = async () => {
    const { status } = await apis.downloadShopSettleOrder(SPUPageParams);
    if (status) {
      pushExportHistory();
    }
  };
  const advanceColumnsProps = { settleComisModeOptions };
  const columnsProps = {};
  return (
    <>
      <TableList
        actionRef={popActionRef}
        columns={columns(columnsProps)}
        scroll={{ y: "calc(100vh - 360px)" }}
        form={{
          initialValues: {
            liveDate: [dayjs().subtract(6, "day"), dayjs().subtract(0, "day")]
          }
        }}
        advanceColumns={advance_columns_search(advanceColumnsProps)}
        api={apis.getShopSettleOrderPage}
        preFetch={handlePageParmas}
        paramsChange={setSPUPageParams}
        rowKey="bizOrderId"
        toolbar={{
          actions: [
            <Button key="export" onClick={() => handleExport()} size={SIZE}>
              导出
            </Button>
          ]
        }}
      />
    </>
  );
};

const AliveRecord = () => (
  <KeepAlive name={PageUrl}>
    <DetailTable />
  </KeepAlive>
);
export default React.memo(AliveRecord);
