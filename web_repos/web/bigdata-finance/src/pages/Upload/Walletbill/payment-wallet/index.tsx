import React, { useContext, useRef } from "react";
import TableList from "@/components/TableList";
import { columns, advance_columns_search } from "./variable";
import apis from "@/services/downloadcenter/walletbill";
import { FieldsContext } from "./../index";
const Res = () => {
  const { handlePageParmas, bizTypeOptions, walletTypeOptions } = useContext(FieldsContext);
  const actionRef = useRef<any>();
  const columnsProps = {};
  const advanceColumnsProps = {
    bizTypeOptions,
    walletTypeOptions
  };
  return (
    <>
      <TableList
        actionRef={actionRef}
        advanceColumns={advance_columns_search(advanceColumnsProps)}
        columns={columns(columnsProps)}
        scroll={{ y: "calc(100vh - 340px)" }}
        form={{
          initialValues: {
            // liveDate: [dayjs().subtract(1, "month"), dayjs().subtract(1, "day")]
          }
        }}
        api={apis.getShopBillFlowPage}
        downloadApi={apis.downloadShopBillFlow}
        preFetch={handlePageParmas}
        rowKey="billFlowId"
      />
    </>
  );
};

export default React.memo(Res);
