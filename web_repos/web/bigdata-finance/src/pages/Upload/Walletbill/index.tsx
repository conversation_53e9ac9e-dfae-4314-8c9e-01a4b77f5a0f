import { Tabs } from "antd";
import React, { useState } from "react";
import KeepAlive from "react-activation";
import { SIZE, tabs_tabBarStyle } from "@/utils/constant";
import DistributionWallet from "./distribution-wallet";
import PaymentWallet from "./payment-wallet";
import dayjs from "dayjs";
import { splitSpaceComma } from "@/utils";
import useCommonOptions from "@/hooks/useCommonOptions";
export const FieldsContext = React.createContext<any>(null);

export const tab_distrib = "tab_distrib";
export const tab_payment = "tab_payment";
export const PageUrl = "/upload/walletbill";

const Res = () => {
  const [shopTypeOptions] = useCommonOptions({ dimName: "店铺性质" });
  const [bizTypeOptions] = useCommonOptions({ dimName: "快手钱包--交易业务类型" });
  const [walletTypeOptions] = useCommonOptions({ dimName: "钱包--钱包类型" });
  const [curTab, setCurTab] = useState(tab_distrib);

  const handlePageParmas = (params: any) => {
    const { current, liveDate, billTime, bizOrderIdList, itemIdList, walletTypeList, billFlowIdList, shopIdList, anchorIdList, ...rest } = params;
    if (liveDate) {
      const [liveDateStart, liveDateEnd] = liveDate;
      rest.liveDateStart = dayjs(liveDateStart).format("YYYYMMDD");
      rest.liveDateEnd = dayjs(liveDateEnd).format("YYYYMMDD");
    }
    if (billTime) {
      const [billTimeStart, billTimeEnd] = billTime;
      rest.billTimeStart = dayjs(billTimeStart).format("YYYY-MM-DD");
      rest.billTimeEnd = dayjs(billTimeEnd).format("YYYY-MM-DD");
    }
    rest.billFlowIdList = splitSpaceComma(billFlowIdList);
    rest.bizOrderIdList = splitSpaceComma(bizOrderIdList);
    rest.itemIdList = splitSpaceComma(itemIdList);
    rest.walletTypeList = splitSpaceComma(walletTypeList);
    rest.shopIdList = splitSpaceComma(shopIdList);
    rest.anchorIdList = splitSpaceComma(anchorIdList);
    return { ...rest, pageNo: current };
  };
  const tabItems = [
    {
      key: tab_distrib,
      label: "主播分销钱包",
      children: <DistributionWallet />
    },
    {
      key: tab_payment,
      label: "商家货款钱包",
      children: <PaymentWallet />
    }
  ];
  return (
    <FieldsContext.Provider value={{ handlePageParmas, shopTypeOptions, bizTypeOptions, walletTypeOptions }}>
      <Tabs activeKey={curTab} tabBarStyle={tabs_tabBarStyle} size={SIZE} onChange={setCurTab} items={tabItems} />
    </FieldsContext.Provider>
  );
};

const AliveAnchorItemSale = props => (
  <KeepAlive name={PageUrl}>
    <Res {...props} />
  </KeepAlive>
);

export default React.memo(AliveAnchorItemSale);
