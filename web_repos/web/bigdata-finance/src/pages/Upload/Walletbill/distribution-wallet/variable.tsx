import { Select } from "antd";
import { filterOptionLabel, returnEllipsisTooltip } from "@/utils";
import AnchorSelect from "@/components/AnchorSelect";
export const advance_columns_search = ({ bizTypeOptions, walletTypeOptions, shopTypeOptions }: any): TableListItem[] => {
  return [
    {
      title: "基本信息",
      valueType: "group",
      colProps: { span: 24 },
      columns: [
        {
          title: "店铺名称",
          dataIndex: "shopName",
          hideInTable: true
        },
        {
          title: "店铺性质",
          dataIndex: "shopTypeList",
          hideInTable: true,
          renderFormItem() {
            return (
              <Select
                placeholder="请选择"
                options={shopTypeOptions}
                filterOption={filterOptionLabel}
                showSearch
                allowClear
                maxTagCount="responsive"
                mode="multiple"
              />
            );
          }
        },
        {
          title: "钱包流水号",
          dataIndex: "billFlowIdList",
          hideInTable: true
        },
        {
          title: "订单ID",
          dataIndex: "bizOrderIdList",
          hideInTable: true
        },
        {
          title: "交易业务类型",
          dataIndex: "bizTypeList",
          hideInTable: true,
          renderFormItem() {
            return (
              <Select
                placeholder="请选择"
                options={bizTypeOptions}
                filterOption={filterOptionLabel}
                showSearch
                allowClear
                maxTagCount="responsive"
                mode="multiple"
              />
            );
          }
        },
        {
          title: "钱包类型",
          dataIndex: "walletTypeList",
          hideInTable: true,
          renderFormItem() {
            return <Select placeholder="请选择" options={walletTypeOptions} filterOption={filterOptionLabel} showSearch allowClear />;
          }
        }
      ]
    }
  ];
};
const columns_search = ({}: any) => {
  return [
    { title: "交易产生时间", dataIndex: "billTime", valueType: "dateRange", hideInTable: true },
    { title: "直播日期", dataIndex: "liveDate", valueType: "dateRange", hideInTable: true },
    {
      title: "主播ID",
      dataIndex: "anchorIdList",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "主播名称",
      dataIndex: "anchorNameList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem() {
        return <AnchorSelect params={{ type: 1 }} showSearch allowClear maxTagCount="responsive" mode="multiple" />;
      }
    },
    {
      title: "商品ID",
      dataIndex: "itemIdList",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "商品名称",
      dataIndex: "itemTitle",
      hideInTable: true
    },
    {
      title: "店铺ID",
      dataIndex: "shopIdList",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    }
  ];
};

export const columns = ({ shopTypeOptions }: any): Array<TableListItem> => {
  return [
    ...columns_search({ shopTypeOptions }),
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      hideInSearch: true,
      fixed: "left",
      render: (_, record, index) => {
        return index + 1;
      }
    },
    {
      title: "钱包流水号",
      dataIndex: "billFlowId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "交易产生时间", dataIndex: "billTime", width: 100, hideInSearch: true },
    { title: "发生金额", dataIndex: "billAmountStr", width: 100, hideInSearch: true },
    { title: "账户余额", dataIndex: "billWalletAmountStr", width: 100, hideInSearch: true },
    {
      title: "订单ID",
      dataIndex: "bizOrderId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "订单渠道",
      dataIndex: "orderChannelDesc",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "直播日期", dataIndex: "liveDate", width: 100, valueType: "date", hideInSearch: true },
    {
      title: "主播ID",
      dataIndex: "anchorId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "主播名称",
      dataIndex: "anchorName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品ID",
      dataIndex: "itemId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品名称",
      dataIndex: "itemName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "店铺ID",
      dataIndex: "shopId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "店铺名称",
      dataIndex: "shopName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "店铺性质",
      dataIndex: "shopTypeDesc",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "收支类型",
      dataIndex: "finaTypeDesc",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "交易业务类型",
      dataIndex: "bizTypeDesc",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "钱包类型",
      dataIndex: "walletTypeDesc",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "退回结算金额", dataIndex: "refundAmountStr", width: 100, hideInSearch: true },
    { title: "售后商品数量", dataIndex: "refundItemNumStr", width: 100, hideInSearch: true },
    { title: "是否订单相关", dataIndex: "isRelateOrderStr", width: 100, hideInSearch: true },
    { title: "是否超售后订单", dataIndex: "isRefundStr", width: 100, hideInSearch: true },
    { title: "是否年框扣费订单", dataIndex: "isYearlyOrderStr", width: 100, hideInSearch: true },
    { title: "是否结算主播", dataIndex: "isSettleAnchorStr", width: 100, hideInSearch: true },
    {
      title: "交易备注",
      dataIndex: "remark",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "交易备注明细",
      dataIndex: "remarkDetail",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      align: "center",
      ...item,
      title: returnEllipsisTooltip({ title })
    };
  });
};
