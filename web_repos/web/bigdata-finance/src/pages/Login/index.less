.login {
  background-image: url("../../assets/images/<EMAIL>");
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;

  &-info {
    font-size: 16px;
    color: #a3a0a0;
  }

  &-logo {
    position: absolute;
    width: 152px;
    height: 49px;
    top: 16px;
    left: 16px;
  }

  &-container {
    display: flex;
    flex-wrap: nowrap;
    justify-content: center;
    align-items: center;
    margin: 129px 266px;
  }

  &-left {
    width: 658px;
    height: 822px;
    background-image: url("../../assets/images/login_left.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: center;
    border-radius: 8px;
  }

  &-right {
    width: 731px;
    height: 778px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0 8px 8px 0;

    &__logo {
      width: 214px;
      height: 68px;
    }

    &__img {
      text-align: center;
    }

    &-container {
      width: 487px;
    }
  }

  .qrcode-container {
    width: 334px;
    height: 435px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: #f7f7f7;
    border-radius: 8px;
    margin-top: 21px;
    margin-left: 85px;
  }

  &-small-qrcode,
  &-small-computer {
    position: absolute;
    top: 22px;
    right: 0;

    img {
      width: 80px;
      height: 80px;
    }
  }
  &-authCode-content {
    display: flex;
    justify-content: space-between;
    &-right {
      flex: 3;
      position: relative;
      .authCode-img {
        position: absolute;
        right: 0;
        top: 9px;
      }
      .img-changeBtn {
        position: absolute;
        right: -65px;
        bottom: 10px;
        font-size: 14px;
        color: #999;
        cursor: pointer;
      }
    }
  }
}

.mainContent {
  color: #fff;
  font-size: 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: center;

  a {
    text-decoration: underline;
  }
}
