/* eslint-disable @typescript-eslint/no-unused-expressions */
import React, { useEffect, useState } from "react";
import useMainSize from "@/hooks/useMainSize";
import { Form, Input, ConfigProvider, Button, Tooltip, Image } from "antd";
import { UserOutlined, ShoppingOutlined, RedoOutlined } from "@ant-design/icons";
import { ajaxBaseUrl } from "@/utils/url";
import apis from "@/services/users";
import styles from "./index.less";
import { goNext } from "@/utils";
import JSEncrypt from "jsencrypt";
const screenHeight = 1080;

const LOGIN_PUBLIC_KEY =
  "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAgqUb1SebxPomwd7wIiKubqdf6ZVWIpKVwi5CdwVFv47tPef+X9bomj3xPBb5eeGpYW4ibwTmkrHeq7lUazIyzsTK+o2omi/PzglTCRv9fmai4wKCy8/XPsOav6uXoPXUNYM4tebpbcLIAAd5s5B2z6Y8Kc/r/qX9x9IBaSpr1uMPHtijBUnem3mh82qP5C4UfnOzkh1mRbHK+0VP+jEpANUc/3BISBQt8X3UxYQLbpDPm/EjkooHZuhEVG/eQCisPgpJKeZhj0jkgQ7oJwXVr32W+lFg48jn6zz1AQOjk+gQVqaSSxoFS8EDGqdVoPXdH3CPCJ1Nl0maocE/bCeh6wIDAQAB";

const authCodeImg = "https://s.xinc818.com/files/webcilysfnajyeuxdqm/验证码占位图.jpg";
const Login: React.FC = () => {
  // let pattern = /^(0|86|\+86)?1[3-9]\d{9}$/;
  const container = useMainSize("#root");
  const [form] = Form.useForm();
  const [showQrcode, setShowQrcode] = useState<boolean>(false);
  const [authCodeURL, setAuthCodeURL] = useState<any>(null);
  const encryptValue = value => {
    let encrypt = new JSEncrypt();
    encrypt.setPublicKey(LOGIN_PUBLIC_KEY);
    return encrypt.encrypt(value);
  };

  const getAuthCode = async () => {
    let name = form.getFieldValue("name");
    const { status, entry } = await apis.validateCode({ phone: name });
    if (status && entry) {
      setAuthCodeURL("data:image/jpeg;base64," + entry);
    } else {
      setAuthCodeURL(null);
    }
  };
  const onFinish = async (values: any) => {
    if (!authCodeURL) {
      await getAuthCode();
    }
    let { password: clearPassword, ...rest } = values;
    let password = encryptValue(clearPassword);
    const { status } = await apis.getLogin({ ...rest, password, source: 1 });
    if (status) {
      goNext();
    }
  };
  // redirect暂定，等待后端给定所有环境的url
  useEffect(() => {
    showQrcode &&
      new WwLogin({
        id: "login-large-qrcode",
        appid: "ww667800a44a6a4906", // 企业微信的CorpID
        agentid: "1000023", // 授权方的网页应用ID
        redirect_uri: encodeURIComponent(`${ajaxBaseUrl}data-finance/security/authenticate/wechartScanCodeLogin`), //重定向地址 url与配置的要一致
        state: "333",
        href: ""
      });
  }, [showQrcode]);

  return (
    <ConfigProvider
      theme={{
        components: {
          Button: {
            colorPrimary: `linear-gradient(263deg, #0D55F8 0%, #0097FF 100%);`,
            colorPrimaryHover: `linear-gradient(263deg, #0D55F8 0%, #0097FF 100%);`,
            colorPrimaryActive: `linear-gradient(263deg, #0D55F8 0%, #0097FF 100%);`
          }
        }
      }}
    >
      <div className={styles["login"]} style={{ height: container.height }}>
        <img className={styles["login-logo"]} src={require("@/assets/images/logo_login.png")} alt="" />
        <div className={styles["login-container"]} style={{ transform: `scale(${container.height / screenHeight})` }}>
          <div className={styles["login-left"]}></div>
          <div className={styles["login-right"]}>
            <div className={styles["login-right-container"]}>
              <div className={styles["login-right__img"]}>
                <img className={styles["login-right__logo"]} src={require("@/assets/images/<EMAIL>")} alt="" />
              </div>

              {/* 登录二维码 */}
              {/* {showQrcode && (
                <>
                  <div className={styles["qrcode-container"]}>
                    <div id="login-large-qrcode"></div>
                  </div>
                  <div className={styles["login-info"]} style={{ textAlign: "center", marginLeft: "15px" }}>
                    *请使用主体为【辛选控股】的企业微信扫码登录
                    <br />
                  </div>
                </>
              )} */}

              {/* 电脑Icon */}
              {showQrcode && (
                <div
                  className={styles["login-small-computer"]}
                  onClick={() => {
                    setShowQrcode(false);
                  }}
                >
                  <Tooltip title="账号密码登录" placement="left" arrow>
                    <img src={require("@/assets/images/computer.png")} alt="" />
                  </Tooltip>
                </div>
              )}

              {/* 二维码Icon */}
              {/* {!showQrcode && (
                <div
                  className={styles["login-small-qrcode"]}
                  onClick={() => {
                    setShowQrcode(true);
                  }}
                >
                  <Tooltip title="企业微信扫描登录" placement="left" arrow>
                    <img src={require("@/assets/images/qrcode.png")} alt="" />
                  </Tooltip>
                </div>
              )} */}

              {!showQrcode && (
                <Form
                  name="basic"
                  size="large"
                  form={form}
                  onFinish={onFinish}
                  // autoComplete="off"
                >
                  <Form.Item name="name" rules={[{ required: true, message: "请输入登录名！" }]}>
                    <Input
                      prefix={<UserOutlined style={{ fontSize: "24px", color: "#ddd" }} />}
                      placeholder="请输入登录名"
                      // onChange={() => {
                      //   if (form.getFieldValue("name")?.length > 0) {
                      //     getAuthCode();
                      //   } else {
                      //     setAuthCodeURL(null);
                      //     form.setFieldValue("code", null);
                      //   }
                      // }}
                      style={{ marginTop: "97px", height: "58px" }}
                    />
                  </Form.Item>
                  <Form.Item name="password" rules={[{ required: true, message: "请输入正确的密码！" }]}>
                    <Input.Password
                      prefix={<ShoppingOutlined style={{ fontSize: "24px", color: "#ddd" }} />}
                      placeholder="请输入密码"
                      style={{ marginTop: "16px", height: "58px" }}
                    />
                  </Form.Item>
                  {authCodeURL ? (
                    <>
                      <div className={styles["login-authCode-content"]}>
                        <Form.Item name="code" style={{ flex: 4 }} rules={[{ required: true, message: "请输入验证码！" }]}>
                          <Input placeholder="请输入验证码" style={{ marginTop: "16px", height: "58px" }} />
                        </Form.Item>
                        <div className={styles["login-authCode-content-right"]}>
                          <div className={styles["authCode-img"]}>
                            <Image
                              width={200}
                              height={70}
                              preview={false}
                              src={authCodeURL || authCodeImg}
                              onClick={getAuthCode}
                              style={{ cursor: "pointer" }}
                              fallback={authCodeImg}
                            />
                          </div>
                          {form.getFieldValue("name")?.length >= 11 ? (
                            <span className={styles["img-changeBtn"]} onClick={getAuthCode}>
                              <RedoOutlined />
                              &nbsp;换一个
                            </span>
                          ) : (
                            <></>
                          )}
                        </div>
                      </div>
                      <div>
                        <span className={styles["login-info"]}>*用户名输入完整手机号获取图形验证码</span>
                      </div>
                    </>
                  ) : null}

                  <Form.Item style={{ marginBottom: "4px" }}>
                    <Button type="primary" htmlType="submit" style={{ marginTop: "16px", height: "58px", width: "100%" }}>
                      立即登录
                    </Button>
                  </Form.Item>
                  <Form.Item>
                    <span className={styles["login-info"]}>*登录失败请联系:供应链中台-张力炜</span>
                  </Form.Item>
                </Form>
              )}
            </div>
          </div>
        </div>
      </div>
    </ConfigProvider>
  );
};

export default React.memo(Login);
