import useMainSize from "@/hooks/useMainSize";
import { Link } from "@umijs/max";
import styles from "./index.less";

const LoginError = () => {
  const container = useMainSize("#root");

  return (
    <div className={styles["login"]} style={{ height: container.height }}>
      <img className={styles["login-logo"]} src={require("@/assets/images/logo_login.png")} alt="" />
      <div className={styles["mainContent"]}>
        <img src={require("@/assets/images/tubiaozhizuomoban-2.png")} alt="" style={{ width: 150, height: 150 }} />
        <h1 style={{ color: "#fff" }}>出错啦</h1>
        <div>请使用【辛选控股】的企业微信扫码登录</div>
        {/* <div style={{ margin: "20px 0" }}>有问题可联系运营中心-张力炜</div> */}
        <div>
          &lt; <Link to="/login">返回登录</Link>
        </div>
      </div>
    </div>
  );
};

export default LoginError;
