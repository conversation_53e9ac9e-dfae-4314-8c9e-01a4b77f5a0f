import React, { useState, useRef } from "react";
import { But<PERSON> } from "antd";
import KeepAlive from "react-activation";
import TableList from "@/components/TableList";
import { SIZE } from "@/utils/constant";
import { Daily_unRecon_columns } from "./variable";
import apis from "@/services/supplierbill/reconciliationDiff";
import { pushExportHistory, splitSpaceComma } from "@/utils";
import ConfirmReconModal from "./ConfirmReconModal";
import useCommonOptions from "@/hooks/useCommonOptions";
import dayjs from "dayjs";
export const PageUrl = "/supplierbill/reconciliationDiff";
const DetailTable = () => {
  const [billSettlePatternOptions, billSettlePatternObj] = useCommonOptions({ dimName: "账单结算模式" });
  const [itemTypeOptions, itemTypeObj] = useCommonOptions({ dimName: "商品类型" });
  const [unReconDiffPageParams, setunReconDiffPageParams] = useState({});
  const [confirmReconDiffShow, setconfirmReconDiffShow] = useState(false);
  const unReconDiffActionsRef = useRef<any>();
  const Daily_unRecon_columnsProps = { billSettlePatternOptions, billSettlePatternObj, itemTypeOptions, itemTypeObj };

  // 处理请求参数
  const handleUnreconPageParmas = (params: any) => {
    const { current, settleDate, liveDate, itemIdList, ...rest } = params;
    if (liveDate) {
      const [liveDateStart, liveDateEnd] = liveDate;
      rest.liveDateStart = dayjs(liveDateStart).format("YYYYMMDD");
      rest.liveDateEnd = dayjs(liveDateEnd).format("YYYYMMDD");
    }
    if (settleDate) {
      const [settleDateStart, settleDateEnd] = settleDate;
      rest.settleDateStart = dayjs(settleDateStart).format("YYYYMMDD");
      rest.settleDateEnd = dayjs(settleDateEnd).format("YYYYMMDD");
    }
    rest.itemIdList = splitSpaceComma(itemIdList);
    return { ...rest, pageNo: current };
  };
  const handleExport = async () => {
    const { status } = await apis.downloadStatementDailyDiff(unReconDiffPageParams);
    if (status) {
      pushExportHistory();
    }
  };

  const ConfirmReconDiffProps = {
    title: "确认对账信息",
    visible: confirmReconDiffShow,
    params: unReconDiffPageParams,
    listApi: apis.getConfirmDiffReconcileItemList,
    confirmApi: apis.confirmDiffReconcilePeriod,
    fresh: () => {
      unReconDiffActionsRef.current?.reload();
    },
    close: () => {
      setconfirmReconDiffShow(false);
    }
  };

  // const confirmReconDiffDisable = useMemo(() => {
  //   const { settleDateStart, settleDateEnd, supplierName } = unReconDiffPageParams;
  //   if (settleDateStart && settleDateEnd && supplierName) return false;
  //   return true;
  // }, [unReconDiffPageParams]);

  return (
    <>
      <TableList
        form={{
          labelWidth: 120
        }}
        actionRef={unReconDiffActionsRef}
        columns={Daily_unRecon_columns(Daily_unRecon_columnsProps)}
        api={apis.getStatementDailyDiffPage}
        scroll={{ y: "calc(100vh - 460px)" }}
        preFetch={handleUnreconPageParmas}
        paramsChange={setunReconDiffPageParams}
        rowKey="md5Id"
        toolbar={{
          actions: [
            // <Button
            //   disabled={confirmReconDiffDisable}
            //   key="confirmReconDiff"
            //   type="primary"
            //   onClick={() => setconfirmReconDiffShow(true)}
            //   size={SIZE}
            // >
            //   确认账期
            // </Button>,
            <Button key="export" onClick={() => handleExport()} size={SIZE}>
              导出
            </Button>
          ]
        }}
      />
      {ConfirmReconDiffProps.visible ? <ConfirmReconModal {...ConfirmReconDiffProps} /> : null}
    </>
  );
};

const AliveRecord = () => (
  <KeepAlive name={PageUrl}>
    <DetailTable />
  </KeepAlive>
);
export default React.memo(AliveRecord);
