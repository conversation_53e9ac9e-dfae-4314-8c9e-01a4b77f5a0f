import { filterOptionLabel, returnEllipsisTooltip } from "@/utils";
import { Select } from "antd";
import AnchorSelect from "@/components/AnchorSelect";
import DepartmentSelect from "@/components/DepartmentSelect";
import { NOVALUE, SIZE } from "@/utils/constant";
import dayjs from "dayjs";
const Daily_unRecon_search = ({ billSettlePatternOptions, itemTypeOptions }: any) => {
  return [
    {
      // 日区间
      title: "结算日期",
      dataIndex: "settleDate",
      hideInSearch: false,
      hideInTable: true,
      valueType: "dateRange",
      fieldProps: { size: SIZE, maxDate: dayjs().add(-1, "day") }
    },
    {
      // 日区间
      title: "直播日期",
      dataIndex: "liveDate",
      hideInSearch: false,
      hideInTable: true,
      valueType: "dateRange"
    },
    {
      // 输入+下拉
      title: "主播",
      dataIndex: "anchorNameList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem() {
        return <AnchorSelect showSearch allowClear maxTagCount="responsive" mode="multiple" />;
      }
    },
    {
      title: "部门",
      dataIndex: "bigDepartmentList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return <DepartmentSelect key="bigDepartmentList" maxTagCount="responsive" mode="multiple" />;
      }
    },
    {
      // 输入搜索，支持逗号分隔搜索多个
      title: "商品ID",
      dataIndex: "itemIdList",
      hideInSearch: false,
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      // 输入
      title: "商品名称",
      dataIndex: "itemTitle",
      hideInSearch: false,
      hideInTable: true
    },
    {
      title: "商品类型",
      dataIndex: "itemType",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return (
          <Select
            placeholder="请选择"
            size={SIZE}
            options={itemTypeOptions}
            filterOption={filterOptionLabel}
            showSearch
            allowClear
            maxTagCount="responsive"
            mode="multiple"
          />
        );
      }
    },
    {
      title: "店铺",
      dataIndex: "shopName",
      hideInSearch: false,
      hideInTable: true
    },
    {
      title: "账单结算模式",
      dataIndex: "billSettlePatternList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return (
          <Select
            placeholder="请选择"
            size={SIZE}
            options={billSettlePatternOptions}
            filterOption={filterOptionLabel}
            showSearch
            allowClear
            maxTagCount="responsive"
            mode="multiple"
          />
        );
      }
    },
    {
      title: "财务对账人",
      dataIndex: "financeReconcile",
      hideInSearch: false,
      hideInTable: true
    },
    {
      title: "最新财务对账人",
      dataIndex: "latestFinanceReconcile",
      hideInSearch: false,
      hideInTable: true
    },
    {
      title: "业务对接人",
      dataIndex: "purchasePrincipal",
      hideInSearch: false,
      hideInTable: true
    },
    {
      title: "供应商名称",
      dataIndex: "supplierName",
      hideInSearch: false,
      hideInTable: true
    },
    {
      title: "我司合同主体",
      dataIndex: "contractName",
      hideInTable: true
    }
  ];
};
export const Daily_unRecon_columns = ({ billSettlePatternOptions, billSettlePatternObj, itemTypeOptions }: any): Array<TableListItem> => {
  return [
    ...Daily_unRecon_search({ billSettlePatternOptions, itemTypeOptions }),
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      hideInSearch: true,
      fixed: "left",
      editable: false,
      render: (_, record, index) => {
        return index + 1;
      }
    },
    { title: "结算日期", dataIndex: "settleDate", width: 100, fixed: "left", hideInSearch: true },
    { title: "直播日期", dataIndex: "liveDate", width: 100, fixed: "left", hideInSearch: true },
    {
      title: "主播",
      dataIndex: "anchorName",
      width: 100,
      hideInSearch: true,
      fixed: "left",
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品信息",
      editable: false,
      dataIndex: "itemInfo",
      width: 300,
      hideInSearch: true,
      render(_, record) {
        return (
          <div className="columnItemInfo">
            <div className="info">
              <div className="id">
                <span>{returnEllipsisTooltip({ title: "ID:" + (record?.itemId || NOVALUE) })}</span>
              </div>
              <div>{returnEllipsisTooltip({ title: record?.itemTitle, rows: 1, width: 250 })}</div>
            </div>
          </div>
        );
      }
    },
    {
      title: "商品类型",
      dataIndex: "itemTypeStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "SKUID",
      dataIndex: "itemSkuId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "SKU名称",
      dataIndex: "itemSkuName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "账单结算模式",
      dataIndex: "billSettlePattern",
      width: 100,
      hideInSearch: true,
      fixed: "left",
      render(text) {
        return returnEllipsisTooltip({ title: billSettlePatternObj[text] });
      }
    },
    { title: "类型", dataIndex: "billTypeStr", width: 100, hideInSearch: true },
    { title: "财务对账人", dataIndex: "financeReconcile", width: 100, hideInSearch: true },
    { title: "最新财务对账人", dataIndex: "latestFinanceReconcile", width: 120, hideInSearch: true },
    {
      title: "部门",
      dataIndex: "bigDepartment",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "业务对接人",
      dataIndex: "purchasePrincipal",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "店铺",
      dataIndex: "shopName",
      width: 140,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "店铺ID", dataIndex: "shopId", width: 100, hideInSearch: true },
    {
      title: "供应商名称",
      dataIndex: "supplierName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "我司合同主体",
      dataIndex: "contractName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "合计收入总和",
      dataIndex: "totalIncomeAmountStr",
      width: 100,
      hideInSearch: true,
      onCell: (record: any) => {
        const { totalIncomeAmount } = record;
        return {
          className: totalIncomeAmount !== 0 ? "waring-target" : ""
        };
      }
    },
    {
      title: "订单退款总和",
      dataIndex: "totalOrderRefundAmountStr",
      width: 100,
      hideInSearch: true,
      onCell: (record: any) => {
        const { totalOrderRefundAmount } = record;
        return {
          className: totalOrderRefundAmount !== 0 ? "waring-target" : ""
        };
      }
    },
    {
      title: "超售后期退款总和",
      dataIndex: "totalOverRefundAmountStr",
      width: 100,
      hideInSearch: true,
      onCell: (record: any) => {
        const { totalOverRefundAmount } = record;
        return {
          className: totalOverRefundAmount !== 0 ? "waring-target" : ""
        };
      }
    },
    {
      title: "退款总和",
      dataIndex: "totalRefundAmountStr",
      width: 100,
      hideInSearch: true,
      onCell: (record: any) => {
        const { totalRefundAmount } = record;
        return {
          className: totalRefundAmount !== 0 ? "waring-target" : ""
        };
      }
    },
    {
      title: "销售收入",
      dataIndex: "salesRevenueAmountStr",
      width: 100,
      hideInSearch: true,
      onCell: (record: any) => {
        const { salesRevenueAmount } = record;
        return {
          className: salesRevenueAmount !== 0 ? "waring-target" : ""
        };
      }
    },
    {
      title: "佣金比例",
      dataIndex: "commissionRateStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "总佣金金额",
      dataIndex: "totalCommissionAmountStr",
      width: 100,
      hideInSearch: true,
      onCell: (record: any) => {
        const { totalCommissionAmount } = record;
        return {
          className: totalCommissionAmount !== 0 ? "waring-target" : ""
        };
      }
    },
    {
      title: "达人佣金总和",
      dataIndex: "pidCommissionAmountStr",
      width: 100,
      hideInSearch: true,
      onCell: (record: any) => {
        const { pidCommissionAmount } = record;
        return {
          className: pidCommissionAmount !== 0 ? "waring-target" : ""
        };
      }
    },
    {
      title: "达人佣金追回总和",
      dataIndex: "pidBackCommissionAmountStr",
      width: 100,
      hideInSearch: true,
      onCell: (record: any) => {
        const { pidBackCommissionAmount } = record;
        return {
          className: pidBackCommissionAmount !== 0 ? "waring-target" : ""
        };
      }
    },
    {
      title: "线上佣金金额",
      dataIndex: "onlineCommissionAmountStr",
      width: 100,
      hideInSearch: true,
      onCell: (record: any) => {
        const { onlineCommissionAmount } = record;
        return {
          className: onlineCommissionAmount !== 0 ? "waring-target" : ""
        };
      }
    },
    {
      title: "销售数量",
      dataIndex: "salesVolumeStr",
      width: 100,
      hideInSearch: true,
      onCell: (record: any) => {
        const { salesVolume } = record;
        return {
          className: salesVolume !== 0 ? "waring-target" : ""
        };
      }
    },
    {
      title: "退款商品数量",
      dataIndex: "refundItemVolumeStr",
      width: 100,
      hideInSearch: true,
      onCell: (record: any) => {
        const { refundItemVolume } = record;
        return {
          className: refundItemVolume !== 0 ? "waring-target" : ""
        };
      }
    },
    {
      title: "实际结算数量",
      dataIndex: "actualSettleItemVolumeStr",
      width: 100,
      hideInSearch: true,
      onCell: (record: any) => {
        const { actualSettleItemVolume } = record;
        return {
          className: actualSettleItemVolume !== 0 ? "waring-target" : ""
        };
      }
    },
    {
      title: "技术服务费",
      dataIndex: "techServiceAmountStr",
      width: 100,
      hideInSearch: true,
      onCell: (record: any) => {
        const { techServiceAmount } = record;
        return {
          className: techServiceAmount !== 0 ? "waring-target" : ""
        };
      }
    },
    {
      title: "技术服务费追回",
      dataIndex: "techServiceBackAmountStr",
      width: 120,
      hideInSearch: true,
      onCell: (record: any) => {
        const { techServiceBackAmount } = record;
        return {
          className: techServiceBackAmount !== 0 ? "waring-target" : ""
        };
      }
    },
    {
      title: "实际技术服务费",
      dataIndex: "actualTechServiceAmountStr",
      width: 120,
      hideInSearch: true,
      onCell: (record: any) => {
        const { actualTechServiceAmount } = record;
        return {
          className: actualTechServiceAmount !== 0 ? "waring-target" : ""
        };
      }
    },
    { title: "供货价", dataIndex: "supplyPriceStr", width: 100, hideInSearch: true },
    {
      title: "供货总成本",
      dataIndex: "totalSupplyCostStr",
      width: 100,
      hideInSearch: true,
      onCell: (record: any) => {
        const { totalSupplyCost } = record;
        return {
          className: totalSupplyCost !== 0 ? "waring-target" : ""
        };
      }
    },
    {
      title: "线下结算金额",
      dataIndex: "offlineSettleAmountStr",
      width: 100,
      hideInSearch: true,
      fixed: "left",
      className: "danger-target"
    },
    { title: "数据源", dataIndex: "dataSourceFile", width: 120, hideInSearch: true }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      align: "center",
      ...item,
      title: returnEllipsisTooltip({ title })
    };
  });
};
export const ConfirmRecon_columns = ({}): Array<TableListItem> => {
  return [
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      hideInSearch: true,
      fixed: "left",
      editable: false,
      render: (_, record, index) => {
        return index + 1;
      }
    },
    {
      title: "主播",
      dataIndex: "anchorName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "直播日期", dataIndex: "liveDate", width: 100, hideInSearch: true },
    {
      title: "商品名称",
      dataIndex: "itemTitle",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品类型",
      dataIndex: "itemTypeStr",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "SKUID",
      dataIndex: "itemSkuId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "SKU名称",
      dataIndex: "itemSkuName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "账单结算模式",
      dataIndex: "billSettlePatternStr",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "类型",
      dataIndex: "billTypeStr",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      align: "center",
      ...item,
      title: returnEllipsisTooltip({ title })
    };
  });
};
