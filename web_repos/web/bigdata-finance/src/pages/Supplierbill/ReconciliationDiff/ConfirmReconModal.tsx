import React, { useEffect, useState } from "react";
import { Modal, Form, DatePicker, Input } from "antd";
import { UploadExcelModalProps } from "@/components/UploadExcelModal";
import TableList from "@/components/TableList";
import { ConfirmRecon_columns } from "./variable";
import { NOVALUE, SIZE } from "@/utils/constant";
import dayjs from "dayjs";

interface IProps extends UploadExcelModalProps {
  params: Record<string, any>;
  // 列表接口
  listApi: (p: Record<string, any>) => Promise<any>;
  // 确认接口
  confirmApi: (p: Record<string, any>) => Promise<any>;
}
const FormLayout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 20 }
};
const UploadExcelModal = (props: IProps) => {
  const { visible, close, fresh, title, params, listApi, confirmApi } = props;
  const [form] = Form.useForm();
  const [lists, setLists] = useState([]);
  const fetchList = async (params: IProps["params"]) => {
    const { entry, status } = await listApi?.(params);
    if (status) {
      const { settleDateEnd, settleDateStart } = params;
      const { confirmReconcileItemVOList = [] } = entry || {};
      form.setFieldsValue({ ...entry, settleDate: [dayjs(settleDateStart), dayjs(settleDateEnd)] });

      setLists(confirmReconcileItemVOList);
    } else {
      setLists([]);
    }
  };
  useEffect(() => {
    fetchList(params);
  }, [params]);
  const handleExcelCancel = () => {
    form.resetFields();
    close?.();
  };

  const handleExcelOk = () => {
    form.validateFields().then(async values => {
      const { settleDate, ...rest } = values;
      if (settleDate) {
        const [settleDateStart, settleDateEnd] = settleDate;
        rest.settleDateStart = dayjs(settleDateStart).format("YYYYMMDD");
        rest.settleDateEnd = dayjs(settleDateEnd).format("YYYYMMDD");
      }

      const { status } = await confirmApi?.({ ...params, ...rest });
      if (status) {
        close?.();
        fresh?.();
      }
    });
  };

  return (
    <Modal
      centered={true}
      title={title}
      open={visible}
      width={700}
      onOk={() => handleExcelOk()}
      onCancel={() => handleExcelCancel()}
      maskClosable={false}
      okButtonProps={{
        size: SIZE
      }}
      cancelButtonProps={{
        size: SIZE
      }}
    >
      <Form size={SIZE} form={form} {...FormLayout}>
        <Form.Item label="选择对账期间" name="settleDate" rules={[{ required: true, message: "请选择对账期间" }]}>
          <DatePicker.RangePicker disabled={true} allowClear style={{ width: "100%" }} maxDate={dayjs().add(-1, "day")} />
        </Form.Item>
        <Form.Item label="供应商" name="supplierName">
          <Input disabled={true} placeholder=""></Input>
        </Form.Item>
        <Form.Item label="对账商品确认" name="confirmReconcileItemVOList">
          <TableList
            columns={ConfirmRecon_columns({})}
            columnEmptyText={NOVALUE}
            scroll={{ x: "max-content" }}
            dataSource={lists}
            options={false}
            pagination={false}
            rowKey="id"
            search={false}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};
export default React.memo(UploadExcelModal);
