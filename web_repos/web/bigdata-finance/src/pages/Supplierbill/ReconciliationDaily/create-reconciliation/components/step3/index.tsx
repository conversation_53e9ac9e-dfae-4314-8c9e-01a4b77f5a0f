import React, { useEffect, useState } from "react";
import apis from "@/services/supplierbill/reconciliationDaily";
import { Empty, Spin } from "antd";
import { ICreateParams } from "../..";
import DetailTable from "./DetailTable";

interface IStep3 {
  step1Params: ICreateParams;
  step2Params: ICreateParams;
  handlePageParams: (params: Record<string, any>) => Record<string, any>;
}
const Res = (props: IStep3) => {
  const { step1Params, step2Params, handlePageParams } = props;
  const [result, setResult] = useState<JSX.Element[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchPreview = async params => {
    setLoading(true);
    const { status, entry } = await apis.getSupplierBillPreview(params);
    if (status) {
      const res = (entry || [])
        .map(item => {
          const { reconcilePrintEnum } = item;
          return reconcilePrintEnum ? <DetailTable key={reconcilePrintEnum} type={reconcilePrintEnum} data={item} /> : null;
        })
        .filter(item => item);
      setResult(res);
    }
    setLoading(false);
  };
  useEffect(() => {
    fetchPreview(handlePageParams({ ...step1Params, ...step2Params }));
  }, [step1Params, step2Params]);
  return (
    <div>
      <Spin spinning={loading}>
        <>{result.length ? result : <Empty />}</>
      </Spin>
    </div>
  );
};
export default React.memo(Res);
