import React, { useEffect, useState } from "react";
import { ProFormDateRangePicker, ProFormSelect } from "@ant-design/pro-components";
import { SIZE } from "@/utils/constant";
import apis from "@/services/supplierbill/reconciliationDaily";
import { returnOptions } from "@/utils";
import dayjs from "dayjs";
const Res = () => {
  const [supplierList, setSupplierList] = useState<IOptions[]>([]);
  const fetchSupplier = async () => {
    const { status, entry } = await apis.getGenerateSupplierNameList();
    if (status) {
      setSupplierList(returnOptions(entry));
    } else {
      setSupplierList([]);
    }
  };
  useEffect(() => {
    fetchSupplier();
  }, []);
  return (
    <>
      <ProFormSelect
        fieldProps={{ size: SIZE }}
        options={supplierList}
        name="supplierName"
        width="md"
        label="供应商名称"
        placeholder="请输入"
        showSearch
        rules={[{ required: true, message: "请选择" }]}
      />
      <ProFormDateRangePicker
        fieldProps={{ size: SIZE, maxDate: dayjs().subtract(1, "day") }}
        name="settleDate"
        width="md"
        label="对账区间"
        rules={[{ required: true, message: "请选择" }]}
      />
    </>
  );
};
export default React.memo(Res);
