import React, { useEffect, useMemo, useRef, useState, useContext } from "react";
import { Space, Divider, Form, Radio, Button, message } from "antd";
import TableList from "@/components/TableList";
import { info_columns } from "./variable";
import apis from "@/services/supplierbill/reconciliationDaily";
import { ifEmptyObj } from "@/utils";
import { history } from "@umijs/max";
import queryString from "query-string";
import { menus } from "@/pages/Supplierbill/Reconciliationbill/reconcili-bill";
import { FieldsContext } from "@/pages/Supplierbill/ReconciliationDaily";
import useCommonOptions from "@/hooks/useCommonOptions";
import { ICreateParams } from "../..";
import { tab_reconcili_bill, PageUrl as reconcile_bill_PageUrl } from "@/pages/Supplierbill/Reconciliationbill";
export const only_company = "2";
export const only_xinxuan = "1,2";
const infoRadioOptions = [
  {
    label: "仅供应商数据计算",
    value: only_company
  },
  {
    label: "包含辛选数据计算",
    value: only_xinxuan
  }
];

interface IStep2 {
  step1Params: ICreateParams;
  setStep2Params: (p: Record<string, any>) => void;
  handlePageParams: (params: Record<string, any>) => Record<string, any>;
}
const BillDetail = ({ query }: any) => {
  return (
    <Button
      type="link"
      onClick={() => {
        history.push(query);
      }}
    >
      点击查看对账单
    </Button>
  );
};
const checkSupplierObj = (params: IStep2["step1Params"]): Record<number, any> => {
  const { settleDateStart, settleDateEnd, supplierName } = params;
  return {
    1: {
      info: ""
    },
    2: {
      info: (
        <BillDetail
          query={{
            pathname: reconcile_bill_PageUrl,
            search: queryString.stringify({
              $_tab: tab_reconcili_bill,
              supplierName,
              menu: menus.menu_draft
            })
          }}
        />
      )
    },
    3: {
      info: (
        <BillDetail
          query={{
            pathname: reconcile_bill_PageUrl,
            search: queryString.stringify({
              $_tab: tab_reconcili_bill,
              supplierName: supplierName,
              menu: menus.menu_draft
            })
          }}
        />
      )
    },
    4: {
      info: (
        <BillDetail
          query={{
            pathname: reconcile_bill_PageUrl,
            search: queryString.stringify({
              $_tab: tab_reconcili_bill,
              settleDateStart,
              settleDateEnd,
              supplierName
            })
          }}
        />
      )
    }
  };
};
const Res = (props: IStep2) => {
  const { billSettlePatternOptions } = useContext(FieldsContext);
  const [undertakerRemarkOptions] = useCommonOptions({ dimName: "快手技术服务费承担方" });

  const { step1Params, handlePageParams, setStep2Params } = props;
  // const [modelCheckEditForm] = Form.useForm();
  const [editForm] = Form.useForm();
  const [infoRadioValue, setInfoRadioValue] = useState(step1Params.dataSourceFileList);

  const [checkSupplierList, setCheckSupplierList] = useState([]);
  // const [modelCheckEditableKeys, setModelCheckEditableKeys] = useState<React.Key[]>([]);
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  // const modelCheckActionRef = useRef<any>();
  const goodsCheckActionRef = useRef<any>();
  const infoTitle = useMemo(() => {
    const { supplierName, settleDateStart, settleDateEnd } = step1Params;
    return `${supplierName} ${settleDateStart}至${settleDateEnd}`;
  }, [step1Params]);
  const fetchCheckSupplierPeriod = async params => {
    setCheckSupplierList([]);
    const { entry = [], status, timestamp } = await apis.checkSupplierPeriod(params);
    if (status) {
      const res = entry?.map(item => {
        const { checkSupplierPeriodType, checkMsgInfo } = item;
        const curChech = checkSupplierObj(step1Params)?.[checkSupplierPeriodType];
        return (
          <div key={checkSupplierPeriodType + timestamp + checkMsgInfo}>
            {`${checkMsgInfo}`}
            {curChech?.info}
          </div>
        );
      });
      setCheckSupplierList(res);
    } else {
      setCheckSupplierList([]);
    }
  };

  // const modelCheckEditable: any = {
  //   type: "single",
  //   form: modelCheckEditForm,
  //   onSave: async (key, editData) => {
  //     const { billSettlePattern, ...rest } = editData;
  //     const params = { ...rest, billSettlePattern: billSettlePattern?.join() };
  //     const { status } = await apis.modifyStatementScheduleItemOrSkuPattern(params);
  //     modelCheckEditForm.resetFields([key]);
  //     if (status) {
  //       message.success("修改成功！");
  //     }
  //     modelCheckActionRef.current?.reload();
  //     goodsCheckActionRef.current?.reload();
  //   },
  //   modelCheckEditableKeys,
  //   onChange: setModelCheckEditableKeys,
  //   actionRender: (row, config, { save, cancel }) => [save, cancel]
  // };
  const editable: any = {
    type: "single",
    form: editForm,
    onSave: async (key, editData) => {
      const params = { ...editData };
      if (Array.isArray(editData.billSettlePattern)) {
        params.billSettlePattern = editData.billSettlePattern?.join();
      }
      const { status } = await apis.modifyStatementScheduleItemOrSkuInfo(params);
      editForm.resetFields([key]);
      if (status) {
        message.success("修改成功！");
      }
      goodsCheckActionRef.current?.reload();
    },
    editableKeys,
    onChange: setEditableRowKeys,
    actionRender: (row, config, { save, cancel }) => [save, cancel]
  };
  useEffect(() => {
    if (ifEmptyObj(step1Params)) {
      fetchCheckSupplierPeriod(handlePageParams(step1Params));
    }
  }, [step1Params]);
  useEffect(() => {
    setStep2Params({ dataSourceFileList: infoRadioValue });
  }, [infoRadioValue]);
  const handleParams = p => {
    setEditableRowKeys([]);
    const params = handlePageParams(p);
    return params;
  };
  return (
    <>
      <h2 style={{ textAlign: "center" }}>{infoTitle}</h2>
      <Divider orientation="left">账期检查</Divider>
      <Form.Item label=" " colon={false} labelCol={{ span: 4 }}>
        <Space direction="vertical" align="start">
          {checkSupplierList.length ? checkSupplierList : "暂无数据！"}
        </Space>
      </Form.Item>
      {/* <Divider orientation="left">模式检查</Divider>
      <Form.Item label=" " colon={false} labelCol={{ span: 1 }}>
        <TableList
          actionRef={modelCheckActionRef}
          columns={mode_columns({ billSettlePatternOptions })}
          options={false}
          pagination={false}
          search={false}
          api={apis.checkSupplierItemPattern}
          handleDatas={data => {
            return data?.map(item => {
              return {
                ...item,
                $_unionPrimaryKey: JSON.stringify(item)
              };
            });
          }}
          params={step1Params}
          preFetch={handlePageParams}
          editable={modelCheckEditable}
          scroll={{ x: 400, y: "330px" }}
          rowKey="$_unionPrimaryKey"
        />
      </Form.Item> */}

      <Divider orientation="left">商品信息核对</Divider>
      <Form.Item label=" " colon={false} labelCol={{ span: 1 }}>
        <TableList
          actionRef={goodsCheckActionRef}
          toolbar={{
            actions: [
              <Radio.Group
                style={{ width: 310 }}
                key="a"
                defaultValue={infoRadioValue}
                options={infoRadioOptions}
                onChange={e => {
                  const val = e.target.value;
                  setInfoRadioValue(val);
                }}
              ></Radio.Group>
            ]
          }}
          editable={editable}
          columns={info_columns({ undertakerRemarkOptions, billSettlePatternOptions })}
          options={false}
          pagination={false}
          search={false}
          params={{ ...step1Params, dataSourceFileList: infoRadioValue }}
          preFetch={handleParams}
          api={apis.checkSupplierItemInfo}
          handleDatas={data => {
            return data?.map(item => {
              return {
                ...item,
                $_unionPrimaryKey: JSON.stringify(item)
              };
            });
          }}
          scroll={{ x: 400, y: "330px" }}
          rowKey="$_unionPrimaryKey"
        />
      </Form.Item>
    </>
  );
};

export default React.memo(Res);
