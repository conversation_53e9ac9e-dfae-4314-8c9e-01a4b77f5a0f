import EditInputNumber from "@/components/EditInputNumber";
import EditInputNumberPercent from "@/components/EditInputNumberPercent";
import EditSelect from "@/components/EditSelect";
import { filterOptionLabel, returnEllipsisTooltip } from "@/utils";
import { SIZE } from "@/utils/constant";
import { Button } from "antd";
export const mode_columns = ({ billSettlePatternOptions }: any): TableListItem[] => {
  return [
    {
      title: "商品ID",
      dataIndex: "itemId",
      width: 100,
      editable: false,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品名称",
      dataIndex: "itemTitle",
      width: 120,
      editable: false,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "SKU名称",
      dataIndex: "skuName",
      width: 120,
      editable: false,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "主播",
      dataIndex: "anchorName",
      width: 100,
      editable: false,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "账单结算模式",
      dataIndex: "billSettlePattern",
      width: 120,
      editable: true,
      fieldProps: {
        size: SIZE
      },
      render(_, record) {
        const { billSettlePatternStr } = record;
        return returnEllipsisTooltip({ title: billSettlePatternStr });
      },
      renderFormItem: record => {
        const {
          entry: { billSettlePattern }
        } = record;
        let val = [];
        if (billSettlePattern) {
          val = billSettlePattern?.split(",");
        }
        return (
          <EditSelect
            placeholder="请选择"
            size={SIZE}
            defaultValue={val}
            style={{ textAlign: "left" }}
            options={billSettlePatternOptions}
            filterOption={filterOptionLabel}
            showSearch
            allowClear
            maxTagCount="responsive"
            mode="multiple"
          />
        );
      }
    },
    {
      title: "操作",
      valueType: "option",
      fixed: "right",
      width: 100,
      editable: true,
      render: (text: string, record: any, index: number, action: any) => {
        const { $_unionPrimaryKey } = record;
        return (
          <Button
            type="link"
            size={SIZE}
            onClick={() => {
              action?.startEditable($_unionPrimaryKey);
            }}
          >
            编辑
          </Button>
        );
      }
    }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      align: "center",
      ...item,
      title: returnEllipsisTooltip({ title })
    };
  });
};
export const info_columns = ({ undertakerRemarkOptions, billSettlePatternOptions }: any): TableListItem[] => {
  return [
    {
      title: "主播",
      dataIndex: "anchorName",
      width: 100,
      fixed: "left",
      editable: false,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "直播日期",
      dataIndex: "liveDate",
      width: 100,
      editable: false,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品ID",
      dataIndex: "itemId",
      width: 100,
      editable: false,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品名称",
      dataIndex: "itemTitle",
      width: 100,
      editable: false,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品类型",
      dataIndex: "itemType",
      width: 100,
      editable: false,
      render(_, record) {
        const { itemTypeStr } = record;
        return returnEllipsisTooltip({ title: itemTypeStr });
      }
    },
    {
      title: "SKU名称",
      dataIndex: "skuNameInfo",
      width: 100,
      editable: false,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },

    {
      title: "账单结算模式",
      dataIndex: "billSettlePattern",
      width: 120,
      editable: true,
      render(_, record) {
        const { billSettlePatternStr } = record;
        return returnEllipsisTooltip({ title: billSettlePatternStr });
      },
      renderFormItem(_, record) {
        const {
          record: { billSettlePattern }
        } = record;
        let val = [];
        if (Array.isArray(billSettlePattern)) {
          val = billSettlePattern;
        } else if (billSettlePattern) {
          val = billSettlePattern?.split(",");
        }
        return (
          <EditSelect
            defaultValue={val}
            placeholder="请选择"
            size={SIZE}
            style={{ textAlign: "left" }}
            options={billSettlePatternOptions}
            filterOption={filterOptionLabel}
            showSearch
            allowClear
            maxTagCount="responsive"
            mode="multiple"
          />
        );
      }
    },
    {
      title: "线上佣金比例",
      dataIndex: "onlineCommissionRate",
      width: 100,
      editable: false,
      render(_, record) {
        const { onlineCommissionRateStr } = record;
        return returnEllipsisTooltip({ title: onlineCommissionRateStr });
      }
    },
    {
      title: "线下佣金比例",
      dataIndex: "offlineCommissionRate",
      width: 100,
      editable: true,
      render(_, record) {
        const { offlineCommissionRateStr } = record;
        return returnEllipsisTooltip({ title: offlineCommissionRateStr });
      },
      renderFormItem: (_, { isEditable }) => {
        return (
          isEditable && (
            <EditInputNumberPercent formatter={value => `${value}%`} style={{ width: "100%" }} max={100} step={1} placeholder="线下佣金比例" />
          )
        );
      }
    },
    {
      title: "主品成本单价",
      dataIndex: "mainProductCostPrice",
      width: 100,
      editable: true,
      render(_, record) {
        const { mainProductCostPriceStr } = record;
        return returnEllipsisTooltip({ title: mainProductCostPriceStr });
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
      }
    },
    {
      title: "赠品成本单价",
      dataIndex: "giveawayProductCostPrice",
      width: 100,
      editable: true,
      render(_, record) {
        const { giveawayProductCostPriceStr } = record;
        return returnEllipsisTooltip({ title: giveawayProductCostPriceStr });
      },
      renderFormItem: (_, { isEditable }) => {
        return isEditable && <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
      }
    },
    {
      title: "技术服务费承担方",
      dataIndex: "undertakerRemark",
      width: 140,
      editable: true,
      render(_, record) {
        const { undertakerRemarkStr } = record;
        return returnEllipsisTooltip({ title: undertakerRemarkStr });
      },
      renderFormItem() {
        return (
          <EditSelect
            placeholder="请选择"
            size={SIZE}
            style={{ textAlign: "left" }}
            options={(undertakerRemarkOptions || [])?.map(item => {
              return {
                ...item,
                value: +item.value
              };
            })}
            filterOption={filterOptionLabel}
            showSearch
            allowClear
          />
        );
      }
    },
    {
      title: "操作",
      valueType: "option",
      fixed: "right",
      width: 100,
      render: (text: string, record: any, index: number, action: any) => {
        const { $_unionPrimaryKey } = record;
        return (
          <Button
            type="link"
            size={SIZE}
            onClick={() => {
              action?.startEditable($_unionPrimaryKey);
            }}
          >
            编辑
          </Button>
        );
      }
    }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      align: "center",
      ...item,
      title: returnEllipsisTooltip({ title })
    };
  });
};
