import { millennialsNumber, returnEllipsisTooltip, handlePercentage } from "@/utils";
import { IProps } from "./DetailTable";
// 收/返佣对账单
export const COMMISSION_RECEIVE_PAY = ({}: any): TableListItem[] => {
  return [
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      hideInSearch: true,
      fixed: "left",
      editable: false,
      render: (_, record, index) => {
        return index + 1;
      }
    },
    {
      title: "店铺",
      dataIndex: "shopName",
      width: 100,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "直播日期",
      dataIndex: "liveDate",
      width: 100,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "主播",
      dataIndex: "anchorName",
      width: 100,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品ID",
      dataIndex: "itemId",
      width: 100,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品名称",
      dataIndex: "itemTitle",
      width: 100,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "SKU信息",
      dataIndex: "skuNameInfo",
      width: 100,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "销售数量",
      dataIndex: "salesVolume",
      width: 100,
      summaryTooltip: {
        divisor: 1,
        millennials: true
      },
      render(text) {
        return millennialsNumber(text);
      }
    },
    {
      title: "合计收入总和",
      dataIndex: "totalIncomeAmount",
      width: 110,
      summaryTooltip: {
        divisor: 10000,
        millennials: true,
        millennialsOptions: { minimumFractionDigits: 4 }
      },
      render(text) {
        return millennialsNumber(text / 10000, { minimumFractionDigits: 4 });
      }
    },
    {
      title: "退款商品数量",
      dataIndex: "refundItemVolume",
      width: 110,
      summaryTooltip: {
        divisor: 1,
        millennials: true
      },
      render(text) {
        return millennialsNumber(text);
      }
    },
    {
      title: "退款",
      dataIndex: "totalRefundAmount",
      width: 100,
      summaryTooltip: {
        divisor: 10000,
        millennials: true,
        millennialsOptions: { minimumFractionDigits: 4 }
      },
      render(text) {
        return millennialsNumber(text / 10000, { minimumFractionDigits: 4 });
      }
    },
    {
      title: "佣金比例",
      dataIndex: "commissionRate",
      width: 100,
      render(text) {
        return returnEllipsisTooltip({ title: handlePercentage(text).toFixed(2) + "%" });
      }
    },
    {
      title: "总佣金",
      dataIndex: "totalCommissionAmount",
      width: 100,
      summaryTooltip: {
        divisor: 10000,
        millennials: true,
        millennialsOptions: { minimumFractionDigits: 4 }
      },
      render(text) {
        return millennialsNumber(text / 10000, { minimumFractionDigits: 4 });
      }
    },
    {
      title: "达人佣金总和",
      dataIndex: "pidCommissionAmount",
      width: 110,
      summaryTooltip: {
        divisor: 10000,
        millennials: true,
        millennialsOptions: { minimumFractionDigits: 4 }
      },
      render(text) {
        return millennialsNumber(text / 10000, { minimumFractionDigits: 4 });
      }
    },
    {
      title: "达人佣金追回总和",
      dataIndex: "pidBackCommissionAmount",
      width: 120,
      summaryTooltip: {
        divisor: 10000,
        millennials: true,
        millennialsOptions: { minimumFractionDigits: 4 }
      },
      render(text) {
        return millennialsNumber(text / 10000, { minimumFractionDigits: 4 });
      }
    },
    {
      title: "线下结算金额",
      dataIndex: "offlineSettleAmount",
      width: 120,
      summaryTooltip: {
        divisor: 10000,
        millennials: true,
        millennialsOptions: { minimumFractionDigits: 4 }
      },
      render(text) {
        return millennialsNumber(text / 10000, { minimumFractionDigits: 4 });
      }
    },
    {
      title: "技术服务费",
      dataIndex: "techServiceAmount",
      width: 110,
      summaryTooltip: {
        divisor: 10000,
        millennials: true,
        millennialsOptions: { minimumFractionDigits: 4 }
      },
      render(text) {
        return millennialsNumber(text / 10000, { minimumFractionDigits: 4 });
      }
    },
    {
      title: "技术服务费退回",
      dataIndex: "techServiceBackAmount",
      width: 120,
      summaryTooltip: {
        divisor: 10000,
        millennials: true,
        millennialsOptions: { minimumFractionDigits: 4 }
      },
      render(text) {
        return millennialsNumber(text / 10000, { minimumFractionDigits: 4 });
      }
    }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      align: "center",
      ...item,
      title: returnEllipsisTooltip({ title })
    };
  });
};
// 供货价对账单
export const SUPPLY_PRICE = ({}: any): TableListItem[] => {
  return [
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      hideInSearch: true,
      fixed: "left",
      editable: false,
      render: (_, record, index) => {
        return index + 1;
      }
    },
    {
      title: "店铺",
      dataIndex: "shopName",
      width: 100,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "直播日期",
      dataIndex: "liveDate",
      width: 100,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "主播",
      dataIndex: "anchorName",
      width: 100,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品ID",
      dataIndex: "itemId",
      width: 100,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品名称",
      dataIndex: "itemTitle",
      width: 100,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "SKU信息",
      dataIndex: "skuNameInfo",
      width: 100,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "销售数量",
      dataIndex: "salesVolume",
      width: 100,
      summaryTooltip: {
        divisor: 1,
        millennials: true
      },
      render(text) {
        return millennialsNumber(text);
      }
    },
    {
      title: "合计收入总和",
      dataIndex: "totalIncomeAmount",
      width: 120,
      summaryTooltip: {
        divisor: 10000,
        millennials: true,
        millennialsOptions: { minimumFractionDigits: 4 }
      },
      render(text) {
        return millennialsNumber(text / 10000, { minimumFractionDigits: 4 });
      }
    },
    {
      title: "退款商品数量",
      dataIndex: "refundItemVolume",
      width: 120,
      summaryTooltip: {
        divisor: 1,
        millennials: true
      },
      render(text) {
        return millennialsNumber(text);
      }
    },
    {
      title: "退款",
      dataIndex: "totalRefundAmount",
      width: 100,
      summaryTooltip: {
        divisor: 10000,
        millennials: true,
        millennialsOptions: { minimumFractionDigits: 4 }
      },
      render(text) {
        return millennialsNumber(text / 10000, { minimumFractionDigits: 4 });
      }
    },
    {
      // 毫
      title: "供货价",
      dataIndex: "supplyPrice",
      width: 100,
      summaryTooltip: {
        divisor: 10000,
        millennials: true,
        millennialsOptions: { minimumFractionDigits: 4 }
      },
      render(text) {
        return millennialsNumber(text / 10000, { minimumFractionDigits: 4 });
      }
    },
    {
      title: "供货总成本",
      dataIndex: "totalSupplyCost",
      width: 110,
      summaryTooltip: {
        divisor: 10000,
        millennials: true,
        millennialsOptions: { minimumFractionDigits: 4 }
      },
      render(text) {
        return millennialsNumber(text / 10000, { minimumFractionDigits: 4 });
      }
    },
    {
      title: "达人佣金总和",
      dataIndex: "pidCommissionAmount",
      width: 120,
      summaryTooltip: {
        divisor: 10000,
        millennials: true,
        millennialsOptions: { minimumFractionDigits: 4 }
      },
      render(text) {
        return millennialsNumber(text / 10000, { minimumFractionDigits: 4 });
      }
    },
    {
      title: "达人佣金追回总和",
      dataIndex: "pidBackCommissionAmount",
      width: 120,
      summaryTooltip: {
        divisor: 10000,
        millennials: true,
        millennialsOptions: { minimumFractionDigits: 4 }
      },
      render(text) {
        return millennialsNumber(text / 10000, { minimumFractionDigits: 4 });
      }
    },
    {
      title: "线下结算金额",
      dataIndex: "offlineSettleAmount",
      width: 120,
      summaryTooltip: {
        divisor: 10000,
        millennials: true,
        millennialsOptions: { minimumFractionDigits: 4 }
      },
      render(text) {
        return millennialsNumber(text / 10000, { minimumFractionDigits: 4 });
      }
    },
    {
      title: "技术服务费",
      dataIndex: "techServiceAmount",
      width: 110,
      summaryTooltip: {
        divisor: 10000,
        millennials: true,
        millennialsOptions: { minimumFractionDigits: 4 }
      },
      render(text) {
        return millennialsNumber(text / 10000, { minimumFractionDigits: 4 });
      }
    },
    {
      title: "技术服务费退回",
      dataIndex: "techServiceBackAmount",
      width: 120,
      summaryTooltip: {
        divisor: 10000,
        millennials: true,
        millennialsOptions: { minimumFractionDigits: 4 }
      },
      render(text) {
        return millennialsNumber(text / 10000, { minimumFractionDigits: 4 });
      }
    }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      align: "center",
      ...item,
      title: returnEllipsisTooltip({ title })
    };
  });
};
// 采购货款对账单;
export const PROCUREMENT_PAYMENT = ({}: any): TableListItem[] => {
  return [
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      hideInSearch: true,
      fixed: "left",
      editable: false,
      render: (_, record, index) => {
        return index + 1;
      }
    },
    {
      title: "店铺",
      dataIndex: "shopName",
      width: 100,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "主播",
      dataIndex: "anchorName",
      width: 100,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "直播日期",
      dataIndex: "liveDate",
      width: 100,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },

    {
      title: "商品ID",
      dataIndex: "itemId",
      width: 100,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品名称",
      dataIndex: "itemTitle",
      width: 100,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "SKU信息",
      dataIndex: "skuNameInfo",
      width: 100,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "采购单价",
      dataIndex: "supplyPrice",
      width: 100,
      render(text) {
        return millennialsNumber(text / 10000, { minimumFractionDigits: 4 });
      }
    },
    {
      title: "开票数量",
      dataIndex: "actualSettleItemVolume",
      width: 100,
      summaryTooltip: {
        divisor: 1,
        millennials: true
      },
      render(text) {
        return millennialsNumber(text);
      }
    },
    {
      title: "开票金额",
      dataIndex: "invoiceAmount",
      width: 100,
      summaryTooltip: {
        divisor: 10000,
        millennials: true,
        millennialsOptions: { minimumFractionDigits: 4 }
      },
      render(text) {
        return millennialsNumber(text / 10000, { minimumFractionDigits: 4 });
      }
    },
    {
      title: "开票公司主体",
      dataIndex: "xinContractSubject",
      width: 120,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      align: "center",
      ...item,
      title: returnEllipsisTooltip({ title })
    };
  });
};
// 代销收入对账单;
export const CONSIGNMENT_REVENUE = ({}: any): TableListItem[] => {
  return [
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      hideInSearch: true,
      fixed: "left",
      editable: false,
      render: (_, record, index) => {
        return index + 1;
      }
    },
    {
      title: "店铺",
      dataIndex: "shopName",
      width: 100,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "主播",
      dataIndex: "anchorName",
      width: 100,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "直播日期",
      dataIndex: "liveDate",
      width: 100,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },

    {
      title: "商品ID",
      dataIndex: "itemId",
      width: 100,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品名称",
      dataIndex: "itemTitle",
      width: 100,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "SKU信息",
      dataIndex: "skuNameInfo",
      width: 100,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "入账金额",
      dataIndex: "totalIncomeAmount",
      width: 100,
      summaryTooltip: {
        divisor: 10000,
        millennials: true,
        millennialsOptions: { minimumFractionDigits: 4 }
      },
      render(text) {
        return millennialsNumber(text / 10000, { minimumFractionDigits: 4 });
      }
    },
    {
      title: "退货金额",
      dataIndex: "totalRefundAmount",
      width: 100,
      summaryTooltip: {
        divisor: 10000,
        millennials: true,
        millennialsOptions: { minimumFractionDigits: 4 }
      },
      render(text) {
        return millennialsNumber(text / 10000, { minimumFractionDigits: 4 });
      }
    },

    {
      title: "技术服务费",
      dataIndex: "techServiceAmount",
      width: 100,
      summaryTooltip: {
        divisor: 10000,
        millennials: true,
        millennialsOptions: { minimumFractionDigits: 4 }
      },
      render(text) {
        return millennialsNumber(text / 10000, { minimumFractionDigits: 4 });
      }
    },
    {
      title: "技术服务费追回",
      dataIndex: "techServiceBackAmount",
      width: 120,
      summaryTooltip: {
        divisor: 10000,
        millennials: true,
        millennialsOptions: { minimumFractionDigits: 4 }
      },
      render(text) {
        return millennialsNumber(text / 10000, { minimumFractionDigits: 4 });
      }
    },
    {
      title: "达人佣金",
      dataIndex: "pidCommissionAmount",
      width: 100,
      summaryTooltip: {
        divisor: 10000,
        millennials: true,
        millennialsOptions: { minimumFractionDigits: 4 }
      },
      render(text) {
        return millennialsNumber(text / 10000, { minimumFractionDigits: 4 });
      }
    },
    {
      title: "达人佣金追回",
      dataIndex: "pidBackCommissionAmount",
      width: 120,
      summaryTooltip: {
        divisor: 10000,
        millennials: true,
        millennialsOptions: { minimumFractionDigits: 4 }
      },
      render(text) {
        return millennialsNumber(text / 10000, { minimumFractionDigits: 4 });
      }
    },
    {
      title: "代销收入金额",
      dataIndex: "offlineSettleAmount",
      width: 100,
      summaryTooltip: {
        divisor: 10000,
        millennials: true,
        millennialsOptions: { minimumFractionDigits: 4 }
      },
      render(text) {
        return millennialsNumber(text / 10000, { minimumFractionDigits: 4 });
      }
    }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      align: "center",
      ...item,
      title: returnEllipsisTooltip({ title })
    };
  });
};
export const columns = ({ type }: { type: IProps["type"] }): TableListItem[] => {
  const columnsMap: Record<string, (p: any) => TableListItem[]> = {
    COMMISSION_RECEIVE_PAY,
    SUPPLY_PRICE,
    PROCUREMENT_PAYMENT,
    CONSIGNMENT_REVENUE
  };
  return columnsMap[type]({});
};
