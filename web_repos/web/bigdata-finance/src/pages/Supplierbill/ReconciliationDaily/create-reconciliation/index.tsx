import React, { useRef, useState } from "react";
import { Modal, Button, Space } from "antd";
import { StepsForm } from "@ant-design/pro-components";
import { SIZE } from "@/utils/constant";
import apis from "@/services/supplierbill/reconciliationDaily";
import Step1 from "./components/step1";
import Step2, { only_company } from "./components/step2";
import Step3 from "./components/step3";
import { ifEmptyObj, splitSpaceComma } from "@/utils";
import { history } from "@umijs/max";
import queryString from "query-string";
import { menus } from "@/pages/Supplierbill/Reconciliationbill/reconcili-bill";
const formGrid = {
  layout: "horizontal",
  labelCol: { span: 6 }
};
export interface ICreateParams {
  dataSourceFileList?: string;
  settleDateStart?: string;
  settleDateEnd?: string;
  // 对账单单据类型（1:每日正常生成；2:每日正常红冲数据；3:每日差异生成数据)
  supplierBillType?: 1 | 2 | 3;
  supplierName?: string;
}
const Res: React.FC = () => {
  const formRef = useRef();
  const [visible, setVisible] = useState(false);
  const [curStep, setCurStep] = useState(0);
  const [step1Params, setStep1Params] = useState<ICreateParams>({});
  const [step2Params, setStep2Params] = useState<ICreateParams>({});
  // const [step3Params, setStep3Params] = useState<ICreateParams>({});
  const handlePageParams = (params: any) => {
    const { dataSourceFileList, ...rest } = params;
    if (dataSourceFileList) {
      rest.dataSourceFileList = splitSpaceComma(dataSourceFileList);
    }
    return rest;
  };
  const stepsFormRender = (dom, submitter) => {
    return (
      <Modal
        title="生成对账单"
        width={"90%"}
        centered
        onCancel={() => setVisible(false)}
        open={visible}
        destroyOnClose
        footer={() => {
          return (
            <div style={{ textAlign: "center", paddingTop: 10 }}>
              <Space>{submitter}</Space>
            </div>
          );
        }}
      >
        {dom}
      </Modal>
    );
  };

  return (
    <>
      <Button type="primary" size={SIZE} onClick={() => setVisible(true)}>
        生成对账单
      </Button>
      {visible ? (
        <StepsForm
          formRef={formRef}
          onCurrentChange={setCurStep}
          stepsProps={{ size: "small" }}
          submitter={{ submitButtonProps: { size: "small" }, resetButtonProps: { size: "small" } }}
          stepsFormRender={stepsFormRender}
          containerStyle={{ maxHeight: 600, overflow: "auto" }}
        >
          <StepsForm.StepForm
            {...formGrid}
            name="confirm"
            title="确认账期"
            onFinish={async values => {
              const { settleDate, ...rest } = values;
              const [settleDateStart, settleDateEnd] = settleDate;
              const params = {
                dataSourceFileList: only_company,
                settleDateStart,
                settleDateEnd,
                supplierBillType: 1,
                ...rest
              };
              setStep1Params(params);
              const finnalP = handlePageParams(params);
              const { status } = await apis.toFirstStepConfirm(finnalP);
              return status;
            }}
          >
            <Step1 />
          </StepsForm.StepForm>
          <StepsForm.StepForm
            {...formGrid}
            name="check"
            title="数据检查"
            onFinish={async () => {
              const params = {
                ...step1Params,
                ...step2Params
              };
              const finnalP = handlePageParams(params);
              const { status } = await apis.toSecondStepConfirm(finnalP);
              return status;
            }}
          >
            {ifEmptyObj(step1Params) ? <Step2 step1Params={step1Params} setStep2Params={setStep2Params} handlePageParams={handlePageParams} /> : null}
          </StepsForm.StepForm>
          <StepsForm.StepForm
            {...formGrid}
            name="preview"
            title="账单预览"
            onFinish={async () => {
              const params = {
                ...step1Params,
                ...step2Params
              };
              const finnalP = handlePageParams(params);
              const { status } = await apis.toFinishConfirm(finnalP);
              if (status) {
                const { settleDateStart, settleDateEnd, supplierName } = finnalP;
                history.push({
                  pathname: "/supplierbill/reconciliationbill",
                  search: queryString.stringify({
                    settleDateStart,
                    settleDateEnd,
                    supplierName,
                    menu: menus.menu_draft
                  })
                });
                setVisible(false);
              }
              return status;
            }}
          >
            {ifEmptyObj(step1Params) && curStep === 2 ? (
              <Step3 step1Params={step1Params} step2Params={step2Params} handlePageParams={handlePageParams} />
            ) : null}
          </StepsForm.StepForm>
        </StepsForm>
      ) : null}
    </>
  );
};

export default React.memo(Res);
