import React from "react";
import { Table } from "antd";
import TableList, { reuturnSummaryValue } from "@/components/TableList";
import styles from "./index.less";
import { columns } from "./variable";
import { NOVALUE } from "@/utils/constant";
export interface IProps {
  type: "COMMISSION_RECEIVE_PAY" | "CONSIGNMENT_REVENUE" | "ONLINE_COMMISSION_INVOICE" | "PROCUREMENT_PAYMENT" | "SUPPLY_PRICE" | "LINK_FEE";
  data: Record<string, any>[];
}
interface ITableInfo {
  type: IProps["type"];
  supplierName: string;
  accountPeriod: number;
  gmtReconcilePeriodStart: string;
  gmtReconcilePeriodEnd: string;
}
const tableInfo = ({ type, supplierName, accountPeriod, gmtReconcilePeriodStart, gmtReconcilePeriodEnd }: ITableInfo) => {
  const tableMap: Record<IProps["type"], { title: string; period: string }> = {
    COMMISSION_RECEIVE_PAY: {
      title: `第(${accountPeriod ?? NOVALUE})期${supplierName ?? NOVALUE}-广州辛选对账单`,
      period: `账单期间：${gmtReconcilePeriodStart} ~ ${gmtReconcilePeriodEnd}`
    },
    SUPPLY_PRICE: {
      title: `第(${accountPeriod ?? NOVALUE})期${supplierName ?? NOVALUE}-广州辛选对账单`,
      period: `账单期间：${gmtReconcilePeriodStart} ~ ${gmtReconcilePeriodEnd}`
    },
    PROCUREMENT_PAYMENT: {
      title: `第(${accountPeriod ?? NOVALUE})期${supplierName ?? NOVALUE}-广州辛选对账单-采购货款账单`,
      period: `账单期间：${gmtReconcilePeriodStart} ~ ${gmtReconcilePeriodEnd}`
    },
    CONSIGNMENT_REVENUE: {
      title: `第(${accountPeriod ?? NOVALUE})期${supplierName ?? NOVALUE}-广州辛选对账单-代销收入账单`,
      period: `账单期间：${gmtReconcilePeriodStart} ~ ${gmtReconcilePeriodEnd}`
    },
    ONLINE_COMMISSION_INVOICE: {
      title: "",
      period: ""
    },
    LINK_FEE: {
      title: "",
      period: ""
    }
  };
  return tableMap[type] ?? {};
};
const Res = (props: IProps) => {
  const { type, data } = props;
  const { detailList, supplierName, accountPeriod, gmtReconcilePeriodStart, gmtReconcilePeriodEnd } = data;
  const columnsProps = {
    type
  };
  const curTable = tableInfo({ type, supplierName, accountPeriod, gmtReconcilePeriodStart, gmtReconcilePeriodEnd });

  const summaryComponent = () => {
    return (
      <Table.Summary fixed="bottom">
        <Table.Summary.Row style={{ textAlign: "center" }}>
          {columns(columnsProps)
            .filter(item => !item.hideInTable)
            .map((item: any, index: number) => {
              const { dataIndex } = item;
              return (
                <Table.Summary.Cell key={dataIndex} align="center" index={index}>
                  {reuturnSummaryValue(item, index, data, {})}
                </Table.Summary.Cell>
              );
            })}
        </Table.Summary.Row>
      </Table.Summary>
    );
  };
  return (
    <div className={styles["step3"]}>
      <div className={styles["step3-header"]}>
        <h2 className={styles["step3-title"]}>{curTable.title}</h2>
        <div className={styles["step3-subtitle"]}>{curTable.period}</div>
      </div>
      <TableList
        columns={columns(columnsProps)}
        dataSource={detailList}
        options={false}
        pagination={false}
        search={false}
        scroll={{ x: 400, y: "330px" }}
        rowKey="id"
        summary={summaryComponent}
      />
    </div>
  );
};
export default React.memo(Res);
