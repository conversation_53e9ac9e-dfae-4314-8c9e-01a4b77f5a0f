import React, { useRef, useContext, useState } from "react";
import { Space, Spin } from "antd";
import TableList from "@/components/TableList";
import { columns, advance_columns_search } from "./variable";
import apis from "@/services/supplierbill/reconciliationDaily";
import dayjs from "dayjs";
import CreateReconciliation from "../create-reconciliation";
import { FieldsContext } from "../index";
import styles from "../index.less";
import { NOVALUE } from "@/utils/constant";
export const PageUrl = "/supplierbill/reconciliationDaily";
const Res = () => {
  const {
    handlePageParmas,
    billSettlePatternOptions,
    billSettlePatternObj,
    itemTypeOptions,
    itemTypeObj,
    shopTypeOptions,
    shopTypeObj,
    billTypeOptions,
    billTypeObj
  } = useContext(FieldsContext);
  const actionsRef = useRef<any>();
  const [freshTime, setFreshTime] = useState(NOVALUE);
  const [freshTimeLoading, setFreshTimeLoading] = useState(false);
  const columnsProps = { billSettlePatternOptions, billSettlePatternObj, itemTypeOptions, itemTypeObj, shopTypeObj, billTypeObj, billTypeOptions };

  const advanceColumnsProps = { itemTypeOptions, shopTypeOptions };
  const actions = () => {
    return [<CreateReconciliation key="create-reconciliation" />];
  };

  const returTitle = () => {
    return (
      <Space>
        <span>未对账明细</span>
        <Spin spinning={freshTimeLoading}>
          <span className={styles["subtitle"]}>数据每小时更新一次，当前数据更新时间:{freshTime}</span>
        </Spin>
      </Space>
    );
  };
  const fetchTime = async () => {
    setFreshTimeLoading(true);
    const { entry, status } = await apis.getMaxProcTime();
    setFreshTimeLoading(false);
    if (status) {
      setFreshTime(entry);
    } else {
      setFreshTime(NOVALUE);
    }
  };
  const handleParmas = (p, sort) => {
    fetchTime();
    return handlePageParmas(p, sort);
  };
  return (
    <>
      <TableList
        headerTitle={returTitle()}
        form={{
          initialValues: {
            settleDate: [dayjs().subtract(2, "month"), dayjs()]
          }
        }}
        actionRef={actionsRef}
        advanceColumns={advance_columns_search(advanceColumnsProps)}
        columns={columns(columnsProps)}
        api={apis.getStatementDailyDetailPage}
        downloadApi={apis.downloadStatementDailyDetail}
        scroll={{ y: "calc(100vh - 400px)" }}
        preFetch={handleParmas}
        rowKey="id"
        toolbar={{
          actions: actions()
        }}
      />
    </>
  );
};

export default React.memo(Res);
