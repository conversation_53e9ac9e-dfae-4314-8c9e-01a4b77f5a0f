import { filterOptionLabel, returnEllipsisTooltip } from "@/utils";
import { Select } from "antd";
import AnchorSelect from "@/components/AnchorSelect";
import DepartmentSelect from "@/components/DepartmentSelect";
import { SIZE } from "@/utils/constant";
export const advance_columns_search = ({ itemTypeOptions, shopTypeOptions }: any): TableListItem[] => {
  return [
    {
      title: "基础信息",
      valueType: "group",
      colProps: { span: 24 },
      columns: [
        {
          title: "商品类型",
          dataIndex: "itemType",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem: () => {
            return <Select placeholder="请选择" size={SIZE} options={itemTypeOptions} filterOption={filterOptionLabel} showSearch allowClear />;
          }
        },
        {
          title: "SKUID",
          dataIndex: "itemSkuIdList",
          hideInSearch: false,
          hideInTable: true
        },
        {
          title: "SKU名称",
          dataIndex: "itemSkuName",
          hideInSearch: false,
          hideInTable: true
        },
        {
          title: "财务对账人",
          dataIndex: "financeReconcile",
          hideInSearch: false,
          hideInTable: true
        },
        {
          title: "最新财务对账人",
          dataIndex: "latestFinanceReconcile",
          hideInSearch: false,
          hideInTable: true
        },
        {
          title: "一级部门",
          dataIndex: "bigDepartmentList",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem: () => {
            return <DepartmentSelect key="bigDepartmentList" style={{ width: "100%" }} />;
          }
        },
        {
          title: "店铺名称",
          dataIndex: "shopName",
          hideInSearch: false,
          hideInTable: true
        },
        {
          title: "店铺性质",
          dataIndex: "shopTypeList",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem: () => {
            return <Select placeholder="请选择" size={SIZE} options={shopTypeOptions} filterOption={filterOptionLabel} showSearch allowClear />;
          }
        },
        {
          title: "品牌",
          dataIndex: "brandName",
          hideInSearch: false,
          hideInTable: true
        },
        {
          title: "供应商名称",
          dataIndex: "supplierName",
          hideInSearch: false,
          hideInTable: true
        },
        {
          title: "我司合同主体",
          dataIndex: "contractName",
          hideInTable: true
        }
      ]
    }
  ];
};
const columns_search = ({ billSettlePatternOptions, billTypeOptions }: any) => {
  return [
    {
      // 日区间
      title: "结算日期",
      dataIndex: "settleDate",
      hideInSearch: false,
      hideInTable: true,
      valueType: "dateRange"
    },
    {
      // 日区间
      title: "直播日期",
      dataIndex: "liveDate",
      hideInSearch: false,
      hideInTable: true,
      valueType: "dateRange"
    },

    {
      // 输入+下拉
      title: "主播名称",
      dataIndex: "anchorNameList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem() {
        return <AnchorSelect showSearch allowClear maxTagCount="responsive" mode="multiple" />;
      }
    },

    {
      title: "商品ID",
      dataIndex: "itemIdList",
      hideInSearch: false,
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      // 输入
      title: "商品名称",
      dataIndex: "itemTitle",
      hideInSearch: false,
      hideInTable: true
    },
    {
      title: "账单结算模式",
      dataIndex: "billSettlePatternList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return <Select placeholder="请选择" size={SIZE} options={billSettlePatternOptions} filterOption={filterOptionLabel} showSearch allowClear />;
      }
    },
    {
      // TODO
      title: "账单佣金模式",
      dataIndex: "billTypeList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return <Select placeholder="请选择" size={SIZE} options={billTypeOptions} filterOption={filterOptionLabel} showSearch allowClear />;
      }
    }
  ];
};
export const columns = ({ billSettlePatternOptions, itemTypeOptions, shopTypeObj, billTypeOptions }: any): Array<TableListItem> => {
  return [
    ...columns_search({ billSettlePatternOptions, itemTypeOptions, billTypeOptions }),
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      hideInSearch: true,
      fixed: "left",
      editable: false,
      render: (_, record, index) => {
        return index + 1;
      }
    },
    { title: "结算日期", dataIndex: "settleDate", width: 100, valueType: "date", hideInSearch: true },
    { title: "直播日期", dataIndex: "liveDate", width: 100, valueType: "date", hideInSearch: true },
    {
      title: "主播ID",
      dataIndex: "anchorId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "主播名称",
      dataIndex: "anchorName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品ID",
      dataIndex: "itemId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品名称",
      editable: false,
      dataIndex: "itemTitle",
      width: 300,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品类型",
      dataIndex: "itemTypeStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "SKUID",
      dataIndex: "itemSkuId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "SKU名称",
      dataIndex: "itemSkuName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "账单结算模式",
      dataIndex: "billSettlePattern",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { billSettlePatternStr } = record;
        return returnEllipsisTooltip({ title: billSettlePatternStr });
      }
    },
    {
      title: "账单佣金模式",
      dataIndex: "billType",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { billTypeStr } = record;
        return returnEllipsisTooltip({ title: billTypeStr });
      }
    },
    { title: "财务对账人", dataIndex: "financeReconcile", width: 100, hideInSearch: true },
    { title: "最新财务对账人", dataIndex: "latestFinanceReconcile", width: 100, hideInSearch: true },
    {
      title: "一级部门",
      dataIndex: "bigDepartment",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "业务对接人",
      dataIndex: "purchasePrincipal",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "店铺ID", dataIndex: "shopId", width: 100, hideInSearch: true },
    {
      title: "店铺名称",
      dataIndex: "shopName",
      width: 140,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "店铺性质",
      dataIndex: "shopType",
      width: 100,
      hideInSearch: true,
      render(_) {
        return returnEllipsisTooltip({ title: shopTypeObj[_] });
      }
    },
    {
      title: "品牌",
      dataIndex: "brandName",
      width: 100,
      hideInSearch: true
    },

    {
      title: "供应商名称",
      dataIndex: "supplierName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "我司合同主体",
      dataIndex: "contractName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "账单收入金额", dataIndex: "totalIncomeAmountStr", width: 100, hideInSearch: true },
    { title: "账单退款金额", dataIndex: "totalOrderRefundAmountStr", width: 100, hideInSearch: true },
    { title: "超售后退款金额", dataIndex: "totalOverRefundAmountStr", width: 100, hideInSearch: true },
    { title: "总退款金额", dataIndex: "totalRefundAmountStr", width: 100, hideInSearch: true },
    { title: "销售收入", dataIndex: "salesRevenueAmountStr", width: 100, hideInSearch: true },
    { title: "总佣金比例", dataIndex: "commissionRateStr", width: 100, hideInSearch: true },
    { title: "总佣金金额", dataIndex: "totalCommissionAmountStr", width: 100, hideInSearch: true },
    { title: "达人佣金追回金额", dataIndex: "pidBackCommissionAmountStr", width: 100, hideInSearch: true },
    { title: "线上佣金金额", dataIndex: "onlineCommissionAmountStr", width: 100, hideInSearch: true },
    { title: "结算件数", dataIndex: "salesVolumeStr", width: 100, hideInSearch: true },
    { title: "退款件数", dataIndex: "refundItemVolumeStr", width: 100, hideInSearch: true },
    { title: "实际结算件数", dataIndex: "actualSettleItemVolumeStr", width: 100, hideInSearch: true },
    { title: "快手技术服务费", dataIndex: "techServiceAmountStr", width: 100, hideInSearch: true },
    { title: "快手技术服务费追回", dataIndex: "techServiceBackAmountStr", width: 100, hideInSearch: true },
    { title: "实际技术服务费", dataIndex: "actualTechServiceAmountStr", width: 100, hideInSearch: true },
    { title: "供货价", dataIndex: "supplyPriceStr", width: 100, hideInSearch: true },
    { title: "供货总成本", dataIndex: "totalSupplyCostStr", width: 100, hideInSearch: true },
    { title: "线下结算金额", dataIndex: "offlineSettleAmountStr", width: 100, hideInSearch: true },
    {
      title: "数据来源",
      dataIndex: "dataSourceFile",
      width: 120,
      hideInSearch: true,
      render(_, record) {
        const { dataSourceFileStr } = record;
        return returnEllipsisTooltip({ title: dataSourceFileStr });
      }
    }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      ...item,
      align: "center",
      title: returnEllipsisTooltip({ title })
    };
  });
};
