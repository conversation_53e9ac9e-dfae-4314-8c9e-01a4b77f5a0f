import React, { useRef, useContext } from "react";
import TableList from "@/components/TableList";
import { columns, advance_columns_search } from "./variable";
import apis from "@/services/supplierbill/reconciliationDaily";
import dayjs from "dayjs";
import { FieldsContext } from "../index";
const Res = () => {
  const {
    handlePageParmas,
    billSettlePatternOptions,
    billSettlePatternObj,
    itemTypeOptions,
    itemTypeObj,
    shopTypeOptions,
    shopTypeObj,
    billTypeOptions,
    billTypeObj
  } = useContext(FieldsContext);
  const actionsRef = useRef<any>();
  const columnsProps = { billSettlePatternOptions, billSettlePatternObj, itemTypeOptions, itemTypeObj, shopTypeObj, billTypeObj, billTypeOptions };

  const advanceColumnsProps = { itemTypeOptions, shopTypeOptions };

  return (
    <>
      <TableList
        form={{
          initialValues: {
            settleDate: [dayjs().subtract(2, "month"), dayjs()]
          }
        }}
        actionRef={actionsRef}
        advanceColumns={advance_columns_search(advanceColumnsProps)}
        columns={columns(columnsProps)}
        api={apis.getStatementDailyDetailHaveReconcilePage}
        downloadApi={apis.downloadStatementDailyDetailHaveReconcile}
        scroll={{ y: "calc(100vh - 380px)" }}
        preFetch={handlePageParmas}
        rowKey="id"
      />
    </>
  );
};

export default React.memo(Res);
