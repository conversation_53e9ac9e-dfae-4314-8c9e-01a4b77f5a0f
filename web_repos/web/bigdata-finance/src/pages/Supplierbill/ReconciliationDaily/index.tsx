import React, { useState } from "react";
import { Tabs } from "antd";
import KeepAlive from "react-activation";
import HasConfirmed from "./has-confirmed";
import UnConfirmed from "./un-confirmed";
import { SIZE, tabs_tabBarStyle } from "@/utils/constant";
import dayjs from "dayjs";
import { splitSpaceComma } from "@/utils";

import useCommonOptions from "@/hooks/useCommonOptions";
import styles from "./index.less";
export const FieldsContext = React.createContext<any>(null);
export const PageUrl = "/supplierbill/reconciliationDaily";

export const tab_un_confirmed = "a";
export const tab_has_onfirmed = "b";
const Res = () => {
  const [curTab, setCurTab] = useState(tab_un_confirmed);
  const [billSettlePatternOptions, billSettlePatternObj] = useCommonOptions({ dimName: "账单结算模式" });
  const [itemTypeOptions, itemTypeObj] = useCommonOptions({ dimName: "商品类型" });
  const [shopTypeOptions, shopTypeObj] = useCommonOptions({ dimName: "店铺性质" });
  const [billTypeOptions, billTypeObj] = useCommonOptions({ dimName: "账单佣金模式" });
  // 处理请求参数
  const handlePageParmas = (params: any) => {
    const {
      current,
      settleDate,
      liveDate,
      itemIdList,
      billSettlePatternList,
      shopTypeList,
      billTypeList,
      itemSkuIdList,
      bigDepartmentList,
      itemType,
      ...rest
    } = params;
    if (liveDate) {
      const [liveDateStart, liveDateEnd] = liveDate;
      rest.liveDateStart = dayjs(liveDateStart).format("YYYYMMDD");
      rest.liveDateEnd = dayjs(liveDateEnd).format("YYYYMMDD");
    }
    if (settleDate) {
      const [settleDateStart, settleDateEnd] = settleDate;
      rest.settleDateStart = dayjs(settleDateStart).format("YYYYMMDD");
      rest.settleDateEnd = dayjs(settleDateEnd).format("YYYYMMDD");
    }
    rest.billSettlePatternList = splitSpaceComma(billSettlePatternList);
    rest.bigDepartmentList = splitSpaceComma(bigDepartmentList);
    rest.itemType = splitSpaceComma(itemType);
    rest.itemSkuIdList = splitSpaceComma(itemSkuIdList);
    rest.shopTypeList = splitSpaceComma(shopTypeList);
    rest.billTypeList = splitSpaceComma(billTypeList);
    rest.itemIdList = splitSpaceComma(itemIdList);
    return { ...rest, pageNo: current };
  };
  const tabItems = [
    {
      key: tab_un_confirmed,
      label: "未对账明细",
      children: <UnConfirmed />
    },
    {
      key: tab_has_onfirmed,
      label: "已对账明细",
      children: <HasConfirmed />
    }
  ];
  return (
    <FieldsContext.Provider
      value={{
        handlePageParmas,
        billSettlePatternOptions,
        billSettlePatternObj,
        itemTypeOptions,
        itemTypeObj,
        shopTypeOptions,
        shopTypeObj,
        billTypeOptions,
        billTypeObj
      }}
    >
      <Tabs
        className={styles["reconciliation-daily"]}
        activeKey={curTab}
        tabBarStyle={tabs_tabBarStyle}
        size={SIZE}
        onChange={setCurTab}
        items={tabItems}
      />
    </FieldsContext.Provider>
  );
};
const AliveRecord = props => (
  <KeepAlive name={PageUrl}>
    <Res {...props} />
  </KeepAlive>
);

export default React.memo(AliveRecord);
