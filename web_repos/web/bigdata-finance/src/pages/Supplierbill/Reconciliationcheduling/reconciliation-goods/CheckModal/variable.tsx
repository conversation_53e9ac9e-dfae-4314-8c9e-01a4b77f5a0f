import { returnEllipsisTooltip } from "@/utils/index";

export const columns = ({}: any): Array<TableListItem> => {
  return [
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      hideInSearch: true,
      fixed: "left",
      render: (_, record, index) => {
        return index + 1;
      }
    },
    { title: "直播日期", dataIndex: "liveDate", fixed: "left", width: 100, hideInSearch: true },

    {
      title: "主播",
      dataIndex: "anchorName",
      width: 100,
      fixed: "left",
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "主品/赠品",
      dataIndex: "itemType",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    }
  ].map(item => {
    const { title } = item;
    return {
      ...item,
      align: "center",
      title: returnEllipsisTooltip({ title })
    };
  });
};
