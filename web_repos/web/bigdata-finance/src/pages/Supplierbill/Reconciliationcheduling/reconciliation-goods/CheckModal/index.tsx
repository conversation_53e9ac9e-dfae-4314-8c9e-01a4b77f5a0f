import React from "react";
import { <PERSON><PERSON>, <PERSON>, But<PERSON>, Alert } from "antd";
import TableList from "@/components/TableList";
import styles from "@/components/UploadModal/index.less";
import { SIZE } from "@/utils/constant";
import { UploadModalProps } from "@/components/UploadModal";
import { columns } from "./variable";
interface IProps extends UploadModalProps {
  dataSource: any[];
}
const UploadModal = (props: IProps) => {
  const { visible, afterOk, close, title, tempUrl, dataSource } = props;
  const handleExcelCancel = () => {
    Modal.destroyAll();
    close?.();
  };

  const handleExcelOk = async () => {
    afterOk?.();
  };

  // 下载导入商品Excel模板
  const handleDownloadExample = (e: React.BaseSyntheticEvent) => {
    e.stopPropagation();
    window.open(tempUrl);
  };
  return (
    <Modal
      centered={true}
      className={styles.uploadModal}
      title={title}
      open={visible}
      onOk={() => handleExcelOk()}
      onCancel={() => handleExcelCancel()}
      maskClosable={false}
      okButtonProps={{
        size: SIZE
      }}
      cancelButtonProps={{
        size: SIZE
      }}
    >
      <Form>
        <Form.Item>
          <Alert message="以下记录已存在对账排班，请检查数据后再次上传，点击确认则用当前记录覆盖系统数据" type="info" showIcon></Alert>
        </Form.Item>
        <Form.Item>
          <TableList search={false} columns={columns({})} dataSource={dataSource} pagination={false} scroll={{ x: "max-content", y: "200px" }} />
        </Form.Item>
        <Form.Item>
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              flexDirection: "column",
              alignItems: "center"
            }}
          >
            <Button type="link" onClick={handleDownloadExample}>
              [下载模板]
            </Button>
          </div>
        </Form.Item>
      </Form>
    </Modal>
  );
};
export default React.memo(UploadModal);
