import React, { useRef, useState, useMemo, useContext, useEffect } from "react";
import { Button } from "antd";
import { columns, advance_columns_search } from "./variable";
import { NOVALUE, Protable_FROM_CONFIG } from "@/utils/constant";
import { ifEmptyObj, splitSpaceComma } from "@/utils";
import UploadModal from "./UploadModal";
import apis from "@/services/supplierbill/reconciliationcheduling";
import dayjs from "dayjs";
import TableList from "@/components/TableList";
import Edit from "../components/edit";
import { FieldsContext } from "../index";
const Res: React.FC = () => {
  const {
    urlParams,
    itemTypeOptions,
    itemTypeObj,
    statementFollowTypeOptions,
    statementFollowTypeObj,
    unStatementTypeOptions,
    unStatementTypeObj,
    isStatementGroupOptions,
    isStatementGroupObj,
    isLaterBillOptions,
    isLaterBillObj,
    billSettlePatternOptions,
    billSettlePatternObj,
    modelIdOptions,
    modelIdObj,
    undertakerRemarkOptions,
    shopTypeObj
  } = useContext(FieldsContext);
  const [ohterParams, setOtherParams] = useState({});
  const formRef = useRef<any>();
  const actionRef = useRef<any>();
  const [showExcelUpload, setshowExcelUpload] = useState(false);
  const [editProps, setEditProps] = useState<any>({
    open: false,
    onFresh: () => {
      actionRef.current?.reload();
    },
    onClose: () => {
      setEditProps({ ...editProps, open: false });
    }
  });

  const handleDelete = async record => {
    await apis.delStatementSchedule({ idDOList: [{ id: record.id }] });
    actionRef.current?.reload();
  };

  const handleEdit = record => {
    setEditProps({
      ...editProps,
      open: true,
      record
    });
  };
  const advanceColumnsProps = { itemTypeOptions, undertakerRemarkOptions, unStatementTypeOptions };
  const columnsProps = {
    handleDelete,
    handleEdit,
    itemTypeObj,
    statementFollowTypeObj,
    unStatementTypeOptions,
    unStatementTypeObj,
    isStatementGroupOptions,
    isStatementGroupObj,
    isLaterBillOptions,
    isLaterBillObj,
    billSettlePatternOptions,
    billSettlePatternObj,
    modelIdOptions,
    modelIdObj,
    statementFollowTypeOptions,
    shopTypeObj,
    undertakerRemarkOptions
  };
  // 上传excel modal属性
  const UploadExcelModalProps = useMemo(() => {
    return {
      title: "上传对账排班",
      visible: showExcelUpload,
      targetType: 0,
      api: apis.addStatementScheduleFromExcel,
      message: (
        <>
          <span>1. 模板中黄色背景字段为必填项</span>
          <br />
          <span>2. 所导入商品必须是直播利润预估表中存在商品</span>
          <br />
          <span>3. 根据【直播日期】+【主播】+【商品ID】+【主品/赠品】对排班表中数据进行插入/更新，仅更新可编辑字段</span>
        </>
      ),
      tempUrl: "https://s.xinc818.com/files/webcim2fwv2ox9ot3hm/供应商对账-对账排班上传模板.xlsx",
      fresh: () => {},
      close: () => {
        setshowExcelUpload(false);
      }
    };
  }, [showExcelUpload]);

  // 处理请求参数
  const handlePageParmas = (params: any) => {
    const { current, liveDate, itemIdList, orderContractIdList, ...rest } = params;
    if (liveDate?.length) {
      const [liveDateStart, liveDateEnd] = liveDate;
      rest.liveDateStart = dayjs(liveDateStart).format("YYYY-MM-DD");
      rest.liveDateEnd = dayjs(liveDateEnd).format("YYYY-MM-DD");
    }

    rest.itemIdList = splitSpaceComma(itemIdList);
    rest.orderContractIdList = splitSpaceComma(orderContractIdList);
    return { ...rest, pageNo: current };
  };
  useEffect(() => {
    if (ifEmptyObj(urlParams)) {
      formRef.current.resetFields();
      const { liveDate, ...rest } = urlParams;
      setOtherParams(rest);
      formRef?.current?.setFieldsValue({ liveDate });
      // actionRef.current.reload();
    }
  }, [urlParams]);
  return (
    <div>
      <TableList
        manualRequest={ifEmptyObj(urlParams)}
        headerTitle="对账商品"
        formRef={formRef}
        actionRef={actionRef}
        columnEmptyText={NOVALUE}
        advanceColumns={advance_columns_search(advanceColumnsProps)}
        columns={columns(columnsProps)}
        scroll={{ x: "max-content", y: "calc(100vh - 390px)" }}
        form={{
          ...Protable_FROM_CONFIG,
          initialValues: {
            liveDate: [dayjs().subtract(3, "month"), dayjs()]
          }
        }}
        api={apis.getFinanceSupplierStatementSchedulePage}
        downloadApi={apis.downloadFinanceSupplierStatementSchedule}
        params={{
          ...ohterParams
        }}
        preFetch={handlePageParmas}
        toolbar={{
          actions: [
            <>
              <Button type="primary" onClick={() => setshowExcelUpload(true)} size="small">
                上传
              </Button>
            </>
          ]
        }}
        rowKey={"id"}
      />
      {showExcelUpload && <UploadModal {...UploadExcelModalProps} />}
      {editProps.open ? <Edit {...editProps} /> : null}
    </div>
  );
};

export default React.memo(Res);
