import { Popconfirm, Select, Space, Button } from "antd";
import { filterOptionLabel, returnEllipsisTooltip } from "@/utils/index";
import { SIZE } from "@/utils/constant";
import AnchorSelect from "@/components/AnchorSelect";
import DepartmentSelect from "@/components/DepartmentSelect";
import BrandDebounSelect from "@/components/BrandDebounSelect";
import AddSKU from "../components/add-sku";

export const advance_columns_search = ({ itemTypeOptions, unStatementTypeOptions }: any): TableListItem[] => {
  return [
    {
      title: "基本信息",
      valueType: "group",
      colProps: { span: 24 },
      columns: [
        {
          title: "商品类型",
          dataIndex: "itemType",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem: () => {
            return <Select placeholder="请选择" size={SIZE} options={itemTypeOptions} filterOption={filterOptionLabel} showSearch allowClear />;
          }
        },
        {
          title: "部门",
          dataIndex: "bigDepartmentList",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem: () => {
            return <DepartmentSelect key="bigDepartmentList" maxTagCount="responsive" mode="multiple" />;
          }
        },
        {
          title: "业务对接人",
          dataIndex: "purchasePrincipal",
          hideInSearch: false,
          hideInTable: true
        },
        {
          title: "店铺",
          dataIndex: "shopName",
          hideInTable: true
        },
        {
          title: "品牌",
          dataIndex: "brandName",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem() {
            return <BrandDebounSelect key="brandName" showSearch allowClear />;
          }
        },
        {
          title: "合同号",
          dataIndex: "orderContractIdList",
          hideInSearch: false,
          hideInTable: true,
          fieldProps: {
            placeholder: "多个使用英文逗号/空格"
          }
        }
      ]
    },
    {
      title: "对账信息",
      valueType: "group",
      colProps: { span: 24 },
      columns: [
        {
          title: "财务对账人",
          dataIndex: "financeReconcile",
          hideInSearch: false,
          hideInTable: true
        },
        {
          title: "最新财务对账人",
          dataIndex: "latestFinanceReconcile",
          hideInSearch: false,
          hideInTable: true
        },
        {
          title: "未对账类型",
          dataIndex: "unStatementTypeList",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem: () => {
            return (
              <Select
                placeholder="请选择"
                size={SIZE}
                options={unStatementTypeOptions}
                filterOption={filterOptionLabel}
                showSearch
                allowClear
                maxTagCount="responsive"
                mode="multiple"
              />
            );
          }
        }
      ]
    }
  ];
};
const columns_search = ({ statementFollowTypeOptions, modelIdOptions }: any): Array<TableListItem> => {
  return [
    {
      title: "直播日期",
      dataIndex: "liveDate",
      valueType: "dateRange",
      hideInTable: true
    },
    {
      title: "主播",
      dataIndex: "anchorNameList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem() {
        return <AnchorSelect showSearch allowClear maxTagCount="responsive" mode="multiple" />;
      }
    },
    {
      title: "商品ID",
      dataIndex: "itemIdList",
      hideInSearch: false,
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "商品名称",
      dataIndex: "itemTitle",
      hideInSearch: false,
      hideInTable: true
    },
    {
      title: "模式",
      dataIndex: "modelId",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return <Select placeholder="请选择" size={SIZE} options={modelIdOptions} filterOption={filterOptionLabel} showSearch allowClear />;
      }
    },

    {
      title: "供应商名称",
      dataIndex: "supplierName",
      hideInTable: true
    },
    {
      title: "对账跟进类型",
      dataIndex: "statementFollowTypeList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return (
          <Select
            placeholder="请选择"
            size={SIZE}
            options={statementFollowTypeOptions}
            filterOption={filterOptionLabel}
            showSearch
            allowClear
            maxTagCount="responsive"
            mode="multiple"
          />
        );
      }
    }
  ];
};

export const columns = ({
  handleEdit,
  handleDelete,
  itemTypeOptions,
  statementFollowTypeOptions,
  statementFollowTypeObj,
  unStatementTypeObj,
  isStatementGroupObj,
  isLaterBillObj,
  modelIdOptions,
  modelIdObj,
  billSettlePatternOptions,
  undertakerRemarkOptions
}: any): Array<TableListItem> => {
  return [
    ...columns_search({ itemTypeOptions, statementFollowTypeOptions, modelIdOptions }),
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      hideInSearch: true,
      fixed: "left",
      render: (_, record, index) => {
        return index + 1;
      }
    },
    { title: "直播日期", dataIndex: "liveDate", fixed: "left", width: 100, hideInSearch: true },
    {
      title: "主播ID",
      dataIndex: "anchorId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "主播",
      dataIndex: "anchorName",
      width: 100,
      fixed: "left",
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "商品ID", dataIndex: "itemId", width: 100, hideInSearch: true },
    {
      title: "商品名称",
      dataIndex: "itemTitle",
      width: 140,
      fixed: "left",
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "模式",
      dataIndex: "modelId",
      width: 100,
      fixed: "left",
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: modelIdObj[text] });
      }
    },
    {
      title: "商品类型",
      dataIndex: "itemType",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { itemTypeStr } = record;
        return returnEllipsisTooltip({ title: itemTypeStr });
      }
    },
    {
      title: "账单结算模式",
      dataIndex: "billSettlePatternStr",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "财务对账人",
      dataIndex: "financeReconcile",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "最新财务对账人",
      dataIndex: "latestFinanceReconcile",
      width: 120,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "部门",
      dataIndex: "bigDepartment",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "业务对接人",
      dataIndex: "purchasePrincipal",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "店铺",
      dataIndex: "shopName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "店铺ID", dataIndex: "shopId", width: 100, hideInSearch: true },
    {
      title: "店铺性质",
      dataIndex: "shopTypeStr",
      width: 100,
      hideInSearch: true
    },
    {
      title: "品牌",
      dataIndex: "brandName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "供应商名称",
      dataIndex: "supplierName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "我司合同主体",
      dataIndex: "contractName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "合同号", dataIndex: "orderContractId", width: 100, hideInSearch: true },
    { title: "24h净支付件数", dataIndex: "netPayVolume24hStr", width: 100, hideInSearch: true },
    { title: "24净支付金额", dataIndex: "netPayAmount24hStr", width: 100, hideInSearch: true },
    { title: "线上佣金比例", dataIndex: "onlineCommissionRateStr", width: 100, hideInSearch: true },
    { title: "线下佣金比例", dataIndex: "offlineCommissionRateStr", width: 100, hideInSearch: true },
    { title: "主品成本单价", dataIndex: "mainProductCostPriceStr", width: 100, hideInSearch: true },
    { title: "赠品成本单价", dataIndex: "giveawayProductCostPriceStr", width: 100, hideInSearch: true },
    { title: "技术服务费承担方", dataIndex: "undertakerRemarkStr", width: 100, hideInSearch: true },
    { title: "首次对账时间", dataIndex: "gmtFirstStatementWork", width: 100, hideInSearch: true },
    { title: "已对账期间", dataIndex: "reconciledPeriod", width: 100, hideInSearch: true },
    {
      title: "未对账类型",
      dataIndex: "unStatementType",
      width: 120,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: unStatementTypeObj[text] });
      }
    },
    {
      title: "对账跟进类型",
      dataIndex: "statementFollowType",
      width: 120,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: statementFollowTypeObj[text] });
      }
    },
    {
      title: "对账跟进情况",
      dataIndex: "statementFollowCondition",
      width: 120,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "是否有对账群",
      dataIndex: "isStatementGroup",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: isStatementGroupObj[text] });
      }
    },
    {
      title: "是否有后期账单",
      dataIndex: "isLaterBill",
      width: 120,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: isLaterBillObj[text] });
      }
    },
    { title: "分配上传时间", dataIndex: "gmtAllotUpload", width: 100, hideInSearch: true },
    { title: "更新时间", dataIndex: "gmtModified", width: 100, hideInSearch: true },
    {
      title: "操作",
      width: 100,
      valueType: "option",
      fixed: "right",
      render: (text: string, record: any) => {
        const { confirmationTime, anchorId, anchorName, itemId, itemType, liveDate } = record;
        return (
          <Space direction="vertical">
            <Space>
              <Button type="link" size={SIZE} disabled={confirmationTime} onClick={() => handleEdit(record)}>
                编辑
              </Button>
              <Popconfirm
                title="是否确认删除?"
                onConfirm={() => handleDelete(record)}
                onCancel={() => {}}
                okButtonProps={{ size: SIZE }}
                cancelButtonProps={{ size: SIZE }}
              >
                <Button type="link" disabled={confirmationTime} size={SIZE}>
                  删除
                </Button>
              </Popconfirm>
            </Space>
            <AddSKU
              params={{ type: "add", anchorId, anchorName, itemId, itemType, liveDate, undertakerRemarkOptions }}
              billSettlePatternOptions={billSettlePatternOptions}
              onClose={() => {}}
              title={"新增SKU"}
              trigger={
                <Button type="link" size={SIZE}>
                  新增对账SKU
                </Button>
              }
            ></AddSKU>
          </Space>
        );
      }
    }
  ].map(item => {
    const { title } = item;
    return {
      ...item,
      align: "center",
      title: returnEllipsisTooltip({ title })
    };
  });
};
