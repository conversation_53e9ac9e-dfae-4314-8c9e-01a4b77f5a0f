import React, { useState, useEffect } from "react";
import { Mo<PERSON>, Form, Button, Alert } from "antd";
import RSSUpload from "@/components/RSS/RSSUpload";
import styles from "@/components/UploadModal/index.less";
import { SIZE } from "@/utils/constant";
import apis from "@/services/supplierbill/reconciliationcheduling";
import { pushImportHistory } from "@/utils";
import { UploadModalProps } from "@/components/UploadModal";
import CheckModal from "./CheckModal";
const UploadModal = (props: UploadModalProps) => {
  const { visible, close, fresh, title, tempUrl, api, targetType, message } = props;
  const [form] = Form.useForm();
  const [uploadParams, setUploadParams] = useState(() => ({ filePath: "", fileName: "", targetType }));
  const [modalOkDisable, setModalOkDisable] = useState<boolean>(true);
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);

  useEffect(() => {
    setModalOkDisable(uploadParams.filePath ? false : true);
  }, [uploadParams]);

  const handleExcelCancel = () => {
    Modal.destroyAll();
    close?.();
  };
  const fetchUpload = async values => {
    const { status } = await api?.({ ...uploadParams, ...values });
    if (status) {
      pushImportHistory();
      close?.();
      fresh?.();
    }
  };
  const [checkModalProps, setCheckModalProps] = useState({
    title: "数据重复确认",
    visible: false,
    tempUrl: "",
    dataSource: [],
    close: () => {
      setCheckModalProps({ ...checkModalProps, visible: false });
    },
    afterOk: () => {}
  });
  const handleExcelOk = async () => {
    form.validateFields().then(async values => {
      setConfirmLoading(true);
      const { entry } = await apis.checkStatementScheduleFromExcel({ ...uploadParams, ...values });
      const { success, errorFilePath, financeSupplierStatementScheduleExcelBOList } = entry;
      if (success) {
        fetchUpload(values);
        fresh?.();
      } else {
        setCheckModalProps({
          ...checkModalProps,
          visible: true,
          tempUrl: errorFilePath,
          dataSource: financeSupplierStatementScheduleExcelBOList ?? [],
          afterOk: () => {
            fetchUpload(values);
            fresh?.();
          }
        });
      }
      setConfirmLoading(false);
    });
  };

  const fileChangeHandle = (url: string, fileName: string) => {
    setUploadParams({ ...uploadParams, fileName, filePath: url });
  };
  const onRemoveHandle = () => {
    setUploadParams({ ...uploadParams, fileName: "", filePath: "" });
  };
  // 下载导入商品Excel模板
  const handleDownloadExample = (e: React.BaseSyntheticEvent) => {
    e.stopPropagation();
    window.open(tempUrl);
  };

  return (
    <Modal
      centered={true}
      className={styles.uploadModal}
      title={title}
      open={visible}
      onOk={() => handleExcelOk()}
      onCancel={() => handleExcelCancel()}
      maskClosable={false}
      confirmLoading={confirmLoading}
      okButtonProps={{
        size: SIZE,
        disabled: modalOkDisable
      }}
      cancelButtonProps={{
        size: SIZE
      }}
    >
      <Form size={SIZE} form={form}>
        {message ? (
          <Form.Item>
            <Alert message={message} type="info" showIcon></Alert>
          </Form.Item>
        ) : null}

        <Form.Item>
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              flexDirection: "column",
              alignItems: "center"
            }}
          >
            <RSSUpload
              accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
              fileChange={(ossResultUrl: string, file: any, fileName: string) => fileChangeHandle(ossResultUrl, fileName)}
              fileRemove={() => onRemoveHandle()}
              onRemove={false}
              size={30 * 1024 * 1024}
              type={["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "application/vnd.ms-exce", "text/csv"]}
              fileLength={1}
              fileList={uploadParams.filePath ? [uploadParams.filePath] : []}
            />
          </div>
        </Form.Item>
        <Form.Item>
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              flexDirection: "column",
              alignItems: "center"
            }}
          >
            <Button type="link" onClick={handleDownloadExample}>
              [下载模板]
            </Button>
          </div>
        </Form.Item>
      </Form>
      <CheckModal {...checkModalProps} />
    </Modal>
  );
};
export default React.memo(UploadModal);
