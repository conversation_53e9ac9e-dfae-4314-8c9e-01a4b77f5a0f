import React, { useEffect, useState } from "react";
import { Space, Tabs, Tooltip } from "antd";
import { InfoCircleOutlined } from "@ant-design/icons";
import { history, useSearchParams } from "@umijs/max";
import KeepAlive, { useAliveController } from "react-activation";
import ReconciliationGoods from "./reconciliation-goods";
import ReconciliationSKU from "./reconciliation-sku";
import ReconciliationDiff from "./reconciliation-diff";
import ReconciliationHistory from "./reconciliation-history";
import useCommonOptions from "@/hooks/useCommonOptions";
import { SIZE, tabs_tabBarStyle } from "@/utils/constant";
import { ifEmptyObj } from "@/utils";
export const PageUrl = "/supplierbill/reconciliationcheduling";
export const tab_reconciliation_goods = "a";
export const tab_reconciliation_sku = "b";
export const tab_reconciliation_diff = "c";
export const tab_reconciliation_history = "d";

export const FieldsContext = React.createContext<any>(null);
const Res = props => {
  const { urlParams } = props;
  const [treatedUrlParams, setTreatedUrlParams] = useState({});
  const [curTab, setCurTab] = useState(tab_reconciliation_goods);
  const [itemTypeOptions, itemTypeObj] = useCommonOptions({ dimName: "商品类型" });
  const [statementFollowTypeOptions, statementFollowTypeObj] = useCommonOptions({ dimName: "对账跟进类型" });
  const [unStatementTypeOptions, unStatementTypeObj] = useCommonOptions({ dimName: "未对账类型" });
  const [isStatementGroupOptions, isStatementGroupObj] = useCommonOptions({ dimName: "是否有对账群" });
  const [isLaterBillOptions, isLaterBillObj] = useCommonOptions({ dimName: "是否有后期账单" });
  const [billSettlePatternOptions, billSettlePatternObj] = useCommonOptions({ dimName: "账单结算模式" });
  const [undertakerRemarkOptions] = useCommonOptions({ dimName: "快手技术服务费承担方" });
  const [modelIdOptions, modelIdObj] = useCommonOptions({ dimName: "模式" });
  const [, shopTypeObj] = useCommonOptions({ dimName: "店铺性质" });
  const tabItems = [
    {
      key: tab_reconciliation_goods,
      label: "对账商品",
      children: <ReconciliationGoods />
    },
    {
      key: tab_reconciliation_sku,
      label: "对账SKU",
      children: <ReconciliationSKU />
    },
    {
      key: tab_reconciliation_diff,
      label: (
        <Space>
          差异列表
          <Tooltip title="对账排班和利润预估表的商品差异信息，仅做展示提醒">
            <InfoCircleOutlined />
          </Tooltip>
        </Space>
      ),
      children: <ReconciliationDiff />
    },
    {
      key: tab_reconciliation_history,
      label: (
        <Space>
          排班历史记录
          <Tooltip title="2024年前的历史记录，只做展示查询">
            <InfoCircleOutlined />
          </Tooltip>
        </Space>
      ),
      children: <ReconciliationHistory />
    }
  ];
  useEffect(() => {
    if (ifEmptyObj(urlParams)) {
      setTreatedUrlParams(urlParams);
    }
  }, [urlParams]);
  return (
    <FieldsContext.Provider
      value={{
        urlParams: treatedUrlParams,
        itemTypeOptions,
        itemTypeObj,
        statementFollowTypeOptions,
        statementFollowTypeObj,
        unStatementTypeOptions,
        unStatementTypeObj,
        isStatementGroupOptions,
        isStatementGroupObj,
        isLaterBillOptions,
        isLaterBillObj,
        billSettlePatternOptions,
        billSettlePatternObj,
        modelIdOptions,
        modelIdObj,
        undertakerRemarkOptions,
        shopTypeObj
      }}
    >
      <Tabs activeKey={curTab} tabBarStyle={tabs_tabBarStyle} size={SIZE} onChange={setCurTab} items={tabItems} />
    </FieldsContext.Provider>
  );
};
/**
 * 上一个页面的状态
 * 用于比较 携带参数跳页 与 正常路由跳页时，页面进行缓存刷新
 */
const prePageStatus = {
  search: ""
};
const AliveRecord = props => {
  const [searchParams] = useSearchParams();
  const hasSearch = searchParams.size > 0;
  let urlParams = {};
  if (hasSearch) {
    const reconcileProgress = searchParams.get("reconcileProgress");
    const liveDate = searchParams.get("liveDate");
    const scopeType = searchParams.get("scopeType");
    if (liveDate === "false") {
      urlParams.liveDate = [];
    }
    urlParams = {
      scopeType,
      reconcileProgress
    };
  }
  let aliveCont = useAliveController();
  const { location } = history;
  // 当 url 上携带参数时，刷新 页面缓存
  if (location.search !== prePageStatus.search) {
    prePageStatus.search = location.search;
    aliveCont.refreshScope(PageUrl);
  }
  return (
    <KeepAlive name={PageUrl}>
      <Res {...props} urlParams={urlParams} />
    </KeepAlive>
  );
};

export default React.memo(AliveRecord);
