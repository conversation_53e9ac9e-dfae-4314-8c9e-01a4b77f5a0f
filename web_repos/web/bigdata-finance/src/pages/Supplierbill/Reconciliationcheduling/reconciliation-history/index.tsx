import React, { useRef, useContext, useState } from "react";
import { Button } from "antd";
import { columns, advance_columns_search } from "./variable";
import { NOVALUE, Protable_FROM_CONFIG, SIZE } from "@/utils/constant";
import dayjs from "dayjs";
import TableList from "@/components/TableList";
import apis from "@/services/supplierbill/reconciliationcheduling";
import UploadModal from "@/components/UploadModal";
import { FieldsContext } from "../index";
import Edit from "./edit";
import { splitSpaceComma } from "@/utils";
const Res: React.FC = () => {
  const { itemTypeOptions, modelIdOptions, shopTypeObj } = useContext(FieldsContext);
  const proTableFormRef = useRef<any>();
  const [showUpload, setshowUpload] = useState(false);
  const actionRef = useRef<any>();
  const [editProps, setEditProps] = useState<any>({
    open: false,
    onFresh: () => {
      actionRef.current?.reload();
    },
    onClose: () => {
      setEditProps({ ...editProps, open: false });
    }
  });

  const handleEdit = async record => {
    setEditProps({
      ...editProps,
      open: true,
      record
    });
  };
  const columnsProps = {
    handleEdit,
    modelIdOptions,
    shopTypeObj
  };
  const advanceColumnsProps = { itemTypeOptions, modelIdOptions };

  // 处理请求参数
  const handlePageParmas = (params: any) => {
    const { current, time, itemIds, shopIds, ...rest } = params;
    if (time) {
      const [startTime, endTime] = time;
      rest.startTime = dayjs(startTime).format("YYYY-MM-DD");
      rest.endTime = dayjs(endTime).format("YYYY-MM-DD");
    }
    rest.itemIds = splitSpaceComma(itemIds);
    rest.shopIds = splitSpaceComma(shopIds);
    return { ...rest, pageNo: current };
  };
  const UploadModalProps = {
    title: "排班历史记录上传",
    visible: showUpload,
    targetType: 0,
    api: apis.historyUpload,
    message: (
      <>
        <span>1. 请参考模板中数据格式进行上传</span>
        <br />
        <span>2. 黄色背景为必填项</span>
        <br />
        <span>3. 文件数据会覆盖线上数据，请确保数据记录的完整性</span>
        <br />
      </>
    ),
    tempUrl: "https://s.xinc818.com/files/webcim7ft7vq542euq4/排班历史记录模板.xlsx",
    fresh: () => {
      actionRef.current?.reload();
    },
    close: () => {
      setshowUpload(false);
    }
  };
  return (
    <div>
      <TableList
        headerTitle={"排班历史记录"}
        formRef={proTableFormRef}
        actionRef={actionRef}
        columnEmptyText={NOVALUE}
        advanceColumns={advance_columns_search(advanceColumnsProps)}
        columns={columns(columnsProps)}
        scroll={{ x: "max-content", y: "calc(100vh - 390px)" }}
        form={{
          ...Protable_FROM_CONFIG,
          initialValues: {
            liveDate: [dayjs().subtract(3, "month"), dayjs()]
          }
        }}
        api={apis.historyPage}
        downloadApi={apis.historyPageDownload}
        preFetch={handlePageParmas}
        toolbar={{
          actions: [
            <>
              <Button type="primary" onClick={() => setshowUpload(true)} size={SIZE}>
                上传
              </Button>
            </>
          ]
        }}
      />
      {showUpload && <UploadModal {...UploadModalProps} />}
      {editProps.open ? <Edit {...editProps} /> : null}
    </div>
  );
};

export default React.memo(Res);
