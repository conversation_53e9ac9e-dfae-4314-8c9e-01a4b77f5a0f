import { Space } from "antd";
import { DeleteBtn, returnEllipsisTooltip } from "@/utils";
import apis from "@/services/supplierbill/reconciliationcheduling";

export const advance_columns_search = ({}: any): TableListItem[] => {
  return [
    {
      title: "基本信息",
      valueType: "group",
      colProps: { span: 24 },
      columns: [
        {
          title: "品牌名称",
          dataIndex: "brandName",
          hideInSearch: false,
          hideInTable: true
        },
        {
          title: "店铺ID",
          dataIndex: "shopIds",
          hideInTable: true,
          fieldProps: {
            placeholder: "多个使用英文逗号/空格"
          }
        },
        {
          title: "店铺性质",
          dataIndex: "shopType",
          hideInSearch: false,
          hideInTable: true
        },
        {
          title: "供应商名称",
          dataIndex: "supplierName",
          hideInSearch: false,
          hideInTable: true
        },
        {
          title: "我司合同主体",
          dataIndex: "contractName",
          hideInSearch: false,
          hideInTable: true
        },
        {
          title: "直播订单编号",
          dataIndex: "orderId",
          hideInSearch: false,
          hideInTable: true
        },
        {
          title: "对接人",
          dataIndex: "purchasePrincipal",
          hideInSearch: false,
          hideInTable: true
        }
      ]
    }
  ];
};
const columns_search = ({}: any): Array<TableListItem> => {
  return [
    {
      title: "直播日期",
      dataIndex: "time",
      valueType: "dateRange",
      hideInTable: true
    },
    {
      title: "主播名称",
      dataIndex: "anchorName",
      hideInSearch: false,
      hideInTable: true
    },
    {
      title: "商品ID",
      dataIndex: "itemIds",
      hideInSearch: false,
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "商品名称",
      dataIndex: "itemName",
      hideInSearch: false,
      hideInTable: true
    },
    {
      title: "模式",
      dataIndex: "module",
      hideInSearch: false,
      hideInTable: true
    },
    {
      title: "部门",
      dataIndex: "department",
      hideInTable: true
    },
    {
      title: "对账人",
      dataIndex: "reconciler",
      hideInSearch: false,
      hideInTable: true
    }
  ];
};

export const columns = ({ handleEdit }: any): Array<TableListItem> => {
  return [
    ...columns_search({}),
    {
      title: "ID",
      dataIndex: "id",
      width: 60,
      hideInSearch: true,
      fixed: "left"
    },
    { title: "直播日期", dataIndex: "liveDate", fixed: "left", width: 100, hideInSearch: true },
    {
      title: "主播名称",
      dataIndex: "anchorName",
      width: 100,
      fixed: "left",
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品ID",
      dataIndex: "itemId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品名称",
      dataIndex: "itemName",
      width: 160,
      fixed: "left",
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品类型",
      dataIndex: "itemType",
      width: 100,
      fixed: "left",
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "模式",
      dataIndex: "module",
      width: 100,
      fixed: "left",
      hideInSearch: true
    },
    {
      title: "品牌名称",
      dataIndex: "brandName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "店铺ID",
      dataIndex: "shopId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "店铺名称",
      dataIndex: "shopName",
      width: 160,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "店铺性质",
      dataIndex: "shopType",
      width: 100,
      hideInSearch: true
    },
    {
      title: "供应商名称",
      dataIndex: "supplierName",
      width: 160,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "我司合同主体",
      dataIndex: "contractName",
      width: 160,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "直播订单编号",
      dataIndex: "orderId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "部门",
      dataIndex: "department",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "对接人",
      dataIndex: "purchasePrincipal",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "对账人",
      dataIndex: "reconciler",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "直播销售金额",
      dataIndex: "gmv",
      width: 120,
      hideInSearch: true,
      render(_, record) {
        const { gmvStr } = record;
        return returnEllipsisTooltip({ title: gmvStr });
      }
    },
    {
      title: "直播销量",
      dataIndex: "volume",
      width: 120,
      hideInSearch: true
    },
    {
      title: "佣金比例",
      dataIndex: "commissionRate",
      width: 120,
      hideInSearch: true
    },
    {
      title: "首次开展对账工作时间",
      dataIndex: "startWorkDate",
      width: 120,
      hideInSearch: true
    },
    {
      title: "对账期间",
      dataIndex: "reconciliationPeriod",
      width: 160,
      hideInSearch: true,
      render(_) {
        return returnEllipsisTooltip({ title: _ });
      }
    },
    {
      title: "未对账类型",
      dataIndex: "unReconciliationType",
      width: 120,
      hideInSearch: true,
      render(_) {
        return returnEllipsisTooltip({ title: _ });
      }
    },
    {
      title: "是否收款",
      dataIndex: "isPayee",
      width: 120,
      hideInSearch: true
    },
    {
      title: "是否有对账群",
      dataIndex: "isStatementGroup",
      width: 120,
      hideInSearch: true
    },
    {
      title: "对账跟进类型",
      dataIndex: "followType",
      width: 120,
      hideInSearch: true,
      render(_) {
        return returnEllipsisTooltip({ title: _ });
      }
    },
    {
      title: "对账跟进情况-具体说明",
      dataIndex: "reason",
      width: 120,
      hideInSearch: true,
      render(_) {
        return returnEllipsisTooltip({ title: _ });
      }
    },
    {
      title: "分配上传时间",
      dataIndex: "uploadTime",
      width: 120,
      hideInSearch: true
    },
    {
      title: "操作",
      valueType: "option",
      align: "center",
      fixed: "right",
      width: 100,
      render: (text: string, record: any, index: number, action: any) => {
        const { id } = record;
        return (
          <Space>
            <a onClick={() => handleEdit(record)}>编辑</a>
            <DeleteBtn params={{ ids: [id] }} btnProps={{ type: "link" }} action={action} api={apis.historyDelete} />
          </Space>
        );
      }
    }
  ].map(item => {
    const { title } = item;
    return {
      align: "center",
      ...item,
      title: returnEllipsisTooltip({ title })
    };
  });
};
