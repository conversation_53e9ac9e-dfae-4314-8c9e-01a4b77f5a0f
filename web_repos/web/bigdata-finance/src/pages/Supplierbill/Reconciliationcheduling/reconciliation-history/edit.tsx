import React, { useEffect } from "react";
import { Form, Input, Space, Button, Drawer, Row, Col, DrawerProps, DatePicker } from "antd";
import { SIZE } from "@/utils/constant";
import { returnEllipsisTooltip } from "@/utils";
import apis from "@/services/supplierbill/reconciliationcheduling";
import EditInputNumber from "@/components/EditInputNumber";
import EditInputNumberPercent from "@/components/EditInputNumberPercent";
import dayjs from "dayjs";
const formItemLayout = {
  labelCol: {
    xs: { span: 12 },
    sm: { span: 8 }
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 16 }
  }
};
interface IProps extends DrawerProps {
  record: Record<string, any>;
  onFresh?: () => void;
  onClose: () => void;
}

// 规则大类 排N
const GoodcostEdit: React.FC<IProps> = props => {
  const { onClose, onFresh, record, ...rest } = props;

  const [form] = Form.useForm();

  const handleSave = () => {
    form.validateFields().then(async values => {
      const { startWorkDate, ...rest } = values;
      rest.startWorkDate = dayjs(startWorkDate).format("YYYY-MM-DD");
      const { status } = await apis.historyUpdate({ ...record, ...rest });
      if (status) {
        onClose?.();
        onFresh?.();
      }
    });
  };
  useEffect(() => {
    const { startWorkDate, ...rest } = record;
    if (startWorkDate) {
      rest.startWorkDate = dayjs(startWorkDate);
    }
    form.setFieldsValue({ ...rest });
  }, [record]);
  return (
    <Drawer
      title="编辑排班历史记录"
      width={800}
      closeIcon={false}
      onClose={onClose}
      footer={
        <div style={{ textAlign: "center" }}>
          <Space align="center">
            <Button type="primary" size={SIZE} onClick={handleSave}>
              保存
            </Button>
            <Button size={SIZE} onClick={onClose}>
              取消
            </Button>
          </Space>
        </div>
      }
      {...rest}
    >
      <Form labelWrap={true} size={SIZE} {...formItemLayout} form={form} onFinish={() => {}}>
        <Row>
          <Col span={12}>
            <Form.Item name="itemName" label={returnEllipsisTooltip({ title: "商品名称" })}>
              <Input placeholder="请输入"></Input>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="brandName" label={returnEllipsisTooltip({ title: "品牌名称" })}>
              <Input placeholder="请输入"></Input>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="shopId" label={returnEllipsisTooltip({ title: "店铺ID" })}>
              <Input placeholder="请输入"></Input>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="shopName" label={returnEllipsisTooltip({ title: "店铺名称" })}>
              <Input placeholder="请输入"></Input>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="shopType" label={returnEllipsisTooltip({ title: "店铺性质" })}>
              <Input placeholder="请输入"></Input>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="supplierName" label={returnEllipsisTooltip({ title: "供应商名称" })}>
              <Input placeholder="请输入"></Input>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="contractName" label={returnEllipsisTooltip({ title: "我司合同主体" })}>
              <Input placeholder="请输入"></Input>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="orderId" label={returnEllipsisTooltip({ title: "直播订单编号" })}>
              <Input placeholder="请输入"></Input>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="department" label={returnEllipsisTooltip({ title: "部门" })}>
              <Input placeholder="请输入"></Input>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="purchasePrincipal" label={returnEllipsisTooltip({ title: "对接人" })}>
              <Input placeholder="请输入"></Input>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="reconciler" label={returnEllipsisTooltip({ title: "对账人" })}>
              <Input placeholder="请输入"></Input>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="gmv" label={returnEllipsisTooltip({ title: "直播销售金额" })}>
              <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="volume" label={returnEllipsisTooltip({ title: "直播销量" })}>
              <EditInputNumber size={SIZE} divide={1} precision={0} style={{ width: "100%" }} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="commissionRate" label={returnEllipsisTooltip({ title: "佣金比例" })}>
              <EditInputNumberPercent formatter={value => `${value}%`} style={{ width: "100%" }} step={1} placeholder="佣金比例" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="startWorkDate" label={returnEllipsisTooltip({ title: "首次开发对账工作时间" })}>
              <DatePicker style={{ width: "100%" }}></DatePicker>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="reconciliationPeriod" label={returnEllipsisTooltip({ title: "对账期间" })}>
              <Input placeholder="请输入"></Input>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="unReconciliationType" label={returnEllipsisTooltip({ title: "未对账类型" })}>
              <Input placeholder="请输入"></Input>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="isPayee" label={returnEllipsisTooltip({ title: "是否收款" })}>
              <Input placeholder="请输入"></Input>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="followType" label={returnEllipsisTooltip({ title: "对账跟进类型" })}>
              <Input placeholder="请输入"></Input>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="reason" label={returnEllipsisTooltip({ title: "对账跟进情况-具体说明" })}>
              <Input placeholder="请输入"></Input>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Drawer>
  );
};

export default GoodcostEdit;
