import { Select } from "antd";
import { filterOptionLabel, returnEllipsisTooltip } from "@/utils/index";
import { SIZE } from "@/utils/constant";
import AnchorSelect from "@/components/AnchorSelect";
import DepartmentSelect from "@/components/DepartmentSelect";
import BrandDebounSelect from "@/components/BrandDebounSelect";

export const advance_columns_search = ({ itemTypeOptions }: any): TableListItem[] => {
  return [
    {
      title: "基本信息",
      valueType: "group",
      colProps: { span: 24 },
      columns: [
        {
          title: "商品类型",
          dataIndex: "itemTypeList",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem: () => {
            return <Select placeholder="请选择" size={SIZE} options={itemTypeOptions} filterOption={filterOptionLabel} showSearch allowClear />;
          }
        },
        {
          title: "部门",
          dataIndex: "bigDepartmentList",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem: () => {
            return <DepartmentSelect key="bigDepartmentList" maxTagCount="responsive" mode="multiple" />;
          }
        },
        {
          title: "业务对接人",
          dataIndex: "purchasePrincipal",
          hideInSearch: false,
          hideInTable: true
        },
        {
          title: "店铺",
          dataIndex: "shopName",
          hideInTable: true
        },
        {
          title: "品牌",
          dataIndex: "brandName",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem() {
            return <BrandDebounSelect key="brandName" showSearch allowClear />;
          }
        },
        {
          title: "合同号",
          dataIndex: "orderContractIdList",
          hideInSearch: false,
          hideInTable: true,
          fieldProps: {
            placeholder: "多个使用英文逗号/空格"
          }
        }
      ]
    },
    {
      title: "对账信息",
      valueType: "group",
      colProps: { span: 24 },
      columns: [
        {
          title: "财务对账人",
          dataIndex: "financeReconcile",
          hideInSearch: false,
          hideInTable: true
        },
        {
          title: "最新财务对账人",
          dataIndex: "latestFinanceReconcile",
          hideInSearch: false,
          hideInTable: true
        }
      ]
    }
  ];
};
const columns_search = ({ modelIdOptions }: any): Array<TableListItem> => {
  return [
    {
      title: "直播日期",
      dataIndex: "liveDate",
      valueType: "dateRange",
      hideInTable: true
    },
    {
      title: "主播",
      dataIndex: "anchorNameList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem() {
        return <AnchorSelect showSearch allowClear maxTagCount="responsive" mode="multiple" />;
      }
    },
    {
      title: "商品ID",
      dataIndex: "itemIdList",
      hideInSearch: false,
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "商品名称",
      dataIndex: "itemTitle",
      hideInSearch: false,
      hideInTable: true
    },
    {
      title: "SKU信息",
      dataIndex: "skuName",
      hideInSearch: false,
      hideInTable: true
    },
    {
      title: "模式",
      dataIndex: "modelIdList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return <Select placeholder="请选择" size={SIZE} options={modelIdOptions} filterOption={filterOptionLabel} showSearch allowClear />;
      }
    },

    {
      title: "供应商名称",
      dataIndex: "supplierName",
      hideInTable: true
    }
  ];
};

export const columns = ({ itemTypeOptions, statementFollowTypeOptions, unStatementTypeOptions, modelIdOptions }: any): Array<TableListItem> => {
  return [
    ...columns_search({ itemTypeOptions, unStatementTypeOptions, statementFollowTypeOptions, modelIdOptions }),
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      hideInSearch: true,
      fixed: "left",
      render: (_, record, index) => {
        return index + 1;
      }
    },
    { title: "直播日期", dataIndex: "liveDate", fixed: "left", width: 100, hideInSearch: true },
    {
      title: "主播ID",
      dataIndex: "anchorId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "主播",
      dataIndex: "anchorName",
      width: 100,
      fixed: "left",
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "商品ID", dataIndex: "itemId", width: 100, hideInSearch: true },
    {
      title: "商品名称",
      dataIndex: "itemTitle",
      width: 140,
      fixed: "left",
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "SKU信息",
      dataIndex: "skuName",
      width: 140,
      fixed: "left",
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "模式",
      dataIndex: "modelId",
      width: 100,
      fixed: "left",
      hideInSearch: true,
      render(_, record) {
        const { modelIdStr } = record;
        return returnEllipsisTooltip({ title: modelIdStr });
      }
    },
    {
      title: "商品类型",
      dataIndex: "itemType",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { itemTypeStr } = record;
        return returnEllipsisTooltip({ title: itemTypeStr });
      }
    },
    {
      title: "账单结算模式",
      dataIndex: "billSettlePatternStr",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "财务对账人",
      dataIndex: "financeReconcile",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "最新财务对账人",
      dataIndex: "latestFinanceReconcile",
      width: 120,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "部门",
      dataIndex: "bigDepartment",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "业务对接人",
      dataIndex: "purchasePrincipal",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "店铺",
      dataIndex: "shopName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "店铺ID", dataIndex: "shopId", width: 100, hideInSearch: true },
    {
      title: "店铺性质",
      dataIndex: "shopType",
      width: 100,
      hideInSearch: true
    },
    {
      title: "品牌",
      dataIndex: "brandName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "供应商名称", dataIndex: "supplierName", width: 100, hideInSearch: true },
    {
      title: "我司合同主体",
      dataIndex: "contractName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "合同号", dataIndex: "orderContractId", width: 100, hideInSearch: true },
    { title: "线上佣金比例", dataIndex: "onlineCommissionRateStr", width: 100, hideInSearch: true },
    { title: "线下佣金比例", dataIndex: "offlineCommissionRateStr", width: 100, hideInSearch: true },
    { title: "主品成本单价", dataIndex: "mainProductCostPriceStr", width: 100, hideInSearch: true },
    { title: "赠品成本单价", dataIndex: "giveawayProductCostPriceStr", width: 100, hideInSearch: true }
  ].map(item => {
    const { title } = item;
    return {
      align: "center",
      ...item,
      title: returnEllipsisTooltip({ title })
    };
  });
};
