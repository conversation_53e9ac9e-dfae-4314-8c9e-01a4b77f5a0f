import React, { useRef, useContext } from "react";
import { columns, advance_columns_search } from "./variable";
import { NOVALUE, Protable_FROM_CONFIG } from "@/utils/constant";
import { splitSpaceComma } from "@/utils";
import dayjs from "dayjs";
import TableList from "@/components/TableList";
import apis from "@/services/supplierbill/reconciliationcheduling";
import { FieldsContext } from "../index";
const Res: React.FC = () => {
  const { itemTypeOptions, modelIdOptions, shopTypeObj } = useContext(FieldsContext);
  const proTableFormRef = useRef<any>();
  const actionRef = useRef<any>();
  const handleDelete = async () => {};
  const columnsProps = {
    handleDelete,
    modelIdOptions,
    shopTypeObj
  };
  const advanceColumnsProps = { itemTypeOptions, modelIdOptions };

  // 处理请求参数
  const handlePageParmas = (params: any) => {
    const { current, liveDate, itemIdList, itemTypeList, modelIdList, orderContractIdList, ...rest } = params;
    if (liveDate) {
      const [liveDateStart, liveDateEnd] = liveDate;
      rest.liveDateStart = dayjs(liveDateStart).format("YYYY-MM-DD");
      rest.liveDateEnd = dayjs(liveDateEnd).format("YYYY-MM-DD");
    }
    if (typeof itemTypeList !== "undefined") {
      rest.itemTypeList = [itemTypeList];
    }
    rest.itemIdList = splitSpaceComma(itemIdList);
    rest.modelIdList = splitSpaceComma(modelIdList);
    rest.orderContractIdList = splitSpaceComma(orderContractIdList);
    return { ...rest, pageNo: current };
  };

  return (
    <div>
      <TableList
        formRef={proTableFormRef}
        actionRef={actionRef}
        columnEmptyText={NOVALUE}
        advanceColumns={advance_columns_search(advanceColumnsProps)}
        columns={columns(columnsProps)}
        scroll={{ x: "max-content", y: "calc(100vh - 390px)" }}
        form={{
          ...Protable_FROM_CONFIG,
          initialValues: {
            liveDate: [dayjs().subtract(3, "month"), dayjs()]
          }
        }}
        api={apis.getDiffSupplierScheduleSkuPage}
        preFetch={handlePageParmas}
        rowKey={record => {
          const { anchorId, anchorName, itemId, itemType, liveDate, skuId } = record;
          return anchorId + anchorName + itemId + itemType + liveDate + skuId;
        }}
      />
    </div>
  );
};

export default React.memo(Res);
