import React, { useEffect, useState } from "react";
import { Form, DrawerProps, Space, Button } from "antd";
import { BetaSchemaForm } from "@ant-design/pro-components";
import styles from "@/components/AdvanceSearch/index.less";
import { columns } from "./variable";
import { formGrid } from "@/components/AdvanceSearch";
import { SIZE, SPLIT_FLAG } from "@/utils/constant";
import apis from "@/services/supplierbill/reconciliationcheduling";

interface IProps extends DrawerProps {
  params: Record<string, any>;
  trigger: JSX.Element;
  billSettlePatternOptions: Record<string, any>[];
  onClose?: () => void;
  onFresh?: () => void;
}

const Res: React.FC<IProps> = props => {
  const { onClose, onFresh, trigger, params, billSettlePatternOptions } = props;
  let { type, id, anchorId, anchorName, itemId, itemType, liveDate, undertakerRemarkOptions } = params;
  const [open, setOpen] = useState(false);
  const [skuInfo, setSkuInfo] = useState([]);
  const [form] = Form.useForm();
  const [initialValues, setInitialValues] = useState({
    mainProductCostPrice: 0,
    giveawayProductCostPrice: 0,
    undertakerRemark: "2",
    offlineCommissionRate: 0
  });

  const fetchSku = async params => {
    const { entry, status } = await apis.getScheduleItemSkuList(params);
    let result = [];
    if (status) {
      result = entry.map(item => {
        const { skuId, skuName, orderVolumeStr, orderAmountStr, skuNameInfo, selectFlag } = item;
        const label = `${skuId}+${skuName}+${orderVolumeStr}+${orderAmountStr}`;
        return {
          label: label,
          value: skuNameInfo,
          disabled: selectFlag
        };
      });
    }
    setSkuInfo(result);
  };
  const fetchDetail = async params => {
    const { entry, status } = await apis.getSupplierScheduleBaseInfoVO(params);
    let res = initialValues;
    if (status) {
      delete entry.billSettlePattern;
      res = { ...initialValues, ...(entry || {}) };
    }
    form.setFieldsValue(res);
    setInitialValues(res);
  };
  const fetchSKUDetail = async params => {
    const { entry, status } = await apis.getSupplierScheduleSku(params);
    let res = initialValues;
    if (status) {
      const { billSettlePattern } = entry;
      res = { ...initialValues, ...(entry || {}), billSettlePattern: billSettlePattern?.split(",") };
    }
    form.setFieldsValue(res);
    setInitialValues(res);
  };
  useEffect(() => {
    if (open) {
      if (type === "edit") {
        fetchSKUDetail({ id });
        fetchSku({ anchorId, anchorName, itemId, itemType, liveDate });
      } else if (type === "add") {
        fetchSku({ anchorId, anchorName, itemId, itemType, liveDate });
        fetchDetail({ anchorId, anchorName, itemId, itemType, liveDate });
      }
    }
  }, [open, params]);
  const handleClose = () => {
    setOpen(false);
    form.resetFields();
  };
  const handleOk = () => {
    form.validateFields().then(async values => {
      let params = { ...values };
      if (params.skuNameInfo) {
        const [skuId, skuName, orderVolume, orderAmount] = params.skuNameInfo?.split(SPLIT_FLAG);
        params.skuId = skuId;
        params.skuName = skuName;
        params.orderVolume = orderVolume || 0;
        params.orderAmount = orderAmount || 0;
      }
      if (Array.isArray(params.billSettlePattern)) {
        params.billSettlePattern = params?.billSettlePattern?.join() || "";
      }

      switch (type) {
        case "add":
          params = {
            ...params,
            anchorId,
            anchorName,
            itemId,
            itemType,
            liveDate
          };
          break;
        case "edit":
          params = {
            ...params,
            id: initialValues.id,
            anchorId,
            anchorName,
            itemId,
            itemType,
            liveDate
          };
      }
      const { status } = await apis.saveOrUpdateSupplierScheduleSku(params);
      if (status) {
        form.resetFields();
        onClose?.();
        onFresh?.();
        handleClose();
      }
    });
  };
  const columnsProps = { initialValues, skuInfo, billSettlePatternOptions, undertakerRemarkOptions };
  const returnTitle = (
    <div className={styles["title"]}>
      <div>{type === "add" ? "新增" : "编辑"}对账SKU</div>
      <Space>
        <Button key="cancel" size={SIZE} onClick={() => handleClose()}>
          取消
        </Button>
        <Button
          key="reset"
          size={SIZE}
          onClick={() => {
            form.resetFields();
            if (type === "add") {
              fetchSku({ anchorId, anchorName, itemId, itemType, liveDate });
              fetchDetail({ anchorId, anchorName, itemId, itemType, liveDate });
            }
          }}
        >
          重置
        </Button>
        <Button key="ok" size={SIZE} type="primary" onClick={() => handleOk()}>
          确认
        </Button>
      </Space>
    </div>
  );
  return (
    <>
      <BetaSchemaForm
        form={form}
        open={open}
        size={SIZE}
        onOpenChange={open => {
          setOpen(open);
          form.resetFields();
        }}
        {...formGrid}
        trigger={trigger}
        title={returnTitle}
        className={styles["advance-search"]}
        layout="horizontal"
        layoutType="DrawerForm"
        columns={columns(columnsProps)}
        submitter={false}
      />
    </>
  );
};

export default React.memo(Res);
