import React, { useMemo, useEffect, useState } from "react";
import { Form, Input, Space, Button, Drawer, Row, Col, DrawerProps, DatePicker } from "antd";
import apis from "@/services/supplierbill/reconciliationcheduling";
import { SIZE } from "@/utils/constant";
import useCommonOptions from "@/hooks/useCommonOptions";
import BrandDebounSelect from "@/components/BrandDebounSelect";
import EditSelect from "@/components/EditSelect";
import SupplierNameSelect from "@/components/SupplierNameSelect";
import dayjs from "dayjs";
import { returnEllipsisTooltip } from "@/utils";
import EditInputNumberPercent from "@/components/EditInputNumberPercent";
import EditInputNumber from "@/components/EditInputNumber";

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 }
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 16 }
  }
};
interface IProps extends DrawerProps {
  record: Record<string, any>;
  onFresh?: () => void;
  onClose: () => void;
}
const Res: React.FC<IProps> = props => {
  const { record, onClose, onFresh, ...rest } = props;
  const [copy] = useState(() => {
    const { gmtFirstStatementWork, billSettlePattern, ...rest } = record;
    if (gmtFirstStatementWork) {
      rest.gmtFirstStatementWork = dayjs(gmtFirstStatementWork);
    }
    rest.billSettlePattern = billSettlePattern?.split(",")?.filter(item => +item) || [];
    return rest;
  });
  const [itemTypeOptions] = useCommonOptions({ dimName: "商品类型" });
  const [statementFollowTypeOptions] = useCommonOptions({ dimName: "对账跟进类型" });
  const [unStatementTypeOptions] = useCommonOptions({ dimName: "未对账类型" });
  const [isStatementGroupOptions] = useCommonOptions({ dimName: "是否有对账群" });
  const [isLaterBillOptions] = useCommonOptions({ dimName: "是否有后期账单" });
  const [billSettlePatternOptions] = useCommonOptions({ dimName: "账单结算模式" });
  const [undertakerRemarkOptions] = useCommonOptions({ dimName: "快手技术服务费承担方" });
  const [form] = Form.useForm();
  const returnTitle = useMemo(() => {
    if (record) {
      return <span>编辑 {record.itemTitle}</span>;
    }
    return "";
  }, [record]);

  const handleSave = () => {
    form.validateFields().then(async values => {
      const params = { ...copy, ...values };
      const { gmtFirstStatementWork, billSettlePattern, ...rest } = params;
      let finP = {
        ...rest
      };
      if (gmtFirstStatementWork) {
        finP.gmtFirstStatementWork = dayjs(gmtFirstStatementWork).format("YYYY-MM-DD");
      }
      finP.billSettlePattern = billSettlePattern?.join();
      // 编辑
      const { status } = await apis.saveOrUpdateStatementSchedule(finP);
      if (status) {
        onFresh?.();
        onClose?.();
      }
    });
  };
  useEffect(() => {
    form.setFieldsValue(copy);
  }, [copy, form]);
  return (
    <Drawer
      title={returnTitle}
      width={800}
      closeIcon={false}
      onClose={onClose}
      footer={
        <div style={{ textAlign: "center" }}>
          <Space align="center">
            <Button type="primary" size={SIZE} onClick={handleSave}>
              保存
            </Button>
            <Button size={SIZE} onClick={onClose}>
              取消
            </Button>
          </Space>
        </div>
      }
      {...rest}
    >
      <Form labelWrap={true} size={SIZE} {...formItemLayout} form={form} onFinish={() => {}}>
        <Row gutter={40}>
          <Col span={12}>
            <Form.Item name="liveDate" label={returnEllipsisTooltip({ title: "直播日期" })}>
              <Input disabled={true} placeholder="直播日期"></Input>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="anchorName" label={returnEllipsisTooltip({ title: "主播" })}>
              <Input disabled={true} placeholder="主播"></Input>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="itemId" label={returnEllipsisTooltip({ title: "商品ID" })}>
              <Input disabled={true} placeholder="商品ID"></Input>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="itemTitle" label={returnEllipsisTooltip({ title: "商品名称" })}>
              <Input disabled={true} placeholder="商品名称"></Input>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="itemType" label={returnEllipsisTooltip({ title: "商品类型" })}>
              <EditSelect placeholder="请选择" size={SIZE} options={itemTypeOptions} showSearch allowClear />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="supplierName" label={returnEllipsisTooltip({ title: "供应商名称" })}>
              <Input placeholder="供应商名称"></Input>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="billSettlePattern" label={returnEllipsisTooltip({ title: "账单结算模式" })}>
              <EditSelect
                placeholder="请选择"
                size={SIZE}
                defaultValue={copy.billSettlePattern}
                options={billSettlePatternOptions}
                showSearch
                allowClear
                maxTagCount="responsive"
                mode="multiple"
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="financeReconcile" label={returnEllipsisTooltip({ title: "财务对账人" })}>
              <Input placeholder="财务对账人"></Input>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="latestFinanceReconcile" label={returnEllipsisTooltip({ title: "最新财务对账人" })}>
              <Input placeholder="最新财务对账人"></Input>
            </Form.Item>
          </Col>
          {copy?.itemType === 2 ? (
            <>
              <Col span={12}>
                <Form.Item name="brandName" label={returnEllipsisTooltip({ title: "品牌" })}>
                  <BrandDebounSelect showSearch allowClear />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="supplierName" label={returnEllipsisTooltip({ title: "供应商名称" })}>
                  <SupplierNameSelect />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="contractName" label={returnEllipsisTooltip({ title: "我司合同主体" })}>
                  <Input placeholder="我司合同主体"></Input>
                </Form.Item>
              </Col>
            </>
          ) : null}
          <Col span={12}>
            <Form.Item name="gmtFirstStatementWork" label={returnEllipsisTooltip({ title: "首次对账时间" })}>
              <DatePicker allowClear style={{ width: "100%" }} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="unStatementType" label={returnEllipsisTooltip({ title: "未对账类型" })}>
              <EditSelect placeholder="请选择" size={SIZE} options={unStatementTypeOptions} showSearch allowClear />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="statementFollowType" label={returnEllipsisTooltip({ title: "对账跟进类型" })}>
              <EditSelect placeholder="请选择" size={SIZE} options={statementFollowTypeOptions} showSearch allowClear />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="statementFollowCondition" label={returnEllipsisTooltip({ title: "对账跟进情况" })}>
              <Input placeholder="对账跟进情况"></Input>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="isStatementGroup" label={returnEllipsisTooltip({ title: "是否有对账群" })}>
              <EditSelect placeholder="请选择" size={SIZE} options={isStatementGroupOptions} showSearch allowClear />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="isLaterBill" label={returnEllipsisTooltip({ title: "是否有后期账单" })}>
              <EditSelect placeholder="请选择" size={SIZE} options={isLaterBillOptions} showSearch allowClear />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="onlineCommissionRateStr"
              label={returnEllipsisTooltip({ title: "线上佣金比例" })}
              rules={[{ required: true, message: "请输入线上佣金比例" }]}
            >
              <Input disabled />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="offlineCommissionRate"
              label={returnEllipsisTooltip({ title: "线下佣金比例" })}
              rules={[{ required: true, message: "请输入线下佣金比例" }]}
            >
              <EditInputNumberPercent formatter={value => `${value}%`} style={{ width: "100%" }} max={100} step={1} placeholder="线下佣金比例" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="mainProductCostPrice"
              label={returnEllipsisTooltip({ title: "主品成本单价" })}
              rules={[{ required: true, message: "请输入主品成本单价" }]}
            >
              <EditInputNumber divide={10000} precision={4} style={{ width: "100%" }} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="giveawayProductCostPrice"
              label={returnEllipsisTooltip({ title: "赠品成本单价" })}
              rules={[{ required: true, message: "请输入赠品成本单价" }]}
            >
              <EditInputNumber divide={10000} precision={4} style={{ width: "100%" }} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="undertakerRemark"
              label={returnEllipsisTooltip({ title: "技术服务费承担方" })}
              rules={[{ required: true, message: "请选择技术服务费承担方" }]}
            >
              <EditSelect placeholder="请选择" options={undertakerRemarkOptions} showSearch allowClear />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Drawer>
  );
};

export default React.memo(Res);
