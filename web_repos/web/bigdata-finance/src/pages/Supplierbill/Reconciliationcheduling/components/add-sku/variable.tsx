import EditInputNumber from "@/components/EditInputNumber";
import EditInputNumberPercent from "@/components/EditInputNumberPercent";
import EditSelect from "@/components/EditSelect";
import { filterOptionLabel } from "@/utils";
import { Alert, Input, Select } from "antd";

export const columns = ({ initialValues, skuInfo, billSettlePatternOptions, undertakerRemarkOptions }: any): TableListItem[] => {
  return [
    {
      colProps: { span: 24 },
      ignoreFormItem: true,
      renderFormItem() {
        return (
          <Alert
            style={{ width: "100%" }}
            message="修改对账SKU信息可能会对当前对账单结果产生影响，但不会影响已对账的单据。请谨慎操作！"
            type="warning"
            showIcon
          />
        );
      }
    },
    {
      title: "商品信息",
      valueType: "group",
      colProps: { span: 24 },
      columns: [
        {
          title: "主播",
          dataIndex: "anchorName",
          fieldProps: {
            disabled: true
          },
          hideInSearch: false,
          hideInTable: true
        },
        {
          title: "直播日期",
          dataIndex: "liveDate",
          hideInSearch: false,
          fieldProps: {
            disabled: true
          },
          hideInTable: true
        },
        {
          title: "商品ID",
          dataIndex: "itemId",
          hideInSearch: false,
          fieldProps: {
            disabled: true
          },
          hideInTable: true
        },
        {
          title: "商品名称",
          dataIndex: "itemTitle",
          fieldProps: {
            disabled: true
          },
          hideInTable: true
        },
        {
          title: "商品类型",
          dataIndex: "itemType",
          hideInSearch: false,
          fieldProps: {
            disabled: true
          },
          hideInTable: true,
          renderFormItem() {
            return <Input value={initialValues.itemTypeStr || ""} />;
          }
        },
        {
          title: "线上佣金比例",
          dataIndex: "onlineCommissionRate",
          hideInSearch: false,
          fieldProps: {
            disabled: true
          },
          hideInTable: true,
          renderFormItem() {
            return <Input value={initialValues.onlineCommissionRateStr || ""} />;
          }
        }
      ]
    },
    {
      title: "SKU信息",
      valueType: "group",
      colProps: { span: 24 },
      columns: [
        {
          title: "SKU信息",
          dataIndex: "skuNameInfo",
          hideInSearch: false,
          hideInTable: true,
          formItemProps: {
            rules: [
              {
                required: true,
                message: "此项为必填项"
              }
            ]
          },
          renderFormItem() {
            return <Select placeholder="请选择" options={skuInfo} filterOption={filterOptionLabel} showSearch allowClear />;
          }
        },
        {
          title: "账单结算模式",
          dataIndex: "billSettlePattern",
          hideInSearch: false,
          hideInTable: true,
          formItemProps: {
            rules: [
              {
                required: true,
                message: "此项为必填项"
              }
            ]
          },
          renderFormItem() {
            return (
              <Select
                placeholder="请选择"
                options={billSettlePatternOptions}
                filterOption={filterOptionLabel}
                showSearch
                allowClear
                maxTagCount="responsive"
                mode="multiple"
              />
            );
          }
        },
        {
          title: "线下佣金比例",
          dataIndex: "offlineCommissionRate",
          hideInSearch: false,
          hideInTable: true,
          formItemProps: {
            rules: [
              {
                required: true,
                message: "此项为必填项"
              }
            ]
          },
          renderFormItem() {
            return (
              <EditInputNumberPercent formatter={value => `${value}%`} style={{ width: "100%" }} max={100} step={1} placeholder="线下佣金比例" />
            );
          }
        },
        {
          title: "主品成本单价",
          dataIndex: "mainProductCostPrice",
          hideInSearch: false,
          hideInTable: true,
          formItemProps: {
            rules: [
              {
                required: true,
                message: "此项为必填项"
              }
            ]
          },
          renderFormItem() {
            return <EditInputNumber divide={10000} precision={4} style={{ width: "100%" }} />;
          }
        },
        {
          title: "赠品成本单价",
          dataIndex: "giveawayProductCostPrice",
          hideInSearch: false,
          hideInTable: true,
          formItemProps: {
            rules: [
              {
                required: true,
                message: "此项为必填项"
              }
            ]
          },
          renderFormItem() {
            return <EditInputNumber divide={10000} precision={4} style={{ width: "100%" }} />;
          }
        },
        {
          title: "技术服务费承担方",
          dataIndex: "undertakerRemark",
          hideInSearch: false,
          hideInTable: true,
          formItemProps: {
            rules: [
              {
                required: true,
                message: "此项为必填项"
              }
            ]
          },
          renderFormItem() {
            return (
              <EditSelect
                defaultValue={initialValues?.undertakerRemark + ""}
                placeholder="请选择"
                options={undertakerRemarkOptions}
                filterOption={filterOptionLabel}
                showSearch
                allowClear
              />
            );
          }
        }
      ]
    }
  ];
};
