import React, { useEffect, useLayoutEffect, useState } from "react";
import { Tabs, Space, Tooltip } from "antd";
import { InfoCircleOutlined } from "@ant-design/icons";
import KeepAlive, { useAliveController } from "react-activation";
import ReconciliBill from "./reconcili-bill";
import ReconciliBillDetail from "./reconcili-bill-detail";
import ReconciliBillHistory from "./reconcili-bill-history";
import ReconciliBillMutiSupplier from "./reconcili-bill-muti-supplier";
import { SIZE, tabs_tabBarStyle } from "@/utils/constant";
import { history, useSearchParams, useAccess } from "@umijs/max";
import { ifEmptyObj } from "@/utils";
import dayjs from "dayjs";
import useCommonOptions from "@/hooks/useCommonOptions";
import { model_auths } from "./auths";
import { useModelAuth, Page_Auths } from "@/wrappers/authButton";
import apis_auths from "@/services/users";
export const FieldsContext = React.createContext<any>(null);
export const PageUrl = "/supplierbill/reconciliationbill";
export const tab_reconcili_bill = model_auths.model_reconcili_bill;
export const tab_reconcili_bill_detail = model_auths.model_reconcili_bill_detail;
export const tab_reconcili_bill_history = model_auths.model_reconcili_bill_history;
export const tab_reconcili_bill_muti_supplier = model_auths.model_reconcili_bill_muti_supplier;
const default_tab = tab_reconcili_bill_detail;
const Res = props => {
  const authModel = useModelAuth({ pageUrl: PageUrl });
  const page_auths = Page_Auths({ pageUrl: PageUrl });
  const { urlParams } = props;
  const { userInfo } = useAccess();
  const [treatedUrlParams, setTreatedUrlParams] = useState({});
  const [billSettlePatternOptions] = useCommonOptions({ dimName: "账单结算模式" });
  const [billtypeOptions] = useCommonOptions({ dimName: "供应商账单类型" });
  const [billTypeOptions] = useCommonOptions({ dimName: "结算类型" });
  const [settlementStatusOptions] = useCommonOptions({ dimName: "账单状态" });
  const [contractNameOptions] = useCommonOptions({ dimName: "我司合同主体" });
  const [roles, setRoles] = useState([]);
  const fetchAuths = async (params = {}) => {
    const { entry } = await apis_auths.getUserRoleList(params);
    setRoles(entry);
  };
  const tabItems = [
    {
      key: tab_reconcili_bill,
      label: "对账单",
      children: <ReconciliBill />
    },
    {
      key: tab_reconcili_bill_detail,
      label: "商品对账明细",
      children: <ReconciliBillDetail />
    },
    {
      key: tab_reconcili_bill_history,
      label: (
        <Space>
          对账单历史记录
          <Tooltip title="2024年前的历史记录">
            <InfoCircleOutlined />
          </Tooltip>
        </Space>
      ),
      children: <ReconciliBillHistory />
    },
    {
      key: tab_reconcili_bill_muti_supplier,
      label: (
        <Space>
          多供应商对账单
          <Tooltip title="同一个商品多个供应商时，产出多供应商对账单，数据由线下计算人工上传">
            <InfoCircleOutlined />
          </Tooltip>
        </Space>
      ),
      children: <ReconciliBillMutiSupplier />
    }
  ].filter(item => {
    return authModel(item.key);
  });

  const [curTab, setCurTab] = useState(() => {
    // 如果权限中存在 默认tab 就使用默认tab
    if (tabItems.some(tab => tab.key === default_tab)) {
      return default_tab;
    } else {
      // 否则 使用 有权限的第一个tab
      return tabItems?.[0]?.key || "";
    }
  });

  useLayoutEffect(() => {
    const { $_tab } = urlParams;
    // 其他页面跳转
    if ($_tab) {
      setCurTab($_tab);
      return;
    }
  }, [urlParams]);
  useEffect(() => {
    if (ifEmptyObj(urlParams)) {
      setTreatedUrlParams(urlParams);
    }
  }, [urlParams]);
  useEffect(() => {
    fetchAuths({ userId: userInfo.userId });
  }, []);

  return (
    <FieldsContext.Provider
      value={{
        page_auths,
        urlParams: treatedUrlParams,
        roles,
        billSettlePatternOptions,
        billTypeOptions,
        contractNameOptions,
        settlementStatusOptions,
        billtypeOptions
      }}
    >
      <Tabs activeKey={curTab} tabBarStyle={tabs_tabBarStyle} size={SIZE} onChange={setCurTab} items={tabItems} />
    </FieldsContext.Provider>
  );
};
/**
 * 上一个页面的状态
 * 用于比较 携带参数跳页 与 正常路由跳页时，页面进行缓存刷新
 */
const prePageStatus = {
  search: ""
};
const AliveRecord = props => {
  const [searchParams] = useSearchParams();
  const hasSearch = searchParams.size > 0;
  let urlParams = {};
  if (hasSearch) {
    const menu = searchParams.get("menu");
    const $_tab = searchParams.get("$_tab");
    const settleDateStart = searchParams.get("settleDateStart");
    const settleDateEnd = searchParams.get("settleDateEnd");
    const supplierName = searchParams.get("supplierName");
    const reconcileCondition = searchParams.get("reconcileCondition");
    const scopeType = searchParams.get("scopeType");
    urlParams = {
      scopeType,
      menu,
      $_tab,
      supplierName,
      reconcileCondition
    };
    if (settleDateStart && settleDateStart) {
      urlParams.reconciledPeriod = [dayjs(settleDateStart), dayjs(settleDateEnd)];
    }
  }
  let aliveCont = useAliveController();
  const { location } = history;
  // 当 url 上携带参数时，刷新 页面缓存
  if (location.search !== prePageStatus.search) {
    prePageStatus.search = location.search;
    aliveCont.refreshScope(PageUrl);
  }
  return (
    <KeepAlive name={PageUrl}>
      <Res {...props} urlParams={urlParams} />
    </KeepAlive>
  );
};

export default React.memo(AliveRecord);
