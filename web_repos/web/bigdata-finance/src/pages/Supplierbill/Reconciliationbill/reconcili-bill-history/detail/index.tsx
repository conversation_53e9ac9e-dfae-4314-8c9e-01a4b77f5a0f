import React, { useEffect, useState, useContext } from "react";
import { Form, DrawerProps, Space, Button } from "antd";
import { BetaSchemaForm } from "@ant-design/pro-components";
import styles from "@/components/AdvanceSearch/index.less";
import { columns } from "./variable";
import { formGrid } from "@/components/AdvanceSearch";
import { SIZE } from "@/utils/constant";
import apis from "@/services/supplierbill/reconciliationbill";
import { FieldsContext } from "../../index";
interface IProps extends DrawerProps {
  params?: Record<string, any>;
  record?: Record<string, any>;
  onClose?: () => void;
  onFresh?: () => void;
}

const Res: React.FC<IProps> = props => {
  const { contractNameOptions, settlementStatusOptions, roles } = useContext(FieldsContext);
  const { onClose, onFresh, open, record } = props;
  const [_open, setOpen] = useState(false);
  const [form] = Form.useForm();

  const handleClose = () => {
    setOpen(false);
    form.resetFields();
  };
  const handleOk = () => {
    form.validateFields().then(async values => {
      const { remitPictureUrl, ...rest } = values;
      if (remitPictureUrl) {
        try {
          let remitImg = JSON.parse(remitPictureUrl);
          if (Array.isArray(remitImg)) {
            rest.remitPictureUrl = remitImg?.[0] ?? void 0;
          }
        } catch {
          rest.remitPictureUrl = remitPictureUrl;
        }
      }
      const { status } = await apis.historyUpdate({ ...record, ...rest });
      if (status) {
        onClose?.();
        onFresh?.();
      }
    });
  };
  const columnsProps = { initialValues: record, contractNameOptions, settlementStatusOptions, roles };
  const returnTitle = (
    <div className={styles["title"]}>
      <div>编辑对账单历史</div>
      <Space>
        <Button key="cancel" size={SIZE} onClick={() => handleClose()}>
          取消
        </Button>
        <Button key="ok" size={SIZE} type="primary" onClick={() => handleOk()}>
          保存
        </Button>
      </Space>
    </div>
  );
  useEffect(() => {
    setOpen(open);
    form.setFieldsValue(record);
  }, [form, open, record]);
  return (
    <>
      <BetaSchemaForm
        form={form}
        open={_open}
        size={SIZE}
        onOpenChange={open => {
          setOpen(open);
          if (!open) {
            form.resetFields();
          }
        }}
        {...formGrid}
        title={returnTitle}
        className={styles["advance-search"]}
        layout="horizontal"
        layoutType="DrawerForm"
        columns={columns(columnsProps)}
        submitter={false}
      />
    </>
  );
};

export default React.memo(Res);
