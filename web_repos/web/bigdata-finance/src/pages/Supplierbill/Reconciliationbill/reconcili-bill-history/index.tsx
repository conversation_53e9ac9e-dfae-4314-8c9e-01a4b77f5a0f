import React, { useState, useRef, useContext } from "react";
import { But<PERSON> } from "antd";
import TableList from "@/components/TableList";
import { SIZE } from "@/utils/constant";
import { columns } from "./variable";
import apis from "@/services/supplierbill/reconciliationbill";
import UploadExcelModal from "@/components/UploadModal";
import Detail from "./detail";
import { FieldsContext, PageUrl } from "../index";
import { pushExportHistory } from "@/utils";
import { btn_auths } from "../auths";
import AuthButton from "@/wrappers/authButton";
const Res = () => {
  const { contractNameOptions, settlementStatusOptions } = useContext(FieldsContext);
  const actionsRef = useRef<any>();
  const tableFormRef = useRef<any>();
  const [pageParmas, setPageParmas] = useState<any>({});
  const [showUpload, setshowUpload] = useState(false);
  const [detailProps, setDetailProps] = useState<any>({
    open: false,
    onFresh: () => {
      actionsRef.current?.reload();
    },
    onClose: () => {
      setDetailProps({ ...detailProps, open: false });
    }
  });
  // 上传excel modal属性
  const UploadReconModalProps = {
    title: "上传对账单",
    visible: showUpload,
    targetType: 0,
    api: apis.historyUpload,
    message: (
      <>
        <span>1. 请参考模板中数据格式进行上传</span>
        <br />
        <span>2. 黄色背景为必填项</span>
        <br />
        <span>3. 文件数据会覆盖线上数据，请确保数据记录的完整性</span>
        <br />
      </>
    ),
    tempUrl: "https://s.xinc818.com/files/webcim5p5d5t72lqjab/对账单-对账单历史记录模板.xlsx",
    fresh: () => {
      actionsRef.current.reload();
    },
    close: () => {
      setshowUpload(false);
    }
  };

  const handlePageParmas = (params: any) => {
    const { current, ...rest } = params;
    return { ...rest, pageNo: current };
  };

  const handleEdit = async record => {
    setDetailProps({
      ...detailProps,
      open: true,
      record: {
        ...record,
        $_fetch: +new Date()
      }
    });
  };

  const columnsProps = {
    handleEdit,
    contractNameOptions,
    settlementStatusOptions
  };
  const handleExport = async () => {
    const { status } = await apis.historyDownload(pageParmas);
    if (status) {
      pushExportHistory();
    }
  };
  const actions = () => {
    return [
      <AuthButton key="upload" pageUrl={PageUrl} code={btn_auths.btn_reconcili_bill_history_upload}>
        <Button type="primary" onClick={() => setshowUpload(true)} size={SIZE}>
          上传
        </Button>
      </AuthButton>,
      <AuthButton key="export" pageUrl={PageUrl} code={btn_auths.btn_reconcili_bill_history_export}>
        <Button onClick={handleExport} size="small">
          导出
        </Button>
      </AuthButton>
    ];
  };
  const handleChange = p => {
    const params = tableFormRef.current?.getFieldsValue() || {};
    const res = handlePageParmas?.({ ...p, ...params });
    return res;
  };
  return (
    <>
      <TableList
        headerTitle="对账单历史记录"
        actionRef={actionsRef}
        formRef={tableFormRef}
        columns={columns(columnsProps)}
        defaultExpandAll={{ deep: 0 }}
        api={apis.historyPage}
        scroll={{ y: "calc(100vh - 510px)" }}
        summaryApi={apis.historySummary}
        preFetch={handleChange}
        paramsChange={setPageParmas}
        rowKey="id"
        toolbar={{
          actions: actions()
        }}
      />

      {showUpload && <UploadExcelModal {...UploadReconModalProps} />}
      {detailProps.open ? <Detail {...detailProps} /> : null}
    </>
  );
};

export default React.memo(Res);
