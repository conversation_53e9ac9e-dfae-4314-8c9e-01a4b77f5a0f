import { filterOptionLabel, returnEllipsisTooltip } from "@/utils";
import { SIZE } from "@/utils/constant";
import { Select, Space, Dropdown, MenuProps, Popconfirm } from "antd";
import { DownOutlined } from "@ant-design/icons";
import { menus } from ".";
import { btn_auths } from "../auths";
const columns_search = ({ billSettlePatternOptions, billTypeOptions, contractNameOptions }: any) => {
  return [
    {
      title: "对账期间",
      dataIndex: "reconciledPeriod",
      hideInSearch: false,
      hideInTable: true,
      valueType: "dateRange"
    },
    {
      title: "供应商名称",
      dataIndex: "supplierName",
      hideInSearch: false,
      hideInTable: true
    },
    {
      title: "账单结算模式",
      dataIndex: "billSettlePatternList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return (
          <Select
            placeholder="请选择"
            size={SIZE}
            options={billSettlePatternOptions}
            filterOption={filterOptionLabel}
            showSearch
            allowClear
            maxTagCount="responsive"
            mode="multiple"
          />
        );
      }
    },
    {
      title: "账单类型",
      dataIndex: "billTypeList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return <Select placeholder="请选择" size={SIZE} options={billTypeOptions} filterOption={filterOptionLabel} showSearch allowClear />;
      }
    },
    {
      title: "财务对账人",
      dataIndex: "financeReconcile",
      hideInSearch: false,
      hideInTable: true
    },
    {
      title: "我司合同主体",
      dataIndex: "contractName",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return <Select placeholder="请选择" size={SIZE} options={contractNameOptions} filterOption={filterOptionLabel} showSearch allowClear />;
      }
    }
  ];
};

const level1dropdownMenus = ({ page_auths, curMenu, handleToDraft, record, handleDownloadRecon, handleConfirm, handleDelete }: any) => {
  const hasReconcili: MenuProps["items"] = [
    {
      key: btn_auths.btn_reconcili_bill_has_download,
      label: (
        <Popconfirm
          title={
            <>
              <div>下载结果包含供应商对应账期</div>
              <div>的所有直播商品，</div>
              <div>多个主播无需重复下载！</div>
            </>
          }
          onConfirm={() => handleDownloadRecon(record)}
          onCancel={() => {}}
          okText="确认"
          cancelText="取消"
          okButtonProps={{ size: SIZE }}
          cancelButtonProps={{ size: SIZE }}
        >
          下载
        </Popconfirm>
      )
    },
    {
      key: btn_auths.btn_reconcili_bill_has_toDraft,
      label: (
        <Popconfirm
          title={
            <>
              <div>供应商对账期间中多个账单</div>
              <div>将同时转为草稿状态，请确认！</div>
            </>
          }
          onConfirm={() => handleToDraft(record)}
          onCancel={() => {}}
          okText="确认"
          cancelText="取消"
          okButtonProps={{ size: SIZE }}
          cancelButtonProps={{ size: SIZE }}
        >
          转为草稿
        </Popconfirm>
      )
    }
  ].filter(item => {
    return page_auths.includes(item.key);
  });
  const draft: MenuProps["items"] = [
    {
      key: btn_auths.btn_reconcili_bill_draft_delete,
      danger: true,
      label: (
        <Popconfirm
          title="是否确认删除?"
          onConfirm={() => handleDelete(record)}
          onCancel={() => {}}
          okText="确认"
          cancelText="取消"
          okButtonProps={{ size: SIZE }}
          cancelButtonProps={{ size: SIZE }}
        >
          删除
        </Popconfirm>
      )
    },
    {
      key: btn_auths.btn_reconcili_bill_draft_confirm,
      label: (
        <Popconfirm
          title="是否确认?"
          onConfirm={() => handleConfirm(record)}
          onCancel={() => {}}
          okText="确认"
          cancelText="取消"
          okButtonProps={{ size: SIZE }}
          cancelButtonProps={{ size: SIZE }}
        >
          确认
        </Popconfirm>
      )
    },
    {
      key: btn_auths.btn_reconcili_bill_draft_download,
      label: (
        <Popconfirm
          title={
            <>
              <div>下载结果包含供应商对应账期</div>
              <div>的所有直播商品，</div>
              <div>多个主播无需重复下载！</div>
            </>
          }
          onConfirm={() => handleDownloadRecon(record)}
          onCancel={() => {}}
          okText="确认"
          cancelText="取消"
          okButtonProps={{ size: SIZE }}
          cancelButtonProps={{ size: SIZE }}
        >
          下载
        </Popconfirm>
      )
    }
  ].filter(item => {
    return page_auths.includes(item.key);
  });
  const res = {
    [menus.menu_hasReconcili]: hasReconcili,
    [menus.menu_draft]: draft
  };
  return res[curMenu] ?? [];
};
const level2dropdownMenus = ({ page_auths, curMenu, record, handleDownloadRecon }: any) => {
  const hasReconcili: MenuProps["items"] = [
    {
      key: btn_auths.btn_reconcili_bill_has_download,
      label: (
        <Popconfirm
          title={
            <>
              <div>下载结果包含供应商对应账期</div>
              <div>的所有直播商品，</div>
              <div>多个主播无需重复下载！</div>
            </>
          }
          onConfirm={() => handleDownloadRecon(record)}
          onCancel={() => {}}
          okText="确认"
          cancelText="取消"
          okButtonProps={{ size: SIZE }}
          cancelButtonProps={{ size: SIZE }}
        >
          下载
        </Popconfirm>
      )
    }
  ].filter(item => {
    return page_auths.includes(item.key);
  });
  const draft: MenuProps["items"] = [
    {
      key: btn_auths.btn_reconcili_bill_draft_download,
      label: (
        <Popconfirm
          title={
            <>
              <div>下载结果包含供应商对应账期</div>
              <div>的所有直播商品，</div>
              <div>多个主播无需重复下载！</div>
            </>
          }
          onConfirm={() => handleDownloadRecon(record)}
          onCancel={() => {}}
          okText="确认"
          cancelText="取消"
          okButtonProps={{ size: SIZE }}
          cancelButtonProps={{ size: SIZE }}
        >
          下载
        </Popconfirm>
      )
    }
  ].filter(item => {
    return page_auths.includes(item.key);
  });
  const res = {
    [menus.menu_hasReconcili]: hasReconcili,
    [menus.menu_draft]: draft
  };
  return res[curMenu] ?? [];
};
export const columns = ({
  page_auths,
  handleRedPunch,
  handleDownloadRecon,
  handleToDraft,
  handleConfirm,
  handleDelete,
  billSettlePatternOptions,
  billTypeOptions,
  contractNameOptions,
  curMenu
}: any): Array<TableListItem> => {
  return [
    ...columns_search({ billSettlePatternOptions, billTypeOptions, contractNameOptions }),
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      hideInSearch: true,
      fixed: "left",
      editable: false,
      render: (_, record, index) => {
        const { children } = record;
        if (children) {
          return index + 1;
        }
      }
    },
    {
      title: "对账期间",
      editable: false,
      dataIndex: "reconciledPeriod",
      width: 100,
      fixed: "left",
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "账期",
      editable: false,
      dataIndex: "periodStr",
      width: 100,
      fixed: "left",
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },

    {
      title: "账单结算模式",
      editable: false,
      fixed: "left",
      dataIndex: "billSettlePatternStr",
      width: 100,
      hideInSearch: true
    },
    { title: "账单类型", fixed: "left", editable: false, dataIndex: "billTypeStr", width: 100, hideInSearch: true },
    { title: "财务对账人", fixed: "left", editable: false, dataIndex: "financeReconcile", width: 100, hideInSearch: true },
    {
      title: "供应商名称",
      editable: false,
      fixed: "left",
      dataIndex: "supplierName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "我司合同主体",
      editable: false,
      dataIndex: "contractName",
      width: 120,
      fixed: "left",
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "账单收入金额", editable: false, dataIndex: "totalIncomeAmountStr", width: 100, hideInSearch: true },
    { title: "账单退款金额", editable: false, dataIndex: "totalOrderRefundAmountStr", width: 100, hideInSearch: true },
    { title: "超售后退款金额", editable: false, dataIndex: "totalOverRefundAmountStr", width: 100, hideInSearch: true },
    { title: "总退款金额", editable: false, dataIndex: "totalRefundAmountStr", width: 100, hideInSearch: true },
    { title: "销售收入", editable: false, dataIndex: "salesRevenueAmountStr", width: 100, hideInSearch: true },
    { title: "总佣金金额", editable: false, dataIndex: "totalCommissionAmountStr", width: 100, hideInSearch: true },
    { title: "达人佣金金额", editable: false, dataIndex: "pidCommissionAmountStr", width: 100, hideInSearch: true },
    { title: "达人佣金追回金额", editable: false, dataIndex: "pidBackCommissionAmountStr", width: 100, hideInSearch: true },
    { title: "线上佣金金额", editable: false, dataIndex: "onlineCommissionAmountStr", width: 100, hideInSearch: true },
    { title: "结算件数", editable: false, dataIndex: "salesVolumeStr", width: 100, hideInSearch: true },
    { title: "退款件数", editable: false, dataIndex: "refundItemVolumeStr", width: 100, hideInSearch: true },
    { title: "实际结算件数", editable: false, dataIndex: "actualSettleItemVolumeStr", width: 100, hideInSearch: true },
    { title: "快手技术服务费", editable: false, dataIndex: "techServiceAmountStr", width: 100, hideInSearch: true },
    { title: "快手技术服务费追回", editable: false, dataIndex: "techServiceBackAmountStr", width: 100, hideInSearch: true },
    { title: "实际技术服务费", editable: false, dataIndex: "actualTechServiceAmountStr", width: 100, hideInSearch: true },
    { title: "供货价", editable: false, dataIndex: "supplyPriceStr", width: 100, hideInSearch: true },
    { title: "供货总成本", editable: false, dataIndex: "totalSupplyCostStr", width: 100, hideInSearch: true },
    { title: "其他金额", editable: false, dataIndex: "otherAmountStr", width: 100, hideInSearch: true },
    { title: "线下结算金额", editable: false, dataIndex: "offlineSettleAmountStr", width: 100, hideInSearch: true },
    { title: "出具账单日期", editable: false, dataIndex: "gmtIssueBill", width: 100, hideInSearch: true },
    {
      title: "操作",
      valueType: "option",
      align: "center",
      fixed: "right",
      width: 100,
      render: (_, record, index, action) => {
        const { parentFlag, isNeedRedPunch, confirmStatus } = record;
        return parentFlag === 1 ? (
          <Space size={SIZE} direction="vertical">
            <>
              {curMenu === menus.menu_hasReconcili &&
              [btn_auths.btn_reconcili_bill_has_download, btn_auths.btn_reconcili_bill_has_toDraft].filter(item => page_auths.includes(item))
                ?.length ? (
                <Dropdown
                  menu={{
                    items: level1dropdownMenus({
                      page_auths,
                      curMenu,
                      isNeedRedPunch,
                      confirmStatus,
                      record,
                      handleRedPunch,
                      handleDownloadRecon,
                      handleToDraft,
                      handleConfirm,
                      handleDelete
                    })
                  }}
                >
                  <a onClick={e => e.preventDefault()}>
                    操作 <DownOutlined />
                  </a>
                </Dropdown>
              ) : null}
            </>
            <>
              {curMenu === menus.menu_draft &&
              [
                btn_auths.btn_reconcili_bill_draft_confirm,
                btn_auths.btn_reconcili_bill_draft_delete,
                btn_auths.btn_reconcili_bill_draft_download
              ].filter(item => page_auths.includes(item))?.length ? (
                <Dropdown
                  menu={{
                    items: level1dropdownMenus({
                      page_auths,
                      curMenu,
                      isNeedRedPunch,
                      confirmStatus,
                      record,
                      handleRedPunch,
                      handleDownloadRecon,
                      handleToDraft,
                      handleConfirm,
                      handleDelete
                    })
                  }}
                >
                  <a onClick={e => e.preventDefault()}>
                    操作 <DownOutlined />
                  </a>
                </Dropdown>
              ) : null}
            </>
          </Space>
        ) : isNeedRedPunch !== 1 ? null : (
          <>
            {curMenu === menus.menu_hasReconcili && [btn_auths.btn_reconcili_bill_has_download].filter(item => page_auths.includes(item))?.length ? (
              <Dropdown
                menu={{
                  items: level2dropdownMenus({ page_auths, curMenu, action, record, handleDownloadRecon })
                }}
              >
                <a onClick={e => e.preventDefault()}>
                  操作 <DownOutlined />
                </a>
              </Dropdown>
            ) : null}
            {curMenu === menus.menu_draft && [btn_auths.btn_reconcili_bill_draft_download].filter(item => page_auths.includes(item))?.length ? (
              <Dropdown
                menu={{
                  items: level2dropdownMenus({ page_auths, curMenu, action, record, handleDownloadRecon })
                }}
              >
                <a onClick={e => e.preventDefault()}>
                  操作 <DownOutlined />
                </a>
              </Dropdown>
            ) : null}
          </>
        );
      }
    }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      align: "center",
      ...item,
      title: returnEllipsisTooltip({ title })
    };
  });
};
