import React, { useState, useRef, useContext, useEffect, useLayoutEffect } from "react";
import { message, Form, Space, Tooltip, Button } from "antd";
import TableList from "@/components/TableList";
import { columns } from "./variable";
import apis from "@/services/supplierbill/reconciliationbill";
import { ifEmptyObj, pushExportHistory } from "@/utils";
import UploadModal from "@/components/UploadModal";
import dayjs from "dayjs";
import { FieldsContext, tab_reconcili_bill, PageUrl } from "../index";
import { InfoCircleOutlined } from "@ant-design/icons";
import { SIZE } from "@/utils/constant";
import { btn_auths } from "../auths";
import AuthButton from "@/wrappers/authButton";
export const menus = {
  menu_hasReconcili: "1",
  menu_draft: "2"
};
const default_menu = menus.menu_hasReconcili;
const Res = () => {
  const { page_auths, billSettlePatternOptions, billTypeOptions, contractNameOptions, urlParams } = useContext(FieldsContext);
  const [editForm] = Form.useForm();
  const actionsRef = useRef<any>();
  const tableFormRef = useRef<any>();
  const [pageParmas, setPageParmas] = useState<any>({});
  const [activeMenu, setActiveMenu] = useState<string>("");

  const [editableKeys, seteditableKeys] = useState<React.Key[]>([]);
  const [showUpload, setshowUpload] = useState(false);
  // 上传excel modal属性
  const UploadModalProps = {
    message: (
      <>
        <span>1. 请参考模板中数据格式进行上传</span>
        <br />
        <span>2. 黄色背景为必填项</span>
        <br />
        <span>3. 文件数据会覆盖线上数据，请确保数据记录的完整性</span>
        <br />
      </>
    ),
    title: "上传对账单",
    visible: showUpload,
    targetType: 0,
    api: apis.addStatementReconcileFromExcel,
    tempUrl: "https://s.xinc818.com/files/webcily6y5kik3v923v/供应商对账单导入模板.xlsx",
    fresh: () => {
      actionsRef.current.reload();
    },
    close: () => {
      setshowUpload(false);
    }
  };

  const handlePageParmas = (params: any) => {
    seteditableKeys([]);
    const { current, reconciledPeriod, billTypeList, ...rest } = params;
    if (reconciledPeriod) {
      const [gmtReconcilePeriodStart, gmtReconcilePeriodEnd] = reconciledPeriod;
      rest.gmtReconcilePeriodStart = dayjs(gmtReconcilePeriodStart).format("YYYY-MM-DD");
      rest.gmtReconcilePeriodEnd = dayjs(gmtReconcilePeriodEnd).format("YYYY-MM-DD");
    }
    if (billTypeList) {
      rest.billTypeList = [billTypeList];
    }
    return { ...rest, pageNo: current };
  };

  const handleRedPunch = async record => {
    const { gmtReconcilePeriodStart, gmtReconcilePeriodEnd, supplierName } = record;
    const { status } = await apis.toProcessRedPunch({
      gmtReconcilePeriodStart,
      gmtReconcilePeriodEnd,
      supplierName
    });
    if (status) {
      message.success("红冲成功！");
      actionsRef.current.reload();
    }
  };
  const handleDownloadRecon = async record => {
    const { downloadStatementKey } = record;
    const { status } = await apis.downloadSupplierReconcile({ downloadStatementKey });
    if (status) {
      pushExportHistory();
    }
  };
  const handleToDraft = async record => {
    const { gmtReconcilePeriodStart, gmtReconcilePeriodEnd, supplierName } = record;
    const { status } = await apis.doUnConfirmSupplierReconcile({ gmtReconcilePeriodStart, gmtReconcilePeriodEnd, supplierName });
    if (status) {
      actionsRef.current.reload();
    }
  };
  const handleDelete = async record => {
    const { gmtReconcilePeriodStart, gmtReconcilePeriodEnd, supplierName } = record;
    const { status } = await apis.deleteDraftSupplierReconcile({ gmtReconcilePeriodStart, gmtReconcilePeriodEnd, supplierName });
    if (status) {
      actionsRef.current.reload();
    }
  };
  const handleConfirm = async record => {
    const { gmtReconcilePeriodStart, gmtReconcilePeriodEnd, supplierName } = record;
    const { status } = await apis.doConfirmSupplierReconcile({ gmtReconcilePeriodStart, gmtReconcilePeriodEnd, supplierName });
    if (status) {
      actionsRef.current.reload();
    }
  };

  const columnsProps = {
    page_auths,
    handleRedPunch,
    handleDownloadRecon,
    handleToDraft,
    handleConfirm,
    handleDelete,
    billSettlePatternOptions,
    billTypeOptions,
    contractNameOptions,
    curMenu: activeMenu
  };

  const editable: any = {
    type: "single",
    form: editForm,
    onSave: async (key, editData) => {
      const { remitScreenshot, gmtReceiptAndPay, ...rest } = editData;
      const params = {
        ...rest
      };
      if (remitScreenshot) {
        try {
          let remitImg = JSON.parse(remitScreenshot);
          if (Array.isArray(remitImg)) {
            params.remitScreenshot = remitImg?.[0] ?? void 0;
          }
        } catch {
          params.remitScreenshot = remitScreenshot;
        }
      }
      if (gmtReceiptAndPay) {
        params.gmtReceiptAndPay = dayjs(gmtReceiptAndPay).format("YYYY-MM-DD");
      }

      const { status } = await apis.saveOrUpdateSupplierStatementReconcile(params);
      editForm.resetFields([key]);
      if (status) {
        message.success("修改成功！");
      }
      actionsRef.current?.reload();
    },
    editableKeys,
    onChange: seteditableKeys,
    actionRender: (row, config, { save, cancel }) => [save, cancel]
  };
  const toolbar_menus = [
    {
      key: menus.menu_hasReconcili,
      name: btn_auths.btn_reconcili_bill_menu_hasReconcili,
      label: "已对账"
    },
    {
      key: menus.menu_draft,
      name: btn_auths.btn_reconcili_bill_menu_draft,
      label: (
        <Space>
          草稿
          <Tooltip title="草稿账单不是最终账单，和供应商确认好账单金额后，即可把草稿确认为已对账账单">
            <InfoCircleOutlined />
          </Tooltip>
        </Space>
      )
    }
  ].filter(menu => page_auths.includes(menu.name));
  const findeActiveMenu = (menu = void 0) => {
    // 如果权限中存在 默认tab 就使用默认tab
    if (toolbar_menus.some(tab => tab.key === default_menu)) {
      return default_menu;
    } else {
      // 否则 使用 有权限的第一个tab
      return toolbar_menus?.[0]?.key ?? menu ?? "";
    }
  };
  useLayoutEffect(() => {
    if (!activeMenu) {
      setActiveMenu(findeActiveMenu());
    }
  }, [toolbar_menus, activeMenu]);
  const handleUrlParamsChange = p => {
    const params = tableFormRef.current?.getFieldsValue() || {};
    const res = handlePageParmas?.({ ...p, ...params });
    return res;
  };
  const handleExport = async () => {
    const { status } = await apis.downloadSupplierStatementReconcile(pageParmas);
    if (status) {
      pushExportHistory();
    }
  };
  useEffect(() => {
    if (ifEmptyObj(urlParams)) {
      const { $_tab, menu, ...rest } = urlParams;
      if ($_tab === tab_reconcili_bill) {
        setActiveMenu(findeActiveMenu(menu));
        tableFormRef?.current?.setFieldsValue(rest);
      }
    }
  }, [urlParams]);
  return (
    <>
      {activeMenu !== "" ? (
        <TableList
          oneExpansion={{ position: "actions", show: true }}
          actionRef={actionsRef}
          formRef={tableFormRef}
          columns={columns(columnsProps)}
          defaultExpandAll={{ deep: 0 }}
          hasChildren={true}
          api={apis.getSupplierStatementReconcilePage}
          scroll={{ y: "calc(100vh - 440px)" }}
          summaryApi={apis.getSupplierStatementReconcileTotal}
          preFetch={handleUrlParamsChange}
          params={{
            subTabType: activeMenu
          }}
          rowKey="combinedKey"
          editable={editable}
          paramsChange={setPageParmas}
          toolbar={{
            menu: {
              type: "tab",
              activeKey: activeMenu,
              items: toolbar_menus,
              onChange: (key: string) => {
                setActiveMenu(key);
              }
            },
            actions: [
              <>
                <AuthButton pageUrl={PageUrl} code={btn_auths.btn_reconcili_bill_export}>
                  <Button onClick={handleExport} size={SIZE}>
                    导出
                  </Button>
                </AuthButton>
              </>
            ]
          }}
        />
      ) : null}

      {showUpload && <UploadModal {...UploadModalProps} />}
    </>
  );
};

export default React.memo(Res);
