import AnchorSelect from "@/components/AnchorSelect";
import EditInputNumber from "@/components/EditInputNumber";
import EditInputNumberPercent from "@/components/EditInputNumberPercent";
import EditRSSUpload from "@/components/EditRSSUpload";
import { SIZE } from "@/utils/constant";
import { Select, Image } from "antd";
import DepartmentSelect from "@/components/DepartmentSelect";
import SupplierNameSelect from "@/components/SupplierNameSelect";
import { filterOptionLabel, ifEmptyObj, returnItemImgUrl, returnValueForNoValue } from "@/utils";
import { readonly, rolesMaps } from "@/pages/Supplierbill/Reconciliationbill/reconcili-bill-detail/detail/variable";

export const columns = ({
  form,
  roles,
  initialValues,
  settlePatternOptions,
  typeOptions,
  settlementStatusOptions,
  problemAttributionOptions,
  isMakeOutOptions,
  itemTypeOptions,
  legalFeedbackProcessOptions,
  lawFeedbackProcessOptions,
  contractNameOptions
}: any): TableListItem[] => {
  return [
    {
      title: "基本信息",
      valueType: "group",
      colProps: { span: 24 },
      columns: [
        {
          title: "直播日期",
          dataIndex: "liveDate",
          hideInSearch: false,
          hideInTable: true,
          fieldProps: {
            style: { width: "100%" }
          },
          valueType: "date",
          readonly: readonly({ roles, role: [rolesMaps.role_finance] })
        },
        {
          title: "主播ID",
          dataIndex: "pid",
          hideInSearch: false,
          hideInTable: true,
          readonly: readonly({ roles, role: [rolesMaps.role_finance] })
        },
        {
          title: "主播名称",
          dataIndex: "anchorName",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem() {
            return <AnchorSelect showSearch allowClear style={{ width: "100%" }} />;
          },
          readonly: readonly({ roles, role: [rolesMaps.role_finance] })
        },
        {
          title: "商品ID",
          dataIndex: "itemId",
          hideInSearch: false,
          hideInTable: true,
          readonly: readonly({ roles, role: [rolesMaps.role_finance] })
        },
        {
          title: "商品名称",
          dataIndex: "itemName",
          hideInTable: true,
          readonly: readonly({ roles, role: [rolesMaps.role_finance] })
        },
        {
          title: "商品类型",
          dataIndex: "itemType",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem() {
            return <Select options={itemTypeOptions}></Select>;
          },
          render() {
            const { itemTypeStr } = initialValues;
            return itemTypeStr;
          },
          readonly: readonly({ roles, role: [rolesMaps.role_finance] })
        },
        {
          title: "部门",
          dataIndex: "departmentName",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem: () => {
            return <DepartmentSelect key="departmentName" style={{ width: "100%" }} />;
          },
          readonly: readonly({ roles, role: [rolesMaps.role_finance] })
        },
        {
          title: "业务对接人",
          dataIndex: "purchasePrincipal",
          hideInSearch: false,
          hideInTable: true,
          readonly: readonly({ roles, role: [rolesMaps.role_finance] })
        },
        {
          title: "店铺ID",
          dataIndex: "shopId",
          hideInSearch: false,
          hideInTable: true,
          readonly: readonly({ roles, role: [rolesMaps.role_finance] })
        },
        {
          title: "店铺名称",
          dataIndex: "shopName",
          hideInSearch: false,
          hideInTable: true,
          readonly: readonly({ roles, role: [rolesMaps.role_finance] })
        }
      ]
    },
    {
      title: "账单信息",
      valueType: "group",
      colProps: { span: 24 },
      columns: [
        {
          title: "对账期间",
          dataIndex: "reconciliationPeriod",
          hideInSearch: false,
          hideInTable: true,
          readonly: readonly({ roles, role: [rolesMaps.role_finance] }),
          render() {
            const { gmtReconcilePeriodStart, gmtReconcilePeriodEnd } = initialValues;
            return `${returnValueForNoValue(gmtReconcilePeriodStart)} 至 ${returnValueForNoValue(gmtReconcilePeriodEnd)}`;
          },
          fieldProps: {
            style: { width: "100%" }
          },
          valueType: "dateRange"
        },
        {
          title: "账期",
          dataIndex: "reconcileDate",
          hideInSearch: false,
          hideInTable: true,
          readonly: readonly({ roles, role: [rolesMaps.role_finance] })
        },

        {
          title: "账单结算模式",
          dataIndex: "settlePattern",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem() {
            return <Select options={settlePatternOptions}></Select>;
          },
          render() {
            const { settlePatternStr } = initialValues;
            return settlePatternStr;
          },
          readonly: readonly({ roles, role: [rolesMaps.role_finance] })
        },
        {
          title: "账单类型",
          dataIndex: "type",
          hideInTable: true,
          renderFormItem() {
            return <Select options={typeOptions}></Select>;
          },
          render() {
            const { typeStr } = initialValues;
            return typeStr;
          },
          readonly: readonly({ roles, role: [rolesMaps.role_finance] })
        },
        {
          title: "财务对账人",
          dataIndex: "reconciler",
          hideInSearch: false,
          hideInTable: true,
          readonly: readonly({ roles, role: [rolesMaps.role_finance] })
        },
        {
          title: "供应商名称",
          dataIndex: "supplierName",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem() {
            return <SupplierNameSelect />;
          },
          readonly: readonly({ roles, role: [rolesMaps.role_finance] })
        },
        {
          title: "我司合同主体",
          dataIndex: "contractName",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem: () => {
            return <Select placeholder="请选择" size={SIZE} options={contractNameOptions} filterOption={filterOptionLabel} showSearch allowClear />;
          },
          readonly: readonly({ roles, role: [rolesMaps.role_finance] })
        },
        {
          title: "出具账单日期",
          dataIndex: "accountBillDate",
          hideInSearch: false,
          hideInTable: true,
          fieldProps: {
            style: { width: "100%" }
          },
          valueType: "date",
          readonly: readonly({ roles, role: [rolesMaps.role_finance] })
        },
        {
          title: "账单收入金额",
          dataIndex: "billIncomeAmount",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem() {
            return <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
          },
          render() {
            const { billIncomeAmountStr } = initialValues;
            return billIncomeAmountStr;
          },
          readonly: readonly({ roles, role: [rolesMaps.role_finance] })
        },
        {
          title: "账单退款金额",
          dataIndex: "billRefundAmount",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem() {
            return <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
          },
          render() {
            const { billRefundAmountStr } = initialValues;
            return billRefundAmountStr;
          },
          readonly: readonly({ roles, role: [rolesMaps.role_finance] })
        },

        {
          title: "超售后退款金额",
          dataIndex: "postSaleRefundAmount",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem() {
            return <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
          },
          render() {
            const { postSaleRefundAmountStr } = initialValues;
            return postSaleRefundAmountStr;
          },
          readonly: readonly({ roles, role: [rolesMaps.role_finance] })
        },
        {
          title: "总退款金额",
          dataIndex: "afterSaleFee",
          hideInTable: true,
          renderFormItem() {
            return <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
          },
          render() {
            const { afterSaleFeeStr } = initialValues;
            return afterSaleFeeStr;
          },
          readonly: readonly({ roles, role: [rolesMaps.role_finance] })
        },
        {
          title: "销售收入",
          dataIndex: "netGmv",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem() {
            return <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
          },
          render() {
            const { netGmvStr } = initialValues;
            return netGmvStr;
          },
          readonly: readonly({ roles, role: [rolesMaps.role_finance] })
        },
        {
          title: "总佣金比例",
          dataIndex: "totalCommissionRate",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem() {
            return <EditInputNumberPercent formatter={value => `${value}%`} style={{ width: "100%" }} step={1} placeholder="总佣金比例" />;
          },
          render() {
            const { totalCommissionRateStr } = initialValues;
            return totalCommissionRateStr;
          },
          readonly: readonly({ roles, role: [rolesMaps.role_finance] })
        },
        {
          title: "总佣金金额",
          dataIndex: "totalCommissionAmount",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem() {
            return <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
          },
          render() {
            const { totalCommissionAmountStr } = initialValues;
            return totalCommissionAmountStr;
          },
          readonly: readonly({ roles, role: [rolesMaps.role_finance] })
        },
        {
          title: "达人佣金金额",
          dataIndex: "anchorCommissionAmount",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem() {
            return <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
          },
          render() {
            const { anchorCommissionAmountStr } = initialValues;
            return anchorCommissionAmountStr;
          },
          readonly: readonly({ roles, role: [rolesMaps.role_finance] })
        },
        {
          title: "达人佣金追回金额",
          dataIndex: "anchorCommissionWithdrawAmount",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem() {
            return <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
          },
          render() {
            const { anchorCommissionWithdrawAmountStr } = initialValues;
            return anchorCommissionWithdrawAmountStr;
          },
          readonly: readonly({ roles, role: [rolesMaps.role_finance] })
        },
        {
          title: "线上佣金金额",
          dataIndex: "onlineCommission",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem() {
            return <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
          },
          render() {
            const { onlineCommissionStr } = initialValues;
            return onlineCommissionStr;
          },
          readonly: readonly({ roles, role: [rolesMaps.role_finance] })
        },
        {
          title: "结算件数",
          dataIndex: "settleVolume",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem() {
            return <EditInputNumber size={SIZE} divide={1} precision={0} style={{ width: "100%" }} />;
          },
          readonly: readonly({ roles, role: [rolesMaps.role_finance] })
        },
        {
          title: "退款件数",
          dataIndex: "refundVolume",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem() {
            return <EditInputNumber size={SIZE} divide={1} precision={0} style={{ width: "100%" }} />;
          },
          readonly: readonly({ roles, role: [rolesMaps.role_finance] })
        },
        {
          title: "实际结算件数",
          dataIndex: "realSettleVolume",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem() {
            return <EditInputNumber size={SIZE} divide={1} precision={0} style={{ width: "100%" }} />;
          },
          readonly: readonly({ roles, role: [rolesMaps.role_finance] })
        },
        {
          title: "快手技术服务费",
          dataIndex: "platformCost",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem() {
            return <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
          },
          render() {
            const { platformCostStr } = initialValues;
            return platformCostStr;
          },
          readonly: readonly({ roles, role: [rolesMaps.role_finance] })
        },
        {
          title: "快手技术服务费追回",
          dataIndex: "platformCostWithdraw",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem() {
            return <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
          },
          render() {
            const { platformCostWithdrawStr } = initialValues;
            return platformCostWithdrawStr;
          },
          readonly: readonly({ roles, role: [rolesMaps.role_finance] })
        },
        {
          title: "实际技术服务费",
          dataIndex: "realTechCost",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem() {
            return <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
          },
          render() {
            const { realTechCostStr } = initialValues;
            return realTechCostStr;
          },
          readonly: readonly({ roles, role: [rolesMaps.role_finance] })
        },
        {
          title: "供货价",
          dataIndex: "supplierPrice",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem() {
            return <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
          },
          render() {
            const { supplierPriceStr } = initialValues;
            return supplierPriceStr;
          },
          readonly: readonly({ roles, role: [rolesMaps.role_finance] })
        },
        {
          title: "供货总成本",
          dataIndex: "supplierCost",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem() {
            return <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
          },
          render() {
            const { supplierCostStr } = initialValues;
            return supplierCostStr;
          },
          readonly: readonly({ roles, role: [rolesMaps.role_finance] })
        },

        {
          title: "其他金额",
          dataIndex: "otherFee",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem() {
            return <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
          },
          render() {
            const { otherFeeStr } = initialValues;
            return otherFeeStr;
          },
          readonly: readonly({ roles, role: [rolesMaps.role_finance] })
        },
        {
          title: "我司应得利润",
          dataIndex: "shouldProfitStr",
          hideInSearch: false,
          hideInTable: true,
          readonly: true
        },
        {
          title: "税点金额",
          dataIndex: "taxFee",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem() {
            return <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
          },
          render() {
            const { taxFeeStr } = initialValues;
            return taxFeeStr;
          },
          readonly: readonly({ roles, role: [rolesMaps.role_finance] })
        },
        {
          title: "线下结算金额",
          dataIndex: "shouldTotalAmount",
          hideInSearch: false,
          hideInTable: true,
          readonly: true,
          render() {
            const { shouldTotalAmountStr } = initialValues;
            return shouldTotalAmountStr;
          }
        }
      ]
    },
    {
      title: "回款情况",
      valueType: "group",
      colProps: { span: 24 },
      columns: [
        {
          title: "已收付款总金额",
          dataIndex: "totalReceiptAndPayment",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem() {
            return <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
          },
          render() {
            const { totalReceiptAndPaymentStr } = initialValues;
            return totalReceiptAndPaymentStr;
          },
          readonly: readonly({ roles, role: [rolesMaps.role_finance] })
        },
        {
          title: "收付款主体",
          dataIndex: "receiptAndPayment",
          hideInSearch: false,
          hideInTable: true,
          readonly: readonly({ roles, role: [rolesMaps.role_finance] })
        },
        {
          title: "收付款日期",
          dataIndex: "receiptAndPaymentDate",
          hideInSearch: false,
          hideInTable: true,
          fieldProps: {
            style: { width: "100%" }
          },
          valueType: "date",
          readonly: readonly({ roles, role: [rolesMaps.role_finance] })
        },
        {
          title: "打款备注",
          dataIndex: "remittanceNote",
          hideInSearch: false,
          hideInTable: true,
          readonly: readonly({ roles, role: [rolesMaps.role_finance] })
        },
        {
          title: "打款截图",
          dataIndex: "remitPictureUrl",
          hideInSearch: false,
          hideInTable: true,
          readonly: readonly({ roles, role: [rolesMaps.role_finance] }),
          render() {
            const { remitPictureUrl } = initialValues;
            let url = remitPictureUrl;
            if (url) {
              try {
                let remitImg = JSON.parse(url);
                if (Array.isArray(remitImg)) {
                  url = remitImg?.[0] ?? void 0;
                }
              } catch {}
            }
            return <Image width={50} height={50} src={returnItemImgUrl(url)} />;
          },
          renderFormItem(a, b) {
            let url = initialValues?.remitPictureUrl;
            if (b.value) {
              url = b.value;
              try {
                let remitImg = JSON.parse(url);
                if (Array.isArray(remitImg)) {
                  url = remitImg?.[0] ?? void 0;
                }
              } catch {}
            }
            return <EditRSSUpload fileLength={1} value={url} />;
          }
        },
        {
          title: "未结款金额",
          dataIndex: "unPayAmount",
          hideInSearch: false,
          hideInTable: true,
          readonly: true,
          render() {
            const vals = form?.getFieldsValue();
            if (!ifEmptyObj(vals)) {
              // 不知道为什么 第一次进入 form 表单获取不到值
              return returnValueForNoValue(initialValues?.unPayAmountStr);
            }
            const { shouldTotalAmount, totalReceiptAndPayment } = vals;
            const val = (shouldTotalAmount ?? 0) - (totalReceiptAndPayment ?? 0);
            form.setFieldValue("unPayAmount", val);
            return (val / 10000).toFixed(4);
          }
        },
        {
          title: "账单状态",
          dataIndex: "status",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem() {
            return <Select options={settlementStatusOptions}></Select>;
          },
          render() {
            const { statusStr } = initialValues;
            return statusStr;
          },
          readonly: readonly({ roles, role: [rolesMaps.role_finance] })
        },
        {
          title: "问题归属",
          dataIndex: "problemAttribution",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem() {
            return <Select options={problemAttributionOptions}></Select>;
          },
          render() {
            const { problemAttributionStr } = initialValues;
            return problemAttributionStr;
          },
          readonly: readonly({ roles, role: [rolesMaps.role_finance] })
        },
        {
          title: "未结款原因",
          dataIndex: "reason",
          hideInSearch: false,
          hideInTable: true,
          readonly: readonly({ roles, role: [rolesMaps.role_finance] })
        },
        {
          title: "是否需要开票",
          dataIndex: "isMakeOut",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem() {
            return <Select options={isMakeOutOptions}></Select>;
          },
          render() {
            const { isMakeOutStr } = initialValues;
            return isMakeOutStr;
          },
          readonly: readonly({ roles, role: [rolesMaps.role_finance] })
        },
        {
          title: "易快报备注",
          dataIndex: "ykbRemark",
          hideInSearch: false,
          hideInTable: true,
          readonly: readonly({ roles, role: [rolesMaps.role_finance] })
        },
        {
          title: "开票时间",
          dataIndex: "makeOutTime",
          hideInSearch: false,
          hideInTable: true,
          fieldProps: {
            style: { width: "100%" }
          },
          valueType: "date",
          readonly: readonly({ roles, role: [rolesMaps.role_finance] })
        },
        {
          title: "开票金额",
          dataIndex: "makeOutAmount",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem() {
            return <EditInputNumber size={SIZE} divide={100} precision={2} style={{ width: "100%" }} />;
          },
          render() {
            const { makeOutAmountStr } = initialValues;
            return makeOutAmountStr;
          },
          readonly: readonly({ roles, role: [rolesMaps.role_finance] })
        },
        {
          title: "备注",
          dataIndex: "remark",
          hideInSearch: false,
          hideInTable: true,
          readonly: readonly({ roles, role: [rolesMaps.role_finance] })
        },
        {
          title: "财务反馈",
          dataIndex: "financeFeedback",
          hideInSearch: false,
          hideInTable: true,
          readonly: readonly({ roles, role: [rolesMaps.role_finance] })
        },
        {
          title: "财务反馈备注",
          dataIndex: "financeFeedbackRemark",
          hideInSearch: false,
          hideInTable: true,
          readonly: readonly({ roles, role: [rolesMaps.role_finance] })
        },
        {
          title: "合同负责人",
          dataIndex: "contractOwner",
          hideInSearch: false,
          hideInTable: true,
          readonly: readonly({ roles, role: [rolesMaps.role_finance, rolesMaps.role_business] })
        },
        {
          title: "是否提交法务",
          dataIndex: "isSubmitLegal",
          hideInSearch: false,
          hideInTable: true,
          readonly: readonly({ roles, role: [rolesMaps.role_finance, rolesMaps.role_business] })
        },
        {
          title: "预计回款日期",
          dataIndex: "expectReceiptDate",
          hideInSearch: false,
          hideInTable: true,
          fieldProps: {
            style: { width: "100%" }
          },
          valueType: "date",
          readonly: readonly({ roles, role: [rolesMaps.role_finance, rolesMaps.role_business] })
        },
        {
          title: "业务反馈备注",
          dataIndex: "bizFeedbackRemark",
          hideInSearch: false,
          hideInTable: true,
          readonly: readonly({ roles, role: [rolesMaps.role_finance, rolesMaps.role_business] })
        },
        {
          title: "法务反馈进度",
          dataIndex: "legalFeedbackProcess",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem() {
            return <Select options={legalFeedbackProcessOptions}></Select>;
          },
          render() {
            const { legalFeedbackProcessStr } = initialValues;
            return legalFeedbackProcessStr;
          },
          readonly: readonly({ roles, role: [rolesMaps.role_finance, rolesMaps.role_legal_affairs] })
        },
        {
          title: "法务备注",
          dataIndex: "legalFeedback",
          hideInSearch: false,
          hideInTable: true,
          readonly: readonly({ roles, role: [rolesMaps.role_finance, rolesMaps.role_legal_affairs] })
        },
        {
          title: "律师反馈进度",
          dataIndex: "lawFeedbackProcess",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem() {
            return <Select options={lawFeedbackProcessOptions}></Select>;
          },
          render() {
            const { lawFeedbackProcessStr } = initialValues;
            return lawFeedbackProcessStr;
          },
          readonly: readonly({ roles, role: [rolesMaps.role_finance, rolesMaps.role_lawyer] })
        },
        {
          title: "律师备注",
          dataIndex: "lawFollow",
          hideInSearch: false,
          hideInTable: true,
          readonly: readonly({ roles, role: [rolesMaps.role_finance, rolesMaps.role_lawyer] })
        }
      ]
    }
  ];
};
