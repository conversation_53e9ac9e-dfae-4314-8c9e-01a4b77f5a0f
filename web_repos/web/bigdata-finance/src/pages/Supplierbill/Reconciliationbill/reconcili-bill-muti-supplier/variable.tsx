import { DeleteBtn, filterOptionLabel, returnEllipsisTooltip, returnItemImgUrl } from "@/utils";
import { SIZE } from "@/utils/constant";
import { Select, Space, Image } from "antd";
import AnchorSelect from "@/components/AnchorSelect";
import apis from "@/services/supplierbill/reconciliationbill";
import DepartmentSelect from "@/components/DepartmentSelect";
import AuthButton from "@/wrappers/authButton";
import { btn_auths } from "../auths";
import { PageUrl } from "../index";
const columns_search = ({ settlementStatusOptions, settlePatternOptions, billtypeOptions, contractNameOptions }: any) => {
  return [
    {
      title: "对账期间",
      dataIndex: "reconciliationPeriod",
      hideInSearch: false,
      hideInTable: true
    },
    {
      title: "直播日期",
      dataIndex: "liveDate",
      hideInSearch: false,
      hideInTable: true,
      valueType: "dateRange"
    },
    {
      title: "主播名称",
      dataIndex: "anchorNames",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem() {
        return <AnchorSelect showSearch allowClear maxTagCount="responsive" mode="multiple" />;
      }
    },
    {
      title: "商品ID",
      dataIndex: "itemIds",
      hideInSearch: false,
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "商品名称",
      dataIndex: "itemName",
      hideInSearch: false,
      hideInTable: true
    },
    {
      title: "账单结算模式",
      dataIndex: "settlePatterns",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return (
          <Select
            placeholder="请选择"
            size={SIZE}
            options={settlePatternOptions}
            filterOption={filterOptionLabel}
            showSearch
            allowClear
            maxTagCount="responsive"
            mode="multiple"
          />
        );
      }
    },
    {
      title: "账单类型",
      dataIndex: "type",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return <Select placeholder="请选择" size={SIZE} options={billtypeOptions} filterOption={filterOptionLabel} showSearch allowClear />;
      }
    },
    {
      title: "财务对账人",
      dataIndex: "reconciler",
      hideInSearch: false,
      hideInTable: true
    },
    {
      title: "部门",
      dataIndex: "departmentName",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return <DepartmentSelect key="departmentName" />;
      }
    },
    {
      title: "业务对接人",
      dataIndex: "purchasePrincipal",
      hideInSearch: false,
      hideInTable: true
    },
    {
      title: "店铺名称",
      dataIndex: "shopName",
      hideInSearch: false,
      hideInTable: true
    },
    {
      title: "供应商名称",
      dataIndex: "supplierName",
      hideInSearch: false,
      hideInTable: true
    },
    {
      title: "我司合同主体",
      dataIndex: "contractName",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return <Select placeholder="请选择" size={SIZE} options={contractNameOptions} filterOption={filterOptionLabel} showSearch allowClear />;
      }
    },
    {
      title: "账单状态",
      dataIndex: "status",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return <Select placeholder="请选择" size={SIZE} options={settlementStatusOptions} filterOption={filterOptionLabel} showSearch allowClear />;
      }
    }
  ];
};

export const columns = ({
  handleEdit,
  settlePatternObj,
  typeObj,
  problemAttributionObj,
  isMakeOutObj,
  itemTypeObj,
  ...rest
}: any): Array<TableListItem> => {
  return [
    ...columns_search(rest),
    {
      title: "ID",
      dataIndex: "id",
      width: 100,
      fixed: "left",
      editable: false,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "对账期间",
      dataIndex: "reconciliationPeriod",
      width: 100,
      fixed: "left",
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "账期",
      dataIndex: "reconcileDate",
      width: 100,
      fixed: "left",
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "直播日期", dataIndex: "liveDate", fixed: "left", width: 100, hideInSearch: true },
    {
      title: "主播ID",
      dataIndex: "pid",
      width: 120,
      fixed: "left",
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "主播名称",
      dataIndex: "anchorName",
      width: 120,
      fixed: "left",
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品ID",
      dataIndex: "itemId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品名称",
      dataIndex: "itemName",
      width: 160,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品类型",
      dataIndex: "itemType",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: itemTypeObj[text] });
      }
    },
    {
      title: "账单结算模式",
      dataIndex: "settlePattern",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: settlePatternObj[text] });
      }
    },
    {
      title: "账单类型",
      dataIndex: "type",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: typeObj[text] });
      }
    },
    {
      title: "财务对账人",
      dataIndex: "reconciler",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "部门",
      dataIndex: "departmentName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "业务对接人",
      dataIndex: "purchasePrincipal",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "店铺ID",
      dataIndex: "shopId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "店铺名称",
      dataIndex: "shopName",
      width: 160,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "供应商名称",
      dataIndex: "supplierName",
      width: 160,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "我司合同主体",
      dataIndex: "contractName",
      width: 160,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "出具账单日期",
      dataIndex: "accountBillDate",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "账单收入金额", dataIndex: "billIncomeAmountStr", width: 100, hideInSearch: true },
    { title: "账单退款金额", dataIndex: "billRefundAmountStr", width: 100, hideInSearch: true },
    { title: "超售后退款金额", dataIndex: "postSaleRefundAmountStr", width: 120, hideInSearch: true },
    { title: "总退款金额", dataIndex: "afterSaleFeeStr", width: 100, hideInSearch: true },
    { title: "销售收入", dataIndex: "netGmvStr", width: 100, hideInSearch: true },
    { title: "总佣金比例", dataIndex: "totalCommissionRateStr", width: 100, hideInSearch: true },
    { title: "总佣金金额", dataIndex: "totalCommissionAmountStr", width: 100, hideInSearch: true },
    { title: "达人佣金金额", dataIndex: "anchorCommissionAmountStr", width: 100, hideInSearch: true },
    { title: "达人佣金追回金额", dataIndex: "anchorCommissionWithdrawAmountStr", width: 100, hideInSearch: true },
    { title: "线上佣金金额", dataIndex: "onlineCommissionStr", width: 100, hideInSearch: true },
    { title: "结算件数", dataIndex: "settleVolume", width: 100, hideInSearch: true },
    { title: "退款件数", dataIndex: "refundVolume", width: 100, hideInSearch: true },
    { title: "实际结算件数", dataIndex: "realSettleVolume", width: 100, hideInSearch: true },
    { title: "快手技术服务费", dataIndex: "platformCostStr", width: 120, hideInSearch: true },
    { title: "快手技术服务费追回", dataIndex: "platformCostWithdrawStr", width: 100, hideInSearch: true },
    { title: "实际技术服务费", dataIndex: "realTechCostStr", width: 120, hideInSearch: true },
    { title: "供货价", dataIndex: "supplierPriceStr", width: 100, hideInSearch: true },
    { title: "供货总成本", dataIndex: "supplierCostStr", width: 100, hideInSearch: true },
    { title: "其他金额", dataIndex: "otherFeeStr", width: 100, hideInSearch: true },
    { title: "我司应得利润", dataIndex: "shouldProfitStr", width: 100, hideInSearch: true },
    { title: "税点金额", tooltip: "收款正数表示、付款负数表示", dataIndex: "taxFeeStr", width: 100, hideInSearch: true },
    { title: "线下结算金额", dataIndex: "shouldTotalAmountStr", width: 100, hideInSearch: true },
    {
      title: "收付款主体",
      dataIndex: "receiptAndPayment",
      width: 160,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "收付款日期", dataIndex: "receiptAndPaymentDate", width: 100, hideInSearch: true },
    { title: "已收付款总金额", dataIndex: "totalReceiptAndPaymentStr", width: 120, hideInSearch: true },
    {
      title: "打款备注",
      dataIndex: "remittanceNote",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "打款截图",
      dataIndex: "remitPictureUrl",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { remitPictureUrl } = record;
        return <Image width={50} height={50} src={returnItemImgUrl(remitPictureUrl)} />;
      }
    },
    { title: "未结款金额", dataIndex: "unPayAmountStr", width: 120, hideInSearch: true },
    {
      title: "账单状态",
      dataIndex: "statusStr",
      width: 160,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "问题归属",
      dataIndex: "problemAttribution",
      width: 160,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: problemAttributionObj[text] });
      }
    },
    {
      title: "未结款原因",
      dataIndex: "reason",
      width: 160,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "是否需要开票",
      dataIndex: "isMakeOut",
      width: 160,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: isMakeOutObj[text] });
      }
    },
    {
      title: "易快报备注",
      dataIndex: "ykbRemark",
      width: 160,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "开票时间", dataIndex: "makeOutTime", width: 100, hideInSearch: true },
    { title: "开票金额", dataIndex: "makeOutAmountStr", width: 100, hideInSearch: true },
    {
      title: "备注",
      dataIndex: "remark",
      width: 160,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "财务反馈",
      dataIndex: "financeFeedback",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "财务反馈备注",
      dataIndex: "financeFeedbackRemark",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "合同负责人",
      dataIndex: "contractOwner",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "是否提交法务", dataIndex: "isSubmitLegal", width: 100, hideInSearch: true },
    { title: "预计回款日期", dataIndex: "expectReceiptDate", width: 100, hideInSearch: true },
    { title: "业务反馈备注", dataIndex: "bizFeedbackRemark", width: 100, hideInSearch: true },
    { title: "法务反馈进度", dataIndex: "legalFeedbackProcessStr", width: 100, hideInSearch: true },
    {
      title: "法务备注",
      dataIndex: "legalFeedback",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "律师反馈进度", dataIndex: "lawFeedbackProcessStr", width: 100, hideInSearch: true },
    {
      title: "律师备注",
      dataIndex: "lawFollow",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "操作",
      valueType: "option",
      align: "center",
      fixed: "right",
      width: 100,
      render: (text: string, record: any, index: number, action: any) => {
        const { id } = record;
        return (
          <Space>
            <AuthButton pageUrl={PageUrl} code={btn_auths.btn_reconcili_bill_muti_supplier_edit}>
              <a onClick={() => handleEdit(record)}>编辑</a>
            </AuthButton>
            <AuthButton pageUrl={PageUrl} code={btn_auths.btn_reconcili_bill_muti_supplier_delete}>
              <DeleteBtn params={{ ids: [id] }} btnProps={{ type: "link" }} action={action} api={apis.supplierDelete} />
            </AuthButton>
          </Space>
        );
      }
    }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      align: "center",
      ...item,
      title: returnEllipsisTooltip({ title })
    };
  });
};
