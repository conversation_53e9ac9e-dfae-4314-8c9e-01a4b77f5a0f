import React, { useEffect, useState, useContext } from "react";
import { Form, DrawerProps, Space, Button } from "antd";
import { BetaSchemaForm } from "@ant-design/pro-components";
import styles from "@/components/AdvanceSearch/index.less";
import { columns } from "./variable";
import { formGrid } from "@/components/AdvanceSearch";
import { SIZE } from "@/utils/constant";
import apis from "@/services/supplierbill/reconciliationbill";
import dayjs from "dayjs";
import { FieldsContext } from "../../index";

interface IProps extends DrawerProps {
  record?: Record<string, any>;
  onClose?: () => void;
  onFresh?: () => void;
  settlePatternOptions: IOptions[];
  typeOptions: IOptions[];
  settlementStatusOptions: IOptions[];
  problemAttributionOptions: IOptions[];
  isMakeOutOptions: IOptions[];
  itemTypeOptions: IOptions[];
  legalFeedbackProcessOptions: IOptions[];
  lawFeedbackProcessOptions: IOptions[];
  contractNameOptions: IOptions[];
}

const Res: React.FC<IProps> = props => {
  const {
    onClose,
    onFresh,
    open,
    record,
    settlePatternOptions,
    typeOptions,
    settlementStatusOptions,
    problemAttributionOptions,
    isMakeOutOptions,
    itemTypeOptions,
    legalFeedbackProcessOptions,
    lawFeedbackProcessOptions,
    contractNameOptions
  } = props;
  const { roles } = useContext(FieldsContext);
  const [_open, setOpen] = useState(false);
  const [form] = Form.useForm();
  const handleInitialValue = data => {
    const { liveDate, accountBillDate, makeOutTime, receiptAndPaymentDate, expectReceiptDate, ...rest } = data;
    if (rest.reconciliationPeriod) {
      [rest.gmtReconcilePeriodStart, rest.gmtReconcilePeriodEnd] = rest.reconciliationPeriod
        ?.split("-")
        .map(item => dayjs(item).format("YYYY-MM-DD"));
      rest.reconciliationPeriod = rest.reconciliationPeriod?.split("-").map(item => dayjs(item));
    }
    if (liveDate) {
      rest.liveDate = dayjs(liveDate);
    }
    if (accountBillDate) {
      rest.accountBillDate = dayjs(accountBillDate);
    }
    if (makeOutTime) {
      rest.makeOutTime = dayjs(makeOutTime);
    }
    if (receiptAndPaymentDate) {
      rest.receiptAndPaymentDate = dayjs(receiptAndPaymentDate);
    }
    if (expectReceiptDate) {
      rest.expectReceiptDate = dayjs(expectReceiptDate);
    }
    return rest;
  };
  const [initialValues, setInitialValues] = useState(() => handleInitialValue(record));
  const handleClose = () => {
    setOpen(false);
    form.resetFields();
  };
  const handleOk = () => {
    form.validateFields().then(async values => {
      const {
        accountBillDate,
        liveDate,
        reconciliationPeriod = [],
        makeOutTime,
        expectReceiptDate,
        receiptAndPaymentDate,
        remitPictureUrl,
        ...rest
      } = values;
      if (reconciliationPeriod) {
        const [start, end] = reconciliationPeriod;
        rest.reconciliationPeriod = [dayjs(start).format("YYYY/MM/DD"), dayjs(end).format("YYYY/MM/DD")].join("-");
      }
      if (remitPictureUrl) {
        try {
          let remitImg = JSON.parse(remitPictureUrl);
          if (Array.isArray(remitImg)) {
            rest.remitPictureUrl = remitImg?.[0] ?? void 0;
          }
        } catch {
          rest.remitPictureUrl = remitPictureUrl;
        }
      }
      rest.accountBillDate = dayjs(accountBillDate).format("YYYY-MM-DD");
      rest.liveDate = dayjs(liveDate).format("YYYY-MM-DD");
      rest.makeOutTime = dayjs(makeOutTime).format("YYYY-MM-DD");
      rest.expectReceiptDate = dayjs(expectReceiptDate).format("YYYY-MM-DD");
      rest.receiptAndPaymentDate = dayjs(receiptAndPaymentDate).format("YYYY-MM-DD");
      const { status } = await apis.supplierUpdate({ ...record, ...rest });
      if (status) {
        onClose?.();
        onFresh?.();
      }
    });
  };
  const columnsProps = {
    form,
    roles,
    initialValues,
    settlePatternOptions,
    typeOptions,
    settlementStatusOptions,
    problemAttributionOptions,
    isMakeOutOptions,
    itemTypeOptions,
    legalFeedbackProcessOptions,
    lawFeedbackProcessOptions,
    contractNameOptions
  };
  const returnTitle = (
    <div className={styles["title"]}>
      <div>编辑多供应商对账单</div>
      <Space>
        <Button key="cancel" size={SIZE} onClick={() => handleClose()}>
          取消
        </Button>
        <Button key="ok" size={SIZE} type="primary" onClick={() => handleOk()}>
          保存
        </Button>
      </Space>
    </div>
  );
  useEffect(() => {
    if (open) {
      setOpen(open);
      form.setFieldsValue({ ...initialValues });
    }
  }, [form, open, initialValues]);
  useEffect(() => {
    setInitialValues(handleInitialValue(record));
  }, [record]);
  return (
    <>
      <BetaSchemaForm
        form={form}
        open={_open}
        size={SIZE}
        onOpenChange={open => {
          setOpen(open);
          if (!open) {
            form.resetFields();
          }
        }}
        {...formGrid}
        title={returnTitle}
        className={styles["advance-search"]}
        layout="horizontal"
        layoutType="DrawerForm"
        columns={columns(columnsProps)}
        submitter={false}
      />
    </>
  );
};

export default React.memo(Res);
