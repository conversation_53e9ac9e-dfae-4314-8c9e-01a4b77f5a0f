import React, { useState, useRef, useEffect, useContext } from "react";
import { Button } from "antd";
import TableList from "@/components/TableList";
import { SIZE } from "@/utils/constant";
import { columns } from "./variable";
import apis from "@/services/supplierbill/reconciliationbill";
import apis2 from "@/services/supplierbill/reconciliationcheduling";
import { pushExportHistory, returnOptions, splitSpaceComma } from "@/utils";
import UploadExcelModal from "@/components/UploadModal";
import useCommonOptions from "@/hooks/useCommonOptions";
import dayjs from "dayjs";
import Detail from "./detail";
import { FieldsContext, PageUrl } from "../index";
import { btn_auths } from "../auths";
import AuthButton from "@/wrappers/authButton";
const Res = () => {
  const { contractNameOptions, billtypeOptions, settlementStatusOptions } = useContext(FieldsContext);
  const [pageParmas, setPageParmas] = useState<any>({});
  const [settlePatternOptions, settlePatternObj] = useCommonOptions({ dimName: "账单结算模式" });
  const [typeOptions, typeObj] = useCommonOptions({ dimName: "供应商账单类型" });
  const [problemAttributionOptions, problemAttributionObj] = useCommonOptions({ dimName: "问题归属" });
  const [isMakeOutOptions, isMakeOutObj] = useCommonOptions({ dimName: "是否需要开票" });
  const [itemTypeOptions, itemTypeObj] = useCommonOptions({ dimName: "商品类型" });
  const [legalFeedbackProcessOptions] = useCommonOptions({ dimName: "法务反馈进度" });
  const [lawFeedbackProcessOptions] = useCommonOptions({ dimName: "律师反馈进度" });
  const [reconcilerOptions, setReconcilerOptions] = useState<IOptions[]>([]);
  const actionsRef = useRef<any>();
  const tableFormRef = useRef<any>();
  const [showUpload, setshowUpload] = useState(false);
  const [detailProps, setDetailProps] = useState<any>({
    open: false,
    onFresh: () => {
      actionsRef.current?.reload();
    },
    onClose: () => {
      setDetailProps({ ...detailProps, open: false });
    }
  });
  // 上传excel modal属性
  const UploadReconModalProps = {
    title: "上传对账单",
    visible: showUpload,
    targetType: 0,
    api: apis.supplierUpload,
    message: (
      <>
        <span>1. 请参考模板中数据格式进行上传</span>
        <br />
        <span>2. 黄色背景为必填项</span>
        <br />
        <span>3. 文件数据会覆盖线上数据，请确保数据记录的完整性</span>
        <br />
      </>
    ),
    tempUrl: "https://s.xinc818.com/files/webcim5p5d66zfk1bih/对账单-多供应商对账单模板.xlsx",
    fresh: () => {
      actionsRef.current.reload();
    },
    close: () => {
      setshowUpload(false);
    }
  };
  const fetchReconciles = async () => {
    const { entry, status } = await apis2.getFinanceReconciles();
    if (status) {
      setReconcilerOptions(returnOptions(entry));
    } else {
      setReconcilerOptions([]);
    }
  };
  const handlePageParmas = (params: any) => {
    const { current, liveDate, itemIds, ...rest } = params;
    if (liveDate) {
      const [startLiveDate, endLiveDate] = liveDate;
      rest.startLiveDate = dayjs(startLiveDate).format("YYYY-MM-DD");
      rest.endLiveDate = dayjs(endLiveDate).format("YYYY-MM-DD");
    }
    rest.itemIds = splitSpaceComma(itemIds);
    return { ...rest, pageNo: current };
  };

  const handleEdit = async record => {
    setDetailProps({
      ...detailProps,
      settlePatternOptions,
      typeOptions,
      problemAttributionOptions,
      isMakeOutOptions,
      itemTypeOptions,
      legalFeedbackProcessOptions,
      lawFeedbackProcessOptions,
      contractNameOptions,
      settlementStatusOptions,
      open: true,
      record: { ...record, $_fetch: +new Date() }
    });
  };

  const columnsProps = {
    handleEdit,
    settlePatternOptions,
    settlePatternObj,
    typeOptions,
    typeObj,
    problemAttributionOptions,
    problemAttributionObj,
    isMakeOutOptions,
    isMakeOutObj,
    itemTypeObj,
    reconcilerOptions,
    contractNameOptions,
    billtypeOptions,
    settlementStatusOptions
  };
  const handleExport = async () => {
    const { status } = await apis.supplierPageDownload(pageParmas);
    if (status) {
      pushExportHistory();
    }
  };
  const actions = () => {
    return [
      <AuthButton key="upload" pageUrl={PageUrl} code={btn_auths.btn_reconcili_bill_muti_supplier_upload}>
        <Button type="primary" onClick={() => setshowUpload(true)} size={SIZE}>
          上传
        </Button>
      </AuthButton>,
      <AuthButton key="export" pageUrl={PageUrl} code={btn_auths.btn_reconcili_bill_muti_supplier_export}>
        <Button onClick={handleExport} size="small">
          导出
        </Button>
      </AuthButton>
    ];
  };
  const handleChange = p => {
    const params = tableFormRef.current?.getFieldsValue() || {};
    const res = handlePageParmas?.({ ...p, ...params });
    return res;
  };

  useEffect(() => {
    fetchReconciles();
  }, []);
  return (
    <>
      <TableList
        headerTitle="多供应商对账单"
        actionRef={actionsRef}
        formRef={tableFormRef}
        columns={columns(columnsProps)}
        defaultExpandAll={{ deep: 0 }}
        api={apis.supplierPage}
        scroll={{ y: "calc(100vh - 510px)" }}
        summaryApi={apis.supplierPageSummary}
        preFetch={handleChange}
        paramsChange={setPageParmas}
        rowKey="id"
        toolbar={{
          actions: actions()
        }}
      />

      {showUpload && <UploadExcelModal {...UploadReconModalProps} />}
      {detailProps.open ? <Detail {...detailProps} /> : null}
    </>
  );
};

export default React.memo(Res);
