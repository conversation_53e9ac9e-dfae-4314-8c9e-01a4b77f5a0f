import EditInputNumber from "@/components/EditInputNumber";
import EditInputNumberPercent from "@/components/EditInputNumberPercent";
import EditRSSUpload from "@/components/EditRSSUpload";
import TableList from "@/components/TableList";
import { filterOptionLabel, returnEllipsisTitle, returnEllipsisTooltip, returnItemImgUrl, returnValueForNoValue } from "@/utils";
import { SIZE } from "@/utils/constant";
import { Col, Image, Select, Space } from "antd";
import Edit from "./edit";
const sku_columns = ({}: any): TableListItem[] => {
  return [
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      hideInSearch: true,
      fixed: "left",
      render: (_, record, index) => {
        return index + 1;
      }
    },
    {
      title: "SKUID",
      dataIndex: "itemSkuId",
      width: 100,
      hideInSearch: true,
      renderText(_, record) {
        const { itemSkuId } = record;
        return returnEllipsisTitle({ title: itemSkuId });
      }
    },
    {
      title: "SKU名称",
      dataIndex: "itemSkuName",
      width: 100,
      hideInSearch: true,
      render(text, record) {
        const { itemSkuName } = record;
        return returnEllipsisTitle({ title: itemSkuName });
      }
    },
    {
      title: "账单收入金额",
      dataIndex: "totalIncomeAmount",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { totalIncomeAmountStr } = record;
        return totalIncomeAmountStr;
      }
    },
    {
      title: "账单退款金额",
      dataIndex: "totalOrderRefundAmount",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { totalOrderRefundAmountStr } = record;
        return totalOrderRefundAmountStr;
      }
    },
    {
      title: "超售后退款金额",
      dataIndex: "totalOverRefundAmount",
      width: 120,
      hideInSearch: true,
      render(_, record) {
        const { totalOverRefundAmountStr } = record;
        return totalOverRefundAmountStr;
      }
    },
    {
      title: "总退款金额",
      dataIndex: "totalRefundAmount",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { totalRefundAmountStr } = record;
        return totalRefundAmountStr;
      }
    },
    {
      title: "销售收入",
      dataIndex: "salesRevenueAmount",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { salesRevenueAmountStr } = record;
        return salesRevenueAmountStr;
      }
    },
    {
      title: "总佣金比例",
      dataIndex: "commissionRate",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { commissionRateStr } = record;
        return commissionRateStr;
      }
    },
    {
      title: "总佣金金额",
      dataIndex: "totalCommissionAmount",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { totalCommissionAmountStr } = record;
        return totalCommissionAmountStr;
      }
    },
    {
      title: "达人佣金金额",
      dataIndex: "pidCommissionAmount",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { pidCommissionAmountStr } = record;
        return pidCommissionAmountStr;
      }
    },
    {
      title: "达人佣金追回金额",
      dataIndex: "pidBackCommissionAmount",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { pidBackCommissionAmountStr } = record;
        return pidBackCommissionAmountStr;
      }
    },
    {
      title: "线上佣金金额",
      dataIndex: "onlineCommissionAmount",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { onlineCommissionAmountStr } = record;
        return onlineCommissionAmountStr;
      }
    },
    {
      title: "结算件数",
      dataIndex: "salesVolume",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { salesVolumeStr } = record;
        return salesVolumeStr;
      }
    },
    {
      title: "退款件数",
      dataIndex: "refundItemVolume",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { refundItemVolumeStr } = record;
        return refundItemVolumeStr;
      }
    },
    {
      title: "实际结算件数",
      dataIndex: "actualSettleItemVolume",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { actualSettleItemVolumeStr } = record;
        return actualSettleItemVolumeStr;
      }
    },
    {
      title: "快手技术服务费",
      dataIndex: "techServiceAmount",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { techServiceAmountStr } = record;
        return techServiceAmountStr;
      }
    },
    {
      title: "快手技术服务费追回",
      dataIndex: "techServiceBackAmount",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { techServiceBackAmountStr } = record;
        return techServiceBackAmountStr;
      }
    },
    {
      title: "实际技术服务费",
      dataIndex: "actualTechServiceAmount",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { actualTechServiceAmountStr } = record;
        return actualTechServiceAmountStr;
      }
    },
    {
      title: "供货价",
      dataIndex: "supplyPrice",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { supplyPriceStr } = record;
        return supplyPriceStr;
      }
    },
    {
      title: "供货总成本",
      dataIndex: "totalSupplyCost",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { totalSupplyCostStr } = record;
        return totalSupplyCostStr;
      }
    },
    {
      title: "线下结算金额",
      dataIndex: "offlineSettleAmount",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { offlineSettleAmountStr } = record;
        return offlineSettleAmountStr;
      }
    }
  ].map(item => {
    const { title } = item;
    return {
      align: "center",
      ...item,
      title: returnEllipsisTooltip({ title })
    };
  });
};
export const rolesMaps = {
  role_lawyer: "律师",
  role_legal_affairs: "法务",
  role_business: "商务",
  role_finance: "财务"
};

export const readonly = ({ type, roles = [], role }: { type?: string; roles?: any[]; role?: string[] }) => {
  if (type === "detail") return true;
  if (role) {
    return roles?.filter(item => role.includes(item.name))?.length > 0 ? false : true;
  }
  return false;
};
export const columns = ({
  form,
  roles,
  initialValues,
  type,
  settlementStatusOptions,
  legalSubmitStatusOptions,
  problemAttributionOptions,
  isMakeOutbutionOptions,
  legalFeedbackScheduleOptions,
  lawyerFeedbackScheduleOptions,
  afterEditSupplier
}: any): TableListItem[] => {
  return [
    {
      title: "基本信息",
      valueType: "group",
      colProps: { span: 24 },
      columns: [
        {
          title: "直播日期",
          dataIndex: "liveDate",
          readonly: true,
          hideInSearch: false,
          hideInTable: true
        },
        {
          title: "主播ID",
          dataIndex: "anchorId",
          readonly: true,
          hideInSearch: false,
          hideInTable: true
        },
        {
          title: "主播",
          dataIndex: "anchorName",
          readonly: true,
          hideInSearch: false,
          hideInTable: true
        },
        {
          title: "商品ID",
          dataIndex: "itemId",
          readonly: true,
          hideInSearch: false,
          hideInTable: true
        },
        {
          title: "商品名称",
          dataIndex: "itemTitle",
          readonly: true,
          hideInTable: true
        },
        {
          title: "商品类型",
          dataIndex: "itemTypeStr",
          hideInSearch: false,
          readonly: true,
          hideInTable: true
        },
        {
          title: "部门",
          dataIndex: "bigDepartment",
          hideInSearch: false,
          readonly: true,
          hideInTable: true
        },
        {
          title: "业务对接人",
          dataIndex: "purchasePrincipal",
          hideInSearch: false,
          readonly: true,
          hideInTable: true
        },
        {
          title: "店铺ID",
          dataIndex: "shopId",
          hideInSearch: false,
          readonly: true,
          hideInTable: true
        },
        {
          title: "店铺名称",
          dataIndex: "shopName",
          hideInSearch: false,
          readonly: true,
          hideInTable: true
        }
      ]
    },
    {
      title: "账单信息",
      valueType: "group",
      colProps: { span: 24 },
      columns: [
        {
          title: "对账期间",
          dataIndex: "reconciledPeriod",
          readonly: true,
          hideInSearch: false,
          hideInTable: true,
          render() {
            const { gmtReconcilePeriodStart, gmtReconcilePeriodEnd } = initialValues;
            return `${returnValueForNoValue(gmtReconcilePeriodStart)} 至 ${returnValueForNoValue(gmtReconcilePeriodEnd)}`;
          }
        },
        {
          title: "账期",
          dataIndex: "periodStr",
          readonly: true,
          hideInSearch: false,
          hideInTable: true
        },
        {
          title: "账单结算模式",
          dataIndex: "billSettlePatternStr",
          readonly: true,
          hideInSearch: false,
          hideInTable: true
        },
        {
          title: "账单类型",
          dataIndex: "billTypeStr",
          readonly: true,
          hideInSearch: false,
          hideInTable: true
        },
        {
          title: "财务对账人",
          dataIndex: "financeReconcile",
          readonly: true,
          hideInTable: true
        },
        {
          title: "供应商名称",
          dataIndex: "supplierName",
          hideInSearch: false,
          readonly: true,
          hideInTable: true,
          render() {
            const { statementReconcileId } = initialValues;
            const { supplierName } = form.getFieldsValue();
            return <Edit supplierName={supplierName} statementReconcileId={statementReconcileId} form={form} afterEditSupplier={afterEditSupplier} />;
          }
        },
        {
          title: "我司合同主体",
          dataIndex: "contractName",
          readonly: true,
          hideInSearch: false,
          hideInTable: true
        },
        {
          title: "出具账单日期",
          dataIndex: "gmtIssueBill",
          hideInSearch: false,
          hideInTable: true,
          readonly: readonly({ type, roles, role: [rolesMaps.role_finance] }),
          fieldProps: {
            style: { width: "100%" }
          },
          valueType: "date"
        },

        {
          title: "账单收入金额",
          dataIndex: "totalIncomeAmount",
          hideInSearch: false,
          hideInTable: true,
          readonly: readonly({ type, roles, role: [rolesMaps.role_finance] }),
          render() {
            const { totalIncomeAmountStr } = initialValues;
            return totalIncomeAmountStr;
          },
          renderFormItem() {
            return <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
          }
        },
        {
          title: "账单退款金额",
          dataIndex: "totalOrderRefundAmount",
          hideInSearch: false,
          hideInTable: true,
          readonly: readonly({ type, roles, role: [rolesMaps.role_finance] }),
          render() {
            const { totalOrderRefundAmountStr } = initialValues;
            return totalOrderRefundAmountStr;
          },
          renderFormItem() {
            return <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
          }
        },
        {
          title: "超售后退款金额",
          dataIndex: "totalOverRefundAmount",
          hideInSearch: false,
          hideInTable: true,
          readonly: readonly({ type, roles, role: [rolesMaps.role_finance] }),
          render() {
            const { totalOverRefundAmountStr } = initialValues;
            return totalOverRefundAmountStr;
          },
          renderFormItem() {
            return <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
          }
        },
        {
          title: "总退款金额",
          dataIndex: "totalRefundAmount",
          hideInSearch: false,
          hideInTable: true,
          // readonly: readonly({ type, roles, role: [rolesMaps.role_finance] }),
          readonly: true,
          render() {
            const vals = form.getFieldsValue();
            const { totalOrderRefundAmount, totalOverRefundAmount } = vals;
            const val = (totalOrderRefundAmount ?? 0) + (totalOverRefundAmount ?? 0);
            form.setFieldValue("totalRefundAmount", val);
            return (val / 10000).toFixed(4);
          }
          // renderFormItem() {
          //   return <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
          // }
        },
        {
          title: "销售收入",
          dataIndex: "salesRevenueAmount",
          hideInSearch: false,
          hideInTable: true,
          // readonly: readonly({ type, roles, role: [rolesMaps.role_finance] }),
          readonly: true,
          render() {
            const vals = form.getFieldsValue();
            const { totalIncomeAmount, totalRefundAmount } = vals;
            const val = (totalIncomeAmount ?? 0) - (totalRefundAmount ?? 0);
            form.setFieldValue("salesRevenueAmount", val);
            return (val / 10000).toFixed(4);
          },
          renderFormItem() {
            return <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
          }
        },
        {
          title: "总佣金比例",
          dataIndex: "commissionRate",
          hideInSearch: false,
          hideInTable: true,
          readonly: readonly({ type, roles, role: [rolesMaps.role_finance] }),
          render() {
            const { commissionRateStr } = initialValues;
            return commissionRateStr;
          },
          renderFormItem() {
            return <EditInputNumberPercent formatter={value => `${value}%`} style={{ width: "100%" }} step={1} placeholder="总佣金比例" />;
          }
        },
        {
          title: "总佣金金额",
          dataIndex: "totalCommissionAmount",
          hideInSearch: false,
          hideInTable: true,
          // readonly: readonly({ type, roles, role: [rolesMaps.role_finance] }),
          readonly: true,
          render() {
            const vals = form.getFieldsValue();
            const { salesRevenueAmount, commissionRate } = vals;
            let val = +((salesRevenueAmount ?? 0) * (commissionRate ?? 0)).toFixed(0);
            form.setFieldValue("totalCommissionAmount", val);
            return (val / 10000).toFixed(4);
          }
          // renderFormItem() {
          //   return <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
          // }
        },
        {
          title: "达人佣金金额",
          dataIndex: "pidCommissionAmount",
          hideInSearch: false,
          hideInTable: true,
          readonly: readonly({ type, roles, role: [rolesMaps.role_finance] }),
          render() {
            const { pidCommissionAmountStr } = initialValues;
            return pidCommissionAmountStr;
          },
          renderFormItem() {
            return <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
          }
        },
        {
          title: "达人佣金追回金额",
          dataIndex: "pidBackCommissionAmount",
          hideInSearch: false,
          hideInTable: true,
          readonly: readonly({ type, roles, role: [rolesMaps.role_finance] }),
          render() {
            const { pidBackCommissionAmountStr } = initialValues;
            return pidBackCommissionAmountStr;
          },
          renderFormItem() {
            return <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
          }
        },
        {
          title: "线上佣金金额",
          dataIndex: "onlineCommissionAmount",
          hideInSearch: false,
          hideInTable: true,
          // readonly: readonly({ type, roles, role: [rolesMaps.role_finance] }),
          readonly: true,
          render() {
            const vals = form.getFieldsValue();
            const { pidCommissionAmount, pidBackCommissionAmount } = vals;
            const val = (pidCommissionAmount ?? 0) - (pidBackCommissionAmount ?? 0);
            form.setFieldValue("onlineCommissionAmount", val);
            return (val / 10000).toFixed(4);
          },
          renderFormItem() {
            return <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
          }
        },
        {
          title: "结算件数",
          dataIndex: "salesVolume",
          hideInSearch: false,
          hideInTable: true,
          readonly: readonly({ type, roles, role: [rolesMaps.role_finance] }),
          render() {
            const { salesVolumeStr } = initialValues;
            return salesVolumeStr;
          },
          renderFormItem() {
            return <EditInputNumber size={SIZE} divide={1} precision={0} style={{ width: "100%" }} />;
          }
        },
        {
          title: "退款件数",
          dataIndex: "refundItemVolume",
          hideInSearch: false,
          hideInTable: true,
          readonly: readonly({ type, roles, role: [rolesMaps.role_finance] }),
          render() {
            const { refundItemVolumeStr } = initialValues;
            return refundItemVolumeStr;
          },
          renderFormItem() {
            return <EditInputNumber size={SIZE} divide={1} precision={0} style={{ width: "100%" }} />;
          }
        },
        {
          title: "实际结算件数",
          dataIndex: "actualSettleItemVolume",
          hideInSearch: false,
          hideInTable: true,
          // readonly: readonly({ type, roles, role: [rolesMaps.role_finance] }),
          readonly: true,
          render() {
            const vals = form.getFieldsValue();
            const { salesVolume, refundItemVolume } = vals;
            const val = (salesVolume ?? 0) - (refundItemVolume ?? 0);
            form.setFieldValue("actualSettleItemVolume", val);
            return val.toFixed(0);
          },
          renderFormItem() {
            return <EditInputNumber size={SIZE} divide={1} precision={0} style={{ width: "100%" }} />;
          }
        },
        {
          title: "快手技术服务费",
          dataIndex: "techServiceAmount",
          hideInSearch: false,
          hideInTable: true,
          readonly: readonly({ type, roles, role: [rolesMaps.role_finance] }),
          render() {
            const { techServiceAmountStr } = initialValues;
            return techServiceAmountStr;
          },
          renderFormItem() {
            return <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
          }
        },
        {
          title: "快手技术服务费追回",
          dataIndex: "techServiceBackAmount",
          hideInSearch: false,
          hideInTable: true,
          readonly: readonly({ type, roles, role: [rolesMaps.role_finance] }),
          render() {
            const { techServiceBackAmountStr } = initialValues;
            return techServiceBackAmountStr;
          },
          renderFormItem() {
            return <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
          }
        },
        {
          title: "实际技术服务费",
          dataIndex: "actualTechServiceAmount",
          hideInSearch: false,
          hideInTable: true,
          // readonly: readonly({ type, roles, role: [rolesMaps.role_finance] }),
          readonly: true,
          render() {
            // const { actualTechServiceAmountStr } = initialValues;
            // return actualTechServiceAmountStr;
            const vals = form.getFieldsValue();
            const { techServiceAmount, techServiceBackAmount } = vals;
            const val = (techServiceAmount ?? 0) - (techServiceBackAmount ?? 0);
            form.setFieldValue("actualTechServiceAmount", val);
            return (val / 10000).toFixed(4);
          },
          renderFormItem() {
            return <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
          }
        },
        {
          title: "供货价",
          dataIndex: "supplyPrice",
          hideInSearch: false,
          hideInTable: true,
          readonly: readonly({ type, roles, role: [rolesMaps.role_finance] }),
          render() {
            const { supplyPriceStr } = initialValues;
            return supplyPriceStr;
          },
          renderFormItem() {
            return <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
          }
        },
        {
          title: "供货总成本",
          dataIndex: "totalSupplyCost",
          hideInSearch: false,
          hideInTable: true,
          // readonly: readonly({ type, roles, role: [rolesMaps.role_finance] }),
          readonly: true,
          render() {
            // const { totalSupplyCostStr } = initialValues;
            // return totalSupplyCostStr;
            const vals = form.getFieldsValue();
            const { supplyPrice, actualSettleItemVolume } = vals;
            const val = (supplyPrice ?? 0) * (actualSettleItemVolume ?? 0);
            form.setFieldValue("totalSupplyCost", val);
            return (val / 10000).toFixed(4);
          },
          renderFormItem() {
            return <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
          }
        },

        {
          title: "其他金额",
          dataIndex: "otherAmount",
          readonly: readonly({ type, roles, role: [rolesMaps.role_finance] }),
          hideInSearch: false,
          hideInTable: true,
          render() {
            const { otherAmountStr } = initialValues;
            return otherAmountStr;
          },
          renderFormItem() {
            return <EditInputNumber divide={10000} precision={4} style={{ width: "100%" }} />;
          }
        },
        {
          title: "我司应得利润",
          dataIndex: "shouldProfit",
          hideInSearch: false,
          hideInTable: true,
          readonly: readonly({ type, roles, role: [rolesMaps.role_finance] }),
          render() {
            const { shouldProfitStr } = initialValues;
            return shouldProfitStr;
          },
          renderFormItem() {
            return <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
          }
        },
        {
          title: "税点金额",
          dataIndex: "taxFee",
          hideInSearch: false,
          hideInTable: true,
          readonly: readonly({ type, roles, role: [rolesMaps.role_finance] }),
          render() {
            const { taxFeeStr } = initialValues;
            return taxFeeStr;
          },
          renderFormItem() {
            return <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
          }
        },
        {
          /**
           * 用户编辑
           * 保存时，不同模式不同公式进行校验，弹出提示
           */
          title: "线下结算金额",
          dataIndex: "offlineSettleAmount",
          hideInSearch: false,
          hideInTable: true,
          readonly: readonly({ type, roles, role: [rolesMaps.role_finance] }),
          render() {
            const { offlineSettleAmountStr } = initialValues;
            return offlineSettleAmountStr;
          },
          renderFormItem() {
            return <EditInputNumber size={SIZE} divide={10000} precision={4} style={{ width: "100%" }} />;
          }
        }
      ]
    },
    {
      title: "SKU明细",
      valueType: "group",
      colProps: { span: 24 },
      columns: [
        {
          colProps: { span: 24 },
          ignoreFormItem: true,
          renderFormItem() {
            return (
              <Col span={24}>
                <TableList columns={sku_columns({})} dataSource={initialValues?.itemSkuList || []} search={false} pagination={false} />
              </Col>
            );
          }
        }
      ]
    },
    {
      title: "回款情况",
      valueType: "group",
      colProps: { span: 24 },
      columns: [
        {
          title: "已收付款总金额",
          dataIndex: "receiptAndPayTotal",
          hideInSearch: false,
          readonly: readonly({ type, roles, role: [rolesMaps.role_finance] }),
          hideInTable: true,
          render() {
            const { receiptAndPayTotalStr } = initialValues;
            return receiptAndPayTotalStr;
          },
          renderFormItem() {
            return <EditInputNumber divide={10000} precision={4} style={{ width: "100%" }} />;
          }
        },
        {
          title: "收付款主体",
          dataIndex: "receiptAndPaySubject",
          hideInSearch: false,
          readonly: readonly({ type, roles, role: [rolesMaps.role_finance] }),
          hideInTable: true
        },
        {
          title: "收付款日期",
          dataIndex: "gmtReceiptAndPay",
          hideInSearch: false,
          readonly: readonly({ type, roles, role: [rolesMaps.role_finance] }),
          hideInTable: true,
          fieldProps: {
            style: { width: "100%" }
          },
          valueType: "date"
        },
        {
          title: "打款备注",
          dataIndex: "remitNote",
          readonly: readonly({ type, roles, role: [rolesMaps.role_finance] }),
          hideInTable: true
        },
        {
          title: "打款截图",
          dataIndex: "remitScreenshot",
          hideInSearch: false,
          readonly: readonly({ type, roles, role: [rolesMaps.role_finance] }),
          hideInTable: true,
          render() {
            const { remitScreenshot } = initialValues;
            let url = remitScreenshot;
            if (url) {
              try {
                let remitImg = JSON.parse(url);
                if (Array.isArray(remitImg)) {
                  url = remitImg?.[0] ?? void 0;
                }
              } catch {}
            }
            return <Image width={50} height={50} src={returnItemImgUrl(url)} />;
          },
          renderFormItem(a, b) {
            let url = initialValues?.remitScreenshot;
            if (b.value) {
              url = b.value;
              try {
                let remitImg = JSON.parse(url);
                if (Array.isArray(remitImg)) {
                  url = remitImg?.[0] ?? void 0;
                }
              } catch {}
            }
            return <EditRSSUpload fileLength={1} value={url} />;
          }
        },
        {
          title: "未结款金额",
          dataIndex: "unPayAmount",
          hideInSearch: false,
          readonly: true,
          hideInTable: true,
          render() {
            const vals = form.getFieldsValue();
            const { offlineSettleAmount, receiptAndPayTotal } = vals;
            const val = (offlineSettleAmount ?? 0) - (receiptAndPayTotal ?? 0);
            form.setFieldValue("unPayAmount", val);
            return (val / 10000).toFixed(4);
          }
        },
        {
          title: "账单状态",
          dataIndex: "settlementStatus",
          hideInSearch: false,
          readonly: readonly({ type, roles, role: [rolesMaps.role_finance] }),
          hideInTable: true,
          render() {
            const { settlementStatusStr } = initialValues;
            return settlementStatusStr;
          },
          renderFormItem() {
            return <Select placeholder="请选择" options={settlementStatusOptions} filterOption={filterOptionLabel} showSearch allowClear />;
          }
        },
        {
          title: "问题归属",
          dataIndex: "problemAttribution",
          hideInSearch: false,
          readonly: readonly({ type, roles, role: [rolesMaps.role_finance] }),
          hideInTable: true,
          render() {
            const { problemAttributionStr } = initialValues;
            return problemAttributionStr;
          },
          renderFormItem() {
            return <Select placeholder="请选择" options={problemAttributionOptions} filterOption={filterOptionLabel} showSearch allowClear />;
          }
        },
        {
          title: "未结款原因",
          dataIndex: "unSettlementReason",
          hideInSearch: false,
          readonly: readonly({ type, roles, role: [rolesMaps.role_finance] }),
          hideInTable: true
        },
        {
          title: "是否需要开票",
          dataIndex: "isMakeOut",
          hideInSearch: false,
          readonly: readonly({ type, roles, role: [rolesMaps.role_finance] }),
          hideInTable: true,
          render() {
            const { isMakeOutStr } = initialValues;
            return isMakeOutStr;
          },
          renderFormItem() {
            return <Select placeholder="请选择" options={isMakeOutbutionOptions} filterOption={filterOptionLabel} showSearch allowClear />;
          }
        },
        {
          title: "易快报备注",
          dataIndex: "easyExpressRemarks",
          hideInSearch: false,
          readonly: readonly({ type, roles, role: [rolesMaps.role_finance] }),
          hideInTable: true
        },
        {
          title: "开票时间",
          dataIndex: "gmtBill",
          hideInSearch: false,
          readonly: readonly({ type, roles, role: [rolesMaps.role_finance] }),
          hideInTable: true,
          fieldProps: {
            style: { width: "100%" }
          },
          valueType: "date"
        },
        {
          title: "开票金额",
          dataIndex: "billAmount",
          hideInSearch: false,
          readonly: readonly({ type, roles, role: [rolesMaps.role_finance] }),
          hideInTable: true,
          render() {
            const { billAmountStr } = initialValues;
            return billAmountStr;
          },
          renderFormItem() {
            return <EditInputNumber divide={10000} precision={4} style={{ width: "100%" }} />;
          }
        },
        {
          title: "备注",
          dataIndex: "remarks",
          hideInSearch: false,
          readonly: readonly({ type, roles, role: [rolesMaps.role_finance] }),
          hideInTable: true
        },
        {
          title: "财务反馈",
          dataIndex: "financeFeedback",
          hideInSearch: false,
          readonly: readonly({ type, roles, role: [rolesMaps.role_finance] }),
          hideInTable: true
        },
        {
          title: "财务反馈备注",
          dataIndex: "financeFeedbackRemarks",
          hideInSearch: false,
          readonly: readonly({ type, roles, role: [rolesMaps.role_finance] }),
          hideInTable: true
        },
        {
          title: "合同负责人",
          dataIndex: "contractOwner",
          hideInSearch: false,
          readonly: readonly({ type, roles, role: [rolesMaps.role_business, rolesMaps.role_finance] }),
          hideInTable: true
        },
        {
          title: "是否提交法务",
          dataIndex: "legalSubmitStatus",
          hideInSearch: false,
          readonly: readonly({ type, roles, role: [rolesMaps.role_business, rolesMaps.role_finance] }),
          hideInTable: true,
          render() {
            const { legalSubmitStatusStr } = initialValues;
            return legalSubmitStatusStr;
          },
          renderFormItem() {
            return <Select placeholder="请选择" options={legalSubmitStatusOptions} filterOption={filterOptionLabel} showSearch allowClear />;
          }
        },
        {
          title: "预计回款日期",
          dataIndex: "gmtEstimatedPayment",
          hideInSearch: false,
          readonly: readonly({ type, roles, role: [rolesMaps.role_business, rolesMaps.role_finance] }),
          fieldProps: {
            style: { width: "100%" }
          },
          hideInTable: true,
          valueType: "date"
        },
        {
          title: "业务反馈备注",
          dataIndex: "businessFeedbackRemark",
          hideInSearch: false,
          readonly: readonly({ type, roles, role: [rolesMaps.role_business, rolesMaps.role_finance] }),
          hideInTable: true
        },
        {
          title: "法务反馈进度",
          dataIndex: "legalFeedbackSchedule",
          hideInSearch: false,
          readonly: readonly({ type, roles, role: [rolesMaps.role_legal_affairs, rolesMaps.role_finance] }),
          hideInTable: true,
          render() {
            const { legalFeedbackScheduleStr } = initialValues;
            return legalFeedbackScheduleStr;
          },
          renderFormItem() {
            return <Select placeholder="请选择" options={legalFeedbackScheduleOptions} filterOption={filterOptionLabel} showSearch allowClear />;
          }
        },
        {
          title: "法务备注",
          dataIndex: "legalRemark",
          hideInSearch: false,
          readonly: readonly({ type, roles, role: [rolesMaps.role_legal_affairs, rolesMaps.role_finance] }),
          hideInTable: true
        },
        {
          title: "律师反馈进度",
          dataIndex: "lawyerFeedbackSchedule",
          hideInSearch: false,
          readonly: readonly({ type, roles, role: [rolesMaps.role_lawyer, rolesMaps.role_finance] }),
          hideInTable: true,
          render() {
            const { lawyerFeedbackScheduleStr } = initialValues;
            return lawyerFeedbackScheduleStr;
          },
          renderFormItem() {
            return <Select placeholder="请选择" options={lawyerFeedbackScheduleOptions} filterOption={filterOptionLabel} showSearch allowClear />;
          }
        },
        {
          title: "律师备注",
          dataIndex: "lawyerRemarks",
          hideInSearch: false,
          readonly: readonly({ type, roles, role: [rolesMaps.role_lawyer, rolesMaps.role_finance] }),
          hideInTable: true
        }
      ]
    }
  ];
};
export const log_columns = ({}: any): TableListItem[] => {
  return [
    {
      title: "操作",
      dataIndex: "reconcileItemOperatorLog",
      hideInSearch: true,
      width: 250,
      render(text) {
        const res = text.split("\n");
        return res.map((item: string) => {
          // 单独处理打款截图修改
          if (item.indexOf("打款截图: 【更改前：") > -1) {
            // 匹配 更改前
            const regOld = /\u66f4\u6539\u524d：\[.*\],/gi;
            // 匹配 更改后
            const regNew = /\u66f4\u6539\u540e：\[.*\]/gi;
            const imgOld = item?.match(regOld)?.[0];
            const imgNew = item?.match(regNew)?.[0];
            // 匹配 图片 url
            const regImg = /(http|https|ftp):\/\/[a-zA-Z0-9.-]+(\/[a-zA-Z0-9._/?&=~-]*)?/gi;
            const img_old = imgOld?.match(regImg);
            const img_new = imgNew?.match(regImg);
            return (
              <>
                打款截图:
                <Space key={item}>
                  <>
                    【更改前：
                    <Image width={50} height={50} src={returnItemImgUrl(img_old)} />
                    ,更改后：
                    <Image width={50} height={50} src={returnItemImgUrl(img_new)} />】
                  </>
                </Space>
              </>
            );
          }
          return <p key={item}>{item}</p>;
        });
      }
    },
    {
      title: "操作人",
      dataIndex: "operator",
      hideInSearch: true,
      width: 100
    },
    {
      title: "操作时间",
      dataIndex: "gmtOperate",
      hideInSearch: true,
      width: 120
    }
  ];
};
