import { filterOptionLabel, returnEllipsisTooltip, returnItemImgUrl } from "@/utils";
import AnchorSelect from "@/components/AnchorSelect";
import { Dropdown, MenuProps, Select, Image, Popconfirm } from "antd";
import { DownOutlined } from "@ant-design/icons";
import DepartmentSelect from "@/components/DepartmentSelect";
import { menus } from "./index";
import { btn_auths } from "../auths";
import { NOVALUE, SIZE } from "@/utils/constant";
const level1dropdownMenus = ({ page_auths, activeMenu, record, handleDownloadRecon, handleDetail, handleEdit, handleDelete, handleToDraft }: any) => {
  const hasReconcili: MenuProps["items"] = [
    {
      key: btn_auths.btn_reconcili_bill_detail_has_edit,
      onClick: () => {
        handleEdit(record);
      },
      label: <>编辑</>
    },
    {
      key: btn_auths.btn_reconcili_bill_detail_has_download,
      // onClick: () => {
      //   handleDownloadRecon(record);
      // },
      label: (
        <Popconfirm
          title={
            <>
              <div>下载结果包含供应商对应账期</div>
              <div>的所有直播商品，</div>
              <div>多个主播无需重复下载！</div>
            </>
          }
          onConfirm={() => handleDownloadRecon(record)}
          onCancel={() => {}}
          okText="确认"
          cancelText="取消"
          okButtonProps={{ size: SIZE }}
          cancelButtonProps={{ size: SIZE }}
        >
          下载
        </Popconfirm>
      )
    },
    {
      key: btn_auths.btn_reconcili_bill_detail_has_detail,
      onClick: () => {
        handleDetail(record);
      },
      label: <>详情</>
    },
    {
      key: btn_auths.btn_reconcili_bill_detail_has_toDraft,
      label: (
        <Popconfirm
          title={
            <>
              <div>供应商对账期间中多个账单</div>
              <div>将同时转为草稿状态，请确认！</div>
            </>
          }
          onConfirm={() => handleToDraft(record)}
          onCancel={() => {}}
          okText="确认"
          cancelText="取消"
          okButtonProps={{ size: SIZE }}
          cancelButtonProps={{ size: SIZE }}
        >
          转为草稿
        </Popconfirm>
      )
    }
  ].filter(item => {
    return page_auths.includes(item.key);
  });
  const draft: MenuProps["items"] = [
    {
      key: btn_auths.btn_reconcili_bill_detail_draft_edit,
      onClick: () => {
        handleEdit(record);
      },
      label: <>编辑</>
    },
    {
      key: btn_auths.btn_reconcili_bill_detail_draft_download,
      // onClick: () => {
      //   handleDownloadRecon(record);
      // },
      label: (
        <Popconfirm
          title={
            <>
              <div>下载结果包含供应商对应账期</div>
              <div>的所有直播商品，</div>
              <div>多个主播无需重复下载！</div>
            </>
          }
          onConfirm={() => handleDownloadRecon(record)}
          onCancel={() => {}}
          okText="确认"
          cancelText="取消"
          okButtonProps={{ size: SIZE }}
          cancelButtonProps={{ size: SIZE }}
        >
          下载
        </Popconfirm>
      )
    },
    {
      key: btn_auths.btn_reconcili_bill_detail_draft_detail,
      onClick: () => {
        handleDetail(record);
      },
      label: <>详情</>
    },
    {
      key: btn_auths.btn_reconcili_bill_detail_draft_delete,
      danger: true,
      label: (
        <Popconfirm
          title="是否删除整个账期数据?"
          onConfirm={() => handleDelete(record)}
          onCancel={() => {}}
          okText="确认"
          cancelText="取消"
          okButtonProps={{ size: SIZE }}
          cancelButtonProps={{ size: SIZE }}
        >
          删除
        </Popconfirm>
      )
    }
  ].filter(item => {
    return page_auths.includes(item.key);
  });
  const res = {
    [menus.menu_hasReconcili]: hasReconcili,
    [menus.menu_draft]: draft
  };
  return res[activeMenu] ?? [];
};
export const advance_columns_search = ({
  itemTypeOptions,
  billTypeOptions,
  // contractNameOptions,
  // settlementStatusOptions,
  legalSubmitStatusOptions
}: any): TableListItem[] => {
  return [
    {
      title: "基本信息",
      valueType: "group",
      colProps: { span: 24 },
      columns: [
        {
          title: "商品类型",
          dataIndex: "itemTypeList",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem() {
            return <Select placeholder="请选择" options={itemTypeOptions} filterOption={filterOptionLabel} showSearch allowClear />;
          }
        },
        {
          title: "业务对接人",
          dataIndex: "purchasePrincipal",
          hideInSearch: false,
          hideInTable: true
        },
        {
          title: "店铺ID",
          dataIndex: "shopIdList",
          hideInSearch: false,
          hideInTable: true,
          fieldProps: {
            placeholder: "多个使用英文逗号/空格"
          }
        },
        {
          // 模糊
          title: "店铺名称",
          dataIndex: "shopName",
          hideInSearch: false,
          hideInTable: true
        }
      ]
    },
    {
      title: "账单信息",
      valueType: "group",
      colProps: { span: 24 },
      columns: [
        {
          title: "账单类型",
          dataIndex: "billTypeList",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem() {
            return <Select placeholder="请选择" options={billTypeOptions} filterOption={filterOptionLabel} showSearch allowClear />;
          }
        },
        {
          title: "财务对接人",
          dataIndex: "financeReconcile",
          hideInSearch: false,
          hideInTable: true
        }
      ]
    },
    {
      title: "回款情况",
      valueType: "group",
      colProps: { span: 24 },
      columns: [
        {
          title: "收付款主体",
          dataIndex: "receiptAndPaySubject",
          hideInSearch: false,
          hideInTable: true
        },
        {
          // 日期范围
          title: "收付款日期",
          dataIndex: "gmtReceiptAndPay",
          hideInSearch: false,
          hideInTable: true,
          valueType: "dateRange"
        },
        {
          title: "是否提交法务",
          dataIndex: "legalSubmitStatusList",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem() {
            return <Select placeholder="请选择" options={legalSubmitStatusOptions} filterOption={filterOptionLabel} showSearch allowClear />;
          }
        }
      ]
    },
    {
      title: "SKU明细",
      valueType: "group",
      colProps: { span: 24 },
      columns: [
        {
          // 多选
          title: "SKUID",
          dataIndex: "skuIdList",
          hideInSearch: false,
          hideInTable: true,
          fieldProps: {
            placeholder: "多个使用英文逗号/空格"
          }
        },
        {
          // 模糊
          title: "SKU名称",
          dataIndex: "skuNameList",
          hideInSearch: false,
          hideInTable: true
        }
      ]
    }
  ];
};
const columns_search = ({ billSettlePatternOptions, contractNameOptions, settlementStatusOptions }: any) => {
  return [
    {
      title: "对账期间",
      dataIndex: "gmtReconcilePeriod",
      hideInSearch: false,
      hideInTable: true,
      valueType: "dateRange"
    },
    {
      title: "直播日期",
      dataIndex: "liveDate",
      hideInSearch: false,
      hideInTable: true,
      valueType: "dateRange"
    },
    {
      title: "主播ID",
      dataIndex: "anchorIdList",
      hideInSearch: false,
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "主播名称",
      dataIndex: "anchorNameList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem() {
        return <AnchorSelect showSearch allowClear maxTagCount="responsive" mode="multiple" />;
      }
    },
    {
      title: "商品ID",
      dataIndex: "itemIdList",
      hideInSearch: false,
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "商品名称",
      dataIndex: "itemTitle",
      hideInSearch: false,
      hideInTable: true
    },
    {
      title: "账单状态",
      dataIndex: "settlementStatusList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem() {
        return <Select placeholder="请选择" options={settlementStatusOptions} filterOption={filterOptionLabel} showSearch allowClear />;
      }
    },
    {
      // 多选
      title: "账单结算模式",
      dataIndex: "billSettlePatternList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem() {
        return (
          <Select
            placeholder="请选择"
            options={billSettlePatternOptions}
            filterOption={filterOptionLabel}
            showSearch
            allowClear
            maxTagCount="responsive"
            mode="multiple"
          />
        );
      }
    },
    {
      title: "供应商名称",
      dataIndex: "supplierName",
      hideInSearch: false,
      hideInTable: true
    },
    {
      title: "部门",
      dataIndex: "bigDepartmentList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return <DepartmentSelect key="bigDepartmentList" style={{ width: "100%" }} />;
      }
    },
    {
      title: "我司合同主体",
      dataIndex: "contractName",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem() {
        return <Select placeholder="请选择" options={contractNameOptions} filterOption={filterOptionLabel} showSearch allowClear />;
      }
    }
  ];
};
export const columns = ({
  page_auths,
  billSettlePatternOptions,
  activeMenu,
  handleDownloadRecon,
  handleDetail,
  handleDelete,
  handleToDraft,
  handleEdit,
  contractNameOptions,
  settlementStatusOptions
}: any): Array<TableListItem> => {
  return [
    ...columns_search({ billSettlePatternOptions, contractNameOptions, settlementStatusOptions }),
    {
      title: "序号",
      dataIndex: "$index",
      width: 70,
      hideInSearch: true,
      fixed: "left",
      render: (_, record, index) => {
        const { children } = record;
        if (children) {
          return index + 1;
        }
      }
    },
    {
      title: "对账期间",
      dataIndex: "reconciledPeriod",
      width: 100,
      fixed: "left",
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "账期",
      fixed: "left",
      dataIndex: "periodStr",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "直播日期", fixed: "left", dataIndex: "liveDate", width: 100, hideInSearch: true },
    {
      title: "主播ID",
      dataIndex: "anchorId",
      width: 100,
      fixed: "left",
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "主播名称",
      fixed: "left",
      dataIndex: "anchorName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品ID",
      dataIndex: "itemId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品名称",
      dataIndex: "itemTitle",
      width: 160,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "商品类型", dataIndex: "itemTypeStr", width: 100, hideInSearch: true },
    {
      title: "SKUID",
      dataIndex: "itemSkuId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "SKU名称",
      dataIndex: "itemSkuName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "账单结算模式", dataIndex: "billSettlePatternStr", width: 100, hideInSearch: true },
    { title: "账单类型", dataIndex: "billTypeStr", width: 100, hideInSearch: true },
    { title: "财务对账人", dataIndex: "financeReconcile", width: 100, hideInSearch: true },
    {
      title: "部门",
      dataIndex: "bigDepartment",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "业务对接人", dataIndex: "purchasePrincipal", width: 100, hideInSearch: true },
    { title: "店铺ID", dataIndex: "shopId", width: 100, hideInSearch: true },
    {
      title: "店铺名称",
      dataIndex: "shopName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "供应商名称",
      dataIndex: "supplierName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "我司合同主体",
      dataIndex: "contractName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "出具账单日期", dataIndex: "gmtIssueBill", width: 100, hideInSearch: true },
    { title: "账单收入金额", dataIndex: "totalIncomeAmountStr", width: 100, hideInSearch: true },
    { title: "账单退款金额", dataIndex: "totalOrderRefundAmountStr", width: 100, hideInSearch: true },
    { title: "超售后退款金额", dataIndex: "totalOverRefundAmountStr", width: 100, hideInSearch: true },
    { title: "总退款金额", dataIndex: "totalRefundAmountStr", width: 100, hideInSearch: true },
    { title: "销售收入", dataIndex: "salesRevenueAmountStr", width: 100, hideInSearch: true },
    { title: "总佣金比例", dataIndex: "commissionRateStr", width: 100, hideInSearch: true },
    { title: "总佣金金额", dataIndex: "totalCommissionAmountStr", width: 100, hideInSearch: true },
    { title: "达人佣金金额", dataIndex: "pidCommissionAmountStr", width: 100, hideInSearch: true },
    { title: "达人佣金追回金额", dataIndex: "pidBackCommissionAmountStr", width: 100, hideInSearch: true },
    { title: "线上佣金金额", dataIndex: "onlineCommissionAmountStr", width: 100, hideInSearch: true },
    { title: "结算件数", dataIndex: "salesVolumeStr", width: 100, hideInSearch: true },
    { title: "退款件数", dataIndex: "refundItemVolumeStr", width: 100, hideInSearch: true },
    { title: "实际结算件数", dataIndex: "actualSettleItemVolumeStr", width: 100, hideInSearch: true },
    { title: "快手技术服务费", dataIndex: "techServiceAmountStr", width: 100, hideInSearch: true },
    { title: "快手技术服务费追回", dataIndex: "techServiceBackAmountStr", width: 100, hideInSearch: true },
    { title: "实际技术服务费", dataIndex: "actualTechServiceAmountStr", width: 100, hideInSearch: true },
    { title: "供货价", dataIndex: "supplyPriceStr", width: 100, hideInSearch: true },
    { title: "供货总成本", dataIndex: "totalSupplyCostStr", width: 100, hideInSearch: true },
    { title: "其他金额", dataIndex: "otherAmountStr", width: 100, hideInSearch: true },
    {
      title: "我司应得利润",
      dataIndex: "shouldProfitStr",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { shouldProfitStr } = record;
        return returnEllipsisTooltip({ title: shouldProfitStr });
      }
    },
    { title: "税点金额", dataIndex: "taxFeeStr", width: 100, hideInSearch: true },
    { title: "线下结算金额", dataIndex: "offlineSettleAmountStr", width: 100, hideInSearch: true },
    {
      title: "收付款主体",
      dataIndex: "receiptAndPaySubject",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "收付款日期", dataIndex: "gmtReceiptAndPay", width: 100, hideInSearch: true },
    { title: "已收付款总金额", dataIndex: "receiptAndPayTotalStr", width: 100, hideInSearch: true },
    {
      title: "打款备注",
      dataIndex: "remitNote",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "打款截图",
      dataIndex: "remitScreenshot",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { remitScreenshot, parentFlag } = record;
        if (parentFlag !== 2) return NOVALUE;
        let url = remitScreenshot;
        if (url) {
          try {
            let remitImg = JSON.parse(url);
            if (Array.isArray(remitImg)) {
              url = remitImg?.[0] ?? void 0;
            }
          } catch {}
        }
        return <Image width={50} height={50} src={returnItemImgUrl(url)} />;
      }
    },
    { title: "未结款金额", dataIndex: "unPayAmountStr", width: 100, hideInSearch: true },
    {
      title: "账单状态",
      dataIndex: "settlementStatusStr",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "问题归属",
      dataIndex: "problemAttributionStr",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "未结款原因",
      dataIndex: "unSettlementReason",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "是否需要开票", dataIndex: "isMakeOutStr", width: 100, hideInSearch: true },
    {
      title: "易快报备注",
      dataIndex: "easyExpressRemarks",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "开票时间", dataIndex: "gmtBill", width: 100, hideInSearch: true },
    { title: "开票金额", dataIndex: "billAmountStr", width: 100, hideInSearch: true },
    {
      title: "备注",
      dataIndex: "remarks",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "财务反馈",
      dataIndex: "financeFeedback",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "财务反馈备注",
      dataIndex: "financeFeedbackRemarks",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "合同负责人", dataIndex: "contractOwner", width: 100, hideInSearch: true },
    { title: "是否提交法务", dataIndex: "legalSubmitStatusStr", width: 100, hideInSearch: true },
    { title: "预计回款日期", dataIndex: "gmtEstimatedPayment", width: 100, hideInSearch: true },
    {
      title: "业务反馈备注",
      dataIndex: "businessFeedbackRemark",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "法务反馈进度", dataIndex: "legalFeedbackScheduleStr", width: 100, hideInSearch: true },
    {
      title: "法务备注",
      dataIndex: "legalRemark",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "律师反馈进度", dataIndex: "lawyerFeedbackScheduleStr", width: 100, hideInSearch: true },
    {
      title: "律师备注",
      dataIndex: "lawyerRemarks",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "操作",
      valueType: "option",
      align: "center",
      fixed: "right",
      width: 100,
      render: (_, record) => {
        const { parentFlag } = record;
        return parentFlag === 2 ? (
          <>
            {activeMenu === menus.menu_hasReconcili &&
            [
              btn_auths.btn_reconcili_bill_detail_has_detail,
              btn_auths.btn_reconcili_bill_detail_has_download,
              btn_auths.btn_reconcili_bill_detail_has_edit
            ].filter(item => page_auths.includes(item))?.length ? (
              <Dropdown
                menu={{
                  items: level1dropdownMenus({
                    page_auths,
                    activeMenu,
                    record,
                    handleDownloadRecon,
                    handleDetail,
                    handleEdit,
                    handleDelete,
                    handleToDraft
                  })
                }}
              >
                <a onClick={e => e.preventDefault()}>
                  操作 <DownOutlined />
                </a>
              </Dropdown>
            ) : null}
            {activeMenu === menus.menu_draft &&
            [
              btn_auths.btn_reconcili_bill_detail_draft_detail,
              btn_auths.btn_reconcili_bill_detail_draft_download,
              btn_auths.btn_reconcili_bill_detail_draft_edit
            ].filter(item => page_auths.includes(item))?.length ? (
              <Dropdown
                menu={{
                  items: level1dropdownMenus({
                    page_auths,
                    activeMenu,
                    record,
                    handleDownloadRecon,
                    handleDetail,
                    handleEdit,
                    handleDelete,
                    handleToDraft
                  })
                }}
              >
                <a onClick={e => e.preventDefault()}>
                  操作 <DownOutlined />
                </a>
              </Dropdown>
            ) : null}
          </>
        ) : null;
      }
    }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      ...item,
      align: "center",
      title: returnEllipsisTooltip({ title })
    };
  });
};
