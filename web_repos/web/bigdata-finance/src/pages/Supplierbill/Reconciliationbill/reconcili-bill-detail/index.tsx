import React, { useState, useRef, useContext, useEffect, useLayoutEffect } from "react";
import { Button } from "antd";
import TableList from "@/components/TableList";
import { SIZE } from "@/utils/constant";
import { columns, advance_columns_search } from "./variable";
import apis from "@/services/supplierbill/reconciliationbill";
import { ifEmptyObj, pushExportHistory, splitSpaceComma } from "@/utils";
import UploadModal from "@/components/UploadModal";
import dayjs from "dayjs";
import Detail from "./detail";
import useCommonOptions from "@/hooks/useCommonOptions";
import { FieldsContext, PageUrl, tab_reconcili_bill_detail } from "../index";
import { btn_auths } from "../auths";
import AuthButton from "@/wrappers/authButton";
export const menus = {
  menu_hasReconcili: "1",
  menu_draft: "2"
};
const default_menu = menus.menu_hasReconcili;
const Res = () => {
  const [ohterParams, setOtherParams] = useState({});
  const { page_auths, billSettlePatternOptions, billTypeOptions, contractNameOptions, urlParams, settlementStatusOptions } =
    useContext(FieldsContext);
  const [pageParmas, setPageParmas] = useState<any>({});
  const actionsRef = useRef<any>();
  const formRef = useRef<any>();
  const [activeMenu, setActiveMenu] = useState<string>("");
  const [showUpload, setshowUpload] = useState(false);
  const [detailProps, setDetailProps] = useState<any>({
    open: false,
    type: "",
    onFresh: () => {
      actionsRef.current?.reload();
    },
    onClose: () => {
      setDetailProps({ ...detailProps, open: false });
    }
  });

  const [itemTypeOptions] = useCommonOptions({ dimName: "商品类型" });
  const [legalSubmitStatusOptions] = useCommonOptions({ dimName: "是否提交法务" });
  const [problemAttributionOptions] = useCommonOptions({ dimName: "问题归属" });
  const [isMakeOutbutionOptions] = useCommonOptions({ dimName: "是否需要开票" });
  const [legalFeedbackScheduleOptions] = useCommonOptions({ dimName: "法务反馈进度" });
  const [lawyerFeedbackScheduleOptions] = useCommonOptions({ dimName: "律师反馈进度" });
  const UploadDetailModalProps = {
    title: "上传对账单明细",
    visible: showUpload,
    targetType: 0,
    api: apis.addSupplierReconcileItemFromExcel,
    message: (
      <>
        <span>1. 请参考模板中数据格式进行上传</span>
        <br />
        <span>2. 黄色背景为必填项，蓝色背景为可编辑字段</span>
        <br />
        <span>3. 线下人工计算的赠品、福利品等特殊商品对账单上传后自动变为已确认对账单</span>
        <br />
      </>
    ),
    tempUrl: "https://s.xinc818.com/files/webcim5qbst5s0i3d9t/供应商对账-对账单-商品对账明细-导入模版 .xlsx",
    fresh: () => {
      actionsRef.current.reload();
    },
    close: () => {
      setshowUpload(false);
    }
  };

  const handlePageParmas = (params: any) => {
    const {
      current,
      liveDate,
      gmtReceiptAndPay,
      shopIdList,
      itemIdList,
      anchorIdList,
      skuIdList,
      gmtReconcilePeriod,
      billTypeList,
      itemTypeList,
      bigDepartmentList,
      settlementStatusList,
      legalSubmitStatusList,
      skuNameList,
      ...rest
    } = params;
    if (gmtReceiptAndPay) {
      const [gmtReceiptAndPayStart, gmtReceiptAndPayEnd] = gmtReceiptAndPay;
      rest.gmtReceiptAndPayStart = dayjs(gmtReceiptAndPayStart).format("YYYY-MM-DD");
      rest.gmtReceiptAndPayEnd = dayjs(gmtReceiptAndPayEnd).format("YYYY-MM-DD");
    }
    if (gmtReconcilePeriod) {
      const [gmtReconcilePeriodStart, gmtReconcilePeriodEnd] = gmtReconcilePeriod;
      rest.gmtReconcilePeriodStart = dayjs(gmtReconcilePeriodStart).format("YYYY-MM-DD");
      rest.gmtReconcilePeriodEnd = dayjs(gmtReconcilePeriodEnd).format("YYYY-MM-DD");
    }
    if (liveDate) {
      const [liveDateStart, liveDateEnd] = liveDate;
      rest.liveDateStart = dayjs(liveDateStart).format("YYYY-MM-DD");
      rest.liveDateEnd = dayjs(liveDateEnd).format("YYYY-MM-DD");
    }
    if (billTypeList) {
      rest.billTypeList = [billTypeList];
    }
    if (itemTypeList) {
      rest.itemTypeList = [itemTypeList];
    }
    if (bigDepartmentList) {
      rest.bigDepartmentList = [bigDepartmentList];
    }
    if (settlementStatusList) {
      rest.settlementStatusList = [settlementStatusList];
    }
    if (skuNameList) {
      rest.skuNameList = [skuNameList];
    }
    if (legalSubmitStatusList) {
      rest.legalSubmitStatusList = [legalSubmitStatusList];
    }
    rest.anchorIdList = splitSpaceComma(anchorIdList);
    rest.itemIdList = splitSpaceComma(itemIdList);
    rest.shopIdList = splitSpaceComma(shopIdList);
    rest.skuIdList = splitSpaceComma(skuIdList);
    return { ...rest, pageNo: current };
  };

  const handleDownloadRecon = async record => {
    const { downloadStatementKey } = record;
    const { status } = await apis.downloadSupplierReconcile({ downloadStatementKey });
    if (status) {
      pushExportHistory();
    }
  };
  const handleDetail = async record => {
    setDetailProps({
      ...detailProps,
      open: true,
      params: { id: record.id },
      type: "detail",
      itemTypeOptions,
      billTypeOptions,
      contractNameOptions,
      settlementStatusOptions,
      legalSubmitStatusOptions,
      problemAttributionOptions,
      isMakeOutbutionOptions,
      legalFeedbackScheduleOptions,
      lawyerFeedbackScheduleOptions
    });
  };
  const handleEdit = async record => {
    setDetailProps({
      ...detailProps,
      open: true,
      params: { id: record.id },
      type: "edit",
      itemTypeOptions,
      billTypeOptions,
      contractNameOptions,
      settlementStatusOptions,
      legalSubmitStatusOptions,
      problemAttributionOptions,
      isMakeOutbutionOptions,
      legalFeedbackScheduleOptions,
      lawyerFeedbackScheduleOptions
    });
  };
  const handleDelete = async record => {
    const { gmtReconcilePeriodStart, gmtReconcilePeriodEnd, supplierName } = record;
    const { status } = await apis.deleteDraftSupplierReconcile({ gmtReconcilePeriodStart, gmtReconcilePeriodEnd, supplierName });
    if (status) {
      actionsRef.current.reload();
    }
  };
  const handleToDraft = async record => {
    const { gmtReconcilePeriodStart, gmtReconcilePeriodEnd, supplierName } = record;
    const { status } = await apis.doUnConfirmSupplierReconcile({ gmtReconcilePeriodStart, gmtReconcilePeriodEnd, supplierName });
    if (status) {
      actionsRef.current.reload();
    }
  };

  const columnsProps = {
    page_auths,
    billSettlePatternOptions,
    activeMenu,
    handleDownloadRecon,
    handleDetail,
    handleEdit,
    handleDelete,
    handleToDraft,
    contractNameOptions,
    settlementStatusOptions
  };
  const advanceColumnsProps = {
    itemTypeOptions,
    billTypeOptions,
    contractNameOptions,
    settlementStatusOptions,
    legalSubmitStatusOptions,
    problemAttributionOptions,
    isMakeOutbutionOptions,
    legalFeedbackScheduleOptions,
    lawyerFeedbackScheduleOptions
  };
  const handleExport = async () => {
    const { status } = await apis.downloadStatementItemReconcileDetail(pageParmas);
    if (status) {
      pushExportHistory();
    }
  };
  const actions = () => {
    return [
      <AuthButton key="upload" pageUrl={PageUrl} code={btn_auths.btn_reconcili_bill_detail_batchupload}>
        <Button type="primary" onClick={() => setshowUpload(true)} size={SIZE}>
          批量编辑上传
        </Button>
      </AuthButton>,
      <AuthButton key="export" pageUrl={PageUrl} code={btn_auths.btn_reconcili_bill_detail_export}>
        <Button onClick={handleExport} size="small">
          导出
        </Button>
      </AuthButton>
    ];
  };
  const toolbar_menus = [
    {
      key: menus.menu_hasReconcili,
      name: btn_auths.btn_reconcili_bill_detail_menu_hasReconcili,
      label: "已对账"
    },
    {
      key: menus.menu_draft,
      name: btn_auths.btn_reconcili_bill_detail_menu_draft,
      label: "草稿"
    }
  ].filter(menu => page_auths.includes(menu.name));
  const findeActiveMenu = (menu = void 0) => {
    // 如果权限中存在 默认tab 就使用默认tab
    if (toolbar_menus.some(tab => tab.key === default_menu)) {
      return default_menu;
    } else {
      // 否则 使用 有权限的第一个tab
      return toolbar_menus?.[0]?.key ?? menu ?? "";
    }
  };
  useLayoutEffect(() => {
    if (!activeMenu) {
      setActiveMenu(findeActiveMenu());
    }
  }, [toolbar_menus, activeMenu]);
  useEffect(() => {
    if (ifEmptyObj(urlParams)) {
      const { $_tab, menu, ...rest } = urlParams;
      if ($_tab === tab_reconcili_bill_detail) {
        formRef.current?.resetFields();
        setActiveMenu(findeActiveMenu(menu));
        setOtherParams(rest);
        formRef?.current?.setFieldsValue(rest);
      }
    }
  }, [urlParams]);
  return (
    <>
      {activeMenu !== "" ? (
        <TableList
          oneExpansion={{ position: "actions", show: true }}
          manualRequest={ifEmptyObj(urlParams)}
          form={{ span: 4, labelWidth: 90, labelWrap: false }}
          defaultExpandAll={{ deep: 0 }}
          hasChildren={true}
          actionRef={actionsRef}
          formRef={formRef}
          columns={columns(columnsProps)}
          advanceColumns={advance_columns_search(advanceColumnsProps)}
          api={apis.getStatementItemReconcileDetailPage}
          summaryApi={apis.getStatementItemReconcileDetailTotal}
          scroll={{ y: "calc(100vh - 440px)" }}
          preFetch={handlePageParmas}
          rowKey="combinedKey"
          params={{
            subTabType: activeMenu,
            ...ohterParams
          }}
          paramsChange={setPageParmas}
          toolbar={{
            actions: actions(),
            menu: {
              type: "tab",
              activeKey: activeMenu,
              items: toolbar_menus,
              onChange: (key: string) => {
                setActiveMenu(key);
              }
            }
          }}
        />
      ) : null}

      {showUpload && <UploadModal {...UploadDetailModalProps} />}
      {detailProps.open ? <Detail {...detailProps} setDetailProps /> : null}
    </>
  );
};

export default React.memo(Res);
