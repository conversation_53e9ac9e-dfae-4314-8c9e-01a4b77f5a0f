import React, { useState } from "react";
import TableList from "@/components/TableList";
import { log_columns } from "./variable";
import { Modal } from "antd";
import apis from "@/services/supplierbill/reconciliationbill";
interface IProps {
  // 显示隐藏 modal
  open: boolean;
  params?: Record<string, any>;
  // 关闭modal
  onClose: () => void;
  // 关闭 modal后 刷新页面
  onFresh?: () => void;
}
const Logs: React.FC<IProps> = props => {
  const { open, onClose, params } = props;
  const [logParams] = useState(() => {
    const { id } = params;
    return {
      statementReconcileItemId: id
    };
  });
  const columnsProps = {};
  const handleCancel = () => {
    onClose?.();
  };
  return (
    <Modal open={open} footer={false} width={900} centered onCancel={handleCancel}>
      <TableList
        options={false}
        search={false}
        scroll={{ y: 500 }}
        columns={log_columns(columnsProps)}
        params={logParams}
        api={apis.getStatementReconcileItemDetailLog}
      ></TableList>
    </Modal>
  );
};
export default React.memo(Logs);
