import React, { useContext, useEffect, useState } from "react";
import { Form, DrawerProps, Space, Button, Modal } from "antd";
import { BetaSchemaForm } from "@ant-design/pro-components";
import styles from "@/components/AdvanceSearch/index.less";
import { columns } from "./variable";
import { formGrid } from "@/components/AdvanceSearch";
import HandleLogs from "./handle-logs";
import { SIZE } from "@/utils/constant";
import apis from "@/services/supplierbill/reconciliationbill";
import dayjs from "dayjs";
import { judgeHasValue } from "@/utils";
import { FieldsContext } from "../../index";
interface IProps extends DrawerProps {
  type: "edit" | "detail";
  params?: Record<string, any>;
  record?: Record<string, any>;
  onClose?: () => void;
  onFresh?: () => void;
  settlementStatusOptions?: IOptions[];
  legalSubmitStatusOptions?: IOptions[];
  problemAttributionOptions?: IOptions[];
  isMakeOutbutionOptions?: IOptions[];
  legalFeedbackScheduleOptions?: IOptions[];
  lawyerFeedbackScheduleOptions?: IOptions[];
}

const Res: React.FC<IProps> = props => {
  const {
    onClose,
    onFresh,
    open,
    params,
    type,
    settlementStatusOptions,
    legalSubmitStatusOptions,
    problemAttributionOptions,
    isMakeOutbutionOptions,
    legalFeedbackScheduleOptions,
    lawyerFeedbackScheduleOptions
  } = props;
  const { roles } = useContext(FieldsContext);
  const [_open, setOpen] = useState(open);
  const [logOpen, setLogOpen] = useState(false);
  const [form] = Form.useForm();
  const [initialValues, setInitialValues] = useState({});
  const fetchDetail = async params => {
    const { status, entry } = await apis.getSupplierReconcileItemDetail(params);
    let res = {};
    if (status) {
      let {
        settlementStatus,
        problemAttribution,
        isMakeOut,
        legalSubmitStatus,
        legalFeedbackSchedule,
        lawyerFeedbackSchedule,
        gmtIssueBill,
        ...rest
      } = entry;
      if (gmtIssueBill) {
        rest.gmtIssueBill = dayjs(gmtIssueBill);
      }
      if (judgeHasValue(settlementStatus)) {
        settlementStatus = settlementStatus + "";
      }
      if (judgeHasValue(problemAttribution)) {
        problemAttribution = problemAttribution + "";
      }
      if (judgeHasValue(isMakeOut)) {
        isMakeOut = isMakeOut + "";
      }
      if (judgeHasValue(legalSubmitStatus)) {
        legalSubmitStatus = legalSubmitStatus + "";
      }
      if (judgeHasValue(legalFeedbackSchedule)) {
        legalFeedbackSchedule = legalFeedbackSchedule + "";
      }
      if (judgeHasValue(lawyerFeedbackSchedule)) {
        lawyerFeedbackSchedule = lawyerFeedbackSchedule + "";
      }
      res = { ...rest, settlementStatus, problemAttribution, isMakeOut, legalSubmitStatus, legalFeedbackSchedule, lawyerFeedbackSchedule };
    }
    form.setFieldsValue(res);
    setInitialValues(res);
  };
  const handleClose = () => {
    setOpen(false);
    form.resetFields();
  };
  // 线下结算金额 公式
  const offlineSettleAmountFormula = params => {
    const {
      billSettlePattern,
      offlineSettleAmount = 0,
      salesRevenueAmount,
      totalSupplyCost,
      onlineCommissionAmount,
      actualTechServiceAmount,
      commissionRate
    } = params;

    // 账单结算模式 对应公式
    const billSettlePatternMap: Record<string, (() => any) | undefined> = {
      // 利润模式
      "1": () => {
        // 公式：销售收入 - 供货总成本 - 线上佣金金额 - 实际技术服务费
        // salesRevenueAmount - totalSupplyCost - onlineCommissionAmount - actualTechServiceAmount
        return (salesRevenueAmount ?? 0) - (totalSupplyCost ?? 0) - (onlineCommissionAmount ?? 0) - (actualTechServiceAmount ?? 0);
      },
      // 收佣模式
      "2": () => {
        // 公式：销售收入 * 总佣金比例 - 线上佣金金额
        // salesRevenueAmount * commissionRate - onlineCommissionAmount
        return (salesRevenueAmount ?? 0) * (commissionRate ?? 0) - (onlineCommissionAmount ?? 0);
      },
      // 返佣、返差价模式
      "3": () => {
        // 公式：销售收入 * 总佣金比例 - 线上佣金金额
        // salesRevenueAmount * commissionRate - onlineCommissionAmount
        return (salesRevenueAmount ?? 0) * (commissionRate ?? 0) - (onlineCommissionAmount ?? 0);
      },
      // 第三方赠品对账
      "4": () => {},
      // 采购模式代销收入
      "5": () => {
        // 公式： 销售收入 - 线上佣金金额 - 实际技术服务费
        // salesRevenueAmount - onlineCommissionAmount - actualTechServiceAmount
        return (salesRevenueAmount ?? 0) - (onlineCommissionAmount ?? 0) - (actualTechServiceAmount ?? 0);
      },
      // 采购模式货款
      "6": () => {
        // 公式： 供货总成本
        // totalSupplyCost
        return totalSupplyCost;
      },
      // 佣金开票对账
      "7": () => {},
      // 推广补贴收入
      "8": () => {},
      // 其他
      "9": () => {},
      // 福利品账单
      "10": () => {}
    };
    let res = billSettlePatternMap[billSettlePattern]?.() ?? offlineSettleAmount ?? 0;
    return parseInt(res?.toFixed(0)) || 0;
  };
  const fetchOk = async p => {
    const { status } = await apis.saveOrUpdateStatementItemReconcile({ ...p });
    if (status) {
      onClose?.();
      onFresh?.();
    }
  };
  const handleOk = () => {
    form.validateFields().then(async values => {
      if (type !== "edit") {
        onClose?.();
      }
      const { gmtReceiptAndPay, gmtBill, gmtIssueBill, gmtEstimatedPayment, ...rest } = values;
      const params = {
        ...initialValues,
        ...rest
      };
      if (gmtReceiptAndPay) {
        params.gmtReceiptAndPay = dayjs(gmtReceiptAndPay).format("YYYY-MM-DD");
      }
      if (gmtBill) {
        params.gmtBill = dayjs(gmtBill).format("YYYY-MM-DD");
      }
      if (gmtIssueBill) {
        params.gmtIssueBill = dayjs(gmtIssueBill).format("YYYY-MM-DD");
      }
      if (gmtEstimatedPayment) {
        params.gmtEstimatedPayment = dayjs(gmtEstimatedPayment).format("YYYY-MM-DD");
      }
      let cale_offlineSettleAmount = offlineSettleAmountFormula(params);
      if (cale_offlineSettleAmount === (values?.offlineSettleAmount ?? 0)) {
        await fetchOk(params);
      } else {
        Modal.confirm({
          title: "二次确认",
          content: (
            <>
              <div>
                当前线下结算金额为 <span style={{ color: "red" }}>{`${((values?.offlineSettleAmount ?? 0) / 10000).toFixed(4)}元`}</span>，
                <span>
                  系统计算的线下结算金额为 <span style={{ color: "red" }}>{`${(cale_offlineSettleAmount / 10000).toFixed(4)}元`}</span>
                </span>
                ，是否继续保存
              </div>
            </>
          ),
          centered: true,
          okButtonProps: {
            size: SIZE
          },
          cancelButtonProps: {
            size: SIZE
          },
          onOk: async () => {
            await fetchOk(params);
          },
          onCancel: () => {}
        });
      }
    });
  };
  // 修改供应商名称后，重新请求账单详情
  const afterEditSupplier = () => {
    const { id } = params;
    fetchDetail({ id });
    onFresh?.();
  };
  const columnsProps = {
    form,
    roles,
    initialValues,
    type,
    settlementStatusOptions,
    legalSubmitStatusOptions,
    problemAttributionOptions,
    isMakeOutbutionOptions,
    legalFeedbackScheduleOptions,
    lawyerFeedbackScheduleOptions,
    afterEditSupplier
  };
  const logsProps = {
    open: logOpen,
    params: params,
    onClose: () => {
      setLogOpen(false);
    },
    onFresh: () => {}
  };
  const returnTitle = (
    <div className={styles["title"]}>
      <div>账单详情</div>
      <Space>
        <Button key="log" size={SIZE} onClick={() => setLogOpen(true)}>
          操作日志
        </Button>
        {type === "edit" ? (
          <>
            <Button key="cancel" size={SIZE} onClick={() => handleClose()}>
              取消
            </Button>
            <Button key="ok" size={SIZE} type="primary" onClick={() => handleOk()}>
              保存
            </Button>
          </>
        ) : null}
      </Space>
    </div>
  );
  useEffect(() => {
    if (open) {
      setOpen(open);
      const { id } = params;
      fetchDetail({ id });
    }
  }, [open, params]);
  return (
    <>
      <BetaSchemaForm
        form={form}
        open={_open}
        size={SIZE}
        onOpenChange={open => {
          setOpen(open);
          form.resetFields();
        }}
        {...formGrid}
        title={returnTitle}
        className={styles["advance-search"]}
        layout="horizontal"
        layoutType="DrawerForm"
        columns={columns(columnsProps)}
        submitter={false}
      />
      {logsProps.open ? <HandleLogs {...logsProps} /> : null}
    </>
  );
};

export default React.memo(Res);
