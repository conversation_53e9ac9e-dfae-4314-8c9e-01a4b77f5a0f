import React, { useState } from "react";
import { Button, Col, FormInstance, Input, message, Row, Space } from "antd";
import { SIZE } from "@/utils/constant";
import apis from "@/services/supplierbill/reconciliationbill";
interface IProps {
  statementReconcileId: number;
  supplierName: string;
  form: FormInstance;
  afterEditSupplier: () => void;
}
const Edit: React.FC<IProps> = ({ supplierName, statementReconcileId, afterEditSupplier }) => {
  const [edit, setEdit] = useState(false);
  const [value, setValue] = useState(supplierName);
  const onSave = async () => {
    if (!value) message.info("供应商名称不能为空");
    const { status } = await apis.doChangeSupplierName({ statementReconcileId, supplierName: value });
    if (status) {
      setEdit(false);
      afterEditSupplier();
    }
  };
  const onCancel = () => {
    setEdit(false);
    setValue(supplierName);
  };
  return (
    <Row gutter={[2, 0]}>
      <Col span={16}>
        {edit ? <Input placeholder="请输入供应商名称" value={value} onChange={e => setValue(e.target.value)} allowClear /> : supplierName}
      </Col>
      <Col span={6}>
        {edit ? (
          <Space wrap={false} size={[2, 2]}>
            <Button size={SIZE} type="primary" onClick={onSave}>
              保存
            </Button>
            <Button size={SIZE} onClick={onCancel}>
              取消
            </Button>
          </Space>
        ) : (
          <Button size={SIZE} type="primary" danger onClick={() => setEdit(true)}>
            修改
          </Button>
        )}
      </Col>
    </Row>
  );
};
export default React.memo(Edit);
