import { Button, Spin, Tag } from "antd";
import { useEffect, useState } from "react";
import { ProTable } from "@ant-design/pro-components";
import api from "@/services/common";
import { paginationOptions, returnEllipsisTooltip } from "@/utils";
import { SIZE } from "@/utils/constant";
const ExportTable = () => {
  const columns = [
    {
      title: "文件类型",
      dataIndex: "fileTypeDesc",
      key: "fileTypeDesc"
    },
    {
      title: "导出状态",
      dataIndex: "status",
      width: 100,
      key: "status",
      render: (_: any, record: any) => {
        if (record.status === 0) return <Spin />;
        if (record.status === 1) return <Tag color="blue">导出成功</Tag>;
        if (record.status === 2) return <Tag color="red">导出失败</Tag>;
        if (record.status === 3) return <Tag>排队中</Tag>;
      }
    },
    {
      title: "导出行数",
      dataIndex: "rowNum",
      width: 100,
      key: "rowNum"
    },
    {
      title: "点击下载",
      dataIndex: "name",
      width: 100,
      key: "name",
      render: (_: any, record: any) =>
        record.status === 1 ? (
          <Button type="link" href={record.url}>
            下载
          </Button>
        ) : (
          <span></span>
        )
    },
    {
      title: "错误信息",
      dataIndex: "errorMsg",
      key: "errorMsg",
      width: 300,
      render(_, record) {
        return record.status === 2 && returnEllipsisTooltip({ title: record.errorMsg });
      }
    },
    {
      title: "创建人",
      dataIndex: "creator",
      width: 100,
      key: "creator"
    },
    {
      title: "下载时间",
      width: 180,
      dataIndex: "gmtCreate",
      key: "gmtCreate"
    }
  ];

  const [downloadData, setDownloadData] = useState<any>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [pageSize, setPageSize] = useState<number>(50);
  const [pageNo, setPageNo] = useState<number>(1);
  const [total, setTotal] = useState<number>(0);
  const [isLoad, setIsLoad] = useState<boolean>(true);
  const [timerId, setTimerId] = useState(null);

  useEffect(() => {
    let id;
    if (isLoad && !timerId) {
      id = setInterval(() => {
        api
          .queryDownloadRecord({ pageNo, pageSize })
          .then(({ entry, totalRecordSize: total }) => {
            if (entry && entry.length) {
              setDownloadData(entry);
              setTotal(total);
              if (!entry.filter(item => item.status === 0 || item.status === 3).length) {
                setIsLoad(false);
              } else {
                setIsLoad(true);
              }
            }
          })
          .catch(err => {
            console.error(err);
          });
      }, 5000);
      setTimerId(id);
    }

    return () => {
      clearInterval(timerId);
    };
  }, [isLoad, timerId]);

  const getDownloadList = () => {
    setLoading(true);
    api
      .queryDownloadRecord({ pageNo, pageSize })
      .then(({ entry, totalRecordSize: total }) => {
        if (entry && entry.length) {
          setDownloadData(entry);
          setTotal(total);
          if (entry.filter(item => item.status === 0 || item.status === 3).length) {
            setIsLoad(true);
          } else {
            setIsLoad(false);
          }
        }
      })
      .catch(err => {
        console.error(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  useEffect(() => {
    getDownloadList();
  }, [pageSize, pageNo]);

  return (
    <ProTable
      headerTitle="文件导出记录"
      toolBarRender={() => [
        <Button key={"fresh"} onClick={getDownloadList} size={SIZE} type="primary">
          手动刷新
        </Button>
      ]}
      loading={loading}
      bordered
      search={false}
      options={false}
      columns={columns}
      dataSource={downloadData}
      size={SIZE}
      scroll={{ x: "max-content", y: "calc(100vh - 240px)" }}
      pagination={{
        showQuickJumper: true,
        ...paginationOptions,
        current: pageNo,
        pageSize: pageSize,
        onChange: (currentPageNo: number) => {
          setPageNo(currentPageNo);
        },
        onShowSizeChange: (_: number, currentPageSize: number) => {
          setPageSize(currentPageSize);
        },
        total
      }}
    />
  );
};
export default ExportTable;
