import { Select } from "antd";
import AnchorSelect from "@/components/AnchorSelect";
import { filterOptionLabel, returnEllipsisTooltip } from "@/utils";
import { SIZE } from "@/utils/constant";
const columns_search = ({ orderStatusOptions }: any) => {
  return [
    { title: "直播日期", dataIndex: "liveDate", valueType: "dateRange", hideInTable: true },
    { title: "支付日期", dataIndex: "payTime", valueType: "dateRange", hideInTable: true },
    { title: "结算日期", dataIndex: "earningTime", valueType: "dateRange", hideInTable: true },
    {
      title: "分销订单状态",
      dataIndex: "orderStatusList",
      hideInTable: true,
      renderFormItem: () => {
        return (
          <Select
            placeholder="请选择"
            size={SIZE}
            options={orderStatusOptions}
            filterOption={filterOptionLabel}
            showSearch
            allowClear
            maxTagCount="responsive"
            mode="multiple"
          />
        );
      }
    },
    {
      title: "主播名称",
      dataIndex: "anchorNameList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem() {
        return <AnchorSelect params={{ type: 1 }} showSearch allowClear maxTagCount="responsive" mode="multiple" />;
      }
    },
    {
      title: "主播ID",
      dataIndex: "anchorIdList",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "商品标题",
      dataIndex: "itemTitle",
      hideInTable: true
    },
    {
      title: "商品ID",
      dataIndex: "itemIdList",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "店铺名称",
      dataIndex: "shopName",
      hideInTable: true
    },
    {
      title: "订单ID",
      dataIndex: "bizOrderIdList",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    }
  ];
};

export const columns = ({ orderStatusOptions }: any): Array<TableListItem> => {
  return [
    ...columns_search({ orderStatusOptions }),
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      hideInSearch: true,
      fixed: "left",
      render: (_, record, index) => {
        return index + 1;
      }
    },
    { title: "直播日期", dataIndex: "liveDateStr", width: 100, hideInSearch: true },
    {
      title: "主播名称",
      dataIndex: "pidName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "主播id",
      dataIndex: "pid",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "订单id", dataIndex: "bizOrderId", width: 100, hideInSearch: true },
    {
      title: "分销订单状态",
      dataIndex: "orderStatusDesc",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "订单创建时间", dataIndex: "createTime", width: 100, hideInSearch: true },
    { title: "订单支付时间", dataIndex: "payTime", width: 100, hideInSearch: true },
    { title: "订单金额", dataIndex: "totalAmountStr", width: 100, hideInSearch: true },
    { title: "订单结算时间", dataIndex: "earningTime", width: 100, hideInSearch: true },
    { title: "结算金额", dataIndex: "billSettleAmountStr", width: 100, hideInSearch: true },
    {
      title: "子订单id",
      dataIndex: "subBizOrderId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品id",
      dataIndex: "itemId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品标题",
      dataIndex: "itemTitle",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "商品单价快照", dataIndex: "itemPriceStr", width: 100, hideInSearch: true },
    { title: "预估收入", dataIndex: "pubSharePreAmtStr", width: 100, hideInSearch: true },
    { title: "佣金比率", dataIndex: "totalCommissionRateStr", width: 100, hideInSearch: true },
    { title: "平台服务费", dataIndex: "serviceAmtStr", width: 100, hideInSearch: true },
    {
      title: "店铺id",
      dataIndex: "shopId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "店铺名称",
      dataIndex: "shopName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "购买数量", dataIndex: "itemNum", width: 100, hideInSearch: true },
    {
      title: "收货时间",
      dataIndex: "receiveTime",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "发货状态",
      dataIndex: "receivedStatus",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "收货时间",
      dataIndex: "receiveTime",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "买家唯一识别id",
      dataIndex: "buyerId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "退款时间",
      dataIndex: "refundTime",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "退款金额", dataIndex: "refundItemAmountStr", width: 100, hideInSearch: true },
    { title: "退款件数", dataIndex: "refundItemVolume", width: 100, hideInSearch: true },
    {
      title: "是否退款",
      dataIndex: "isRefundStr",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      ...item,
      align: "center",
      title: returnEllipsisTooltip({ title })
    };
  });
};
