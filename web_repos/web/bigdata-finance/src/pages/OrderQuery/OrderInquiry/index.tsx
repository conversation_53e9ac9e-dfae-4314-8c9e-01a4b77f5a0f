import React, { useState, useRef } from "react";
import { But<PERSON> } from "antd";
import KeepAlive from "react-activation";
import TableList from "@/components/TableList";
import { SIZE } from "@/utils/constant";
import { columns } from "./variable";
import apis from "@/services/OrderQuery/OrderInquiry";
import { pushExportHistory, splitSpaceComma } from "@/utils";
import dayjs from "dayjs";
import useCommonOptions from "@/hooks/useCommonOptions";
export const PageUrl = "/orderquery/KSDistrOrder";
const DetailTable = () => {
  const [orderStatusOptions, orderStatusObj] = useCommonOptions({ dimName: "订单查询-订单状态" });
  const subsidyActionRef = useRef<any>();
  const [SPUPageParams, setSPUPageParams] = useState({});

  const columnsProps = {
    orderStatusOptions,
    orderStatusObj
  };

  // 处理请求参数
  const handlePageParmas = (p: any) => {
    const { current, liveDate, payTime, earningTime, anchorIdList, itemIdList, bizOrderIdList, ...rest } = p;
    if (liveDate) {
      const [liveDateStart, liveDateEnd] = liveDate;
      rest.liveDateStart = dayjs(liveDateStart).format("YYYYMMDD");
      rest.liveDateEnd = dayjs(liveDateEnd).format("YYYYMMDD");
    }
    if (payTime) {
      const [payTimeStart, payTimeEnd] = payTime;
      rest.payTimeStart = dayjs(payTimeStart).format("YYYY-MM-DD");
      rest.payTimeEnd = dayjs(payTimeEnd).format("YYYY-MM-DD");
    }
    if (earningTime) {
      const [earningTimeStart, earningTimeEnd] = earningTime;
      rest.earningTimeStart = dayjs(earningTimeStart).format("YYYY-MM-DD");
      rest.earningTimeEnd = dayjs(earningTimeEnd).format("YYYY-MM-DD");
    }
    rest.anchorIdList = splitSpaceComma(anchorIdList);
    rest.itemIdList = splitSpaceComma(itemIdList);
    rest.bizOrderIdList = splitSpaceComma(bizOrderIdList);
    const params = {
      pageNo: current,
      ...rest
    };
    return params;
  };
  const handleExport = async () => {
    const { status } = await apis.downloadDwdTrdOrderAllRi(SPUPageParams);
    if (status) {
      pushExportHistory();
    }
  };

  return (
    <>
      <TableList
        actionRef={subsidyActionRef}
        columns={columns(columnsProps)}
        scroll={{ y: "calc(100vh - 380px)" }}
        form={{
          initialValues: {
            liveDate: [dayjs().subtract(1, "day"), dayjs().subtract(1, "day")]
          }
        }}
        api={apis.getDwdTrdOrderAllRiPage}
        preFetch={handlePageParmas}
        paramsChange={setSPUPageParams}
        toolbar={{
          actions: [
            <Button key="export" onClick={() => handleExport()} size={SIZE}>
              导出
            </Button>
          ]
        }}
      />
    </>
  );
};

const AliveRecord = () => (
  <KeepAlive name={PageUrl}>
    <DetailTable />
  </KeepAlive>
);
export default React.memo(AliveRecord);
