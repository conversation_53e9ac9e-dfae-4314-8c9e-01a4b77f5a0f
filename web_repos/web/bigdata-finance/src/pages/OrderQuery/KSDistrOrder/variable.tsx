import { Select } from "antd";
import { filterOptionLabel, returnEllipsisTooltip } from "@/utils";
import { NOVALUE, SIZE } from "@/utils/constant";
const columns_search = ({ cpsOrderStatusOptions, settlementBizTypeOptions }: any) => {
  return [
    { title: "支付时间", dataIndex: "payTime", valueType: "dateRange", hideInTable: true },
    { title: "结算成功时间", dataIndex: "settlementSuccessTime", valueType: "dateRange", hideInTable: true },
    {
      title: "分销订单状态",
      dataIndex: "cpsOrderStatusList",
      hideInTable: true,
      renderFormItem: () => {
        return (
          <Select
            placeholder="请选择"
            size={SIZE}
            options={cpsOrderStatusOptions}
            filterOption={filterOptionLabel}
            showSearch
            allowClear
            maxTagCount="responsive"
            mode="multiple"
          />
        );
      }
    },

    {
      title: "商品ID",
      dataIndex: "itemIdList",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },

    {
      title: "商品标题",
      dataIndex: "itemTitle",
      hideInTable: true
    },
    {
      title: "主播ID",
      dataIndex: "anchorIdList",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "店铺ID",
      dataIndex: "sellerIdList",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "店铺名称",
      dataIndex: "sellerNickName",
      hideInTable: true
    },
    {
      title: "订单业务结算类型",
      dataIndex: "settlementBizTypeList",
      hideInTable: true,
      renderFormItem: () => {
        return (
          <Select
            placeholder="请选择"
            size={SIZE}
            options={settlementBizTypeOptions}
            filterOption={filterOptionLabel}
            showSearch
            allowClear
            maxTagCount="responsive"
            mode="multiple"
          />
        );
      }
    },
    {
      title: "订单ID",
      dataIndex: "oidList",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    }
  ];
};

export const columns = ({ cpsOrderStatusOptions, settlementBizTypeOptions }: any): Array<TableListItem> => {
  return [
    ...columns_search({ cpsOrderStatusOptions, settlementBizTypeOptions }),
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      hideInSearch: true,
      fixed: "left",
      render: (_, record, index) => {
        return index + 1;
      }
    },
    {
      title: "主播id",
      dataIndex: "distributorId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "订单id", dataIndex: "oid", width: 100, hideInSearch: true },
    {
      title: "分销订单状态",
      dataIndex: "cpsOrderStatusStr",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "订单创建时间", dataIndex: "orderCreateTimeStr", width: 100, hideInSearch: true },
    { title: "支付时间", dataIndex: "payTimeStr", width: 100, hideInSearch: true },
    { title: "订单金额", dataIndex: "orderTradeAmountStr", width: 100, hideInSearch: true },
    { title: "分销订单生成时间", dataIndex: "createTimeStr", width: 100, hideInSearch: true },
    { title: "分销订单更新时间", dataIndex: "updateTimeStr", width: 100, hideInSearch: true },
    { title: "结算成功时间", dataIndex: "settlementSuccessTimeStr", width: 100, hideInSearch: true },
    { title: "结算金额", dataIndex: "settlementAmountStr", width: 100, hideInSearch: true },
    {
      title: "子订单id",
      dataIndex: "subOid",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品信息",
      editable: false,
      dataIndex: "itemInfo",
      width: 300,
      hideInSearch: true,
      render(_, record) {
        const { itemId } = record;
        return (
          <div className="columnItemInfo">
            <div className="info">
              <div className="id">
                <span>{returnEllipsisTooltip({ title: "ID:" + (itemId || NOVALUE) })}</span>
              </div>
              <div>{returnEllipsisTooltip({ title: record?.itemTitle, rows: 1, width: 280 })}</div>
            </div>
          </div>
        );
      }
    },
    { title: "商品单价快照", dataIndex: "itemPriceStr", width: 100, hideInSearch: true },
    { title: "预估收入", dataIndex: "estimatedIncomeStr", width: 100, hideInSearch: true },
    { title: "佣金比率", dataIndex: "commissionRateStr", width: 100, hideInSearch: true },
    { title: "平台服务费率", dataIndex: "serviceRateStr", width: 100, hideInSearch: true },
    { title: "平台服务费", dataIndex: "serviceAmountStr", width: 100, hideInSearch: true },
    {
      title: "推广位id",
      dataIndex: "cpsPid",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "店铺Id",
      dataIndex: "sellerId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "店铺名称",
      dataIndex: "sellerNickName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "购买数量", dataIndex: "numStr", width: 100, hideInSearch: true },
    { title: "数据处理时间", dataIndex: "procTimeStr", width: 100, hideInSearch: true },
    { title: "发货时间", dataIndex: "sendTimeStr", width: 100, hideInSearch: true },
    { title: "发货状态", dataIndex: "sendStatusStr", width: 100, hideInSearch: true },
    { title: "收货时间", dataIndex: "recvTimeStr", width: 100, hideInSearch: true },
    {
      title: "买家唯一识别id",
      dataIndex: "buyerOpenId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "订单业务结算类型",
      dataIndex: "settlementBizTypeStr",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    }
    // {
    //   title: "退款信息",
    //   dataIndex: "orderRefundInfo",
    //   width: 100,
    //   hideInSearch: true,
    //   render(text) {
    //     return returnEllipsisTooltip({ title: text });
    //   }
    // },
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      ...item,
      align: "center",
      title: returnEllipsisTooltip({ title })
    };
  });
};
