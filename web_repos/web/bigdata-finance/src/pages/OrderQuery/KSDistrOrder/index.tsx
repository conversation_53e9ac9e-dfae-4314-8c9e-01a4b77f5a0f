import React, { useState, useRef } from "react";
import { But<PERSON> } from "antd";
import KeepAlive from "react-activation";
import TableList from "@/components/TableList";
import { SIZE } from "@/utils/constant";
import { columns } from "./variable";
import apis from "@/services/OrderQuery/KSDistrOrder";
import { pushExportHistory, splitSpaceComma } from "@/utils";
import dayjs from "dayjs";
import useCommonOptions from "@/hooks/useCommonOptions";
export const PageUrl = "/orderquery/KSDistrOrder";
const DetailTable = () => {
  const [cpsOrderStatusOptions, cpsOrderStatusObj] = useCommonOptions({ dimName: "分销订单状态" });
  const [settlementBizTypeOptions, settlementBizTypeObj] = useCommonOptions({ dimName: "订单业务结算类型" });
  const subsidyActionRef = useRef<any>();
  const [SPUPageParams, setSPUPageParams] = useState({});

  const columnsProps = {
    cpsOrderStatusOptions,
    cpsOrderStatusObj,
    settlementBizTypeOptions,
    settlementBizTypeObj
  };

  // 处理请求参数
  const handlePageParmas = (p: any) => {
    const { current, payTime, settlementSuccessTime, itemIdList, sellerIdList, anchorIdList, oidList, ...rest } = p;
    if (payTime) {
      const [payTimeStart, payTimeEnd] = payTime;
      rest.payTimeStart = dayjs(payTimeStart).format("YYYY-MM-DD");
      rest.payTimeEnd = dayjs(payTimeEnd).format("YYYY-MM-DD");
    }
    if (settlementSuccessTime) {
      const [settlementSuccessTimeStart, settlementSuccessTimeEnd] = settlementSuccessTime;
      rest.settlementSuccessTimeStart = dayjs(settlementSuccessTimeStart).format("YYYY-MM-DD");
      rest.settlementSuccessTimeEnd = dayjs(settlementSuccessTimeEnd).format("YYYY-MM-DD");
    }
    rest.itemIdList = splitSpaceComma(itemIdList);
    rest.sellerIdList = splitSpaceComma(sellerIdList);
    rest.anchorIdList = splitSpaceComma(anchorIdList);
    rest.oidList = splitSpaceComma(oidList);
    const params = {
      pageNo: current,
      ...rest
    };
    return params;
  };
  const handleExport = async () => {
    const { status } = await apis.downloadOdsOutWayKsCpsOrder(SPUPageParams);
    if (status) {
      pushExportHistory();
    }
  };

  return (
    <>
      <TableList
        actionRef={subsidyActionRef}
        columns={columns(columnsProps)}
        scroll={{ y: "calc(100vh - 380px)" }}
        form={{
          initialValues: {
            payTime: [dayjs().subtract(1, "day"), dayjs().subtract(1, "day")]
          }
        }}
        api={apis.getOdsOutWayKsCpsOrderPage}
        preFetch={handlePageParmas}
        paramsChange={setSPUPageParams}
        toolbar={{
          actions: [
            <Button key="export" onClick={() => handleExport()} size={SIZE}>
              导出
            </Button>
          ]
        }}
      />
    </>
  );
};

const AliveRecord = () => (
  <KeepAlive name={PageUrl}>
    <DetailTable />
  </KeepAlive>
);
export default React.memo(AliveRecord);
