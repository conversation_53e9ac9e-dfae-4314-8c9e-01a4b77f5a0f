import { Select, Image } from "antd";
import { filterOptionLabel, returnEllipsisTooltip, returnItemImgUrl } from "@/utils";
import { NOVALUE, SIZE } from "@/utils/constant";
const columns_search = ({ cpsTypeOptions, carrierTypeOptions, statusOptions }: any) => {
  return [
    { title: "创建时间", dataIndex: "createTime", valueType: "dateRange", hideInTable: true },
    {
      title: "订单编号",
      dataIndex: "oidList",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "订单状态",
      dataIndex: "statusList",
      hideInTable: true,
      renderFormItem: () => {
        return (
          <Select
            placeholder="请选择"
            size={SIZE}
            options={statusOptions}
            filterOption={filterOptionLabel}
            showSearch
            allowClear
            maxTagCount="responsive"
            mode="multiple"
          />
        );
      }
    },

    {
      title: "分销类型",
      dataIndex: "cpsType",
      hideInTable: true,
      renderFormItem: () => {
        return (
          <Select
            placeholder="请选择"
            size={SIZE}
            options={cpsTypeOptions}
            filterOption={filterOptionLabel}
            showSearch
            allowClear
            // maxTagCount="responsive"
            // mode="multiple"
          />
        );
      }
    },
    {
      title: "店铺ID",
      dataIndex: "sellerIdList",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "店铺名称",
      dataIndex: "sellerNick",
      hideInTable: true
    },
    {
      title: "快手商品id",
      dataIndex: "itemIdList",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "订单渠道来源",
      dataIndex: "carrierTypeList",
      hideInTable: true,
      renderFormItem: () => {
        return (
          <Select
            placeholder="请选择"
            size={SIZE}
            options={carrierTypeOptions}
            filterOption={filterOptionLabel}
            showSearch
            allowClear
            maxTagCount="responsive"
            mode="multiple"
          />
        );
      }
    },
    {
      title: "售卖人昵称",
      dataIndex: "roleName",
      hideInTable: true
    }
  ];
};

export const columns = ({ cpsTypeOptions, carrierTypeOptions, statusOptions }: any): Array<TableListItem> => {
  return [
    ...columns_search({ cpsTypeOptions, carrierTypeOptions, statusOptions }),
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      hideInSearch: true,
      fixed: "left",
      render: (_, record, index) => {
        return index + 1;
      }
    },
    { title: "创建时间", dataIndex: "createTimeStr", width: 100, hideInSearch: true },
    { title: "支付时间", dataIndex: "payTimeStr", width: 100, hideInSearch: true },
    {
      title: "订单编号",
      dataIndex: "oid",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "买家信息",
      editable: false,
      dataIndex: "buyerInfo",
      width: 150,
      hideInSearch: true,
      render(_, record) {
        const { buyerImage } = record;
        return (
          <div className="columnItemInfo">
            <Image width={50} height={50} src={returnItemImgUrl(buyerImage)} />
            <div className="infoLeft">
              {/* <div>{returnEllipsisTooltip({ title: "ID:" + (record?.buyerId || NOVALUE) })}</div> */}
              <div>{returnEllipsisTooltip({ title: record?.buyerNick, rows: 1, width: 130 })}</div>
            </div>
          </div>
        );
      }
    },
    { title: "店铺Id", dataIndex: "sellerId", width: 100, hideInSearch: true },
    {
      title: "店铺名称",
      dataIndex: "sellerNick",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "收件人手机号（以下单时填写的为准）", dataIndex: "mobile", width: 100, hideInSearch: true },
    // {
    //   title: "收货地址",
    //   dataIndex: "address",
    //   width: 100,
    //   hideInSearch: true,
    //   render(text) {
    //     return returnEllipsisTooltip({ title: text });
    //   }
    // },
    { title: "是否退款", dataIndex: "refundStr", width: 100, hideInSearch: true },
    { title: "子订单商品总价", dataIndex: "totalFeeStr", width: 100, hideInSearch: true },
    { title: "折扣价格", dataIndex: "discountFeeStr", width: 100, hideInSearch: true },
    { title: "运费", dataIndex: "expressFeeStr", width: 100, hideInSearch: true },
    {
      title: "订单状态",
      dataIndex: "statusStr",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "发货时间", dataIndex: "sendTimeStr", width: 100, hideInSearch: true },
    { title: "退款时间(只要买家有申请退款行为)", dataIndex: "refundTimeStr", width: 100, hideInSearch: true },
    { title: "sku总数", dataIndex: "numStr", width: 100, hideInSearch: true },
    {
      title: "买家留言",
      dataIndex: "remark",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "卖家备注",
      dataIndex: "sellerNoteList",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "分销类型", dataIndex: "cpsTypeStr", width: 100, hideInSearch: true },
    { title: "货物到达日期", dataIndex: "theDayOfDeliverGoodsTimeStr", width: 100, hideInSearch: true },
    { title: "激活状态", dataIndex: "activityTypeStr", width: 100, hideInSearch: true },
    {
      title: "子订单编号",
      dataIndex: "oid",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "快手商品skuid",
      dataIndex: "skuId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "sku规格",
      dataIndex: "skuDesc",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "sku编码",
      dataIndex: "skuNick",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    // { title: "快手商品id", dataIndex: "itemId", width: 100, hideInSearch: true },
    // { title: "商品名称", dataIndex: "itemTitle", width: 100, hideInSearch: true },
    // { title: "商品链接", dataIndex: "itemLinkUrl", width: 100, hideInSearch: true },
    // { title: "商品图片地址", dataIndex: "itemPicUrl", width: 100, hideInSearch: true },
    {
      title: "商品信息",
      editable: false,
      dataIndex: "itemInfo",
      width: 300,
      hideInSearch: true,
      render(_, record) {
        const { itemPicUrl, itemId, itemLinkUrl } = record;
        return (
          <div className="columnItemInfo">
            <Image width={50} height={50} src={returnItemImgUrl(itemPicUrl)} />
            <div className="info">
              <div className="id">
                <span>{returnEllipsisTooltip({ title: "ID:" + (itemId || NOVALUE) })}</span>
              </div>
              <a href={`${itemLinkUrl ? itemLinkUrl : "#"}`}>{returnEllipsisTooltip({ title: record?.itemTitle, rows: 1, width: 250 })}</a>
            </div>
          </div>
        );
      }
    },
    { title: "服务商商品id", dataIndex: "relItemId", width: 100, hideInSearch: true },
    {
      title: "服务商商品skuid",
      dataIndex: "relSkuId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "子订单成交数量", dataIndex: "inumStr", width: 100, hideInSearch: true },
    { title: "商品促销前单价快照", dataIndex: "originalPriceStr", width: 100, hideInSearch: true },
    { title: "商品单价快照", dataIndex: "priceStr", width: 100, hideInSearch: true },
    { title: "折扣金额", dataIndex: "discountFeeStr", width: 100, hideInSearch: true },
    { title: "退款订单id", dataIndex: "refundId", width: 100, hideInSearch: true },
    { title: "退款状态", dataIndex: "refundStatusStr", width: 100, hideInSearch: true },
    // {
    //   title: "订单对应的退款单列表信息(支持多次退款后增加)",
    //   dataIndex: "refundList",
    //   width: 100,
    //   hideInSearch: true,
    //   render(text) {
    //     return returnEllipsisTooltip({ title: text });
    //   }
    // },
    { title: "商品类型", dataIndex: "itemTypeStr", width: 100, hideInSearch: true },
    {
      title: "订单修改之前的原订单信息",
      dataIndex: "prevInfo",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    // {
    //   title: "订单物流信息",
    //   dataIndex: "logisticsInfo",
    //   width: 100,
    //   hideInSearch: true,
    //   render(text) {
    //     return returnEllipsisTooltip({ title: text });
    //   }
    // },
    {
      title: "分佣信息",
      dataIndex: "cpsInfo",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "推广者id",
      dataIndex: "distributorId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "承诺发货时间点", dataIndex: "promiseTimeStampOfDeliveryStr", width: 100, hideInSearch: true },
    {
      title: "支付方式",
      dataIndex: "payTypeStr",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "插旗颜色", dataIndex: "flagTagCodeStr", width: 100, hideInSearch: true },
    { title: "预售发货时间", dataIndex: "validPromiseShipmentTimeStampStr", width: 100, hideInSearch: true },
    { title: "是否预售", dataIndex: "preSaleStr", width: 100, hideInSearch: true },
    { title: "确认发货时间", dataIndex: "recvTimeStr", width: 100, hideInSearch: true },
    {
      title: "虚拟订单类型",
      dataIndex: "coTypeStr",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "买家Id",
      dataIndex: "buyerOpenId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "卖家Id",
      dataIndex: "sellerOpenId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "评价状态", dataIndex: "commentStatusStr", width: 100, hideInSearch: true },
    {
      title: "外部货品编码",
      dataIndex: "goodsCode",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "快手仓库编码",
      dataIndex: "wareHouseCode",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    // { title: "服务标签", dataIndex: "serviceRule", width: 100, hideInSearch: true },
    { title: "是否使用运费险", dataIndex: "freightStr", width: 100, hideInSearch: true },
    { title: "运费险提供者类型", dataIndex: "freightProviderTypeStr", width: 100, hideInSearch: true },
    { title: "首单保障", dataIndex: "firstOrderGuaranteeStr", width: 100, hideInSearch: true },
    { title: "急速发货", dataIndex: "instantDeliveryStr", width: 100, hideInSearch: true },
    { title: "急速退款", dataIndex: "instantRefundStr", width: 100, hideInSearch: true },
    // {
    //   title: "假一赔N",
    //   dataIndex: "compensateFake",
    //   width: 100,
    //   hideInSearch: true,
    //   render(text) {
    //     return returnEllipsisTooltip({ title: text });
    //   }
    // },
    // {
    //   title: "服务规则",
    //   dataIndex: "serviceRule",
    //   width: 100,
    //   hideInSearch: true,
    //   render(text) {
    //     return returnEllipsisTooltip({ title: text });
    //   }
    // },
    {
      title: "主播名称",
      dataIndex: "distributorName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "支付渠道", dataIndex: "payChannelStr", width: 100, hideInSearch: true },
    { title: "商家报备审核后的发货时间", dataIndex: "sellerDelayPromiseTimeStampStr", width: 100, hideInSearch: true },
    {
      title: "风险Code码",
      dataIndex: "riskCodeStr",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    // {
    //   title: "加密信息-地址,手机,收件人",
    //   dataIndex: "userEncrypt",
    //   width: 100,
    //   hideInSearch: true,
    //   render(text) {
    //     return returnEllipsisTooltip({ title: text });
    //   }
    // },
    { title: "店铺新客", dataIndex: "shopNewBuyerStr", width: 100, hideInSearch: true },
    { title: "平台新客", dataIndex: "platformNewBuyerStr", width: 100, hideInSearch: true },
    { title: "订单渠道来源", dataIndex: "carrierTypeStr", width: 100, hideInSearch: true },
    { title: "类目ID", dataIndex: "itemCid", width: 100, hideInSearch: true },
    {
      title: "类目名称",
      dataIndex: "categoryName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "是否优先发货", dataIndex: "priorityDeliveryStr", width: 100, hideInSearch: true },
    { title: "催发货的次数", dataIndex: "remindShipmentSign", width: 100, hideInSearch: true },
    { title: "最近催发货的时间戳", dataIndex: "remindShipmentTimeStr", width: 100, hideInSearch: true },
    { title: "售卖人角色类型", dataIndex: "roleTypeStr", width: 100, hideInSearch: true },
    { title: "售卖人id", dataIndex: "roleId", width: 100, hideInSearch: true },
    {
      title: "售卖人昵称",
      dataIndex: "roleName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "直播间id或短视频id",
      dataIndex: "carrierId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "数据处理时间", dataIndex: "procTimeStr", width: 100, hideInSearch: true }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      ...item,
      align: "center",
      title: returnEllipsisTooltip({ title })
    };
  });
};
