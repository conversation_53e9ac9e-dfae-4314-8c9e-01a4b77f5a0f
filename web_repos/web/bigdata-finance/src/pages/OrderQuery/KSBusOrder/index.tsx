import React, { useState, useRef } from "react";
import { But<PERSON> } from "antd";
import KeepAlive from "react-activation";
import TableList from "@/components/TableList";
import { SIZE } from "@/utils/constant";
import { columns } from "./variable";
import apis from "@/services/OrderQuery/KSBusOrder";
import { pushExportHistory, splitSpaceComma } from "@/utils";
import useCommonOptions from "@/hooks/useCommonOptions";
import dayjs from "dayjs";
export const PageUrl = "/orderquery/KSBusOrder";
const DetailTable = () => {
  const [cpsTypeOptions, cpsTypeObj] = useCommonOptions({ dimName: "分销类型" });
  const [carrierTypeOptions, carrierTypeObj] = useCommonOptions({ dimName: "订单渠道来源" });
  const [statusOptions, statusObj] = useCommonOptions({ dimName: "订单状态" });
  const subsidyActionRef = useRef<any>();
  const [SPUPageParams, setSPUPageParams] = useState({});

  const columnsProps = {
    cpsTypeOptions,
    cpsTypeObj,
    carrierTypeOptions,
    carrierTypeObj,
    statusOptions,
    statusObj
  };

  // 处理请求参数
  const handlePageParmas = (p: any) => {
    const { current, createTime, oidList, itemIdList, sellerIdList, ...rest } = p;
    if (createTime) {
      const [createTimeStart, createTimeEnd] = createTime;
      rest.createTimeStart = dayjs(createTimeStart).format("YYYY-MM-DD");
      rest.createTimeEnd = dayjs(createTimeEnd).format("YYYY-MM-DD");
    }
    rest.oidList = splitSpaceComma(oidList);
    rest.itemIdList = splitSpaceComma(itemIdList);
    rest.sellerIdList = splitSpaceComma(sellerIdList);
    const params = {
      pageNo: current,
      ...rest
    };
    return params;
  };
  const handleExport = async () => {
    const { status } = await apis.downloadOutWayKsTradeOrder(SPUPageParams);
    if (status) {
      pushExportHistory();
    }
  };

  return (
    <>
      <TableList
        actionRef={subsidyActionRef}
        columns={columns(columnsProps)}
        scroll={{ y: "calc(100vh - 390px)" }}
        form={{
          initialValues: {
            createTime: [dayjs().subtract(1, "day"), dayjs().subtract(1, "day")]
          }
        }}
        api={apis.getOutWayKsTradeOrderPage}
        preFetch={handlePageParmas}
        paramsChange={setSPUPageParams}
        toolbar={{
          actions: [
            <Button key="export" onClick={() => handleExport()} size={SIZE}>
              导出
            </Button>
          ]
        }}
      />
    </>
  );
};

const AliveRecord = () => (
  <KeepAlive name={PageUrl}>
    <DetailTable />
  </KeepAlive>
);
export default React.memo(AliveRecord);
