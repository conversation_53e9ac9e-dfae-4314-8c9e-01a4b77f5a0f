import { Select, Image } from "antd";
import { filterOptionLabel, returnEllipsisTooltip, returnItemImgUrl } from "@/utils/index";
import { NOVALUE, SIZE } from "@/utils/constant";

const columns_search = ({ refundStatusOptions }: any): Array<TableListItem> => {
  return [
    {
      title: "结束时间",
      dataIndex: "endTime",
      hideInSearch: false,
      hideInTable: true,
      valueType: "dateRange"
    },
    {
      title: "退款状态描述",
      dataIndex: "refundStatusList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return (
          <Select
            placeholder="请选择"
            style={{ minWidth: 120, textAlign: "left" }}
            size={SIZE}
            options={refundStatusOptions}
            filterOption={filterOptionLabel}
            showSearch
            allowClear
            maxTagCount="responsive"
            mode="multiple"
          />
        );
      }
    },
    {
      title: "退款单ID",
      dataIndex: "refundIdList",
      hideInSearch: false,
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },

    {
      title: "主订单编号",
      dataIndex: "bizOrderIdList",
      hideInSearch: false,
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "商品ID",
      dataIndex: "itemIdList",
      hideInSearch: false,
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "商品skuid",
      dataIndex: "skuIdList",
      hideInSearch: false,
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "店铺ID",
      dataIndex: "shopIdList",
      hideInSearch: false,
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    }
  ];
};
export const columns = ({ refundStatusOptions }: any): Array<TableListItem> => {
  return [
    ...columns_search({ refundStatusOptions }),
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      editable: false,
      hideInSearch: true,
      fixed: "left",
      render: (_, record, index) => {
        return index + 1;
      }
    },
    {
      title: "退款单ID",
      dataIndex: "refundId",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "退款状态描述",
      dataIndex: "refundStatusDesc",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "主订单编号",
      dataIndex: "bizOrderId",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "子订单编号",
      dataIndex: "subBizOrderId",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "退款金额", dataIndex: "refundAmtStr", width: 100, hideInSearch: true },
    { title: "子订单应付金额", dataIndex: "totalAmountStr", width: 100, hideInSearch: true },
    { title: "子订单成交数量", dataIndex: "itemNumStr", width: 100, hideInSearch: true },
    {
      title: "退款方式描述",
      dataIndex: "refundWayDesc",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "退款原因描述",
      dataIndex: "refundReasonDesc",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "退款类型描述",
      dataIndex: "refundTypeDesc",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "退款描述",
      dataIndex: "refundDesc",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    // { title: "申请人关于退款的举证图片列表", dataIndex: "pictures", width: 100, hideInSearch: true },
    {
      title: "商品信息",
      editable: false,
      dataIndex: "itemInfo",
      width: 300,
      hideInSearch: true,
      render(_, record) {
        const { itemMainImg } = record;
        return (
          <div className="columnItemInfo">
            <Image width={50} height={50} src={returnItemImgUrl(itemMainImg)} />
            <div className="info">
              <div className="id">
                <span>{returnEllipsisTooltip({ title: "ID:" + (record?.itemId || NOVALUE) })}</span>
              </div>
              <div>{returnEllipsisTooltip({ title: record?.itemTitle, rows: 1, width: 250 })}</div>
            </div>
          </div>
        );
      }
    },
    {
      title: "服务商商品ID",
      dataIndex: "outerItemId",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "服务商skuid",
      dataIndex: "outerSkuId",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品skuid",
      dataIndex: "skuId",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "sku规格",
      dataIndex: "skuName",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "sku编码",
      dataIndex: "skuCode",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "商品原价", dataIndex: "itemOriginalPriceStr", width: 100, hideInSearch: true },
    { title: "商品单价", dataIndex: "itemPriceStr", width: 100, hideInSearch: true },
    {
      title: "店铺ID",
      dataIndex: "shopId",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商家昵称",
      dataIndex: "sellerNick",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商家拒绝原因描述",
      dataIndex: "sellerDisagreeReasonDesc",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商家拒绝文字说明",
      dataIndex: "sellerDisagreeDesc",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    // { title: "商家拒绝图片凭证", dataIndex: "sellerDisagreeImages", width: 100, hideInSearch: true },
    {
      title: "买家快手ID",
      dataIndex: "buyerId",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "买家昵称",
      dataIndex: "buyerNick",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "收件人姓名",
      dataIndex: "receiverName",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "省",
      dataIndex: "province",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "市",
      dataIndex: "city",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "区",
      dataIndex: "district",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "物流公司ID",
      dataIndex: "logisticsId",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "快递单号",
      dataIndex: "invoiceNo",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "快递公司编码",
      dataIndex: "logisticsCode",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "快递公司",
      dataIndex: "logisticsCompany",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "内部快递公司编",
      dataIndex: "logisticsInnerCode",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "协商状态描述",
      dataIndex: "negotiateStatusDesc",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "协商状态变更原因",
      dataIndex: "negotiateReason",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "协商相关核心信息变更时间", dataIndex: "negotiateUpdateTimeStr", width: 100, hideInSearch: true },
    { title: "买家能修改WAIT_MODIFY的时间限制", dataIndex: "negotiateTimeLimitStr", width: 100, hideInSearch: true },
    { title: "买家修改商家不同意的退款单时限", dataIndex: "negotiateTimeValidStr", width: 100, hideInSearch: true },
    { title: "退款创建时间", dataIndex: "createTime", width: 100, hideInSearch: true },
    { title: "退款修改时间", dataIndex: "updateTime", width: 100, hideInSearch: true },
    { title: "超期时间", dataIndex: "expireTime", width: 100, hideInSearch: true },
    { title: "结束时间", dataIndex: "endTime", width: 100, hideInSearch: true },
    {
      title: "退款商品ID",
      dataIndex: "refundItemId",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "货物状态",
      dataIndex: "receiptStatusStr",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "销售订单创建时间", dataIndex: "tradeOrderCreateTime", width: 100, hideInSearch: true },
    { title: "退货申请时间", dataIndex: "submitTime", width: 100, hideInSearch: true },
    {
      title: "退款原因文字说明",
      dataIndex: "refundReasonDescNew",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "地址",
      dataIndex: "address",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "买家的加密ID",
      dataIndex: "buyerOpenId",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "数据同步创建时间", dataIndex: "etlGmtCreate", width: 100, hideInSearch: true },
    { title: "数据同步修改时间", dataIndex: "etlGmtModified", width: 100, hideInSearch: true },
    { title: "数据处理时间", dataIndex: "procTime", width: 100, hideInSearch: true }
  ].map(item => {
    const { title } = item;
    return {
      ...item,
      align: "center",
      title: returnEllipsisTooltip({ title })
    };
  });
};
