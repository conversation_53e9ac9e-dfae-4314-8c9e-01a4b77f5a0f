import React, { useRef, useState } from "react";
import { Button } from "antd";
import { columns } from "./variable";
import { NOVALUE, Protable_FROM_CONFIG } from "@/utils/constant";
import KeepAlive from "react-activation";
import { pushExportHistory, splitSpaceComma } from "@/utils";
import apis from "@/services/aftersalequery/KSAftersaleBill";
import TableList from "@/components/TableList";
import useCommonOptions from "@/hooks/useCommonOptions";
import dayjs from "dayjs";
const PageUrl = "/aftersalequery/KSAftersaleBill";
const Res: React.FC = () => {
  const [refundStatusOptions, refundStatusObj] = useCommonOptions({ dimName: "退款状态" });
  const proTableFormRef = useRef<any>();
  const actionRef = useRef<any>();
  const [pageParmas, setPageParmas] = useState<any>({});

  const columnsProps = {
    refundStatusOptions,
    refundStatusObj
  };

  const handleExport = async () => {
    const { status } = await apis.downloadOrderShopRefund(pageParmas);
    if (status) {
      pushExportHistory();
    }
  };
  // 处理请求参数
  const handlePageParmas = (params: any) => {
    const { current, refundIdList, bizOrderIdList, itemIdList, skuIdList, shopIdList, endTime, ...rest } = params;
    if (endTime) {
      const [endTimeStart, endTimeEnd] = endTime;
      rest.endTimeStart = dayjs(endTimeStart).format("YYYY-MM-DD");
      rest.endTimeEnd = dayjs(endTimeEnd).format("YYYY-MM-DD");
    }
    rest.refundIdList = splitSpaceComma(refundIdList);
    rest.bizOrderIdList = splitSpaceComma(bizOrderIdList);
    rest.itemIdList = splitSpaceComma(itemIdList);
    rest.skuIdList = splitSpaceComma(skuIdList);
    rest.shopIdList = splitSpaceComma(shopIdList);
    return { ...rest, pageNo: current };
  };

  return (
    <div>
      <TableList
        formRef={proTableFormRef}
        actionRef={actionRef}
        columnEmptyText={NOVALUE}
        columns={columns(columnsProps)}
        scroll={{ x: "max-content", y: "calc(100vh - 360px)" }}
        form={{
          ...Protable_FROM_CONFIG,
          initialValues: {
            endTime: [dayjs().subtract(1, "day"), dayjs().subtract(1, "day")]
          }
        }}
        api={apis.getOrderShopRefundPage}
        preFetch={handlePageParmas}
        paramsChange={setPageParmas}
        toolbar={{
          actions: [
            <>
              <Button onClick={handleExport} size="small">
                导出
              </Button>
            </>
          ]
        }}
        rowKey={"refundId"}
      />
    </div>
  );
};

const AliveRecord = () => (
  <KeepAlive name={PageUrl}>
    <Res />
  </KeepAlive>
);
export default React.memo(AliveRecord);
