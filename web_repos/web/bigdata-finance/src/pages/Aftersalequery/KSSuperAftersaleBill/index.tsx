import React, { useRef, useState } from "react";
import { Button } from "antd";
import { columns } from "./variable";
import { NOVALUE, Protable_FROM_CONFIG } from "@/utils/constant";
import KeepAlive from "react-activation";
import { pushExportHistory, splitSpaceComma } from "@/utils";
import apis from "@/services/aftersalequery/KSSuperAftersaleBill";
import TableList from "@/components/TableList";
import dayjs from "dayjs";
import useCommonOptions from "@/hooks/useCommonOptions";
const PageUrl = "/aftersalequery/KSSuperAftersaleBill";
const Res: React.FC = () => {
  const [orderChannelOptions, orderChannelObj] = useCommonOptions({ dimName: "订单渠道" });
  const [orderTypeOptions, orderTypeObj] = useCommonOptions({ dimName: "订单类型" });
  const [shopTypeOptions, shopTypeObj] = useCommonOptions({ dimName: "店铺性质" });
  const proTableFormRef = useRef<any>();
  const actionRef = useRef<any>();
  const [pageParmas, setPageParmas] = useState<any>({});

  const columnsProps = {
    orderChannelOptions,
    orderChannelObj,
    orderTypeOptions,
    orderTypeObj,
    shopTypeOptions,
    shopTypeObj
  };

  const handleExport = async () => {
    const { status } = await apis.downloadShopRefundOrder(pageParmas);
    if (status) {
      pushExportHistory();
    }
  };
  // 处理请求参数
  const handlePageParmas = (params: any) => {
    const { current, liveDate, refundFinishTime, refundNoList, itemIdList, bizOrderIdList, shopIdList, ...rest } = params;
    if (liveDate) {
      const [liveDateStart, liveDateEnd] = liveDate;
      rest.liveDateStart = dayjs(liveDateStart).format("YYYYMMDD");
      rest.liveDateEnd = dayjs(liveDateEnd).format("YYYYMMDD");
    }
    if (refundFinishTime) {
      const [refundFinishTimeStart, refundFinishTimeEnd] = refundFinishTime;
      rest.refundFinishTimeStart = dayjs(refundFinishTimeStart).format("YYYY-MM-DD");
      rest.refundFinishTimeEnd = dayjs(refundFinishTimeEnd).format("YYYY-MM-DD");
    }
    rest.refundNoList = splitSpaceComma(refundNoList);
    rest.itemIdList = splitSpaceComma(itemIdList);
    rest.bizOrderIdList = splitSpaceComma(bizOrderIdList);
    rest.shopIdList = splitSpaceComma(shopIdList);
    return { ...rest, pageNo: current };
  };

  return (
    <div>
      <TableList
        formRef={proTableFormRef}
        actionRef={actionRef}
        columnEmptyText={NOVALUE}
        columns={columns(columnsProps)}
        scroll={{ x: "max-content", y: "calc(100vh - 440px)" }}
        form={{
          ...Protable_FROM_CONFIG,
          initialValues: {
            refundFinishTime: [dayjs().subtract(1, "month"), dayjs().subtract(1, "day")]
          }
        }}
        api={apis.getShopRefundOrderPage}
        preFetch={handlePageParmas}
        paramsChange={setPageParmas}
        toolbar={{
          actions: [
            <>
              <Button onClick={handleExport} size="small">
                导出
              </Button>
            </>
          ]
        }}
        rowKey={"orderId"}
      />
    </div>
  );
};

const AliveRecord = () => (
  <KeepAlive name={PageUrl}>
    <Res />
  </KeepAlive>
);
export default React.memo(AliveRecord);
