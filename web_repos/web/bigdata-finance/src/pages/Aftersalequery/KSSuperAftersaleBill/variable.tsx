import { Select } from "antd";
import { filterOptionLabel, returnEllipsisTooltip } from "@/utils/index";
import { NOVALUE, SIZE } from "@/utils/constant";
import AnchorSelect from "@/components/AnchorSelect";

const columns_search = ({ orderChannelOptions, orderTypeOptions, shopTypeOptions }: any): Array<TableListItem> => {
  return [
    {
      title: "直播日期",
      dataIndex: "liveDate",
      hideInSearch: false,
      hideInTable: true,
      valueType: "dateRange",
      fieldProps: {
        allowClear: true
      }
    },
    {
      title: "维权完成时间",
      dataIndex: "refundFinishTime",
      hideInSearch: false,
      hideInTable: true,
      valueType: "dateRange",
      fieldProps: {
        allowClear: true
      }
    },

    {
      title: "超售后期退款单号",
      dataIndex: "refundNoList",
      hideInSearch: false,
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "子订单编号",
      dataIndex: "bizOrderIdList",
      hideInSearch: false,
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "订单来源",
      dataIndex: "orderChannelList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return (
          <Select
            placeholder="请选择"
            size={SIZE}
            options={orderChannelOptions}
            filterOption={filterOptionLabel}
            showSearch
            allowClear
            maxTagCount="responsive"
            mode="multiple"
          />
        );
      }
    },
    {
      title: "订单类型",
      dataIndex: "orderTypeList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return (
          <Select
            placeholder="请选择"
            size={SIZE}
            options={orderTypeOptions}
            filterOption={filterOptionLabel}
            showSearch
            allowClear
            maxTagCount="responsive"
            mode="multiple"
          />
        );
      }
    },
    {
      title: "商品ID",
      dataIndex: "itemIdList",
      hideInSearch: false,
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "商品标题",
      dataIndex: "itemTitle",
      hideInSearch: false,
      hideInTable: true
    },

    {
      title: "主播",
      dataIndex: "anchorNameList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return <AnchorSelect maxTagCount="responsive" mode="multiple" />;
      }
    },
    {
      title: "商品品牌",
      dataIndex: "brandName",
      hideInSearch: false,
      hideInTable: true
    },
    {
      title: "店铺ID",
      dataIndex: "shopIdList",
      hideInSearch: false,
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "店铺名称",
      dataIndex: "shopName",
      hideInSearch: false,
      hideInTable: true
    },
    {
      title: "店铺性质",
      dataIndex: "shopTypeList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return (
          <Select
            placeholder="请选择"
            size={SIZE}
            options={shopTypeOptions}
            filterOption={filterOptionLabel}
            showSearch
            allowClear
            maxTagCount="responsive"
            mode="multiple"
          />
        );
      }
    }
  ];
};
export const columns = ({ orderChannelOptions, orderTypeOptions, shopTypeOptions }: any): Array<TableListItem> => {
  return [
    ...columns_search({ orderChannelOptions, orderTypeOptions, shopTypeOptions }),
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      editable: false,
      hideInSearch: true,
      fixed: "left",
      render: (_, record, index) => {
        return index + 1;
      }
    },
    {
      title: "超售后期退款单号",
      dataIndex: "refundNo",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "子订单编号",
      dataIndex: "bizOrderId",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "订单创建时间", dataIndex: "orderTime", width: 100, hideInSearch: true },
    {
      title: "订单来源描述",
      dataIndex: "orderChannelDesc",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "订单类型描述",
      dataIndex: "orderTypeDesc",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "是否结算主播", dataIndex: "isSettleAnchorStr", width: 100, hideInSearch: true },
    { title: "用户实际付款金额", dataIndex: "actualAmountStr", width: 100, hideInSearch: true },
    { title: "店铺侧技术服务费", dataIndex: "shopServiceAmountStr", width: 100, hideInSearch: true },
    { title: "主播补贴款金额", dataIndex: "anchorAllowAmountStr", width: 100, hideInSearch: true },
    { title: "平台补贴金额", dataIndex: "platAllowAmountStr", width: 100, hideInSearch: true },
    { title: "平台补贴退款金额", dataIndex: "platAllowRefundAmountStr", width: 100, hideInSearch: true },
    { title: "维权金额", dataIndex: "refundAmountStr", width: 100, hideInSearch: true },
    { title: "运费退款金额", dataIndex: "freightRefundAmountStr", width: 100, hideInSearch: true },
    {
      title: "维权追回状态",
      dataIndex: "backStatusStr",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "不追回原因",
      dataIndex: "unbackReason",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "维权订单创建时间", dataIndex: "refundStartTime", width: 100, hideInSearch: true },
    { title: "维权完成时间", dataIndex: "refundFinishTime", width: 100, hideInSearch: true },
    { title: "达人追回状态", dataIndex: "anchorBackStatus", width: 100, hideInSearch: true },
    { title: "达人应追回佣金金额", dataIndex: "anchorBackAmountStr", width: 100, hideInSearch: true },
    { title: "达人追回时间", dataIndex: "anchorBackTime", width: 100, hideInSearch: true },
    {
      title: "平台TR追回状态",
      dataIndex: "platformStatus",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "平台TR应追回金额", dataIndex: "platformAmountStr", width: 100, hideInSearch: true },
    { title: "平台TR追回时间", dataIndex: "platformTime", width: 100, hideInSearch: true },
    { title: "直播日期/支付日期", dataIndex: "liveDate", width: 100, hideInSearch: true },

    {
      title: "商品信息",
      dataIndex: "itemInfo",
      width: 300,
      hideInSearch: true,
      render(_, record) {
        return (
          <div className="columnItemInfo">
            <div className="info">
              <div className="id">
                <span>{returnEllipsisTooltip({ title: "ID:" + (record?.itemId || NOVALUE) })}</span>
              </div>
              <div>{returnEllipsisTooltip({ title: record?.itemTitle, rows: 1, width: 250 })}</div>
            </div>
          </div>
        );
      }
    },
    { title: "主播ID", dataIndex: "anchorId", width: 100, hideInSearch: true },
    {
      title: "主播名称",
      dataIndex: "anchorName",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品品牌",
      dataIndex: "brandName",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "店铺ID", dataIndex: "shopId", width: 100, hideInSearch: true },
    {
      title: "店铺名称",
      dataIndex: "shopName",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "店铺性质描述",
      dataIndex: "shopTypeDesc",
      width: 100,
      hideInSearch: true,
      render(text: string) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "数据处理时间", dataIndex: "procTime", width: 100, hideInSearch: true }
  ].map(item => {
    const { title } = item;
    return {
      ...item,
      align: "center",
      title: returnEllipsisTooltip({ title })
    };
  });
};
