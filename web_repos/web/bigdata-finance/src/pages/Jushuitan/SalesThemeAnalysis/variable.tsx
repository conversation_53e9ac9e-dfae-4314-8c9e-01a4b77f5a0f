import { Select } from "antd";
import { filterOptionLabel, returnEllipsisTooltip } from "@/utils";
import { SIZE } from "@/utils/constant";
import AnchorSelect from "@/components/AnchorSelect";
import SupplierNameSelect from "@/components/SupplierNameSelect";
const columns_search = ({ shopTypeOptions }: any) => {
  return [
    { title: "直播日期", dataIndex: "liveDate", valueType: "dateRange", hideInTable: true },
    { title: "付款日期", dataIndex: "payTime", valueType: "dateRange", hideInTable: true },
    { title: "订单日期", dataIndex: "orderTime", valueType: "dateRange", hideInTable: true },
    { title: "发货日期", dataIndex: "deliveryTime", valueType: "dateRange", hideInTable: true },
    {
      title: "原始线上订单号",
      dataIndex: "orginOnlineOrderIdList",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "线上订单号",
      dataIndex: "onlineOrderIdList",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "主播",
      dataIndex: "anchorNameList",
      hideInTable: true,
      renderFormItem() {
        return <AnchorSelect showSearch allowClear maxTagCount="responsive" mode="multiple" />;
      }
    },
    {
      title: "商品编码集合",
      dataIndex: "itemCodeList",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "款式编码集合",
      dataIndex: "styleCodeList",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "供应商名称",
      dataIndex: "supplierName",
      hideInTable: true,
      renderFormItem() {
        return <SupplierNameSelect />;
      }
    },
    { title: "店铺名称", dataIndex: "platShopName", hideInTable: true },
    {
      title: "店铺性质描述",
      dataIndex: "shopTypeList",
      hideInTable: true,
      renderFormItem: () => {
        return (
          <Select
            placeholder="请选择"
            size={SIZE}
            options={shopTypeOptions}
            filterOption={filterOptionLabel}
            showSearch
            allowClear
            maxTagCount="responsive"
            mode="multiple"
          />
        );
      }
    }
  ];
};

export const columns = ({ shopTypeOptions }: any): Array<TableListItem> => {
  return [
    ...columns_search({ shopTypeOptions }),
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      hideInSearch: true,
      fixed: "left",
      render: (_, record, index) => {
        return index + 1;
      }
    },
    {
      title: "主键ID",
      dataIndex: "deliveryKeyId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "内部订单号",
      dataIndex: "jstInnerId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "线上订单号",
      dataIndex: "onlineOrderId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "原始线上订单号",
      dataIndex: "orginOnlineOrderId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "订单渠道描述", dataIndex: "orderChannelDesc", width: 100, hideInSearch: true },
    { title: "店铺ID", dataIndex: "platShopId", width: 100, hideInSearch: true },
    {
      title: "店铺名称",
      dataIndex: "platShopName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "店铺性质描述", dataIndex: "shopTypeDesc", width: 100, hideInSearch: true },
    { title: "商品ID", dataIndex: "itemId", width: 100, hideInSearch: true },
    {
      title: "直播商品名称",
      dataIndex: "itemTitle",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品名称",
      dataIndex: "itemName",
      width: 140,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "直播日期", dataIndex: "liveDate", width: 100, hideInSearch: true },
    {
      title: "主播名称",
      dataIndex: "anchorName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "主播ID", dataIndex: "anchorId", width: 100, hideInSearch: true },
    { title: "是否需要结算主播", dataIndex: "isSettleAnchorStr", width: 100, hideInSearch: true },
    {
      title: "商品编码",
      dataIndex: "itemCode",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "款式编码",
      dataIndex: "styleCode",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "颜色规格",
      dataIndex: "colorSize",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "线上颜色规格",
      dataIndex: "onlineColorSize",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "供应商",
      dataIndex: "supplierName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "供应商款号",
      dataIndex: "supplierStyleCode",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "店铺编号",
      dataIndex: "shopCode",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "店铺",
      dataIndex: "shopName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "商店站点", dataIndex: "shopSite", width: 100, hideInSearch: true },
    { title: "店铺商品编码", dataIndex: "shopItemCode", width: 100, hideInSearch: true },
    { title: "订单来源", dataIndex: "orderSource", width: 100, hideInSearch: true },
    { title: "是否赠品", dataIndex: "isGiftStr", width: 100, hideInSearch: true },
    { title: "是否预售", dataIndex: "isPresale", width: 100, hideInSearch: true },
    { title: "订单日期", dataIndex: "orderTime", width: 100, hideInSearch: true },
    { title: "发货日期", dataIndex: "deliveryTime", width: 100, hideInSearch: true },
    { title: "付款日期", dataIndex: "payTime", width: 100, hideInSearch: true },
    { title: "确认收货日期", dataIndex: "confirmTime", width: 100, hideInSearch: true },
    { title: "售后登记日期", dataIndex: "refundSignTime", width: 100, hideInSearch: true },
    { title: "售后确认日期", dataIndex: "refundConfirmTime", width: 100, hideInSearch: true },
    {
      title: "快递单号",
      dataIndex: "expressNo",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "发货仓",
      dataIndex: "deliveryWarehouse",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "快递公司",
      dataIndex: "expressCompany",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "省",
      dataIndex: "province",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "市",
      dataIndex: "city",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "区县",
      dataIndex: "district",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "实发数量", dataIndex: "actualDeliveryVolumeStr", width: 100, hideInSearch: true },
    { title: "实发金额", dataIndex: "actualDeliveryAmountStr", width: 100, hideInSearch: true },
    { title: "订单重量", dataIndex: "orderWeight", width: 100, hideInSearch: true },
    { title: "商品资料设置重量", dataIndex: "itemWeight", width: 100, hideInSearch: true },
    { title: "退货数量", dataIndex: "refundGoodsVolumeStr", width: 100, hideInSearch: true },
    { title: "退货金额", dataIndex: "refundGoodsAmountStr", width: 100, hideInSearch: true },
    { title: "实退数量", dataIndex: "actualRefundVolumeStr", width: 100, hideInSearch: true },
    { title: "实退金额", dataIndex: "actualRefundAmountStr", width: 100, hideInSearch: true },
    { title: "售价", dataIndex: "priceStr", width: 100, hideInSearch: true },
    { title: "销售数量", dataIndex: "saleVolumeStr", width: 100, hideInSearch: true },
    { title: "赠品数量", dataIndex: "giftVolumeStr", width: 100, hideInSearch: true },
    { title: "价格为零的商品数量", dataIndex: "itemZeroVolumeStr", width: 100, hideInSearch: true },
    { title: "销售金额", dataIndex: "saleAmountStr", width: 100, hideInSearch: true },
    { title: "已付金额", dataIndex: "payAmountStr", width: 100, hideInSearch: true },
    { title: "应付金额", dataIndex: "shouldPayAmountStr", width: 100, hideInSearch: true },
    { title: "基本售价", dataIndex: "basicPriceStr", width: 100, hideInSearch: true },
    { title: "运费收入", dataIndex: "incodeTransAmountStr", width: 100, hideInSearch: true },
    { title: "运费收入分摊", dataIndex: "incodeShareTransAmountStr", width: 100, hideInSearch: true },
    { title: "运费支出", dataIndex: "outTransAmountStr", width: 100, hideInSearch: true },
    { title: "运费支出分摊", dataIndex: "outShareTransAmountStr", width: 100, hideInSearch: true },
    { title: "优惠金额", dataIndex: "discountAmountStr", width: 100, hideInSearch: true },
    {
      title: "便签",
      dataIndex: "lable",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "备注",
      dataIndex: "remark",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },

    { title: "数据处理时间", dataIndex: "procTime", width: 100, hideInSearch: true }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      ...item,
      align: "center",
      title: returnEllipsisTooltip({ title })
    };
  });
};
