import React, { useState, useRef } from "react";
import { But<PERSON> } from "antd";
import KeepAlive from "react-activation";
import TableList from "@/components/TableList";
import { SIZE } from "@/utils/constant";
import { columns } from "./variable";
import apis from "@/services/jushuitan/salesthemeanalysis";
import { pushExportHistory, splitSpaceComma } from "@/utils";
import useCommonOptions from "@/hooks/useCommonOptions";
import dayjs from "dayjs";
export const PageUrl = "/jushuitan/salesthemeanalysis";
const DetailTable = () => {
  const [shopTypeOptions, shopTypeObj] = useCommonOptions({ dimName: "店铺性质" });
  const subsidyActionRef = useRef<any>();
  const [SPUPageParams, setSPUPageParams] = useState({});

  const columnsProps = { shopTypeOptions, shopTypeObj };

  // 处理请求参数
  const handlePageParmas = (p: any) => {
    const { current, payTime, liveDate, orderTime, deliveryTime, orginOnlineOrderIdList, onlineOrderIdList, itemCodeList, styleCodeList, ...rest } =
      p;
    if (payTime) {
      const [payTimeStart, payTimeEnd] = payTime;
      rest.payTimeStart = dayjs(payTimeStart).format("YYYY-MM-DD");
      rest.payTimeEnd = dayjs(payTimeEnd).format("YYYY-MM-DD");
    }

    if (liveDate) {
      const [liveDateStart, liveDateEnd] = liveDate;
      rest.liveDateStart = dayjs(liveDateStart).format("YYYYMMDD");
      rest.liveDateEnd = dayjs(liveDateEnd).format("YYYYMMDD");
    }

    if (orderTime) {
      const [orderTimeStart, orderTimeEnd] = orderTime;
      rest.orderTimeStart = dayjs(orderTimeStart).format("YYYY-MM-DD");
      rest.orderTimeEnd = dayjs(orderTimeEnd).format("YYYY-MM-DD");
    }
    if (deliveryTime) {
      const [deliveryTimeStart, deliveryTimeEnd] = deliveryTime;
      rest.deliveryTimeStart = dayjs(deliveryTimeStart).format("YYYY-MM-DD");
      rest.deliveryTimeEnd = dayjs(deliveryTimeEnd).format("YYYY-MM-DD");
    }
    rest.orginOnlineOrderIdList = splitSpaceComma(orginOnlineOrderIdList);
    rest.onlineOrderIdList = splitSpaceComma(onlineOrderIdList);
    rest.itemCodeList = splitSpaceComma(itemCodeList);
    rest.styleCodeList = splitSpaceComma(styleCodeList);
    const params = {
      pageNo: current,
      ...rest
    };
    return params;
  };
  const handleExport = async () => {
    const { status } = await apis.downloadJstDeliveryOrder(SPUPageParams);
    if (status) {
      pushExportHistory();
    }
  };

  return (
    <>
      <TableList
        actionRef={subsidyActionRef}
        columns={columns(columnsProps)}
        scroll={{ y: "calc(100vh - 390px)" }}
        form={{
          initialValues: {
            liveDate: [dayjs().subtract(1, "month"), dayjs().subtract(1, "day")]
          }
        }}
        api={apis.getJstDeliveryOrderPage}
        preFetch={handlePageParmas}
        paramsChange={setSPUPageParams}
        rowKey="deliveryKeyId"
        toolbar={{
          actions: [
            <Button key="export" onClick={() => handleExport()} size={SIZE}>
              导出
            </Button>
          ]
        }}
      />
    </>
  );
};

const AliveRecord = () => (
  <KeepAlive name={PageUrl}>
    <DetailTable />
  </KeepAlive>
);
export default React.memo(AliveRecord);
