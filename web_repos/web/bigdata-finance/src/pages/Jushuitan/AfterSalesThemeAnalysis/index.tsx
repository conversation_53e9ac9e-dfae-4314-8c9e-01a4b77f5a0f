import React, { useState, useRef, useEffect } from "react";
import { Button } from "antd";
import KeepAlive from "react-activation";
import TableList from "@/components/TableList";
import { SIZE } from "@/utils/constant";
import { columns } from "./variable";
import apis from "@/services/jushuitan/aftersalesthemeanalysis";
import { pushExportHistory, splitSpaceComma } from "@/utils";
import dayjs from "dayjs";
export const PageUrl = "/jushuitan/aftersalesthemeanalysis";
const DetailTable = () => {
  const subsidyActionRef = useRef<any>();
  const [ConfirmWarehouse, setConfirmWarehouse] = useState([]);
  const [SPUPageParams, setSPUPageParams] = useState({});

  const columnsProps = { ConfirmWarehouse };
  const fetchConfirmWarehouseList = async () => {
    const { entry, status } = await apis.getConfirmWarehouseList();
    if (status) {
      setConfirmWarehouse(
        (entry || [])?.map(item => {
          return { label: item, value: item };
        })
      );
    } else {
      setConfirmWarehouse([]);
    }
  };
  useEffect(() => {
    fetchConfirmWarehouseList();
  }, []);
  // 处理请求参数
  const handlePageParmas = (p: any) => {
    const { current, liveDate, payTime, bizOrderIdList, itemCodeList, itemIdList, jstRefundNoList, ...rest } = p;
    if (liveDate) {
      const [liveDateStart, liveDateEnd] = liveDate;
      rest.liveDateStart = dayjs(liveDateStart).format("YYYYMMDD");
      rest.liveDateEnd = dayjs(liveDateEnd).format("YYYYMMDD");
    }
    if (payTime) {
      const [payTimeStart, payTimeEnd] = payTime;
      rest.payTimeStart = dayjs(payTimeStart).format("YYYY-MM-DD");
      rest.payTimeEnd = dayjs(payTimeEnd).format("YYYY-MM-DD");
    }

    rest.bizOrderIdList = splitSpaceComma(bizOrderIdList);
    rest.itemCodeList = splitSpaceComma(itemCodeList);
    rest.jstRefundNoList = splitSpaceComma(jstRefundNoList);
    rest.itemIdList = splitSpaceComma(itemIdList);
    const params = {
      pageNo: current,
      ...rest
    };
    return params;
  };
  const handleExport = async () => {
    const { status } = await apis.downloadJstRefund(SPUPageParams);
    if (status) {
      pushExportHistory();
    }
  };

  return (
    <>
      <TableList
        actionRef={subsidyActionRef}
        columns={columns(columnsProps)}
        scroll={{ y: "calc(100vh - 400px)" }}
        form={{
          initialValues: {
            liveDate: [dayjs().subtract(1, "month"), dayjs().subtract(1, "day")]
          }
        }}
        api={apis.getJstRefundPage}
        preFetch={handlePageParmas}
        paramsChange={setSPUPageParams}
        rowKey="subJstRefundNo"
        toolbar={{
          actions: [
            <Button key="export" onClick={() => handleExport()} size={SIZE}>
              导出
            </Button>
          ]
        }}
      />
    </>
  );
};

const AliveRecord = () => (
  <KeepAlive name={PageUrl}>
    <DetailTable />
  </KeepAlive>
);
export default React.memo(AliveRecord);
