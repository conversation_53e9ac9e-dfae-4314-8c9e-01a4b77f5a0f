import { Select } from "antd";
import { filterOptionLabel, returnEllipsisTooltip } from "@/utils";
import { SIZE } from "@/utils/constant";
import AnchorSelect from "@/components/AnchorSelect";
const columns_search = ({ ConfirmWarehouse }: any) => {
  return [
    { title: "直播日期", dataIndex: "liveDate", valueType: "dateRange", hideInTable: true },
    { title: "支付日期", dataIndex: "payTime", valueType: "dateRange", hideInTable: true },
    {
      title: "主订单编号",
      dataIndex: "bizOrderIdList",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "聚水潭退款订单号",
      dataIndex: "jstRefundNoList",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "主播",
      dataIndex: "anchorNameList",
      hideInTable: true,
      renderFormItem() {
        return <AnchorSelect showSearch allowClear maxTagCount="responsive" mode="multiple" />;
      }
    },
    { title: "店铺名称", dataIndex: "shopName", hideInTable: true },

    {
      title: "商品ID",
      dataIndex: "itemIdList",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },

    {
      title: "商品编码",
      dataIndex: "itemCodeList",
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    { title: "商品标题", dataIndex: "itemTitle", hideInTable: true },
    {
      title: "收货仓库",
      dataIndex: "confirmWarehouseList",
      hideInTable: true,
      renderFormItem: () => {
        return (
          <Select
            placeholder="请选择"
            size={SIZE}
            options={ConfirmWarehouse}
            filterOption={filterOptionLabel}
            showSearch
            allowClear
            maxTagCount="responsive"
            mode="multiple"
          />
        );
      }
    }
  ];
};

export const columns = ({ ConfirmWarehouse }: any): Array<TableListItem> => {
  return [
    ...columns_search({ ConfirmWarehouse }),
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      hideInSearch: true,
      fixed: "left",
      render: (_, record, index) => {
        return index + 1;
      }
    },
    { title: "直播日期", dataIndex: "liveDate", width: 100, hideInSearch: true },
    {
      title: "主订单编号",
      dataIndex: "bizOrderId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "聚水潭退款订单号", dataIndex: "jstRefundNo", width: 100, hideInSearch: true },
    { title: "聚水潭退款子订单号", dataIndex: "subJstRefundNo", width: 100, hideInSearch: true },
    { title: "原订单类型", dataIndex: "originOrderType", width: 100, hideInSearch: true },
    { title: "聚水潭订单状态", dataIndex: "jstOrderStatus", width: 100, hideInSearch: true },
    {
      title: "进仓单号",
      dataIndex: "inOrderId",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "进仓时间", dataIndex: "inTime", width: 100, hideInSearch: true },
    { title: "售后制单日期", dataIndex: "refundCreateTime", width: 100, hideInSearch: true },
    { title: "聚水潭售后状态", dataIndex: "refundStatus", width: 100, hideInSearch: true },
    {
      title: "聚水潭售后类型",
      dataIndex: "refundType",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "问题类型",
      dataIndex: "questionType",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "推广者id",
      dataIndex: "pid",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "主播名称", dataIndex: "pidName", width: 100, hideInSearch: true },
    { title: "店铺id", dataIndex: "shopId", width: 100, hideInSearch: true },
    {
      title: "店铺名称",
      dataIndex: "shopName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "店铺站点", dataIndex: "shopSite", width: 100, hideInSearch: true },
    {
      title: "买家昵称",
      dataIndex: "buyerNick",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "退款单快递包裹运单号",
      dataIndex: "refundInvoiceNo",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "退款单快递物流公司",
      dataIndex: "refundLogisticsCompany",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "商品id", dataIndex: "itemId", width: 100, hideInSearch: true },
    { title: "商品编码", dataIndex: "itemCode", width: 100, hideInSearch: true },
    {
      title: "款式编码",
      dataIndex: "styleCode",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "商品标题",
      dataIndex: "itemTitle",
      width: 140,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "颜色规格",
      dataIndex: "colorSize",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "类目id", dataIndex: "catId", width: 100, hideInSearch: true },
    {
      title: "类目名称",
      dataIndex: "catName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "产品分类",
      dataIndex: "itemType",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "商品单格", dataIndex: "itemPriceStr", width: 100, hideInSearch: true },
    { title: "退款商品数量", dataIndex: "refundGoodsVolumeStr", width: 100, hideInSearch: true },
    { title: "实退商品数量", dataIndex: "actualRefundVolumeStr", width: 100, hideInSearch: true },
    { title: "退款商品金额", dataIndex: "refundGoodsAmountStr", width: 100, hideInSearch: true },
    { title: "换货补发数量", dataIndex: "exchangeVolumeStr", width: 100, hideInSearch: true },
    { title: "补发金额", dataIndex: "reissueAmountStr", width: 100, hideInSearch: true },
    { title: "订单创建时间", dataIndex: "orderCreateTime", width: 100, hideInSearch: true },
    { title: "支付时间", dataIndex: "payTime", width: 100, hideInSearch: true },
    {
      title: "收货仓库",
      dataIndex: "confirmWarehouse",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "仓库",
      dataIndex: "warehouse",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "订单物流单号",
      dataIndex: "orderInvoiceNo",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "订单快递公司",
      dataIndex: "orderLogisticsCompany",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "数据处理时间", dataIndex: "procTime", width: 100, hideInSearch: true },
    {
      title: "卖家备注",
      dataIndex: "sellerRemark",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "订单渠道描述", dataIndex: "orderChannelDesc", width: 100, hideInSearch: true }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      ...item,
      align: "center",
      title: returnEllipsisTooltip({ title })
    };
  });
};
