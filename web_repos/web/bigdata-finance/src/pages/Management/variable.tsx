import { returnTargetObject } from "@/utils";
import { AllValue } from "@/utils/constant";

// 敏感字段 类型
export const sensitiveOptions = [
  {
    label: "按钮",
    value: 3
  },
  {
    label: "指标",
    value: 4
  }
];
export const sensitiveObj = returnTargetObject(sensitiveOptions);

export const WechatUser = 0;
export const VirtualUser = 1;

// 账户类型
export const accountType = [
  {
    label: "企微用户",
    value: WechatUser
  }
  // {
  //   label: "虚拟用户",
  //   value: VirtualUser
  // }
];
export const accountTypeObj = returnTargetObject([AllValue, ...accountType]);

export const jobOn = 1;
export const jobOff = 2;
// 在职状态
export const jobStatus = [
  {
    label: "在职",
    value: jobOn
  },
  {
    label: "离职",
    value: jobOff
  }
];
export const jobStatusObj = returnTargetObject([AllValue, ...jobStatus]);

export const accountOn = 1;
export const accountOff = 0;
// 账号状态
export const accountStatus = [
  {
    label: "启用",
    value: accountOn
  },
  {
    label: "禁用",
    value: accountOff
  }
];
// 菜单显示状态
export const hideInMenu = [
  {
    label: "显示",
    value: 0
  },
  {
    label: "隐藏",
    value: 1
  }
];
export const accountStatusObj = returnTargetObject([AllValue, ...accountStatus]);
