import { useEffect, useRef, useState } from "react";
import { ProTable } from "@ant-design/pro-components";
import { AutoComplete, Button, Form, Input, Modal, Popconfirm, Select, Space, Switch, message } from "antd";
import api from "@/services/management/globalOptions";
import { DeleteFilled, EditFilled } from "@ant-design/icons";
import { filterOptionLabel, paginationOptions, returnEllipsisTooltip } from "@/utils";
import { NOVALUE, Protable_FROM_CONFIG, SIZE } from "@/utils/constant";

export default () => {
  const [visible, setVisible] = useState<boolean>(false);
  const [selectedKeys, setSelectKeys] = useState<any>({});
  const [typeList, setTypeList] = useState<any>([]);
  const [useForm] = Form.useForm();
  const actionRef = useRef<any>();

  useEffect(() => {
    useForm.setFieldsValue({ ...selectedKeys });
  }, [selectedKeys]);

  const getTypeLists = async () => {
    const { entry: data, success } = await api.getTypeList();
    if (success) {
      setTypeList(data.map(item => ({ label: item, value: item })));
    }
  };

  return (
    <div>
      <ProTable
        size={SIZE}
        columnEmptyText={NOVALUE}
        actionRef={actionRef}
        bordered
        form={{ ...Protable_FROM_CONFIG }}
        toolbar={{
          actions: [
            <Button key="add" size={SIZE} type="primary" onClick={() => setVisible(true)}>
              添加选项
            </Button>
          ]
        }}
        options={{
          fullScreen: true,
          reload: false,
          setting: true
        }}
        scroll={{ x: "max-content", y: "calc(100vh - 310px)" }}
        columns={[
          {
            title: "选项类型",
            hideInSearch: false,
            hideInTable: true,
            key: "dimName",
            dataIndex: "dimName",
            renderFormItem() {
              return (
                <Select
                  allowClear
                  showSearch
                  placeholder="请选择"
                  options={typeList}
                  onFocus={() => {
                    if (typeList.length) return;
                    getTypeLists();
                  }}
                />
              );
            }
          },
          {
            title: "选项名称",
            hideInSearch: false,
            hideInTable: true,
            key: "optionName",
            dataIndex: "optionName"
          },
          {
            title: "选项ID",
            hideInSearch: false,
            hideInTable: true,
            key: "optionId",
            dataIndex: "optionId"
          },
          {
            title: "序号",
            hideInSearch: true,
            hideInTable: false,
            width: 60,
            align: "center",
            render(_, item, index) {
              return index + 1 < 10 ? "0" + (index + 1) : index + 1;
            }
          },
          {
            title: "选项类型",
            key: "dimName",
            dataIndex: "dimName",
            hideInSearch: true,
            hideInTable: false,
            align: "center",
            width: 80
          },
          {
            title: "选项名称",
            key: "optionName",
            dataIndex: "optionName",
            hideInSearch: true,
            hideInTable: false,
            align: "center",
            width: 160
          },
          {
            title: "选项内容",
            key: "optionId",
            dataIndex: "optionId",
            hideInSearch: true,
            hideInTable: false,
            align: "center",
            width: 160
          },
          {
            title: "排序",
            key: "sort",
            dataIndex: "sort",
            hideInSearch: true,
            hideInTable: false,
            align: "center",
            width: 60
          },
          {
            title: "状态",
            key: "enable",
            dataIndex: "enable",
            hideInSearch: true,
            hideInTable: false,
            align: "center",
            width: 100,
            render(_, item) {
              return (
                <Switch
                  checked={item?.enable}
                  onChange={e => {
                    api
                      .saveOrUpdateDimOption({ ...item, enable: e ? 1 : 0 })
                      .then(res => {
                        if (res.success) {
                          message.success("编辑成功!");
                          actionRef.current.reload();
                        } else {
                          message.error("编辑失败!");
                        }
                      })
                      .catch(err => err);
                  }}
                />
              );
            }
          },
          {
            title: "最近修改人",
            key: "modifier",
            dataIndex: "modifier",
            hideInSearch: true,
            hideInTable: false,
            align: "center",
            width: 100
          },
          {
            title: "最近修改时间",
            key: "gmtModified",
            dataIndex: "gmtModified",
            hideInSearch: true,
            hideInTable: false,
            align: "center",
            width: 100
          },
          {
            title: "操作",
            hideInSearch: true,
            hideInTable: false,
            align: "center",
            width: 140,
            render(_, item) {
              return (
                <Space>
                  <Button
                    type="primary"
                    size="small"
                    onClick={() => {
                      setSelectKeys(item);
                      setVisible(true);
                    }}
                  >
                    <EditFilled />
                    编辑
                  </Button>

                  <Popconfirm
                    title="是否确认删除?"
                    onConfirm={() => {
                      api.delDimOption({ id: item.id }).then(res => {
                        if (res.success) {
                          message.success("删除成功!");
                          getTypeLists();
                          actionRef.current.reload();
                        } else {
                          message.error("删除失败!");
                        }
                      });
                    }}
                    okText="是"
                    cancelText="否"
                  >
                    <Button type="primary" danger size="small">
                      <DeleteFilled />
                      删除
                    </Button>
                  </Popconfirm>
                </Space>
              );
            }
          }
        ].map((item: TableListItem) => {
          const { title } = item;
          return {
            align: "center",
            ...item,
            title: returnEllipsisTooltip({ title })
          };
        })}
        request={async (p: any) => {
          const { current, ...rest } = p;
          const { entry: data, success, totalRecordSize: total } = await api.getList({ ...rest, pageNo: current });
          if (success) {
            return {
              data,
              total,
              success
            };
          } else {
            return { data: [], total: 0, success: false };
          }
        }}
        pagination={{
          showQuickJumper: true,
          ...paginationOptions
        }}
      />

      <Modal
        title={Object.keys(selectedKeys).length ? "编辑选项" : "添加选项"}
        okButtonProps={{ size: SIZE }}
        cancelButtonProps={{ size: SIZE }}
        centered
        width={400}
        open={visible}
        onOk={() => {
          let data = useForm.getFieldsValue();
          if (Object.keys(selectedKeys).length) {
            api
              .saveOrUpdateDimOption({ ...selectedKeys, ...data })
              .then(res => {
                if (res.success) {
                  message.success("编辑成功!");
                  getTypeLists();
                  actionRef.current.reload();
                } else {
                  message.error("编辑失败!");
                }
                setVisible(false);
                setSelectKeys({});
                useForm.resetFields();
              })
              .catch(err => err);
          } else {
            api
              .saveOrUpdateDimOption({ ...data, enable: 1 })
              .then(res => {
                if (res.success) {
                  message.success("添加成功!");
                  getTypeLists();
                  actionRef.current.reload();
                } else {
                  message.error("添加失败!");
                }
                setVisible(false);
                setSelectKeys({});
                useForm.resetFields();
              })
              .catch(err => err);
          }
        }}
        onCancel={() => {
          setVisible(false);
          setSelectKeys({});
          useForm.resetFields();
        }}
      >
        <Form form={useForm} size={SIZE} layout="horizontal" labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
          <Form.Item label={returnEllipsisTooltip({ title: "选项类型" })} name="dimName">
            <AutoComplete
              allowClear
              showSearch
              options={typeList}
              onFocus={() => {
                if (typeList.length) return;
                getTypeLists();
              }}
              filterOption={filterOptionLabel}
            />
          </Form.Item>
          <Form.Item label={returnEllipsisTooltip({ title: "选项名称" })} name="optionName">
            <Input placeholder="请输入" />
          </Form.Item>
          <Form.Item label={returnEllipsisTooltip({ title: "选项ID" })} name="optionId">
            <Input placeholder="请输入" />
          </Form.Item>
          <Form.Item label={returnEllipsisTooltip({ title: "选项排序" })} name="sort">
            <Input placeholder="请输入" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};
