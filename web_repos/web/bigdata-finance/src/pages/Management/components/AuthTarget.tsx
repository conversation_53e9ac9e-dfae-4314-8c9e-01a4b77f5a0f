import React from "react";
import { Card, Table } from "antd";
import type { ColumnsType } from "antd/lib/table";
type AuthTargetProps = {
  height?: string | number;
  title?: string;
  columns: ColumnsType;
  data: any[];
  extra?: JSX.Element;
  onItemChange?: (record: any) => void;
  selectedRowKeys?: any[];
  [propname: string]: any;
};
const AuthTarget: React.FC<AuthTargetProps> = props => {
  const { title, columns, height, data = [], extra, onSelectChange, selectedRowKeys = [] } = props;
  return (
    <Card style={{ width: "100%" }} bordered title={title} extra={extra}>
      <Table
        style={{ height: height }}
        scroll={{ x: "max-content", y: height }}
        columns={columns}
        dataSource={data}
        rowSelection={{
          type: "checkbox",
          selectedRowKeys: selectedRowKeys,
          onChange: (selectedRowKeys: React.Key[], selectedRows: any[]) => {
            onSelectChange?.(selectedRowKeys, selectedRows);
          },
          getCheckboxProps: (record: any) => {
            const { status, type } = record;
            return {
              disabled: status === 0 || type === 4
            };
          }
        }}
        pagination={{
          hideOnSinglePage: true,
          pageSize: 10000
        }}
        rowKey="id"
      />
    </Card>
  );
};
export default AuthTarget;
