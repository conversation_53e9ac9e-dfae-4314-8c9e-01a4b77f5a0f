import { Space, Button } from "antd";
import { StopOutlined } from "@ant-design/icons";
import { history } from "@umijs/max";
import queryString from "query-string";

// 一级页面表头
export const oneLevelColumns: TableListItem = ({ setShowLevelPage, setTypeHandle, setPageLevel, setType, setCurrentIndex }: any) => {
  return [
    {
      title: "名称",
      dataIndex: "$index",
      width: 200,
      hideInSearch: true,
      render(_, record) {
        const { status } = record;
        return (
          <>
            {status === 1 && <StopOutlined />}
            {record.name}
          </>
        );
      }
    },
    {
      title: "操作",
      width: 120,
      valueType: "option",
      render(_, record, index) {
        return (
          <Space>
            <a
              onClick={() => {
                setShowLevelPage(true);
                setPageLevel("one");
                setTypeHandle("edit");
                setType(0);
                setCurrentIndex(index);
              }}
            >
              编辑
            </a>
            <a
              onClick={() => {
                history.push({
                  pathname: "/management/roles/users",
                  search: queryString.stringify({
                    id: record.id,
                    name: record.name
                  })
                });
              }}
            >
              权限
            </a>
          </Space>
        );
      }
    }
  ];
};
// 二级页面表头
export const twoLevelColumns: TableListItem = ({ setShowLevelPage, setTypeHandle, setPageLevel, setType, setCurrentIndex }: any) => {
  return [
    {
      title: "名称",
      dataIndex: "$index",
      hideInSearch: true,
      render(_, record) {
        const { status } = record;
        if (status === 1) {
          return (
            <Button type="text" onClick={() => {}}>
              {record.name}
            </Button>
          );
        } else {
          return (
            <Button type="text" icon={<StopOutlined />}>
              {record.name}
            </Button>
          );
        }
      }
    },
    {
      title: "操作",
      width: 80,
      valueType: "option",
      render(_, record, index) {
        return (
          <Space>
            <Button
              type="link"
              onClick={() => {
                setShowLevelPage(true);
                setPageLevel("two");
                setTypeHandle("edit");
                setType(1);
                setCurrentIndex(index);
              }}
            >
              编辑
            </Button>
            <Button
              type="link"
              onClick={() => {
                history.push({
                  pathname: "/management/roles/users",
                  search: queryString.stringify({
                    id: record.id,
                    name: record.name
                  })
                });
              }}
            >
              权限
            </Button>
          </Space>
        );
      }
    }
  ];
};
// 模块表头
export const moduleColumns: TableListItem = ({ setShowModulePage, setTypeHandle, setType, setCurrentIndex }: any) => {
  return [
    {
      title: "名称",
      dataIndex: "$index",
      hideInSearch: true,
      render(_, record) {
        const { status } = record;
        if (status === 1) {
          return (
            <Button type="text" onClick={() => {}}>
              {record.name}
            </Button>
          );
        } else {
          return (
            <Button type="text" icon={<StopOutlined />}>
              {record.name}
            </Button>
          );
        }
      }
    },
    {
      title: "操作",
      width: 80,
      valueType: "option",
      render(_, record, index) {
        return (
          <Space>
            <Button
              type="link"
              onClick={() => {
                setShowModulePage(true);
                setTypeHandle("edit");
                setType(2);
                setCurrentIndex(index);
              }}
            >
              编辑
            </Button>
            <Button
              type="link"
              onClick={() => {
                history.push({
                  pathname: "/management/roles/users",
                  search: queryString.stringify({
                    id: record.id,
                    name: record.name
                  })
                });
              }}
            >
              权限
            </Button>
          </Space>
        );
      }
    }
  ];
};
// 功能/敏感字段表头
export const funColumns: TableListItem = ({}: any) => {
  return [
    {
      title: "名称",
      dataIndex: "$index1",
      width: 80,
      hideInSearch: true,
      render(_, record) {
        return (
          <Button type="text" onClick={() => {}}>
            {record.name}
          </Button>
        );
      }
    },
    {
      title: "类型",
      dataIndex: "$index2",
      width: 80,
      hideInSearch: true,
      render(_, record) {
        if (record.type === 3) {
          return "按钮";
        } else {
          return "指标";
        }
      }
    },
    {
      title: "操作",
      width: 80,
      valueType: "option",
      render(_, record) {
        if (record.type === 3) {
          return (
            <Button type="link" onClick={() => {}}>
              权限
            </Button>
          );
        } else {
          return (
            <Space>
              <Button
                type="link"
                onClick={() => {
                  history.push({
                    pathname: "/management/roles/users",
                    search: queryString.stringify({
                      id: record.id,
                      name: record.name
                    })
                  });
                }}
              >
                可读权限
              </Button>
              <Button
                type="link"
                onClick={() => {
                  history.push({
                    pathname: "/management/roles/users",
                    search: queryString.stringify({
                      id: record.id,
                      name: record.name
                    })
                  });
                }}
              >
                可写权限
              </Button>
            </Space>
          );
        }
      }
    }
  ];
};
