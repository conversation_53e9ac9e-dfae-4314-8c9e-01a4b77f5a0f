import React, { useMemo, useCallback, useRef } from "react";
import { Modal, Form, Input, Select, message } from "antd";
import api from "@/services/management/dataSource";
import { accountStatus } from "@/pages/Management/variable";
import { handleStringTrim, returnEllipsisTooltip } from "@/utils/index";
import { SIZE, FormLayout } from "@/utils/constant";
const { Option } = Select;

const handleTypeValues = {
  edit: "编辑",
  add: "新增"
};

const ModulePage = (props: any) => {
  const { visible, close, fresh, handleType, cilckData, clickSecondData } = props;
  const initialValues = useRef(
    handleType === "edit"
      ? cilckData
      : {
          status: 1
        }
  );
  const [form] = Form.useForm();

  //onOk回调
  const handleOk = useCallback(() => {
    form.validateFields().then(async values => {
      if (handleType === "add") {
        const { status, message: errInfo } = await api.bodyResourceAdd({
          ...handleStringTrim(values),
          parentId: clickSecondData.id,
          type: 2
        });
        if (status) {
          close?.();
          fresh?.();
        } else {
          message.error(errInfo);
        }
      } else {
        const { status, message: errInfo } = await api.bodyResourceEdit({
          ...handleStringTrim(values),
          type: 2,
          parentId: clickSecondData.id
        });
        if (status) {
          close?.();
          fresh?.();
        } else {
          message.error(errInfo);
        }
      }
    });
  }, [clickSecondData]);

  //onCancel回调
  const handleCancel = () => {
    form.resetFields();
    close();
  };
  //弹框标题
  const returnModalTitle = useMemo(() => {
    return handleTypeValues[handleType] + "模块";
  }, [handleType]);
  return (
    <Modal
      title={returnModalTitle}
      open={visible}
      onOk={handleOk}
      onCancel={handleCancel}
      maskClosable={false}
      okText="保存"
      okButtonProps={{
        size: SIZE
      }}
      cancelButtonProps={{
        size: SIZE
      }}
      centered={true}
    >
      <Form form={form} initialValues={initialValues.current} size={SIZE} {...FormLayout}>
        <Form.Item name="name" label={returnEllipsisTooltip({ title: "名称" })} rules={[{ required: true }]}>
          <Input placeholder="请输入"></Input>
        </Form.Item>
        {handleType === "edit" && (
          <Form.Item name="id" label={returnEllipsisTooltip({ title: "I D" })}>
            <Input placeholder="请输入" disabled={true}></Input>
          </Form.Item>
        )}
        <Form.Item name="code" label={returnEllipsisTooltip({ title: "CODE" })} rules={[{ required: true }]}>
          <Input placeholder="请输入"></Input>
        </Form.Item>
        <Form.Item name="description" label={returnEllipsisTooltip({ title: "提示内容" })}>
          <Input placeholder="请输入"></Input>
        </Form.Item>
        <Form.Item name="status" label={returnEllipsisTooltip({ title: "状态" })} rules={[{ required: true }]}>
          <Select placeholder="请选择">
            {accountStatus.map(item => {
              return (
                <Option value={item.value} key={item.value}>
                  {item.label}
                </Option>
              );
            })}
          </Select>
        </Form.Item>
      </Form>
    </Modal>
  );
};
export default React.memo(ModulePage);
