import React, { useMemo } from "react";
import { Modal, Form, Input, message, Alert } from "antd";

import api from "@/services/management/dataSource";
import { handleStringTrim, returnEllipsisTooltip } from "@/utils/index";

const formLayout = {
  labelWrap: true,
  labelCol: { span: 4 },
  wrapperCol: { span: 18 }
};
const BatchAddAuth = (props: any) => {
  const { visible, close, fresh, batchType, platform, clickData } = props;
  const [form] = Form.useForm();

  const looseJsonParse = obj => {
    let res;
    try {
      res = Function('"use strict";return (' + obj + ")")();
    } catch (error) {
      message.error(error);
      res = [];
    }
    return res;
  };
  const batchType2 = [
    {
      name: "",
      code: "",
      description: null,
      status: 1,
      type: 2
    }
  ];
  const batchType3 = [
    {
      name: "",
      code: "",

      description: null,
      status: 1,
      // 3 按钮权限 4 指标权限
      type: 3
    }
  ];
  const batchType4 = [
    {
      name: "",
      code: "",
      field: "",
      description: null,
      status: 1,
      // 3 按钮权限 4 指标权限
      type: 4
    }
  ];
  const returnTemp = useMemo(() => {
    if (batchType === 2) return JSON.stringify(batchType2);
    return (
      <>
        <div>按钮权限：{JSON.stringify(batchType3)}</div>
        <div>指标权限：{JSON.stringify(batchType4)}</div>
      </>
    );
  }, [batchType]);
  //onOk回调
  const handleOk = () => {
    let p = {
      description: null,
      status: 1,
      parentId: clickData.id,
      platform
    };
    form.validateFields().then(async ({ auths }) => {
      const auth = looseJsonParse(auths);
      if (Array.isArray(auth)) {
        for (let item of auth) {
          if (batchType === 2) {
            p = {
              ...p,
              description: null,
              ...handleStringTrim(item),
              type: 2
            };
          } else if (batchType === 3) {
            p = {
              ...p,
              type: 3,
              ...handleStringTrim(item)
            };
          }
          const { status, message: errInfo } = await api.bodyResourceAdd(p);
          if (status) {
            message.success(errInfo);
          } else {
            message.error(errInfo);
          }
        }
      }
    });
  };

  //onCancel回调
  const handleCancel = () => {
    form.resetFields();
    close();
    fresh?.();
  };

  return (
    <Modal
      title="批量添加权限"
      visible={visible}
      onOk={handleOk}
      onCancel={handleCancel}
      maskClosable={false}
      okText="保存"
      width={800}
      okButtonProps={{
        size: "small"
      }}
      cancelButtonProps={{
        size: "small"
      }}
      centered={true}
    >
      <Form form={form} size="small" {...formLayout}>
        <Form.Item name="auths" label={returnEllipsisTooltip({ title: "批量添加权限" })}>
          <Input.TextArea autoSize={{ minRows: 6, maxRows: 6 }}></Input.TextArea>
        </Form.Item>
        <Form.Item noStyle>
          <Alert message="权限格式" description={returnTemp} type="error" />
        </Form.Item>
      </Form>
    </Modal>
  );
};
export default React.memo(BatchAddAuth);
