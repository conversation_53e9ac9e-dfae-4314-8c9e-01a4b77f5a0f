/* eslint-disable no-unused-vars */
import React, { useState, useEffect, useRef } from "react";
import { Card, Table } from "antd";
import type { ColumnsType } from "antd/lib/table";

type AuthItemProps = {
  title?: string;
  columns: ColumnsType;
  data: any[];
  extra?: JSX.Element;
  // 高度
  height?: number;
  // 清除点击状态样式
  clearClickStyle?: boolean;
  dontFetch?: boolean;
  // 行数据 点击
  onItemClick?: (record: any) => void;
  [propname: string]: any;
};

const AuthChild: React.FC<AuthItemProps> = props => {
  const { title, columns, data, extra, onItemClick, height = 500, dontFetch, clearClickStyle = true } = props;
  const [curRowIndex, setCurRowIndex] = useState(-1);
  const wrapperRef = useRef(null);
  // 当数据变化时，如果允许清除 点击样式，则清除
  useEffect(() => {
    if (clearClickStyle) {
      setCurRowIndex(-1);
    }
  }, [data, clearClickStyle]);
  return (
    <div ref={wrapperRef}>
      <Card bordered title={title} extra={extra}>
        <Table
          style={{ height: height }}
          scroll={{ x: "max-content", y: height - 60 }}
          expandable={{ showExpandColumn: false }}
          onRow={(record, index) => {
            return {
              onClick: () => {
                // 连续点击 同一行， 不再处理
                // 不再进行下一步操作
                onItemClick?.(record);
                if (dontFetch) return;
                setCurRowIndex(index!);
              }
            };
          }}
          rowClassName={(_, index) => {
            if (index === curRowIndex) {
              return "tableClickRowClass";
            }
            return "";
          }}
          pagination={{
            hideOnSinglePage: true,
            pageSize: 10000
          }}
          columns={columns}
          dataSource={data}
          rowKey="id"
        />
      </Card>
    </div>
  );
};
export default React.memo(AuthChild);
