// 批量导入商品
import React, { useState, useEffect, useCallback, useMemo } from "react";
import { Modal, Form, Input, Tag } from "antd";
import { NOVALUE } from "@/utils/constant";
import api from "@/services/management/roles";
import apisUsers from "@/services/management/users";
import { returnEllipsisTooltip, splitSpaceComma } from "@/utils";
import { SIZE, FormLayout } from "@/utils/constant";
type IRowAuth = {
  anchorNames?: string;
  bigDepartments?: string;
  departGroups?: string;
  purchasePrincipals?: string;
  brandNames?: string;
  contractNames?: string;
  bigDepartmentLv2s?: string;
  bigDepartmentLv3s?: string;
  catLv1Names?: Array<string>;
  livePlatforms?: Array<string>;
  traditionalPlatforms?: Array<string>;
  companyNames?: string;
  shopIdList?: string;
};
interface IParams {
  roleId: string | number;
  anchorNames: string[];
  bigDepartments: string[];
  departGroups: string[];
  purchasePrincipals: string[];
  brandNames: string[];
  contractNames: string[];
  bigDepartmentLv2s?: string[];
  bigDepartmentLv3s?: string[];
  catLv1Names?: Array<string>;
  livePlatforms?: Array<string>;
  traditionalPlatforms?: Array<string>;
  companyNames?: string[];
  shopIdList?: string[];
}
const placeholder = "该用户无权限";

const RowAuth = (props: any) => {
  const { visible, close, fresh, currentData, handleType } = props;
  const [initialValues, setInitialValues] = useState<IRowAuth>({});
  const [form] = Form.useForm();
  const [name, setName] = useState(NOVALUE);
  const [label, setLabel] = useState(NOVALUE);
  const getRoleFieldValues = useCallback(
    async params => {
      const response = await api.getRoleFieldValues(params);
      const { status, entry = {} } = response ?? {};
      if (status) {
        const {
          anchorNames = [],
          bigDepartments = [],
          departGroups = [],
          purchasePrincipals = [],
          brandNames = [],
          contractNames = [],
          shopIdList = [],
          bigDepartmentLv2s,
          bigDepartmentLv3s,
          catLv1Names,
          livePlatforms,
          traditionalPlatforms,
          companyNames = []
        } = entry;
        const values: IRowAuth = {
          anchorNames: anchorNames.join(),
          bigDepartments: bigDepartments.join(),
          departGroups: departGroups.join(),
          purchasePrincipals: purchasePrincipals.join(),
          brandNames: brandNames.join(),
          contractNames: contractNames.join(),
          shopIdList: shopIdList.join(),
          companyNames: companyNames.join(),
          catLv1Names,
          livePlatforms,
          traditionalPlatforms
        };
        if (Array.isArray(bigDepartmentLv2s)) {
          values.bigDepartmentLv2s = bigDepartmentLv2s.join();
        }
        if (Array.isArray(bigDepartmentLv3s)) {
          values.bigDepartmentLv3s = bigDepartmentLv3s.join();
        }
        setInitialValues(values);
        form.setFieldsValue(values);
      } else {
        setInitialValues({});
        form.setFieldsValue({});
      }
    },
    [currentData.id, form]
  );
  // 获取用户行权限
  const getUserRowAuth = useCallback(
    async params => {
      const { status, entry = {} } = await apisUsers.queryRowAuths({ ...params, platform: 0 });
      if (status) {
        const {
          anchorNames = [],
          bigDepartments = [],
          departGroups = [],
          purchasePrincipals = [],
          brandNames = [],
          contractNames = [],
          shopIdList = [],
          bigDepartmentLv2s,
          bigDepartmentLv3s,
          catLv1Names,
          livePlatforms,
          traditionalPlatforms,
          companyNames = []
        } = entry;
        const values: IRowAuth = {
          anchorNames: anchorNames.join(),
          bigDepartments: bigDepartments.join(),
          departGroups: departGroups.join(),
          purchasePrincipals: purchasePrincipals.join(),
          brandNames: brandNames.join(),
          contractNames: contractNames.join(),
          shopIdList: shopIdList.join(),
          companyNames: companyNames.join(),
          catLv1Names,
          livePlatforms,
          traditionalPlatforms
        };
        if (Array.isArray(bigDepartmentLv2s)) {
          values.bigDepartmentLv2s = bigDepartmentLv2s.join();
        }
        if (Array.isArray(bigDepartmentLv3s)) {
          values.bigDepartmentLv3s = bigDepartmentLv3s.join();
        }
        setInitialValues(values);
        form.setFieldsValue(values);
      } else {
        setInitialValues({});
        form.setFieldsValue({});
      }
    },
    [currentData.userId, form]
  );

  useEffect(() => {
    if (handleType) {
      // TODO 区分平台
      switch (handleType) {
        // 角色
        case "roles":
          setName(currentData.name);
          setLabel("角色");
          getRoleFieldValues({ roleId: currentData.id });
          break;
        // 用户
        case "users":
          setName(currentData.displayName);
          setLabel("用户");
          getUserRowAuth({ userId: currentData.userId });
          break;
      }
    }
  }, [handleType, currentData]);
  const modelProps = useMemo(() => {
    if (handleType === "roles") {
      return {};
    } else {
      return { footer: null };
    }
  }, [handleType]);

  const handleAddCancel = () => {
    form.resetFields();
    close?.();
  };
  const handleOk = useCallback(() => {
    form.validateFields().then(async values => {
      // TODO 编辑行权限时 区分平台
      const {
        anchorNames = "",
        bigDepartments = "",
        departGroups = "",
        purchasePrincipals = "",
        brandNames = "",
        contractNames = "",
        shopIdList = "",
        companyNames = ""
      } = values;
      const params: IParams = {
        roleId: currentData.id,
        anchorNames: [...new Set(splitSpaceComma(anchorNames, true))],
        bigDepartments: [...new Set(splitSpaceComma(bigDepartments, true))],
        departGroups: [...new Set(splitSpaceComma(departGroups, true))],
        purchasePrincipals: [...new Set(splitSpaceComma(purchasePrincipals, true))],
        brandNames: [...new Set(splitSpaceComma(brandNames, true))],
        contractNames: [...new Set(splitSpaceComma(contractNames, true))],
        shopIdList: [...new Set(splitSpaceComma(shopIdList, true))],
        companyNames: [...new Set(splitSpaceComma(companyNames, true))]
      };
      const { entry } = (await api.bodyUpdataRow(params)) || {};
      if (entry) {
        handleAddCancel();
        fresh?.();
      }
    });
  }, [form, currentData]);

  const returnModalTitle = useMemo(() => {
    return `${label}：${name || NOVALUE} - 行权限配置`;
  }, [currentData, label, name]);

  return (
    <Modal
      title={returnModalTitle}
      open={visible}
      maskClosable={true}
      onCancel={handleAddCancel}
      onOk={handleOk}
      okText="保存"
      okButtonProps={{
        size: SIZE
      }}
      cancelButtonProps={{
        size: SIZE
      }}
      centered={true}
      {...modelProps}
    >
      <Form
        // initialValues={initialValues}
        form={form}
        size={SIZE}
        {...FormLayout}
      >
        <div style={{ display: "flex" }}>
          <div style={{ flex: 1 }}>
            <Form.Item label={label}>
              <Tag>{name || NOVALUE}</Tag>
            </Form.Item>
            <Form.Item label={returnEllipsisTooltip({ title: "主播" })} name="anchorNames">
              <Input.TextArea
                value={initialValues?.anchorNames}
                onChange={e => {
                  setInitialValues({
                    ...initialValues,
                    anchorNames: e.target.value
                  });
                }}
                placeholder={placeholder}
              ></Input.TextArea>
            </Form.Item>
            <Form.Item label={returnEllipsisTooltip({ title: "部门" })} name="bigDepartments">
              <Input.TextArea
                value={initialValues?.bigDepartments}
                onChange={e => {
                  setInitialValues({
                    ...initialValues,
                    bigDepartments: e.target.value
                  });
                }}
                placeholder={placeholder}
              ></Input.TextArea>
            </Form.Item>

            <Form.Item label={returnEllipsisTooltip({ title: "小组" })} name="departGroups">
              <Input.TextArea
                value={initialValues?.departGroups}
                onChange={e => {
                  setInitialValues({
                    ...initialValues,
                    departGroups: e.target.value
                  });
                }}
                placeholder={placeholder}
              ></Input.TextArea>
            </Form.Item>
            <Form.Item label={returnEllipsisTooltip({ title: "提报人" })} name="purchasePrincipals">
              <Input.TextArea
                value={initialValues?.purchasePrincipals}
                onChange={e => {
                  setInitialValues({
                    ...initialValues,
                    purchasePrincipals: e.target.value
                  });
                }}
                placeholder={placeholder}
              ></Input.TextArea>
            </Form.Item>
            <Form.Item label={returnEllipsisTooltip({ title: "品牌" })} name="brandNames">
              <Input.TextArea
                value={initialValues?.brandNames}
                onChange={e => {
                  setInitialValues({
                    ...initialValues,
                    brandNames: e.target.value
                  });
                }}
                placeholder={placeholder}
              ></Input.TextArea>
            </Form.Item>
            <Form.Item label={returnEllipsisTooltip({ title: "我司合同主体" })} name="contractNames">
              <Input.TextArea
                value={initialValues?.contractNames}
                onChange={e => {
                  setInitialValues({
                    ...initialValues,
                    contractNames: e.target.value
                  });
                }}
                placeholder={placeholder}
              ></Input.TextArea>
            </Form.Item>
            <Form.Item label={returnEllipsisTooltip({ title: "店铺ID" })} name="shopIdList">
              <Input.TextArea
                value={initialValues?.shopIdList}
                onChange={e => {
                  setInitialValues({
                    ...initialValues,
                    shopIdList: e.target.value
                  });
                }}
                placeholder={placeholder}
              ></Input.TextArea>
            </Form.Item>
            <Form.Item label="主播主体" name="companyNames">
              <Input.TextArea
                value={initialValues?.companyNames}
                onChange={e => {
                  setInitialValues({
                    ...initialValues,
                    companyNames: e.target.value
                  });
                }}
                placeholder={placeholder}
              ></Input.TextArea>
            </Form.Item>
          </div>
        </div>
      </Form>
    </Modal>
  );
};
export default React.memo(RowAuth);
