import React, { useMemo, useState, useEffect, useCallback } from "react";
import { Modal, Form, Input, Select, message } from "antd";
import api from "@/services/management/dataSource";
import { accountStatus, hideInMenu } from "@/pages/Management/variable";
import { handleStringTrim, returnEllipsisTooltip } from "@/utils/index";
import { SIZE, FormLayout } from "@/utils/constant";
const { Option } = Select;
const handleTypeValues = {
  edit: "编辑",
  add: "新增"
};
const levelPageType = {
  0: "一",
  1: "二"
};

const LevelPage = (props: any) => {
  const { visible, close, fresh, handleType, clickFirstData, clickSecondData, type } = props;
  const [parentId, setparentId] = useState(null);
  const [form] = Form.useForm();
  useEffect(() => {
    if (handleType === "edit") {
      // 编辑时
      if (type === 0) {
        // 编辑一级页面， 设置一级页面的数据
        form.setFieldsValue(clickFirstData);
      } else {
        // 编辑二级页面， 设置二级页面的数据
        form.setFieldsValue(clickSecondData);
        setparentId(clickFirstData.id);
      }
    } else {
      // 新增时 ，默认值为空
      form.setFieldsValue({
        status: 1,
        hideInMenu: 0
      });
      if (type === 1) {
        setparentId(clickFirstData.id);
      }
    }
    // 设置父级id
  }, [clickFirstData, clickSecondData, form, handleType, type]);
  //onOk回调
  const handleOk = useCallback(() => {
    form.validateFields().then(async values => {
      if (handleType === "add") {
        const { status, message: errorInfo } = await api.bodyResourceAdd({
          ...handleStringTrim(values),
          type,
          parentId: parentId
        });
        if (status) {
          close?.();
          fresh?.();
        } else {
          message.error(errorInfo);
        }
      } else {
        const { status, message: errorInfo } = await api.bodyResourceEdit({
          ...handleStringTrim(values),
          parentId: parentId
        });
        if (status) {
          close?.();
          fresh?.();
        } else {
          message.error(errorInfo);
        }
      }
    });
  }, [handleType, type, parentId]);
  //onCancel回调
  const handleCancel = () => {
    form.resetFields();
    close();
  };
  //一二级页面弹框标题
  const returnModalTitle = useMemo(() => {
    return handleTypeValues[handleType] + levelPageType[type] + "级页面";
  }, [handleType]);

  return (
    <Modal
      title={returnModalTitle}
      open={visible}
      onOk={handleOk}
      onCancel={handleCancel}
      maskClosable={false}
      okText="保存"
      okButtonProps={{
        size: SIZE
      }}
      cancelButtonProps={{
        size: SIZE
      }}
      centered={true}
    >
      <Form form={form} size={SIZE} {...FormLayout}>
        <Form.Item name="name" label={returnEllipsisTooltip({ title: "名称" })} rules={[{ required: true }]}>
          <Input placeholder="请输入"></Input>
        </Form.Item>

        {handleType === "edit" && (
          <Form.Item name="id" label={returnEllipsisTooltip({ title: "I D" })}>
            <Input disabled={handleType === "edit"}></Input>
          </Form.Item>
        )}
        {type === 1 && (
          <>
            <Form.Item name="groupName" label={returnEllipsisTooltip({ title: "分组名称" })} rules={[{ required: true }]}>
              <Input placeholder="请输入"></Input>
            </Form.Item>
          </>
        )}
        {type === 1 && (
          <>
            <Form.Item name="iconPath" label={returnEllipsisTooltip({ title: "分组图标" })} rules={[{ required: true }]}>
              <Input placeholder="请输入"></Input>
            </Form.Item>
          </>
        )}
        {type === 1 && (
          <>
            <Form.Item name="groupOrder" label={returnEllipsisTooltip({ title: "分组排序" })} rules={[{ required: true }]}>
              <Input placeholder="请输入"></Input>
            </Form.Item>
          </>
        )}
        <Form.Item name="path" label={returnEllipsisTooltip({ title: "前端路由" })} rules={[{ required: true }]}>
          <Input placeholder="请输入"></Input>
        </Form.Item>

        <Form.Item name="status" label={returnEllipsisTooltip({ title: "启用" })} rules={[{ required: true }]}>
          <Select placeholder="请选择">
            {accountStatus.map(item => {
              return (
                <Option value={item.value} key={item.value}>
                  {item.label}
                </Option>
              );
            })}
          </Select>
        </Form.Item>
        {/* 二级页面 */}
        {type === 1 && (
          <Form.Item name="hideInMenu" label={returnEllipsisTooltip({ title: "显示菜单" })} rules={[{ required: true }]}>
            <Select placeholder="请选择">
              {hideInMenu.map(item => {
                return (
                  <Option value={item.value} key={item.value + ""}>
                    {item.label}
                  </Option>
                );
              })}
            </Select>
          </Form.Item>
        )}
        <Form.Item name="order" label={returnEllipsisTooltip({ title: "排序" })}>
          <Input placeholder="请输入"></Input>
        </Form.Item>
      </Form>
    </Modal>
  );
};
export default React.memo(LevelPage);
