/* eslint-disable no-unused-vars */
import React, { useState, useEffect } from "react";
import { Card, Table } from "antd";
import type { ColumnsType } from "antd/lib/table";

type AuthItemProps = {
  height?: string | number;
  title?: string;
  columns: ColumnsType;
  data: any[];
  extra?: JSX.Element;
  onItemClick?: (record: any) => void;
  onSelectChange?: (selectedRowKeys: React.Key[], selectedRows: any[]) => void;
  selectedRowKeys?: any[];
  [propname: string]: any;
};
const AuthItem: React.FC<AuthItemProps> = props => {
  const { title, columns, height, data = [], extra, onItemClick, onSelectChange, selectedRowKeys = [] } = props;
  const [curRowIndex, setCurRowIndex] = useState<number>(-1);

  useEffect(() => {
    setCurRowIndex(-1);
  }, [data]);
  return (
    <Card style={{ width: "100%" }} bordered title={title} extra={extra}>
      <Table
        style={{ height: height }}
        scroll={{ y: height }}
        rowSelection={{
          type: "checkbox",
          selectedRowKeys: selectedRowKeys,
          onChange: (selectedRowKeys: React.Key[], selectedRows: any[]) => {
            onSelectChange?.(selectedRowKeys, selectedRows);
          },
          getCheckboxProps: record => ({
            disabled: record?.status === 0
          })
        }}
        columns={columns}
        dataSource={data}
        onRow={(record, index) => {
          return {
            onClick: () => {
              // 连续点击 同一行， 不再处理
              if (index === curRowIndex) return;
              setCurRowIndex(index || 0);
              onItemClick?.(record);
            }
          };
        }}
        pagination={{
          hideOnSinglePage: true,
          pageSize: 10000
        }}
        rowClassName={(_, index) => {
          if (index === curRowIndex) {
            return "tableClickRowClass";
          }
          return "";
        }}
        rowKey="id"
      />
    </Card>
  );
};
export default AuthItem;
