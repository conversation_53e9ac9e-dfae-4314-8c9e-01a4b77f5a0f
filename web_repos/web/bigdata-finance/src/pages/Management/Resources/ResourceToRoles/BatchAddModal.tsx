import { Input, Modal, message } from "antd";
import React, { useState } from "react";

export default React.memo((props: any) => {
  const { visible, otherProps, close, okClose } = props;
  const [copyValue, setCopyValue] = useState<string>();

  const onOk = () => {
    try {
      let roleLists = JSON.parse(copyValue);
      let params = {
        roleIds: roleLists,
        ...otherProps
      };
      okClose(params);
    } catch (error) {
      message.error("数据解析失败！");
    }
  };

  const onCancel = () => {
    setCopyValue(null);
    close();
  };

  return (
    <Modal
      visible={visible}
      title="批量写入新增角色"
      cancelButtonProps={{ size: "small" }}
      okButtonProps={{ size: "small" }}
      onCancel={onCancel}
      onOk={onOk}
      centered
    >
      <Input.TextArea
        rows={8}
        value={copyValue}
        onChange={e => {
          setCopyValue(e.target.value);
        }}
      />
    </Modal>
  );
});
