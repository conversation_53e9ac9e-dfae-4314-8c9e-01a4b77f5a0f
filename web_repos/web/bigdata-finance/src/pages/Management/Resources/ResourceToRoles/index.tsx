import React, { useState, useEffect, useCallback, useMemo } from "react";
import { history, useSearchParams } from "@umijs/max";
import { Card, Button, Space, Typography } from "antd";
import TableTransfer from "./TransferTable";
import mainHeight from "@/hooks/useMainSize";
import { SIZE } from "@/utils/constant";
import { debounce } from "lodash-es";
import BatchAddModal from "./BatchAddModal";
import apis from "@/services/management/authorize";
const { Paragraph } = Typography;
const tableColumns = [
  {
    dataIndex: "name",
    title: "角色名称",
    width: 300
  },
  {
    dataIndex: "desc",
    title: "描述",
    width: 300
  }
];

const ResourceToRoles = () => {
  const [searchParams] = useSearchParams();
  const name = searchParams.get("name"); // 'John'
  const id = searchParams.get("id");
  const type = searchParams.get("type");
  const permissionType = searchParams.get("permissionType");
  const containerInfo = mainHeight(".ant-layout-content");
  const [roleLists, setRoleLists] = useState([]);

  const [batchAddVisible, setBatchAddVisible] = useState<boolean>(false);
  const [addRoleDisabled, setAddRoleDisabled] = useState<boolean>(false);

  // 将这些变量和函数的定义移到前面，解决 no-use-before-define 错误
  const permisstionObj = {
    0: "readPermission",
    1: "writePermission"
  };
  const key = permisstionObj[permissionType] ?? "";

  // 获取带有资源授权信息的角色列表
  const getList = useCallback(async params => {
    const { status, entry } = await apis.queryResourceAuthorizedRoles(params);
    if (status) {
      setRoleLists(
        entry.map(item => {
          return {
            ...item,
            __id: item.id + ""
          };
        })
      );
    } else {
      setRoleLists([]);
    }
  }, []);

  // 资源对角色授权
  const fetchAuthorize = useCallback(async params => {
    await apis.bodyAuthorizeRoles(params);
  }, []);

  const batchAddModalProps = {
    visible: batchAddVisible,
    otherProps: {
      resourceId: id,
      type,
      ...(key ? { [key]: 1 } : {})
    },
    okClose: async params => {
      setBatchAddVisible(false);
      await fetchAuthorize(params);
      getList({ type, resourceId: id, permissionType });
    },
    close() {
      setBatchAddVisible(false);
    }
  };

  //资源对角色取消授权
  const fetchUnauthorize = useCallback(async params => {
    await apis.bodyUnauthorizeRoles(params);
  }, []);

  const handleChange = useCallback(async (targetKeys, direction, moveKeys) => {
    const rolesIds = moveKeys.map(item => +item);
    const params = {
      resourceId: id,
      type,
      roleIds: rolesIds
    };
    if (direction === "right") {
      if (key) {
        params[key] = 1;
      }
      await fetchAuthorize(params);
    } else {
      if (key) {
        params[key] = 0;
      }
      await fetchUnauthorize(params);
    }
    getList({ type, resourceId: id, permissionType });
  }, []);

  const handleFilter = useCallback((inputValue, item) => {
    return item?.desc.indexOf(inputValue) !== -1 || item?.name.indexOf(inputValue) !== -1;
  }, []);
  const originTargetKeys = useMemo(() => {
    return roleLists?.filter(item => item.resourceAuthorized === 1).map(item => item.__id);
  }, [roleLists]);
  const returnTitle = useMemo(() => {
    return (
      <div>
        <Space direction="vertical">
          <span>{`${name} - 角色分配`}</span>
          <Space>
            <Button size={SIZE} onClick={() => history.back()}>
              返回
            </Button>
            <Button
              size={SIZE}
              onClick={debounce(
                () =>
                  getList({
                    type,
                    resourceId: id,
                    permissionType
                  }),
                500
              )}
            >
              刷新
            </Button>

            <Paragraph
              style={{ marginBottom: 0 }}
              copyable={{
                icon: [
                  <Button key="copy" size="small">
                    复制角色
                  </Button>,
                  <Button key="success" size="small">
                    复制成功
                  </Button>
                ],
                text: JSON.stringify(originTargetKeys),
                tooltips: ["复制当前资源已授权角色", "复制成功"],
                onCopy() {
                  setAddRoleDisabled(true);
                }
              }}
            />
            <Button size="small" disabled={addRoleDisabled} onClick={() => setBatchAddVisible(true)}>
              批量新增角色
            </Button>
          </Space>
        </Space>
      </div>
    );
  }, [id, addRoleDisabled, originTargetKeys]);
  useEffect(() => {
    getList({ type, resourceId: id, permissionType });
  }, [id]);
  return (
    <Card styles={{ body: { minHeight: `${containerInfo.height - 100}px` } }} title={returnTitle}>
      <TableTransfer
        containerHeight={containerInfo.height}
        titles={["未授权角色", "已授权角色"]}
        dataSource={roleLists}
        targetKeys={originTargetKeys}
        showSearch={true}
        rowKey={record => record.__id}
        onChange={handleChange}
        filterOption={handleFilter}
        listStyle={{
          width: 500
        }}
        leftColumns={tableColumns}
        rightColumns={tableColumns}
      />
      {batchAddVisible ? <BatchAddModal {...batchAddModalProps} /> : <></>}
    </Card>
  );
};
export default React.memo(ResourceToRoles);
