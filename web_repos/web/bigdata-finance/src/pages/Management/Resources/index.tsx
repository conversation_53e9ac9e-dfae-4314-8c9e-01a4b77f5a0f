import React, { useState, useMemo, useEffect, useRef } from "react";
import { Card, Row, Col, Space } from "antd";
import KeepAlive from "react-activation";
import AuthChild from "@/pages/Management/components/AuthChild";
import LevelPage from "@/pages/Management/components/LevelPage";
import ModulePage from "@/pages/Management/components/ModulePage";
import { oneLevelColumns, twoLevelColumns, moduleColumns, funColumns } from "./variable";
import EditSensitive from "../components/EditSensitive";
import apis from "@/services/management/dataSource";
import BatchAddAuth from "@/pages/Management/components/BatchAddAuth";
import useMainSize from "@/hooks/useMainSize";
const pageUrl = "/management/resources";
const Resources = () => {
  // 当前平台
  // 显示 一级 二级 页面 新增 编辑
  const [showLevelPage, setShowLevelPage] = useState(false);
  // 显示 模块 新增/编辑
  const [showModulePage, setShowModulePage] = useState(false);
  const [showsensitiveModal, setShowsensitiveModal] = useState(false);
  // 一级页面数据
  const [currentData, setCurrentData] = useState<any>([]);
  // 当前是新增还是编辑
  const [typeHandle, setTypeHandle] = useState<"add" | "edit">("add");
  // 当前操作的是 一级页面 、二级页面 、模块
  const [type, setType] = useState<0 | 1 | 2 | 3>(0);
  // 二级页面数据
  const [twoPageData, setTwoPageData] = useState([]);
  // 模块数据
  const [modalPageData, setModalPageData] = useState([]);
  // 功能/敏感数据
  const [sensitivePageData, setSensitivePageData] = useState([]);
  // 当前点击的一级页面数据
  const [clickFirstData, setClickFirstData] = useState({});
  // 当前点击的二级页面数据
  const [clickSecondData, setClickSecondData] = useState({});
  // 当前点击的模块数据
  const [clickModuleData, setClickModuleData] = useState({});
  // 当前点击的 敏感数据
  const [clickSensiveData, setClickSensiveData] = useState({});
  // 二级页面 点击样式
  const [secondClearClickStyle, setsecondClearClickStyle] = useState(false);
  // 模块点击样式
  const [moduleClearClickStyle, setmoduleClearClickStyle] = useState(false);
  // 显示批量添加权限
  const [showBatchModal, setshowBatchModal] = useState(false);
  const [batchType, setbatchType] = useState<2 | 3>(2);

  const containerInfo = useMainSize(".ant-layout-content");
  const wrapperRef = useRef(null);

  // 查询资源列表

  const getResourceList = async params => {
    const { type = 0 } = params;
    const response = await apis.queryResourceList(params);
    const { status, entry } = response;
    if (status) {
      switch (type) {
        case 0:
          setCurrentData(entry);
          break;
        case 1:
          setTwoPageData(entry);
          break;
        case 2:
          setModalPageData(entry);
          break;
        case 3:
          setSensitivePageData(entry);
          break;
        default:
          setCurrentData(entry);
      }
    } else {
      switch (type) {
        case 0:
          setCurrentData([]);
          break;
        case 1:
          setTwoPageData([]);
          break;
        case 2:
          setModalPageData([]);
          break;
        case 3:
          setSensitivePageData([]);
          break;
        default:
          setCurrentData([]);
      }
    }
  };

  useEffect(() => {
    getResourceList({
      type: 0
    });
  }, []);

  const levelModalProps = useMemo(() => {
    return {
      visible: showLevelPage,
      type,
      clickFirstData: clickFirstData,
      clickSecondData: clickSecondData,
      firstPage: currentData,
      fresh: () => {
        if (type === 0) {
          getResourceList({
            type: 0
          });
        } else if (type === 1) {
          setsecondClearClickStyle(false);
          getResourceList({
            parentId: clickFirstData.id,
            type: 1
          });
        }
      },
      close: () => {
        setShowLevelPage(false);
      },
      handleType: typeHandle
    };
  }, [showLevelPage, clickFirstData, clickSecondData, currentData, type, typeHandle, twoPageData]);

  const moduleModalProps = useMemo(() => {
    return {
      visible: showModulePage,
      cilckData: clickModuleData,
      clickSecondData: clickSecondData,
      type,
      fresh: () => {
        // 重新请求 模块数据
        getResourceList({
          parentId: clickSecondData.id,
          type: 2
        });
      },
      close: () => {
        setShowModulePage(false);
        setmoduleClearClickStyle(false);
      },
      handleType: typeHandle
    };
  }, [showModulePage, clickSecondData, clickSecondData, type, typeHandle]);
  const SensitiveProps = useMemo(() => {
    return {
      visible: showsensitiveModal,
      cilckData: clickSensiveData,
      clickModuleData: clickModuleData,
      fresh: () => {
        // 重新请求 模块数据
        getResourceList({
          parentId: clickModuleData.id,
          type: 3
        });
      },
      close: () => {
        setShowsensitiveModal(false);
      },
      handleType: typeHandle
    };
  }, [showsensitiveModal, clickSensiveData, clickModuleData, typeHandle]);
  const BatchProps = useMemo(() => {
    return batchType === 2
      ? {
          visible: showBatchModal,
          clickData: clickSecondData,
          batchType,
          fresh: () => {
            // 重新请求 模块数据
            getResourceList({
              parentId: clickSecondData.id,
              type: 2
            });
          },
          close: () => {
            setshowBatchModal(false);
          }
        }
      : {
          visible: showBatchModal,
          clickData: clickModuleData,
          batchType,
          fresh: () => {
            // 重新请求 模块数据
            getResourceList({
              parentId: clickModuleData.id,
              type: 3
            });
          },
          close: () => {
            setshowBatchModal(false);
          }
        };
  }, [showBatchModal, clickSecondData, batchType, clickModuleData]);

  const handleSensiveEdit = record => {
    setClickSensiveData(record);
    setShowsensitiveModal(true);
    setTypeHandle("edit");
  };
  return (
    <>
      <div ref={wrapperRef}>
        <Card title="资源配置">
          <Row wrap={true}>
            <Col xs={{ span: 12 }} lg={{ span: 6 }}>
              <AuthChild
                height={containerInfo.height - 140}
                title="一级页面"
                clearClickStyle={false}
                onItemClick={record => {
                  // 设置 一级 已点击数据
                  setClickFirstData(record);
                  if (record.id === clickFirstData.id) return;
                  // 将 二级 模块 已点击数据清空
                  setClickSecondData({});
                  setClickModuleData({});
                  // 将二级 模块 敏感 数据清空
                  setTwoPageData([]);
                  setModalPageData([]);
                  setSensitivePageData([]);
                  setsecondClearClickStyle(true);
                  // 请求 二级页面数据
                  getResourceList({
                    parentId: record.id,
                    type: 1
                  });
                }}
                columns={oneLevelColumns({
                  setShowLevelPage,
                  setTypeHandle,
                  setType
                })}
                data={currentData}
                extra={
                  <a
                    onClick={() => {
                      setShowLevelPage(true);
                      setTypeHandle("add");
                      setType(0);
                    }}
                  >
                    新增
                  </a>
                }
              />
            </Col>
            <Col xs={{ span: 12 }} lg={{ span: 6 }}>
              <AuthChild
                height={containerInfo.height - 140}
                title="二级页面"
                clearClickStyle={secondClearClickStyle}
                onItemClick={record => {
                  // 设置 二级 已点击数据
                  setClickSecondData(record);
                  if (record.id === clickSecondData.id) return;
                  // 清空模块 已点击数据
                  setClickModuleData({});
                  // 情况 模块 敏感 数据
                  setModalPageData([]);
                  setSensitivePageData([]);
                  setmoduleClearClickStyle(true);
                  // 请求 模块数据
                  getResourceList({
                    parentId: record.id,
                    type: 2
                  });
                }}
                columns={twoLevelColumns({
                  setShowLevelPage,
                  setTypeHandle,
                  setType
                })}
                data={twoPageData}
                extra={
                  <>
                    {clickFirstData?.id && (
                      <a
                        onClick={() => {
                          setShowLevelPage(true);
                          setTypeHandle("add");
                          setType(1);
                        }}
                      >
                        新增
                      </a>
                    )}
                  </>
                }
              />
            </Col>
            <Col xs={{ span: 12 }} lg={{ span: 6 }}>
              <AuthChild
                height={containerInfo.height - 140}
                title="模块"
                clearClickStyle={moduleClearClickStyle}
                onItemClick={record => {
                  // 设置 模块点击数据
                  setClickModuleData(record);
                  if (record.id === clickModuleData.id) return;
                  // 清空 敏感数据
                  setSensitivePageData([]);
                  // 请求 敏感数据
                  getResourceList({
                    parentId: record.id,
                    type: 3
                  });
                }}
                columns={moduleColumns({
                  setShowModulePage,
                  setTypeHandle,
                  setType
                })}
                data={modalPageData}
                extra={
                  <>
                    {clickSecondData.id && (
                      <Space>
                        <a
                          onClick={() => {
                            setshowBatchModal(true);
                            setbatchType(2);
                          }}
                        >
                          批量新增
                        </a>
                        <a
                          onClick={() => {
                            setShowModulePage(true);
                            setTypeHandle("add");
                            setType(2);
                          }}
                        >
                          新增
                        </a>
                      </Space>
                    )}
                  </>
                }
              />
            </Col>
            <Col xs={{ span: 12 }} lg={{ span: 6 }}>
              <AuthChild
                clearClickStyle={false}
                height={containerInfo.height - 140}
                title="功能/敏感模块"
                columns={funColumns({
                  handleSensiveEdit
                })}
                data={sensitivePageData}
                dontFetch={true}
                extra={
                  <>
                    {clickModuleData.id ? (
                      <Space>
                        <a
                          onClick={() => {
                            setshowBatchModal(true);
                            setbatchType(3);
                          }}
                        >
                          批量新增
                        </a>
                        <a
                          onClick={() => {
                            setShowsensitiveModal(true);
                            setTypeHandle("add");
                          }}
                        >
                          新增
                        </a>
                      </Space>
                    ) : null}
                  </>
                }
              />
            </Col>
          </Row>
        </Card>
        {showLevelPage && <LevelPage {...levelModalProps} />}
        {showModulePage && <ModulePage {...moduleModalProps} />}
        {showsensitiveModal && <EditSensitive {...SensitiveProps} />}
        {showBatchModal && <BatchAddAuth {...BatchProps} />}
      </div>
    </>
  );
};

const AliveRecord = props => (
  <KeepAlive name={pageUrl}>
    <Resources {...props} />
  </KeepAlive>
);

export default React.memo(AliveRecord);
