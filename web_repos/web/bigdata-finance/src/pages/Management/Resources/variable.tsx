import { Space } from "antd";
import { StopOutlined } from "@ant-design/icons";
import { history } from "@umijs/max";
import { sensitiveObj } from "../variable";
import queryString from "query-string";

// 一级页面表头
export const oneLevelColumns: TableListItem = ({ setShowLevelPage, setTypeHandle, setType }: any) => {
  return [
    {
      title: "名称",
      dataIndex: "name",
      hideInSearch: true,
      width: 100,
      render(_, record) {
        const { status } = record;
        return (
          <Space>
            {status === 0 && <StopOutlined />}
            {record.name}
          </Space>
        );
      }
    },
    {
      title: "操作",
      width: 60,
      valueType: "option",
      render(_, record) {
        return (
          <Space>
            <a
              type="link"
              onClick={() => {
                setShowLevelPage(true);
                setTypeHandle("edit");
                setType(0);
              }}
            >
              编辑
            </a>
            <a
              type="link"
              onClick={() => {
                history.push({
                  pathname: "/management/resources/resourcetoroles",
                  search: queryString.stringify({
                    id: record.id,
                    name: record.name,
                    type: "0"
                  })
                });
              }}
            >
              权限
            </a>
          </Space>
        );
      }
    }
  ];
};
// 二级页面表头
export const twoLevelColumns: TableListItem = ({ setShowLevelPage, setTypeHandle, setType }: any) => {
  return [
    {
      title: "名称",
      dataIndex: "name",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { status } = record;
        return (
          <Space>
            {status === 0 && <StopOutlined />}
            {record.name}
          </Space>
        );
      }
    },
    {
      title: "操作",
      width: 60,
      valueType: "option",
      render(_, record) {
        return (
          <Space>
            <a
              type="link"
              onClick={() => {
                setShowLevelPage(true);
                setTypeHandle("edit");
                setType(1);
              }}
            >
              编辑
            </a>
            <a
              type="link"
              onClick={() => {
                history.push({
                  pathname: "/management/resources/resourcetoroles",
                  search: queryString.stringify({
                    id: record.id,
                    name: record.name,
                    type: "1"
                  })
                });
              }}
            >
              权限
            </a>
          </Space>
        );
      }
    }
  ];
};
// 模块表头
export const moduleColumns: TableListItem = ({ setShowModulePage, setTypeHandle, setType }: any) => {
  return [
    {
      title: "名称",
      dataIndex: "name",
      hideInSearch: true,
      width: 100,
      render(_, record) {
        const { status } = record;
        return (
          <Space>
            {status === 0 && <StopOutlined />}
            {record.name}
          </Space>
        );
      }
    },
    {
      title: "操作",
      width: 60,
      valueType: "option",
      render(_, record) {
        return (
          <Space>
            <a
              type="link"
              onClick={() => {
                setShowModulePage(true);
                setTypeHandle("edit");
                setType(2);
              }}
            >
              编辑
            </a>
            <a
              type="link"
              onClick={() => {
                history.push({
                  pathname: "/management/resources/resourcetoroles",
                  search: queryString.stringify({
                    id: record.id,
                    name: record.name,
                    type: "2"
                  })
                });
              }}
            >
              权限
            </a>
          </Space>
        );
      }
    }
  ];
};
// 功能/敏感字段表头
export const funColumns: TableListItem = ({ handleSensiveEdit }: any) => {
  return [
    {
      title: "名称",
      dataIndex: "name",
      width: 100,
      hideInSearch: true,
      render(_, record) {
        const { status } = record;
        return (
          <Space>
            {status === 0 && <StopOutlined />}
            {record.name}
          </Space>
        );
      }
    },
    {
      title: "类型",
      dataIndex: "type",
      width: 100,
      render(_, record) {
        return sensitiveObj[record.type];
      }
    },
    {
      title: "操作",
      valueType: "operation",
      width: 120,
      fixed: "right",
      render(_, record) {
        return (
          <Space>
            <a
              type="link"
              onClick={() => {
                handleSensiveEdit(record);
              }}
            >
              编辑
            </a>
            <a
              type="link"
              onClick={() => {
                history.push({
                  pathname: "/management/resources/resourcetoroles",
                  search: queryString.stringify({
                    id: record.id,
                    name: record.name,
                    type: "3"
                  })
                });
              }}
            >
              权限
            </a>
            <a
              type="link"
              onClick={() => {
                history.push({
                  pathname: "/management/resources/resourcetoroles",
                  search: queryString.stringify({
                    id: record.id,
                    name: record.name,
                    type: "4",
                    permissionType: "0"
                  })
                });
              }}
            >
              读权限
            </a>
            <a
              type="link"
              onClick={() => {
                history.push({
                  pathname: "/management/resources/resourcetoroles",
                  search: queryString.stringify({
                    id: record.id,
                    name: record.name,
                    type: "4",
                    permissionType: "1"
                  })
                });
              }}
            >
              写权限
            </a>
          </Space>
        );
      }
    }
  ];
};
