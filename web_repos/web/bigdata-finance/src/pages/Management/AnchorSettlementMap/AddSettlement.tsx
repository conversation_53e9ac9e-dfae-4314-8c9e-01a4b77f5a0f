import React from "react";
import { Modal, Form, DatePicker } from "antd";
import apis from "@/services/management/anchorSettlementMap";
import { SIZE, FormLayout } from "@/utils/constant";
import dayjs from "dayjs";
import { dateFormat, settleMonthFormat } from "./index";
import { returnEllipsisTooltip } from "@/utils";
export type HandleType = "add" | "edit" | void;

interface IProps {
  // 显示隐藏 modal
  visible: boolean;
  // 关闭modal
  close: () => void;
  // 关闭 modal后 刷新页面
  fresh?: () => void;
  handlePageParmas?: () => void;
  handleType: HandleType;
  // 标题
  title?: React.ReactNode;
  curData?: Record<string, any>;
}
const UploadExcelModal = (props: IProps) => {
  const { visible, close, fresh, handleType } = props;
  const [form] = Form.useForm();

  const handleExcelCancel = () => {
    close?.();
  };
  const returnTitle = () => {
    return (handleType === "add" ? "新增" : "编辑") + "结算周期";
  };
  const handleExcelOk = () => {
    form.validateFields().then(async values => {
      const { date, settleMonth } = values;
      const finP = {
        settleMonth: dayjs(settleMonth).format(settleMonthFormat),
        startDate: dayjs(date?.[0]).format(dateFormat),
        endDate: dayjs(date?.[1]).format(dateFormat)
      };
      const { status } = await apis.add(finP);
      if (status) {
        close?.();
        fresh?.();
      }
    });
  };

  return (
    <Modal
      centered={true}
      title={returnTitle()}
      open={visible}
      onOk={() => handleExcelOk()}
      onCancel={() => handleExcelCancel()}
      maskClosable={false}
      okButtonProps={{
        size: SIZE
      }}
      cancelButtonProps={{
        size: SIZE
      }}
    >
      <Form size={SIZE} form={form} {...FormLayout}>
        <Form.Item label={returnEllipsisTooltip({ title: "结算月份" })} name="settleMonth" rules={[{ required: true, message: "请选择" }]}>
          <DatePicker picker="month" allowClear style={{ width: "100%" }} />
        </Form.Item>
        <Form.Item label={returnEllipsisTooltip({ title: "周期" })} name="date" rules={[{ required: true, message: "请选择" }]}>
          <DatePicker.RangePicker picker="date" allowClear style={{ width: "100%" }} />
        </Form.Item>
      </Form>
    </Modal>
  );
};
export default React.memo(UploadExcelModal);
