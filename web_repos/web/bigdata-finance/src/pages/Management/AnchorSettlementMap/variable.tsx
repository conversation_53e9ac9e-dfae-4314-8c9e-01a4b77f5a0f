import { returnEllipsisTooltip } from "@/utils";
import { SIZE } from "@/utils/constant";
import { Space, Button, Popconfirm, DatePicker } from "antd";
import dayjs from "dayjs";
const settleMonthFormat = "YYYY-MM";
const dateFormat = "YYYY-MM-DD";
const columns_search = (): Array<TableListItem> => {
  return [
    {
      title: "结算月份",
      dataIndex: "settleMonth",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return <DatePicker.RangePicker picker="month" allowClear />;
      }
    }
  ];
};
export const columns = ({ handleDelete }: any): Array<TableListItem> => {
  return [
    ...columns_search(),
    {
      title: "序号",
      dataIndex: "$index",
      width: 100,
      hideInSearch: true,
      fixed: "left",
      editable: false,
      render: (_, record, index) => {
        return index + 1;
      }
    },
    {
      title: "结算月份",
      dataIndex: "settleMonth",
      width: 200,
      hideInSearch: true,
      fieldProps: {
        allowClear: true,
        size: SIZE
      },
      formItemProps: () => {
        return {
          rules: [{ required: true, message: "此项为必填项" }]
        };
      },
      valueType: "dateMonth",
      render(_, record) {
        const { settleMonth } = record;
        return dayjs(settleMonth).format(settleMonthFormat);
      }
    },
    {
      title: "周期",
      dataIndex: "date",
      width: 200,
      hideInSearch: true,
      formItemProps: () => {
        return {
          rules: [{ required: true, message: "此项为必填项" }]
        };
      },
      render(_, record) {
        const { date } = record;
        return `${dayjs(date[0]).format(dateFormat)} 到 ${dayjs(date[1]).format(dateFormat)}`;
      },
      renderFormItem: (_, { isEditable, defaultRender }: any) => {
        const {
          entry: { date }
        } = _;
        return isEditable ? <DatePicker.RangePicker defaultValue={date} allowClear size={SIZE} /> : <>{defaultRender()}</>;
      }
    },
    {
      title: "操作",
      width: 100,
      valueType: "option",
      fixed: "right",
      render: (text: string, record: any, index, action) => {
        const { id } = record;
        return (
          <Space>
            <Button
              size={SIZE}
              type="primary"
              onClick={() => {
                action?.startEditable?.(id);
              }}
            >
              编辑
            </Button>
            <Popconfirm
              title="是否确认删除?"
              onConfirm={() => handleDelete(record)}
              onCancel={() => {}}
              okButtonProps={{ size: SIZE }}
              cancelButtonProps={{ size: SIZE }}
            >
              <Button type="link" size={SIZE}>
                删除
              </Button>
            </Popconfirm>
          </Space>
        );
      }
    }
  ].map(item => {
    const { title } = item;
    return {
      ...item,
      align: "center",
      title: returnEllipsisTooltip({ title })
    };
  });
};
