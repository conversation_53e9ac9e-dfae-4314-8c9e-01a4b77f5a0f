import { useRef, useState } from "react";
import { columns } from "./variable";
import TableList from "@/components/TableList";
import KeepAlive from "react-activation";
import apis from "@/services/management/anchorSettlementMap";
import { Button, message, Form } from "antd";
import { SIZE } from "@/utils/constant";
import AddSettlement, { HandleType } from "./AddSettlement";
import dayjs from "dayjs";
const PageUrl = `/management/anchorSettlementMap`;
export const settleMonthFormat = "YYYYMM";
export const dateFormat = "YYYYMMDD";
const Res = () => {
  const actions = useRef<any>();
  const formRef = useRef<any>();
  const [editForm] = Form.useForm();
  const [showAdd, setshowAdd] = useState(false);
  const [handleType, sethandleType] = useState<HandleType>();
  const [curData, setCurData] = useState<Record<string, any>>();
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const handleDelete = async record => {
    const { id } = record;
    const { status } = await apis.delete({ ids: [id] });
    if (status) {
      message.success("删除成功！");
      actions?.current?.reload();
    }
  };

  const columnsProps = { handleDelete };
  // 处理请求参数
  const handlePageParmas = (params: any) => {
    const { current, settleMonth, ...rest } = params;
    setEditableRowKeys([]);
    if (settleMonth) {
      rest.startMonth = dayjs(settleMonth[0]).format(settleMonthFormat);
      rest.endMonth = dayjs(settleMonth[1]).format(settleMonthFormat);
    }
    return { ...rest, pageNo: current };
  };
  const returnTitle = () => {
    return (
      <>
        <Button
          type="primary"
          size={SIZE}
          onClick={() => {
            setshowAdd(true);
            setCurData({});
            sethandleType("add");
          }}
        >
          新增结算周期
        </Button>
      </>
    );
  };
  const AddSettlementProps = {
    visible: showAdd,
    handleType,
    curData,
    fresh: () => {
      actions?.current?.reload();
    },
    close: () => {
      setshowAdd(false);
    }
  };
  const handleDatas = data => {
    return (data || []).map(item => {
      const { startDate, endDate } = item;
      return {
        ...item,
        date: [dayjs(startDate), dayjs(endDate)]
      };
    });
  };
  const editable: any = {
    type: "single",
    form: editForm,
    onSave: async (key, editData) => {
      const { id, date, settleMonth } = editData;
      const params = {
        ...editData,
        id,
        settleMonth: dayjs(settleMonth).format(settleMonthFormat),
        startDate: dayjs(date?.[0]).format(dateFormat),
        endDate: dayjs(date?.[1]).format(dateFormat)
      };
      const { status } = await apis.update(params);
      editForm.resetFields([key]);
      if (status) {
        message.success("修改成功！");
      }
      actions.current?.reload();
    },
    editableKeys,
    onChange: setEditableRowKeys,
    actionRender: (row, config, { save, cancel }) => [save, cancel]
  };
  return (
    <div>
      <TableList
        actionRef={actions}
        formRef={formRef}
        columns={columns(columnsProps)}
        headerTitle={returnTitle()}
        api={apis.getList}
        scroll={{ y: "calc(100vh - 330px)" }}
        preFetch={handlePageParmas}
        handleDatas={handleDatas}
        editable={editable}
        rowKey="id"
      />
      {AddSettlementProps.visible ? <AddSettlement {...AddSettlementProps}></AddSettlement> : null}
    </div>
  );
};
const AliveRecord = () => (
  <KeepAlive name={PageUrl}>
    <Res />
  </KeepAlive>
);
export default AliveRecord;
