import React, { useMemo, useRef, useState, useCallback } from "react";
import { Button } from "antd";
import { ProTable, ActionType } from "@ant-design/pro-components";
import { paginationOptions } from "@/utils";
import { NOVALUE, SIZE, Protable_FROM_CONFIG } from "@/utils/constant";
import { columns } from "./variable";
import EditModal from "./EditModal";
import KeepAlive from "react-activation";
import api from "@/services/management/users";
import UserRowAuth from "@/pages/Management/components/UserRowAuth";
import UserResource from "./UserResource";
import EditPhoneModal from "./EditPhoneModal";
const pageUrl = "/management/users";
const Users = () => {
  const curPageBtnPermissions: string[] = [];
  const formRef = useRef<any>();
  const actionRef = useRef<ActionType>();
  const [showEditModal, setShowEditModal] = useState(false);
  const [handleType, setHandleType] = useState<string>("");
  const [currentData, setCurrentData] = useState<any>({});
  const [pageParams] = useState<any>({
    type: "",
    staffStatus: "",
    status: ""
  });
  const [showRowModal, setshowRowModal] = useState<boolean>(false);
  const [showResourceModal, setshowResourceModal] = useState<boolean>(false);
  const [showPhoneEditModal, setShowPhoneEditModal] = useState<boolean>(false);

  // 表格高度
  const wrapperRef = useRef<HTMLDivElement | null>(null);

  const handlePhoneEdit = useCallback(record => {
    setShowPhoneEditModal(true);
    setCurrentData(record);
  }, []);
  const handleEdit = useCallback(record => {
    setHandleType("edit");
    setShowEditModal(true);
    setCurrentData(record);
  }, []);

  const handleRolesRowAuth = useCallback(record => {
    setshowRowModal(true);
    setCurrentData(record);
  }, []);

  const handleUserResource = useCallback(record => {
    setshowResourceModal(true);
    setCurrentData(record);
  }, []);

  const userRowAuthModalProps = useMemo(() => {
    return {
      visible: showRowModal,
      currentData,
      fresh: () => {
        actionRef?.current?.reload();
      },
      close: () => {
        setshowRowModal(false);
      },
      handleType: "users"
    };
  }, [showRowModal, currentData, actionRef]);

  const editModalProps = useMemo(() => {
    return {
      visible: showEditModal,
      close: () => {
        actionRef?.current?.reload();
        setShowEditModal(false);
      },
      handleType,
      currentData
      // passwordValue
    };
  }, [showEditModal, handleType, currentData, actionRef]);
  const editPhoneModalProps = useMemo(() => {
    return {
      visible: showPhoneEditModal,
      close: () => {
        actionRef?.current?.reload();
        setShowPhoneEditModal(false);
      },
      currentData
    };
  }, [showPhoneEditModal, currentData, actionRef]);
  const userResourceModalprops = useMemo(() => {
    return {
      visible: showResourceModal,
      close: () => {
        setshowResourceModal(false);
      },
      currentData
    };
  }, [showResourceModal]);

  const columnsProps = useMemo(() => {
    return {
      handlePhoneEdit,
      handleEdit,
      pageParams,
      handleRolesRowAuth,
      handleUserResource,
      curPageBtnPermissions
    };
  }, [pageParams, curPageBtnPermissions]);
  return (
    <div ref={wrapperRef}>
      <ProTable
        size={SIZE}
        formRef={formRef}
        actionRef={actionRef}
        columnEmptyText={NOVALUE}
        columns={columns(columnsProps)}
        scroll={{ x: "max-content", y: "calc(100vh - 330px)" }}
        headerTitle={
          <>
            <Button
              type="primary"
              size={SIZE}
              onClick={() => {
                setShowEditModal(true);
                setHandleType("add");
                setCurrentData({});
              }}
            >
              新增账号
            </Button>
          </>
        }
        form={Protable_FROM_CONFIG}
        options={false}
        //获取用户列表
        request={async p => {
          const { current, pageSize } = p;
          let params = formRef.current?.getFieldsValue();
          const {
            entry = [],
            status,
            totalRecordSize = 0
          } = await api.queryUserList({
            pageNo: current,
            pageSize: pageSize,
            ...params
          });
          if (status) {
            return {
              data: entry,
              success: true,
              total: totalRecordSize
            };
          }
          return {
            data: [],
            success: false,
            total: 0
          };
        }}
        pagination={{
          showQuickJumper: true,
          ...paginationOptions
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false
        }}
      />
      {showEditModal && <EditModal {...editModalProps} />}
      {showRowModal && <UserRowAuth {...userRowAuthModalProps} />}
      {showResourceModal && <UserResource {...userResourceModalprops} />}
      {showPhoneEditModal && <EditPhoneModal {...editPhoneModalProps} />}
    </div>
  );
};

const AliveRecord = props => (
  <KeepAlive name={pageUrl}>
    <Users {...props} />
  </KeepAlive>
);
export default React.memo(AliveRecord);
