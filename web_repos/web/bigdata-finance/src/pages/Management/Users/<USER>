/* eslint-disable no-unused-vars */
import { Tag, Select, Space } from "antd";
import { StopOutlined } from "@ant-design/icons";
import { accountTypeObj, jobStatusObj, accountStatusObj, accountStatus, accountType, jobStatus, jobOn, accountOn } from "@/pages/Management/variable";
import { AllValue } from "@/utils/constant";
import { returnEllipsisTooltip } from "@/utils";
const columns_search = (pageParams: any) => {
  return [
    {
      title: "账户类型",
      dataIndex: "type",
      hideInTable: true,
      renderFormItem: () => {
        return (
          <Select defaultValue={pageParams?.type} placeholder="请选择">
            {[AllValue, ...accountType].map(item => {
              const { label, value } = item;
              return (
                <Select.Option key={value} value={value}>
                  {label}
                </Select.Option>
              );
            })}
          </Select>
        );
      }
    },
    {
      title: "在职状态",
      dataIndex: "staffStatus",
      hideInTable: true,
      renderFormItem: () => {
        return (
          <Select defaultValue={pageParams?.staffStatus} placeholder="请选择">
            {[AllValue, ...jobStatus].map(item => {
              const { label, value } = item;
              return (
                <Select.Option key={value} value={value}>
                  {label}
                </Select.Option>
              );
            })}
          </Select>
        );
      }
    },
    {
      title: "账户状态",
      dataIndex: "status",
      hideInTable: true,
      renderFormItem: () => {
        return (
          <Select defaultValue={pageParams?.status} placeholder="请选择">
            {[AllValue, ...accountStatus].map(item => {
              const { label, value } = item;
              return (
                <Select.Option key={value} value={value}>
                  {label}
                </Select.Option>
              );
            })}
          </Select>
        );
      }
    },
    {
      title: "姓名",
      dataIndex: "displayName",
      hideInTable: true
    },
    {
      title: "手机号",
      dataIndex: "phone",
      hideInTable: true
    }
  ];
};
export const columns = ({ handlePhoneEdit, handleEdit, pageParams, handleRolesRowAuth, handleUserResource }: any): Array<TableListItem> => {
  return [
    ...columns_search(pageParams),
    {
      title: "序号",
      dataIndex: "$index",
      width: 60,
      hideInSearch: true,
      fixed: "left",
      render: (_, record, index) => {
        return index + 1;
      }
    },
    {
      title: "用户名",
      dataIndex: "userName",
      width: 80,
      hideInSearch: true,
      render: _ => {
        return returnEllipsisTooltip({ title: _ });
      }
    },
    {
      title: "账户类型",
      dataIndex: "type",
      width: 80,
      hideInSearch: true,
      render: (_, record) => {
        const { type } = record;
        return <Tag>{accountTypeObj[type]}</Tag>;
      }
    },
    {
      title: "姓名",
      dataIndex: "displayName",
      width: 60,
      hideInSearch: true,
      render: _ => {
        return returnEllipsisTooltip({ title: _ });
      }
    },
    {
      title: "手机号",
      dataIndex: "phone",
      width: 90,
      hideInSearch: true,
      render: _ => {
        return returnEllipsisTooltip({ title: _ });
      }
    },
    {
      title: "角色信息",
      dataIndex: "roles",
      hideInSearch: true,
      width: 120,
      render: (_, record) => {
        const { roles = [] } = record;
        const val = roles.map(item => item.name).join();
        return returnEllipsisTooltip({ title: val });
      }
    },
    {
      //菜单权限   行权限
      title: "权限详情",
      dataIndex: "$auths",
      hideInSearch: true,
      width: 150,
      render: (_, record) => {
        return (
          <Space>
            <a
              onClick={() => {
                handleUserResource(record);
              }}
            >
              菜单权限
            </a>
            <a
              onClick={() => {
                handleRolesRowAuth(record);
              }}
            >
              行权限
            </a>
          </Space>
        );
      }
    },
    {
      title: "最近一次访问时间",
      dataIndex: "lastLoginTime",
      hideInSearch: true,
      width: 140
    },
    {
      title: "在职状态",
      dataIndex: "staffStatus",
      width: 80,
      hideInSearch: true,
      render: (_, record) => {
        const { staffStatus = jobOn } = record;
        return <Tag color={staffStatus === jobOn ? "success" : "error"}>{jobStatusObj[staffStatus]}</Tag>;
      }
    },
    {
      title: "账户状态",
      dataIndex: "status",
      width: 80,
      hideInSearch: true,
      render: (_, record) => {
        const { status = accountOn } = record;
        return <Tag color={status === accountOn ? "success" : "error"}>{accountStatusObj[status]}</Tag>;
      }
    },

    {
      title: "操作",
      width: 80,
      valueType: "option",
      fixed: "right",
      render(_, record) {
        return (
          <>
            <a onClick={() => handlePhoneEdit(record)}>编辑(手机号)</a>
            <a onClick={() => handleEdit(record)}>编辑</a>
          </>
        );
      }
    }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      align: "center",
      ...item,
      title: returnEllipsisTooltip({ title })
    };
  });
};

// 一级页面表头
export const oneLevelColumns: TableListItem = ({}: any) => {
  return [
    {
      title: "名称",
      dataIndex: "name",
      hideInSearch: true,
      render(_, record) {
        const { status } = record;
        return (
          <Space>
            {status === 0 && <StopOutlined />}
            {record.name}
          </Space>
        );
      }
    }
  ];
};
// 二级页面表头
export const twoLevelColumns: TableListItem = ({}: any) => {
  return [
    {
      title: "名称",
      dataIndex: "name",
      hideInSearch: true,
      render(_, record) {
        const { status } = record;
        return (
          <Space>
            {status === 0 && <StopOutlined />}
            {record.name}
          </Space>
        );
      }
    }
  ];
};
// 模块表头
export const moduleColumns: TableListItem = ({}: any) => {
  return [
    {
      title: "名称",
      dataIndex: "name",
      hideInSearch: true,
      render(_, record) {
        const { status } = record;
        return (
          <Space>
            {status === 0 && <StopOutlined />}
            {record.name}
          </Space>
        );
      }
    }
  ];
};
// 功能/敏感字段表头
export const funColumns: TableListItem = ({}: any) => {
  return [
    {
      title: "名称",
      dataIndex: "name",
      hideInSearch: true,
      render(_, record) {
        const { status } = record;
        return (
          <Space>
            {status === 0 && <StopOutlined />}
            {record.name}
          </Space>
        );
      }
    }
  ];
};
