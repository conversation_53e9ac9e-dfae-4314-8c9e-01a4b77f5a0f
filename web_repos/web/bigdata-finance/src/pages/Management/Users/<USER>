/* eslint-disable no-unused-vars */
import React, { useState, useMemo, useEffect, useCallback } from "react";
import { useAccess } from "@umijs/max";
import { Row, Col, Space, Modal } from "antd";
import apis from "@/services/management/users";
import AuthChild from "../components/AuthChild";
import { oneLevelColumns, twoLevelColumns, moduleColumns, funColumns } from "./variable";
type AuthComponentProps = {
  title?: string;
  [propname: string]: any;
};
const UserResource: React.FC<AuthComponentProps> = props => {
  const { userInfo } = useAccess();
  const { visible, close, currentData } = props;

  // const [form] = Form.useForm();
  const [userResourceList, setUserResourceList] = useState([]);
  const [twoPageData, setTwoPageData] = useState([]);
  const [modalPageData, setModalPageData] = useState([]);
  const [sensitivePageData, setSensitivePageData] = useState([]);
  //用户拥有的资源列表
  const getUserResourceList = useCallback(async (params: any) => {
    const { status, entry } = await apis.queryUserAuthorizedResources(params);
    if (status) {
      setUserResourceList(entry);
    } else {
      setUserResourceList([]);
    }
  }, []);
  useEffect(() => {
    if (currentData?.userId) {
      getUserResourceList({
        platform: 1,
        userId: currentData.userId,
        currentUserId: userInfo.userId
      });
    }
  }, [currentData, userInfo]);

  const returnTitle = useMemo(() => {
    return (
      <Space direction="vertical">
        <span>{` ${currentData?.displayName}-资源分配详情`}</span>
      </Space>
    );
  }, []);

  const handleOk = () => {};

  return (
    <Modal
      open={visible}
      onCancel={() => {
        close();
      }}
      title={returnTitle}
      centered
      footer={null}
      styles={{ body: { minHeight: 650 } }}
      width={1000}
      onOk={handleOk}
    >
      <Row wrap={true}>
        <Col xs={{ span: 12 }} lg={{ span: 6 }}>
          <AuthChild
            height={550}
            title="一级页面"
            onItemClick={record => {
              setModalPageData([]);
              setSensitivePageData([]);
              setTwoPageData(record?.children || []);
            }}
            columns={oneLevelColumns({})}
            data={userResourceList}
          />
        </Col>
        <Col xs={{ span: 12 }} lg={{ span: 6 }}>
          <AuthChild
            height={550}
            title="二级页面"
            onItemClick={record => {
              setSensitivePageData([]);
              setModalPageData(record?.children || []);
            }}
            columns={twoLevelColumns({})}
            data={twoPageData}
          />
        </Col>
        <Col xs={{ span: 12 }} lg={{ span: 6 }}>
          <AuthChild
            height={550}
            title="模块"
            onItemClick={record => {
              setSensitivePageData(record?.children || []);
            }}
            columns={moduleColumns({})}
            data={modalPageData}
          />
        </Col>
        <Col xs={{ span: 12 }} lg={{ span: 6 }}>
          <AuthChild height={550} title="功能/敏感模块" columns={funColumns({})} dontFetch={true} data={sensitivePageData} />
        </Col>
      </Row>
    </Modal>
  );
};
export default React.memo(UserResource);
