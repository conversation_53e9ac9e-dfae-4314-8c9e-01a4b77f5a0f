import { Form, Input, Modal, message } from "antd";
import { useEffect, useState } from "react";
import api from "@/services/management/users";

export default props => {
  const {
    visible,
    close,
    currentData: { phone, mobileEncryption, id }
  } = props;
  const [initialValue, setInitialValue] = useState<any>({ phone });
  const [form] = Form.useForm();

  useEffect(() => {
    if (mobileEncryption) {
      api.decryptPhone({ phone: mobileEncryption }).then(res => {
        if (res.status && res.entry) {
          form.setFieldValue("phone", res.entry);
          setInitialValue({ phone: res.entry });
        }
      });
    }
  }, [mobileEncryption]);

  const onHandleOk = () => {
    form.validateFields().then(values => {
      if (values.phone.includes("*")) return message.error("请完善手机号！");
      api.modifyPhone({ id, phone: values.phone }).then(({ status, message: msg }) => {
        if (status) {
          message.success("编辑成功！");
          close();
        } else {
          message.error(msg);
        }
      });
    });
  };

  return (
    <Modal
      centered
      title="编辑手机号"
      visible={visible}
      onCancel={close}
      cancelButtonProps={{ size: "small" }}
      okButtonProps={{ size: "small" }}
      onOk={onHandleOk}
    >
      <Form size="small" initialValues={initialValue} form={form}>
        <Form.Item name="phone" label="手机号" rules={[{ required: true }]}>
          <Input placeholder="请输入新手机号" />
        </Form.Item>
      </Form>
    </Modal>
  );
};
