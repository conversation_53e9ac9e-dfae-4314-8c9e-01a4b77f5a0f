import React, { useState, useEffect, useCallback, useMemo } from "react";
import { Modal, Form, Select, Input, Space, Button, message, Tabs } from "antd";
import { jobStatus, accountStatus, jobOn, accountType, WechatUser } from "@/pages/Management/variable";
import SearchSelectInput from "@/components/SearchSelectInput";
import api from "@/services/management/users";
import apiRoles from "@/services/management/roles";
import { SPLIT_FLAG, SIZE, NOVALUE, FormLayout } from "@/utils/constant";
import { returnEllipsisTooltip } from "@/utils";
import { EyeInvisibleOutlined, EyeOutlined } from "@ant-design/icons";
const { Option } = Select;
const { Password } = Input;

const EditModal = (props: any) => {
  const { visible, close, handleType, currentData, passwordValue } = props;
  const [form] = Form.useForm();
  const [currentTab, setcurrentTab] = useState(WechatUser);
  const [newPasswordValue] = useState(passwordValue);
  const [phoneIsShow, setPhoneIsShow] = useState<boolean>(false);
  const [decodePhone, setDecodePhone] = useState<any>();
  const [initialValues] = useState(
    handleType === "edit"
      ? {
          staffStatus: jobOn,
          password: newPasswordValue,
          ...currentData,
          name: `${currentData?.displayName}(${currentData?.userId})`,
          // roleIds: currentData?.roles?.map(item => item.id) || [],
          TJroleIds: currentData?.roles?.map(item => item.id) || []
        }
      : { status: 1 }
  );
  const [roleList, setRoleList] = useState([]);
  // 当前选择用户的 姓名
  const [curSelectUsername, setcurSelectUsername] = useState(currentData?.displayName);

  const handleAddCancel = () => {
    form.resetFields();
    close();
  };

  //获取角色列表
  const getRolesList = useCallback(async params => {
    const { status, entry = [] } = await apiRoles.queryRoleList(params);
    if (status) {
      setRoleList(entry.filter(item => item.status === 1));
    }
  }, []);

  useEffect(() => {
    getRolesList({ pageSize: 10000 });
  }, []);

  const handleAddOk = () => {
    form.validateFields().then(async values => {
      const { TJroleIds = [], phone, ...rest } = values;
      if (handleType === "add") {
        if (phone.includes("*")) return message.error("请完善手机号！");
        const { status, message: errInfo } = await api.bodyUserAdd({
          ...rest,
          phone,
          roleIds: [...TJroleIds],
          displayName: curSelectUsername,
          type: currentTab,
          userId: rest.userName
        });
        if (status) {
          close?.();
        } else {
          message.error(errInfo);
        }
      } else {
        const { status, message: errInfo } = await api.bodyUserEdit({
          ...rest,
          roleIds: [...TJroleIds],
          id: currentData.id,
          type: currentTab,
          userId: currentData.userId,
          displayName: curSelectUsername
        });
        if (status) {
          close?.();
        } else {
          message.error(errInfo);
        }
      }
    });
  };

  const returnModalTitle = useMemo(() => {
    return `${handleType === "edit" ? "编辑" : "新增"}账号`;
  }, [handleType]);
  const getUserPhone = async phone => {
    let res = await api.decryptPhone({ phone });
    if (res.status && res.entry) {
      form.setFieldValue("phone", res.entry);
      setDecodePhone(res.entry);
    }
  };
  const handleUserChange = (_, option: any) => {
    const { name = "", mobileEncryption } = option;
    const [displayName, userId, phone, staffStatus] = name.split(SPLIT_FLAG);
    setcurSelectUsername(displayName);
    form.setFieldsValue({
      ...initialValues,
      userName: userId,
      name: `${displayName}(${userId})`,
      phone,
      staffStatus: +staffStatus
    });
    if (mobileEncryption) getUserPhone(mobileEncryption);
  };

  const phoneOtherProps = useMemo(() => {
    if (handleType === "edit") {
      return {
        disabled: true,
        ...(currentData.mobileEncryption
          ? {
              addonAfter: phoneIsShow ? (
                <EyeOutlined
                  style={{
                    color: "rgba(0,0,0,.85)"
                  }}
                  onClick={() => {
                    setPhoneIsShow(false);
                    form.setFieldValue("phone", currentData.phone);
                  }}
                />
              ) : (
                <EyeInvisibleOutlined
                  style={{
                    color: "rgba(0,0,0,.85)"
                  }}
                  onClick={() => {
                    setPhoneIsShow(true);
                    if (!currentData.mobileEncryption) return;
                    if (decodePhone) {
                      form.setFieldValue("phone", decodePhone);
                      return;
                    }
                    getUserPhone(currentData.mobileEncryption);
                  }}
                />
              )
            }
          : {})
      };
    }
    return {};
  }, [handleType, phoneIsShow, currentData]);
  return (
    <Modal
      title={returnModalTitle}
      open={visible}
      width={700}
      onOk={handleAddOk}
      onCancel={handleAddCancel}
      maskClosable={false}
      okText="保存"
      okButtonProps={{
        size: SIZE
      }}
      cancelButtonProps={{
        size: SIZE
      }}
      centered={true}
    >
      <Tabs
        defaultActiveKey={currentTab + ""}
        centered
        onChange={activeKey => {
          setcurrentTab(+activeKey);
          form.resetFields();
        }}
      >
        {accountType.map(item => {
          return (
            <Tabs.TabPane tab={item.label} key={item.value} disabled={currentData.id}>
              <Form form={form} initialValues={initialValues} size={SIZE} {...FormLayout}>
                <Form.Item name="name" label={returnEllipsisTooltip({ title: "姓名" })} rules={[{ required: true }]}>
                  <SearchSelectInput
                    disabled={handleType === "edit"}
                    key="WechatUser"
                    placeholder="搜索用户"
                    onSelectChange={handleUserChange}
                    fetchOptions={async value => {
                      if (value === "") return;
                      const { status, entry } = await api.querySearch({
                        name: value
                      });
                      if (status) {
                        return entry.map((item: any) => ({
                          name: `${item.name || ""}${SPLIT_FLAG}${item.userId || ""}${SPLIT_FLAG}${item.mobile || ""}${SPLIT_FLAG}${
                            item.staffStatus || ""
                          }`,
                          label: `${item.name || NOVALUE}(${item.userId || NOVALUE})・${item.deptName || NOVALUE}`,
                          value: `${item.name || NOVALUE}(${item.userId || NOVALUE})`,
                          mobileEncryption: item.mobileEncryption
                        }));
                      }
                      return [];
                    }}
                  />
                </Form.Item>
                <Form.Item name="userName" label="用户名" rules={[{ required: true }]}>
                  <Input placeholder="请输入" disabled={handleType === "edit"}></Input>
                </Form.Item>
                <Form.Item name="phone" label="手机号" rules={[{ required: true }]}>
                  <Input placeholder="请输入" {...phoneOtherProps}></Input>
                </Form.Item>

                <Form.Item name="TJroleIds" label={returnEllipsisTooltip({ title: "角色" })}>
                  <Select
                    allowClear
                    style={{ width: "100%", maxHeight: "60px", overflowY: "scroll" }}
                    placeholder="请选择"
                    mode="multiple"
                    filterOption={(value: string, options) => {
                      const { children } = options;
                      return children?.indexOf(value) > -1;
                    }}
                  >
                    {roleList.map(item => {
                      const { name, id } = item;
                      return (
                        <Option key={id} value={id}>
                          {name}
                        </Option>
                      );
                    })}
                  </Select>
                </Form.Item>

                <Form.Item label={returnEllipsisTooltip({ title: "在职状态" })} style={{ marginBottom: 0 }}>
                  <Space style={{ display: "flex", alignItems: "flex-start" }}>
                    <Form.Item key="staffStatus" name="staffStatus" rules={[{ required: true, message: "请选择在职状态" }]}>
                      <Select allowClear style={{ width: "150px" }} placeholder="请选择">
                        {jobStatus.map(item => {
                          const { label, value } = item;
                          return (
                            <Option key={value} value={value}>
                              {label}
                            </Option>
                          );
                        })}
                      </Select>
                    </Form.Item>
                    <Form.Item key="tips">
                      <span style={{ color: "red" }}>*选择离职后，账号会自动禁用</span>
                    </Form.Item>
                  </Space>
                </Form.Item>
                <Form.Item name="status" label={returnEllipsisTooltip({ title: "账号状态" })} rules={[{ required: true, message: "请选择账号状态" }]}>
                  <Select allowClear style={{ width: "150px" }} placeholder="请选择">
                    {accountStatus.map(item => {
                      return (
                        <Option key={item.value} value={item.value}>
                          {item.label}
                        </Option>
                      );
                    })}
                  </Select>
                </Form.Item>
                <Form.Item label={returnEllipsisTooltip({ title: "设置密码" })}>
                  <Space style={{ display: "flex", alignItems: "flex-start" }}>
                    <Form.Item
                      name="password"
                      key="password"
                      rules={[
                        {
                          required: currentData?.id ? false : true,
                          message: "请生成密码"
                        }
                      ]}
                    >
                      <Password disabled={true} visibilityToggle={false} defaultValue={newPasswordValue} value={newPasswordValue} />
                    </Form.Item>
                    <Form.Item key="passwordBtn">
                      <Button
                        type="primary"
                        size={SIZE}
                        onClick={() => {
                          const { id } = currentData;
                          api.queryResetPassword({ id: id }).then(res => {
                            const password = res?.entry || "";
                            form.setFieldsValue({
                              password: password
                            });
                            Modal.info({
                              title: "密码重置",
                              centered: true,
                              content: (
                                <div style={{ textAlign: "center" }}>
                                  <p>新密码：{password}</p>
                                  <p>请妥善保存，丢失只能重置</p>
                                </div>
                              )
                            });
                          });
                        }}
                      >
                        重置
                      </Button>
                    </Form.Item>
                  </Space>
                </Form.Item>
              </Form>
            </Tabs.TabPane>
          );
        })}
      </Tabs>
    </Modal>
  );
};
export default React.memo(EditModal);
