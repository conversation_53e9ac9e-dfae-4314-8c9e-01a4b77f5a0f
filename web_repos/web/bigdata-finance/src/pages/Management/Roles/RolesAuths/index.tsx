import React, { useMemo, useEffect, useState, useCallback } from "react";
import { Card, Row, Col, Space, Button, message } from "antd";
import { history, useSearchParams } from "@umijs/max";
import "./index.less";
import { SIZE } from "@/utils/constant";
import { oneLevelColumns, twoLevelColumns, moduleColumns, funHandleColumns } from "@/pages/Management/Roles/variable";
import AuthItem from "@/pages/Management/components/AuthItem";
import AuthTarget from "@/pages/Management/components/AuthTarget";
import apis from "@/services/management/authorize";
import useMainSize from "@/hooks/useMainSize";
// 列表查询
type QueryListInfo = {
  roleId: number | string;
  parentId?: number;
} & { type?: 0 | 1 | 2 | 3 | 4 };
const defaultSelectID = -1;
// TODO 区分平台
const RolesAuths = () => {
  const [searchParams] = useSearchParams();
  const name = searchParams.get("name"); // 'John'
  const id = searchParams.get("id");

  const containerInfo = useMainSize(".ant-layout-content");
  const [currentData, setCurrentData] = useState<any>([]);
  const [currentSelect, setCurrentSelect] = useState<any>([]);
  const [currentSelectID, setCurrentSelectID] = useState(defaultSelectID);
  const [twoPageData, setTwoPageData] = useState([]);
  const [twoPageSelect, setTwoPageSelect] = useState([]);
  const [twoPageSelectID, setTwoPageSelectID] = useState(defaultSelectID);
  const [modalPageData, setModalPageData] = useState([]);
  const [modalPageSelect, setModalPageSelect] = useState([]);
  const [modalPageSelectID, setModalPageSelectID] = useState(defaultSelectID);
  const [sensitivePageData, setSensitivePageData] = useState([]);
  const [sensitivePageSelect, setSensitivePageSelect] = useState([]);

  // 过滤已选择权限，返回对应id
  const fiterSelect = (target: any[]) => {
    return target.filter(item => item.authorizedStatus === 1).map(item => item.id);
  };
  // 角色对资源授权;
  const getAuthorizeResources = useCallback(async params => {
    const response = await apis.queryAuthorizeResources(params);
    const { entry = false } = response || {};
    return entry;
  }, []);
  // 角色对资源取消授权
  const getUnauthorizeResources = useCallback(async params => {
    const response = await apis.queryUnauthorizeResources(params);
    const { entry = false } = response || {};
    return entry;
  }, []);

  const differenceTypeObj = useMemo(() => {
    return {
      del: getUnauthorizeResources,
      add: getAuthorizeResources
    };
  }, [getAuthorizeResources, getUnauthorizeResources]);
  // 角色拥有的资源列表
  const getResourceList = async (params: QueryListInfo) => {
    const { type = 0, ...rest } = params;

    const { status, entry = [] } = await apis.queryRoleAuthorizedResources({ ...rest, platform: 0 });
    if (status) {
      switch (type) {
        case 0:
          setCurrentData(entry);
          setCurrentSelect(fiterSelect(entry));
          break;
        case 1:
          setTwoPageData(entry);
          setTwoPageSelect(fiterSelect(entry));
          break;
        case 2:
          setModalPageData(entry);
          setModalPageSelect(fiterSelect(entry));
          break;
        case 3:
          setSensitivePageData(entry);
          setSensitivePageSelect(entry.filter(item => item.type === 3 && item.authorizedStatus === 1).map(item => item.id));
          break;
        default:
          setCurrentData(entry);
          setCurrentSelect(fiterSelect(entry));
      }
    } else {
      switch (type) {
        case 0:
          setCurrentData([]);
          break;
        case 1:
          setTwoPageData([]);
          break;
        case 2:
          setModalPageData([]);
          break;
        case 3:
          setSensitivePageData([]);
          break;
        default:
          setCurrentData([]);
      }
    }
  };
  useEffect(() => {
    if (id) {
      getResourceList({
        roleId: id
      });
    }
  }, [id]);

  const handleWriteRead = async (checked: boolean, record: any, index: number, type: "read" | "write") => {
    const isWrite = type === "write";
    const fetchApi = differenceTypeObj[checked ? "add" : "del"];
    const { parentId, writePermission } = record;
    // 模块数据
    const { parentId: modalParentId } = modalPageData.filter(item => item.id === parentId)?.[0] || {};
    // 二级页面数据
    const { id: twoPageId, parentId: twoPageParentId } = twoPageData.filter(item => item.id === modalParentId)?.[0] || {};
    let params = {};
    const selfPermission: any = {
      type: 4,
      resourceId: record.id
    };
    // 根据 是可读还是可写权限 赋值
    if (isWrite) {
      if (checked) {
        selfPermission.writePermission = 1;
        selfPermission.readPermission = 1;
      } else {
        selfPermission.writePermission = 0;
        selfPermission.readPermission = 1;
      }
    } else {
      if (checked) {
        selfPermission.readPermission = 1;
        selfPermission.writePermission = writePermission;
      } else {
        selfPermission.readPermission = 0;
        selfPermission.writePermission = 0;
      }
    }
    if (checked) {
      params = {
        resources: [
          // 添加自身的权限
          { ...selfPermission },
          // 将模块 权限也同时加上
          {
            type: 2,
            resourceId: parentId
          },
          // 将二级页面 权限也同时加上
          {
            type: 1,
            resourceId: twoPageId
          },
          // 将一级页面权限加上
          {
            type: 0,
            resourceId: twoPageParentId
          }
        ],
        roleId: id
      };
    } else {
      params = { resources: [selfPermission], roleId: id };
    }
    const re = await fetchApi(params);
    if (re) {
      // 设置父级的权限  不再请求接口
      setCurrentSelect([...new Set([...currentSelect, twoPageParentId])]);
      setTwoPageSelect([...new Set([...twoPageSelect, twoPageId])]);
      setModalPageSelect([...new Set([...modalPageSelect, parentId])]);
      getResourceList({
        roleId: id,
        parentId: record.parentId,
        type: 3
      });
    }
  };

  const handleAuth = async (checked, record) => {
    const fetchApi = differenceTypeObj[checked ? "add" : "del"];
    const { parentId } = record;
    // 模块数据
    const { parentId: modalParentId } = modalPageData.filter(item => item.id === parentId)?.[0] || {};
    // 二级页面数据
    const { id: twoPageId, parentId: twoPageParentId } = twoPageData.filter(item => item.id === modalParentId)?.[0] || {};
    let params = {};
    if (checked) {
      params = {
        resources: [
          // 添加自身的权限
          {
            type: 3,
            resourceId: record.id
          },
          // 将模块 权限也同时加上
          {
            type: 2,
            resourceId: parentId
          },
          // 将二级页面 权限也同时加上
          {
            type: 1,
            resourceId: twoPageId
          },
          // 将一级页面权限加上
          {
            type: 0,
            resourceId: twoPageParentId
          }
        ],
        roleId: id
      };
    } else {
      params = {
        resources: [
          {
            resourceId: record.id,
            type: 3
          }
        ],

        roleId: id
      };
    }
    const re = await fetchApi(params);
    if (re) {
      // 设置父级的权限  不再请求接口
      setCurrentSelect([...new Set([...currentSelect, twoPageParentId])]);
      setTwoPageSelect([...new Set([...twoPageSelect, twoPageId])]);
      setModalPageSelect([...new Set([...modalPageSelect, parentId])]);
      getResourceList({
        roleId: id,
        parentId: record.parentId,
        type: 3
      });
      // sensitivePageData[index].authorizedStatus = +checked || 0;
      // setSensitivePageData([...sensitivePageData]);
    }
  };
  const returnTitle = useMemo(() => {
    return (
      <div>
        <Space>
          <span>{`${name}-资源分配`}</span>
          <Space>
            <Button size={SIZE} onClick={() => history.back()}>
              返回
            </Button>
          </Space>
        </Space>
      </div>
    );
  }, [name]);
  // 两个数组的 差值
  const differenceValue = useCallback((target: number[], vals: number[]): number[] => {
    return target.filter(item => !vals.includes(item));
  }, []);
  // 返回 改变的值
  const returnChangedValue = (originArr: number[], newArr: number[]): { type: string; diff: number[] } => {
    if (originArr.length > newArr.length) {
      return {
        type: "add",
        diff: differenceValue(originArr, newArr)
      };
    } else {
      return {
        type: "del",
        diff: differenceValue(newArr, originArr)
      };
    }
  };

  // 控制指标的 全部可读、全部可写
  const handleAllReadWrite = async (type: "read" | "write") => {
    const fetchApi = differenceTypeObj["add"];
    const targets = sensitivePageData.filter(item => item.type === 4);
    if (targets.length === 0) return message.warning("只能控制指标权限");

    const { parentId } = sensitivePageData?.[0] || {};
    // 模块数据
    const { parentId: modalParentId } = modalPageData.filter(item => item.id === parentId)?.[0] || {};
    // 二级页面数据
    const { id: twoPageId, parentId: twoPageParentId } = twoPageData.filter(item => item.id === modalParentId)?.[0] || {};
    let params = {};
    let permissions = targets.map(item => {
      const temp = {};
      if (type === "read") {
        temp.readPermission = 1;
        temp.writePermission = 0;
      } else if (type === "write") {
        temp.readPermission = 1;
        temp.writePermission = 1;
      }
      return {
        type: 4,
        resourceId: item.id,
        ...temp
      };
    });
    params = {
      resources: [
        // 添加所有指标权限
        ...permissions,
        // 将模块 权限也同时加上
        {
          type: 2,
          resourceId: parentId
        },
        // 将二级页面 权限也同时加上
        {
          type: 1,
          resourceId: twoPageId
        },
        // 将一级页面权限加上
        {
          type: 0,
          resourceId: twoPageParentId
        }
      ],
      roleId: id
    };
    const re = await fetchApi(params);
    if (re) {
      // 设置父级的权限  不再请求接口
      setCurrentSelect([...new Set([...currentSelect, twoPageParentId])]);
      setTwoPageSelect([...new Set([...twoPageSelect, twoPageId])]);
      setModalPageSelect([...new Set([...modalPageSelect, parentId])]);
      // 重新请求 功能/敏感字段 数据
      getResourceList({
        roleId: id,
        parentId: parentId,
        type: 3
      });
    }
  };
  const returnSensitiveExtra = () => {
    return (
      <Space>
        <Button size={SIZE} type="primary" onClick={() => handleAllReadWrite("read")}>
          全部可读
        </Button>
        <Button size={SIZE} type="primary" onClick={() => handleAllReadWrite("write")}>
          全部可写
        </Button>
      </Space>
    );
  };
  return (
    <Card title={returnTitle}>
      <Row wrap={true}>
        <Col md={{ span: 12 }} lg={{ span: 6 }}>
          <AuthItem
            height={containerInfo.height - 140}
            title="一级页面"
            selectedRowKeys={currentSelect}
            onItemClick={async record => {
              setCurrentSelectID(record.id);
              setTwoPageData([]);
              setModalPageData([]);
              setSensitivePageData([]);
              getResourceList({
                roleId: id,
                parentId: record.id,
                type: 1
              });
            }}
            onSelectChange={async (selectedRowKeys: number[]) => {
              const { type, diff } = returnChangedValue(selectedRowKeys, currentSelect);
              const fetchApi = differenceTypeObj[type];
              let params = {
                roleId: id,
                resources: diff.map(item => {
                  return { type: 0, resourceId: item };
                })
              };
              const re = await fetchApi(params);
              if (re) {
                setCurrentSelect(selectedRowKeys);
                if (currentSelectID === diff[0]) {
                  getResourceList({
                    roleId: id,
                    parentId: diff[0],
                    type: 1
                  });
                }

                setModalPageData([]);
                setSensitivePageData([]);
              }
            }}
            columns={oneLevelColumns({})}
            data={currentData}
          />
        </Col>
        <Col md={{ span: 12 }} lg={{ span: 6 }}>
          <AuthItem
            height={containerInfo.height - 140}
            title="二级页面"
            onSelectChange={async (selectedRowKeys: number[], selectedRows: any[]) => {
              const { type, diff } = returnChangedValue(selectedRowKeys, twoPageSelect);
              const fetchApi = differenceTypeObj[type];
              let params = { roleId: id, resources: [] };
              const { parentId } = selectedRows[0] || {};

              params.resources = diff.map(item => {
                return { type: 1, resourceId: item };
              });
              if (type === "add") {
                params.resources = [
                  ...params.resources,
                  {
                    type: 0,
                    resourceId: parentId
                  }
                ];
              }
              const re = await fetchApi(params);
              if (re) {
                setTwoPageSelect(selectedRowKeys);
                // 设置父级的权限  不再请求接口
                if (type === "add") {
                  setCurrentSelect([...new Set([...currentSelect, parentId])]);
                }
                if (diff[0] === twoPageSelectID) {
                  // 重新获取下级 数据
                  getResourceList({
                    roleId: id,
                    parentId: diff[0],
                    type: 2
                  });
                }
                setSensitivePageData([]);
              }
            }}
            selectedRowKeys={twoPageSelect}
            onItemClick={record => {
              setTwoPageSelectID(record.id);
              setModalPageData([]);
              setSensitivePageData([]);
              getResourceList({
                roleId: id,
                parentId: record.id,
                type: 2
              });
            }}
            columns={twoLevelColumns({})}
            data={twoPageData}
          />
        </Col>
        <Col md={{ span: 12 }} lg={{ span: 6 }}>
          <AuthItem
            height={containerInfo.height - 140}
            title="模块"
            onSelectChange={async (selectedRowKeys: number[], selectedRows: any[]) => {
              const { type, diff } = returnChangedValue(selectedRowKeys, modalPageSelect);
              const fetchApi = differenceTypeObj[type];
              // 当前选择行数据
              // const curRow =
              //   selectedRows.filter(item => item.id === diff)?.[0] || {};
              const { parentId } = selectedRows[0] || {};
              // 父级 行数据
              const parentRow = twoPageData.filter(item => item.id === parentId)?.[0] || {};
              const { parentId: parentParentId } = parentRow;
              let params = {
                roleId: id,
                resources: []
              };
              params.resources = diff.map(item => {
                return {
                  resourceId: item,
                  type: 2
                };
              });
              if (type === "add") {
                params.resources = [
                  // 自身权限
                  ...params.resources,
                  // 父级权限
                  {
                    type: 1,
                    resourceId: parentId
                  },
                  // 父级的父级权限
                  {
                    type: 0,
                    resourceId: parentParentId
                  }
                ];
              }
              const re = await fetchApi(params);
              if (re) {
                setModalPageSelect(selectedRowKeys);
                if (diff[0] === modalPageSelectID) {
                  // 重新获取下级 数据
                  getResourceList({
                    roleId: id,
                    parentId: diff[0],
                    type: 3
                  });
                }
                if (type === "add") {
                  // 设置父级的权限  不再请求接口
                  setTwoPageSelect([...new Set([...twoPageSelect, parentId])]);
                  setCurrentSelect([...new Set([...currentSelect, parentParentId])]);
                }
              }
            }}
            selectedRowKeys={modalPageSelect}
            onItemClick={record => {
              setModalPageSelectID(record.id);
              setSensitivePageData([]);
              getResourceList({
                roleId: id,
                parentId: record.id,
                type: 3
              });
            }}
            columns={moduleColumns({})}
            data={modalPageData}
          />
        </Col>
        <Col md={{ span: 12 }} lg={{ span: 6 }}>
          <AuthTarget
            height={containerInfo.height - 140}
            title="功能/敏感模块"
            columns={funHandleColumns({
              handleWriteRead,
              handleAuth
            })}
            selectedRowKeys={sensitivePageSelect}
            data={sensitivePageData}
            extra={returnSensitiveExtra()}
            onSelectChange={async (selectedRowKeys: number[], selectedRows: any[]) => {
              const { type, diff } = returnChangedValue(selectedRowKeys, sensitivePageSelect);
              const fetchApi = differenceTypeObj[type];
              const { parentId } = selectedRows[0] || {};
              // 模块数据
              const { parentId: modalParentId } = modalPageData.filter(item => item.id === parentId)?.[0] || {};
              // 二级页面数据
              const { id: twoPageId, parentId: twoPageParentId } = twoPageData.filter(item => item.id === modalParentId)?.[0] || {};
              let params = {
                roleId: id,
                resources: []
              };
              params.resources = diff.map(item => {
                return {
                  resourceId: item,
                  type: 3
                };
              });
              if (type === "add") {
                params.resources = [
                  // 自身权限
                  ...params.resources,
                  // 父级权限
                  // 将模块 权限也同时加上
                  {
                    type: 2,
                    resourceId: parentId
                  },
                  // 将二级页面 权限也同时加上
                  {
                    type: 1,
                    resourceId: twoPageId
                  },
                  // 将一级页面权限加上
                  {
                    type: 0,
                    resourceId: twoPageParentId
                  }
                ];
              }
              const re = await fetchApi(params);
              if (re) {
                if (type === "add") {
                  getResourceList({
                    roleId: id,
                    parentId: parentId,
                    type: 3
                  });
                  // 设置父级的权限  不再请求接口
                  setModalPageSelect([...new Set([...modalPageSelect, parentId])]);
                  setTwoPageSelect([...new Set([...twoPageSelect, twoPageId])]);
                  setCurrentSelect([...new Set([...currentSelect, twoPageParentId])]);
                } else if (type === "del") {
                  getResourceList({
                    roleId: id,
                    parentId: modalPageSelectID,
                    type: 3
                  });
                }
              }
            }}
          />
        </Col>
      </Row>
    </Card>
  );
};
export default React.memo(RolesAuths);
