import React from "react";
import { Transfer, Table } from "antd";
import { difference } from "lodash-es";
import { SIZE } from "@/utils/constant";
const TableTransfer = ({ leftColumns, rightColumns, ...restProps }) => (
  <Transfer {...restProps}>
    {({ direction, filteredItems, onItemSelectAll, onItemSelect, selectedKeys: listSelectedKeys, disabled: listDisabled }) => {
      const columns = direction === "left" ? leftColumns : rightColumns;
      const rowSelection = {
        // getCheckboxProps: item => ({ disabled: listDisabled || item.disabled }),
        onSelectAll(selected, selectedRows) {
          const treeSelectedKeys = selectedRows.map(({ key }) => key);
          const diffKeys = selected ? difference(treeSelectedKeys, listSelectedKeys) : difference(listSelectedKeys, treeSelectedKeys);
          onItemSelectAll(diffKeys, selected);
        },
        onSelect({ userId }, selected) {
          onItemSelect(userId, selected);
        },
        selectedRowKeys: listSelectedKeys
      };
      return (
        <Table
          scroll={{ y: restProps.containerHeight - 380 }}
          rowSelection={rowSelection}
          columns={columns}
          dataSource={filteredItems}
          size={SIZE}
          rowKey="userId"
          pagination={{
            defaultPageSize: 20
          }}
          style={{ pointerEvents: listDisabled ? "none" : null }}
        />
      );
    }}
  </Transfer>
);

export default React.memo(TableTransfer);
