import React, { useState, useEffect, useCallback, useMemo } from "react";
import { history, useSearchParams } from "@umijs/max";
import { Card, Button, Space } from "antd";
import TableTransfer from "./TransferTable";
import mainHeight from "@/hooks/useMainSize";
import { debounce } from "lodash-es";
import { SIZE } from "@/utils/constant";
import apis from "@/services/management/roles";

const tableColumns = [
  {
    dataIndex: "displayName",
    title: "名称",
    width: 100
  },
  {
    dataIndex: "phone",
    title: "手机号",
    width: 200
  },
  {
    dataIndex: "userName",
    title: "登录用户名",
    width: 200
  }
];

const Rolesdistribution = () => {
  const [searchParams] = useSearchParams();
  const name = searchParams.get("name"); // 'John'
  const id = searchParams.get("id");

  const containerInfo = mainHeight(".ant-layout-content");
  const [userLists, setUserLists] = useState([]);

  // 获取带有角色授权信息的用户列表
  const getList = useCallback(async params => {
    const { status, entry } = await apis.queryRoleAuthorizedUsers(params);
    if (status) {
      setUserLists(entry);
    } else {
      setUserLists([]);
    }
  }, []);

  // 对用户授权;
  const fetchAuthorize = useCallback(async params => {
    await apis.bodyAuthorizeUsers(params);
  }, []);
  // 对用户取消授权;
  const fetchUnauthorize = useCallback(async params => {
    await apis.bodyUnauthorizeUsers(params);
  }, []);

  const handleChange = useCallback(
    async (_, direction, moveKeys) => {
      if (direction === "right") {
        await fetchAuthorize({
          roleId: id,
          userIds: moveKeys
        });
      } else {
        await fetchUnauthorize({
          roleId: id,
          userIds: moveKeys
        });
      }
      getList({ roleId: id });
    },
    [id]
  );

  const handleFilter = useCallback((inputValue, item) => {
    return item.displayName.indexOf(inputValue) !== -1 || item.phone.indexOf(inputValue) !== -1 || item.userName.indexOf(inputValue) !== -1;
  }, []);
  const originTargetKeys = useMemo(() => {
    return userLists.filter(item => item.roleAuthorized === 1).map(item => item.userId);
  }, [userLists]);
  useEffect(() => {
    getList({ roleId: id });
  }, [id]);
  const returnTitle = useMemo(() => {
    return (
      <div>
        <Space direction="vertical">
          <span>{`${name} - 角色分配`}</span>
          <Space>
            <Button size={SIZE} onClick={() => history.back()}>
              返回
            </Button>
            <Button
              size={SIZE}
              onClick={debounce(
                () =>
                  getList({
                    roleId: id
                  }),
                500
              )}
            >
              刷新
            </Button>
          </Space>
        </Space>
      </div>
    );
  }, [id]);
  return (
    <div>
      <Card styles={{ body: { minHeight: `${containerInfo.height - 100}px` } }} title={returnTitle}>
        <TableTransfer
          containerHeight={containerInfo.height}
          titles={["未授权用户", "已授权用户"]}
          dataSource={userLists}
          targetKeys={originTargetKeys}
          showSearch={true}
          rowKey={record => record.userId}
          onChange={handleChange}
          listStyle={{
            width: 500
          }}
          filterOption={handleFilter}
          leftColumns={tableColumns}
          rightColumns={tableColumns}
        />
      </Card>
    </div>
  );
};
export default React.memo(Rolesdistribution);
