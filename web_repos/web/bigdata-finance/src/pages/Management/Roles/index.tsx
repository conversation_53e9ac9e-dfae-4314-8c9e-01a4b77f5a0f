import { useState, useRef, useCallback, useMemo, useEffect } from "react";
import { ActionType, ProTable } from "@ant-design/pro-components";
import { history } from "@umijs/max";
import { NOVALUE, Protable_FROM_CONFIG } from "@/utils/constant";
import { Button } from "antd";
import { columns } from "./variable";
import { paginationOptions } from "@/utils";
import RolesEditModal from "./RolesEditModal";
import api from "@/services/management/roles";
import KeepAlive from "react-activation";
import UserResource from "@/pages/Management/components/UserRowAuth";
import { SIZE } from "@/utils/constant";
import queryString from "query-string";
const pageUrl = "/management/roles";
const Roles = () => {
  const defaultStatus = 1;
  const formRef = useRef<any>();
  const actionRef = useRef<ActionType>();
  const [handleType, setHandleType] = useState<string>("");
  const [showModal, setShowModal] = useState<boolean>(false);
  const [showRowModal, setshowRowModal] = useState<boolean>(false);
  const [currentData, setCurrentData] = useState<any>({});

  const [pageParams] = useState<any>({
    status: defaultStatus
  });
  useEffect(() => {
    formRef?.current.setFieldsValue({ status: defaultStatus });
  }, [formRef]);
  const handleRolesEdit = useCallback(record => {
    setHandleType("edit");
    setShowModal(true);
    setCurrentData(record);
  }, []);

  const handleRoleRepeat = useCallback(record => {
    setHandleType("repeat");
    setShowModal(true);
    setCurrentData(record);
  }, []);

  const handleRolesUsers = useCallback(record => {
    history.push({
      pathname: "/management/roles/users",
      search: queryString.stringify({
        id: record.id,
        name: record.name
      })
    });
  }, []);

  const handleRolesAuth = useCallback(record => {
    history.push({
      pathname: "/management/roles/auths",
      search: queryString.stringify({
        id: record.id,
        name: record.name
      })
    });
  }, []);

  const handleAdd = useCallback(() => {
    setShowModal(true);
    setCurrentData({});
    setHandleType("add");
  }, []);

  const handleRolesRowAuth = useCallback(record => {
    setshowRowModal(true);
    setCurrentData(record);
  }, []);

  const columnsProps = useMemo(() => {
    return {
      handleRolesEdit,
      handleRoleRepeat,
      handleRolesAuth,
      handleRolesUsers,
      handleRolesRowAuth,
      pageParams
    };
  }, [pageParams]);

  const rolesEditModalProps = useMemo(() => {
    return {
      visible: showModal,
      handleType,
      currentData,
      close: () => {
        setShowModal(false);
        actionRef?.current?.reload();
      }
    };
  }, [showModal, handleType, currentData, actionRef]);

  const rowPermissionModalProps = useMemo(() => {
    return {
      visible: showRowModal,
      currentData,
      fresh: () => {
        actionRef?.current?.reload();
      },
      close: () => {
        setshowRowModal(false);
      },
      handleType: "roles"
    };
  }, [showRowModal, currentData, actionRef]);

  return (
    <>
      <ProTable
        actionRef={actionRef}
        formRef={formRef}
        columnEmptyText={NOVALUE}
        columns={columns(columnsProps)}
        scroll={{ x: "max-content", y: "calc(100vh - 290px)" }}
        params={pageParams}
        onReset={() => {
          formRef?.current.setFieldsValue({ status: defaultStatus });
        }}
        request={async p => {
          let params = formRef.current?.getFieldsValue();
          const { current, pageSize } = p;
          const {
            entry = [],
            status,
            totalRecordSize = 0
          } = await api.queryRoleList({
            pageNo: current,
            pageSize,
            ...params
          });
          if (status) {
            return {
              data: entry,
              success: true,
              total: totalRecordSize
            };
          }
          return {
            data: [],
            success: false,
            total: 0
          };
        }}
        headerTitle={
          <Button type="primary" size={SIZE} onClick={() => handleAdd()}>
            新增角色
          </Button>
        }
        form={Protable_FROM_CONFIG}
        options={false}
        pagination={{
          showQuickJumper: true,
          ...paginationOptions,
          defaultPageSize: 50
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false
        }}
      />
      {showModal && <RolesEditModal {...rolesEditModalProps} />}
      {showRowModal && <UserResource {...rowPermissionModalProps} />}
    </>
  );
};
const AliveRecord = () => (
  <KeepAlive name={pageUrl}>
    <Roles />
  </KeepAlive>
);
export default AliveRecord;
