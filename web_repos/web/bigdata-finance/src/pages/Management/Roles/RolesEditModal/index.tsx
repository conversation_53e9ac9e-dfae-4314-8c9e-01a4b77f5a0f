// 批量导入商品
import React, { useCallback, useMemo } from "react";
import { Modal, Form, Input, Select } from "antd";
import { accountStatus } from "@/pages/Management/variable";
import api from "@/services/management/roles";
import { SIZE, FormLayout } from "@/utils/constant";
import { returnEllipsisTooltip } from "@/utils";
const handleTypeValues = {
  edit: "编辑",
  add: "添加",
  repeat: "复制"
};
const RolesEditModal = (props: any) => {
  const { visible, close, handleType, currentData } = props;
  const [form] = Form.useForm();

  const handleAddCancel = () => {
    form.resetFields();
    close();
  };

  const handleAddOk = () => {
    form.validateFields().then(async values => {
      if (handleType === "add") {
        const { status } = await api.bodyRoleAdd({ ...values });
        if (status) {
          close?.();
        }
      } else if (handleType === "repeat") {
        const { status } = await api.bodyCopyRole({
          roleId: currentData.id,
          ...values
        });
        if (status) {
          close?.();
        }
      } else {
        const { status } = await api.bodyRoleEdit({
          id: currentData.id,
          ...values
        });
        if (status) {
          close?.();
        }
      }
    });
  };
  const returnModalTitle = useMemo(() => {
    return handleTypeValues[handleType] + "角色";
  }, [handleType]);
  const judgeUnique = useCallback(async params => {
    const response = await api.queryRoleNameExists(params);
    const { entry } = response;
    if (entry) {
      return Promise.reject("角色名称重复");
    }
  }, []);
  return (
    <Modal
      title={returnModalTitle}
      open={visible}
      onOk={handleAddOk}
      onCancel={handleAddCancel}
      maskClosable={false}
      okText="保存"
      okButtonProps={{
        size: SIZE
      }}
      cancelButtonProps={{
        size: SIZE
      }}
      centered={true}
    >
      <Form form={form} initialValues={{ oldname: currentData.name, status: 1, ...currentData }} size={SIZE} {...FormLayout}>
        {handleType === "repeat" ? (
          <>
            <Form.Item name="oldname" label="原角色名称">
              <Input value={currentData.name} disabled={true} />
            </Form.Item>

            <Form.Item
              name="name"
              label="新角色名称"
              rules={[
                {
                  required: true,
                  validator: async (_, value: any) => {
                    if (handleType === "add") {
                      await judgeUnique({
                        name: value
                      });
                    } else {
                      if (value !== currentData.name) {
                        await judgeUnique({
                          name: value
                        });
                      }
                    }
                    return Promise.resolve();
                  }
                }
              ]}
            >
              <Input placeholder="请输入" />
            </Form.Item>
          </>
        ) : (
          <>
            <Form.Item
              name="name"
              label={returnEllipsisTooltip({ title: "角色" })}
              rules={[
                {
                  required: true,
                  validator: async (_, value: any) => {
                    if (handleType === "add") {
                      await judgeUnique({
                        name: value
                      });
                    } else {
                      if (value !== currentData.name) {
                        await judgeUnique({
                          name: value
                        });
                      }
                    }
                    return Promise.resolve();
                  }
                }
              ]}
            >
              <Input placeholder="请输入" />
            </Form.Item>
          </>
        )}

        <Form.Item name="desc" label={returnEllipsisTooltip({ title: "描述" })}>
          <Input.TextArea placeholder="请输入" />
        </Form.Item>

        <Form.Item name="status" label={returnEllipsisTooltip({ title: "状态" })}>
          <Select style={{ width: "200px" }} placeholder="请选择">
            {accountStatus.map(item => {
              const { label, value } = item;
              return (
                <Select.Option key={value} value={value}>
                  {label}
                </Select.Option>
              );
            })}
          </Select>
        </Form.Item>
      </Form>
    </Modal>
  );
};
export default React.memo(RolesEditModal);
