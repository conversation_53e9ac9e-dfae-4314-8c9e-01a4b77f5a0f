import { Space, Tag, Switch, Select } from "antd";
import { accountStatus, accountStatusObj, sensitiveObj } from "@/pages/Management/variable";
import { SIZE, AllValue } from "@/utils/constant";
import { returnEllipsisTooltip } from "@/utils";
const columns_search = pageParams => {
  return [
    {
      title: "状态",
      dataIndex: "status",
      hideInTable: true,
      renderFormItem: () => {
        return (
          <Select defaultValue={pageParams?.status} style={{ width: "100%" }} placeholder="请选择">
            {[AllValue, ...accountStatus].map(item => {
              const { label, value } = item;
              return (
                <Select.Option key={value} value={value}>
                  {label}
                </Select.Option>
              );
            })}
          </Select>
        );
      }
    },
    {
      title: "角色",
      dataIndex: "name",
      hideInTable: true
    }
  ];
};
export const columns = ({
  handleRolesEdit,
  handleRoleRepeat,
  handleRolesAuth,
  handleRolesUsers,
  handleRolesRowAuth,
  pageParams
}): Array<TableListItem> => {
  return [
    ...columns_search(pageParams),
    {
      title: "序号",
      dataIndex: "$index",
      valueType: "index",
      width: 60,
      fixed: "left",
      render: (a, b, index) => {
        return index + 1;
      }
    },
    // 搜索
    {
      title: "角色",
      dataIndex: "name",
      hideInSearch: true,
      width: 150,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "描述",
      dataIndex: "desc",
      hideInSearch: true,
      width: 150,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "状态",
      dataIndex: "status",
      hideInSearch: true,
      width: 150,
      render: (_, record) => {
        const { status = 0 } = record;
        return <Tag color={status ? "success" : "error"}>{accountStatusObj[status]}</Tag>;
      }
    },
    {
      title: "创建人",
      dataIndex: "creator",
      hideInSearch: true,
      width: 150
    },
    {
      title: "创建时间",
      dataIndex: "gmtCreate",
      hideInSearch: true,
      width: 150
    },
    {
      title: "操作",
      dataIndex: "operation",
      width: 280,
      fixed: "right",
      hideInSearch: true,
      render: (_?: any, record?: any) => {
        return (
          <Space>
            <a onClick={() => handleRolesEdit(record)}>编辑</a>
            <a onClick={() => handleRolesAuth(record)}>权限配置</a>
            <a onClick={() => handleRolesRowAuth(record)}>行权限配置</a>
            <a onClick={() => handleRolesUsers(record)}>用户配置</a>
            <a onClick={() => handleRoleRepeat(record)}>复制角色</a>
          </Space>
        );
      }
    }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      align: "center",
      ...item,
      title: returnEllipsisTooltip({ title })
    };
  });
};
// 一级页面表头
export const oneLevelColumns: TableListItem = ({}: any) => {
  return [
    {
      title: "名称",
      dataIndex: "name",
      hideInSearch: true
    }
  ];
};
// 二级页面表头
export const twoLevelColumns: TableListItem = ({}: any) => {
  return [
    {
      title: "名称",
      dataIndex: "name",
      hideInSearch: true
    }
  ];
};
// 模块表头
export const moduleColumns: TableListItem = ({}: any) => {
  return [
    {
      title: "名称",
      dataIndex: "name",
      hideInSearch: true
    }
  ];
};
// 功能/敏感字段表头
export const funColumns: TableListItem = ({}: any) => {
  return [
    {
      title: "名称",
      dataIndex: "name",
      hideInSearch: true
    }
  ];
};

// 功能/敏感字段表头 操作
export const funHandleColumns: TableListItem = ({ handleWriteRead, handleAuth }: any) => {
  return [
    {
      title: "名称",
      dataIndex: "name",
      hideInSearch: true,
      ellipsis: true,
      width: 80
    },
    {
      title: "类型",
      dataIndex: "type",
      hideInSearch: true,
      width: 60,
      render: (_, record) => {
        const { type } = record;
        return sensitiveObj[type] || "";
      }
    },
    {
      title: "操作",
      dataIndex: "operation",
      width: 150,
      fixed: "right",
      hideInSearch: true,
      render: (_?: any, record?: any, index?: number) => {
        const { type, readPermission, writePermission, authorizedStatus, status } = record;
        return (
          <Space>
            {type === 4 ? (
              <>
                <Switch
                  disabled={!(status === 1)}
                  size={SIZE}
                  checkedChildren="可读"
                  unCheckedChildren="不可读"
                  checked={readPermission ?? 0}
                  onChange={checked => handleWriteRead(checked, record, index, "read")}
                />
                <Switch
                  disabled={!(status === 1)}
                  size={SIZE}
                  checkedChildren="可写"
                  unCheckedChildren="不可写"
                  checked={writePermission ?? 0}
                  onChange={checked => handleWriteRead(checked, record, index, "write")}
                />
              </>
            ) : (
              <Switch
                disabled={!(status === 1)}
                size={SIZE}
                checkedChildren="有权限"
                unCheckedChildren="无权限"
                checked={authorizedStatus ?? 0}
                onChange={checked => handleAuth(checked, record, index)}
              />
            )}
          </Space>
        );
      }
    }
  ];
};
