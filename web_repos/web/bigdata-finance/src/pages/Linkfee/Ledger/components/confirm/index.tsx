import React, { useEffect, useState } from "react";
import { Modal, Form, Alert } from "antd";
import { NOVALUE, SIZE } from "@/utils/constant";
import TableList from "@/components/TableList";
import { confirm_columns } from "./variable";
import apis from "@/services/linkfee/ledger";

interface IProps {
  // 显示隐藏 modal
  visible: boolean;
  // 关闭modal
  close: () => void;
  // 关闭 modal后 刷新页面
  fresh?: () => void;
  settleMonth: string;
  // 标题
  title: React.ReactNode;
}
const Res = (props: IProps) => {
  const { visible, close, fresh, title, settleMonth = "" } = props;
  const [form] = Form.useForm();
  const [okDisable, setokDisable] = useState(false);
  const [pendingConfirmList, setpendingConfirmList] = useState([]);
  const fetchList = async () => {
    const { entry, status } = await apis.getObtainSettleMonthConfirmData({ entryMonth: settleMonth });
    if (status) {
      setpendingConfirmList(entry);
    } else {
      setpendingConfirmList([]);
    }
  };
  useEffect(() => {
    fetchList();
  }, [settleMonth]);
  const handleCancel = () => {
    form.resetFields();
    close?.();
  };
  const batchErrorInfo = (info: string) => {
    Modal.confirm({
      title: "错误信息",
      content: info,
      centered: true,
      onOk: async () => {
        close?.();
        fresh?.();
      },
      onCancel: () => {
        close?.();
        fresh?.();
      }
    });
  };

  const handleOk = () => {
    form.validateFields().then(async () => {
      setokDisable(true);
      const { status, entry } = await apis.doConfirmSettleMonthData({ financeLiveItemLinkfeeSettleMonthDataVO: pendingConfirmList });
      if (status) {
        close?.();
        fresh?.();
      } else {
        batchErrorInfo(entry);
      }
      setokDisable(false);
    });
  };
  return (
    <Modal
      centered={true}
      title={title}
      open={visible}
      onOk={() => handleOk()}
      onCancel={() => handleCancel()}
      maskClosable={false}
      okButtonProps={{
        size: SIZE,
        loading: okDisable
      }}
      cancelButtonProps={{
        size: SIZE
      }}
    >
      <Form size={SIZE} form={form}>
        <Form.Item style={{ color: "red" }}>
          <Alert message="确认后数据不可取消，请核对无误后再点击确认。如需取消确认请联系系统管理员" type="info" showIcon></Alert>
        </Form.Item>
        <Form.Item>
          <TableList
            size={SIZE}
            columns={confirm_columns({})}
            columnEmptyText={NOVALUE}
            scroll={{ x: "max-content", y: "350px" }}
            dataSource={pendingConfirmList}
            options={false}
            pagination={false}
            rowKey={record => {
              return JSON.stringify(record);
            }}
            search={false}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};
export default React.memo(Res);
