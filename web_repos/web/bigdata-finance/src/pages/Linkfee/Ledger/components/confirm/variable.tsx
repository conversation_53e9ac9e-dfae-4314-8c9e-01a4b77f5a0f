import { returnEllipsisTooltip } from "@/utils";

export const confirm_columns = ({}): Array<TableListItem> => {
  return [
    { title: "结算月份", dataIndex: "entryMonth", width: 100, hideInSearch: true },
    { title: "主播", dataIndex: "anchorName", width: 100, hideInSearch: true },
    { title: "金额", dataIndex: "amountStr", width: 100, hideInSearch: true }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      align: "center",
      ...item,
      title: returnEllipsisTooltip({ title })
    };
  });
};
