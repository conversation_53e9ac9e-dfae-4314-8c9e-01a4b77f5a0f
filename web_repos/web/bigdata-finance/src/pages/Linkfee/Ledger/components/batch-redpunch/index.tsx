import React, { useState } from "react";
import { Modal, Form, message } from "antd";
import { SIZE } from "@/utils/constant";
import apis from "@/services/linkfee/ledger";

interface IProps {
  // 显示隐藏 modal
  visible: boolean;
  // 关闭modal
  close: () => void;
  // 关闭 modal后 刷新页面
  fresh?: () => void;
  selectedRows: any[];
  // 标题
  title: React.ReactNode;
}
const UploadExcelModal = (props: IProps) => {
  const { visible, close, fresh, title, selectedRows = [] } = props;
  const [form] = Form.useForm();
  const [okDisable, setokDisable] = useState(false);

  const handleExcelCancel = () => {
    form.resetFields();
    close?.();
  };

  const handleExcelOk = () => {
    form.validateFields().then(async () => {
      setokDisable(true);
      const { status, entry = "" } = await apis.batchRedPunch({ liveItemLinkfeeRedPunchDOList: selectedRows });
      if (status) {
        close?.();
        fresh?.();
      }
      message.info(entry);
      setokDisable(false);
    });
  };
  return (
    <Modal
      centered={true}
      title={title}
      open={visible}
      onOk={() => handleExcelOk()}
      onCancel={() => handleExcelCancel()}
      maskClosable={false}
      okButtonProps={{
        size: SIZE,
        loading: okDisable
      }}
      cancelButtonProps={{
        size: SIZE
      }}
    >
      <Form size={SIZE} form={form}>
        <Form.Item style={{ color: "red" }}>对是否红冲状态为“链接费更新，建议红冲”的数据记录进行红冲动作</Form.Item>
      </Form>
    </Modal>
  );
};
export default React.memo(UploadExcelModal);
