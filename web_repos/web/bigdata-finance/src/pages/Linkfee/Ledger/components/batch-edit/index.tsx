import React, { useState } from "react";
import { Modal, Form, DatePicker } from "antd";
import apis from "@/services/linkfee/ledger";
import { SIZE } from "@/utils/constant";
import { returnEllipsisTooltip } from "@/utils";
import dayjs from "dayjs";
export type HandleType = "add" | "delete" | void;

interface IProps {
  // 显示隐藏 modal
  visible: boolean;
  // 关闭modal
  close: () => void;
  // 关闭 modal后 刷新页面
  fresh?: () => void;
  selectedRows: any[];
  settleMonth: string;
  // 标题
  title: React.ReactNode;
}
const UploadExcelModal = (props: IProps) => {
  const { visible, close, fresh, title, selectedRows = [], settleMonth } = props;
  const [form] = Form.useForm();
  const [okDisable, setokDisable] = useState(false);

  const handleExcelCancel = () => {
    close?.();
  };

  const handleExcelOk = () => {
    form.validateFields().then(async values => {
      const { entryMonth } = values;
      setokDisable(true);
      const { status } = await apis.batchSaveOrUpdateLiveItemLinkfeeOperate({
        entryMonth: dayjs(entryMonth).format("YYYY-MM"),
        liveItemLinkfeeOperateEditDOList: selectedRows
      });
      if (status) {
        close?.();
        fresh?.();
      }
      setokDisable(false);
    });
  };

  return (
    <Modal
      centered={true}
      title={title}
      open={visible}
      onOk={() => handleExcelOk()}
      onCancel={() => handleExcelCancel()}
      maskClosable={false}
      okButtonProps={{
        size: SIZE,
        loading: okDisable
      }}
      cancelButtonProps={{
        size: SIZE
      }}
    >
      <Form size={SIZE} form={form}>
        <Form.Item label={returnEllipsisTooltip({ title: "结算月份" })} name="entryMonth" rules={[{ required: true, message: "请选择结算月份" }]}>
          <DatePicker picker="month" style={{ width: "80%" }} minDate={dayjs(settleMonth)}></DatePicker>
        </Form.Item>
      </Form>
    </Modal>
  );
};
export default React.memo(UploadExcelModal);
