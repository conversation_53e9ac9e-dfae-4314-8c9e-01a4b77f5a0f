import React, { useState, useRef, useMemo, useEffect } from "react";
import { Button, message } from "antd";
import { TableRowSelection } from "antd/es/table/interface";
import KeepAlive from "react-activation";
import TableList from "@/components/TableList";
import { SIZE } from "@/utils/constant";
import { anchorLink_detail_columns, advance_columns_search } from "./variable";
import apis from "@/services/linkfee/ledger";
import dayjs from "dayjs";
import { pushExportHistory, splitSpaceComma } from "@/utils";
import useCommonOptions from "@/hooks/useCommonOptions";
import AuthButton from "@/wrappers/authButton";
import { btn_auths, model_auths } from "./auths";
import BatchRedpunch from "./components/batch-redpunch";
import BatchEdit from "./components/batch-edit";
import Confirm from "./components/confirm";

export const anchorLink_detail = model_auths.model_linkfee_ledger_anchorlink_detail;
export const PageUrl = "/linkfee/ledger";
const menuKeys = {
  redpunch: "1",
  historyMonthUnconfirm: "2",
  curMonthUnconfirm: "3",
  hasconfirm: "4",
  all: "0"
};
const DetailTable = () => {
  // const authModel = useModelAuth({ pageUrl: PageUrl });
  const [isNeedRedPunchOptions, isNeedRedPunchObj] = useCommonOptions({ dimName: "是否红冲" });
  const [linkfeeTypeOptions, linkfeeTypeObj] = useCommonOptions({ dimName: "链接费类型" });
  const [confirmStatusOptions, confirmStatusObj] = useCommonOptions({ dimName: "确认状态" });
  const anchorLinkformRef = useRef<any>();
  const anchorLinkactionRef = useRef<any>();
  const [anchorlinkPageParams, setanchorlinkPageParams] = useState({});
  const [activeMenu, setActiveMenu] = useState(menuKeys.redpunch);
  const [selectedKeys, setSelectedKeys] = useState([]);
  const [selectedRows, setSelectedRows] = useState([]);
  const [showBatchRedpunch, setshowBatchRedpunch] = useState(false);
  const [showBatchEdit, setshowBatchEdit] = useState(false);
  const [showConfirm, setshowConfirm] = useState(false);
  const [settleMonth, setsettleMonth] = useState("");

  const advanceColumnsProps: any = {
    isNeedRedPunchOptions,
    linkfeeTypeOptions,
    confirmStatusOptions
  };
  const anchorLink_detail_columnsProps = {
    isNeedRedPunchOptions,
    isNeedRedPunchObj,
    linkfeeTypeOptions,
    linkfeeTypeObj,
    confirmStatusOptions,
    confirmStatusObj,
    settleMonth
  };

  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const resetSelectedRows = () => {
    setSelectedKeys([]);
    setSelectedRows([]);
  };

  // 处理请求参数
  const handlePageParmas = (params: any) => {
    resetSelectedRows();
    // 重新请求后，清空当前编辑行
    setEditableRowKeys([]);
    const { liveDate, settleMonth, current, ...rest } = params;
    // 单独处理 直播日期
    if (liveDate) {
      const [liveDateStart, liveDateEnd] = liveDate;
      rest.liveDateStart = dayjs(liveDateStart).format("YYYYMMDD");
      rest.liveDateEnd = dayjs(liveDateEnd).format("YYYYMMDD");
    }
    // 结算日期
    if (settleMonth) {
      const [settleMonthStart, settleMonthEnd] = settleMonth;
      rest.settleMonthStart = dayjs(settleMonthStart).format("YYYY-MM");
      rest.settleMonthEnd = dayjs(settleMonthEnd).format("YYYY-MM");
    }
    rest.itemIdList = splitSpaceComma(rest.itemIdList);
    return { ...rest, pageNo: current };
  };
  // 获取结算月份
  const fetchSettleMonth = async () => {
    const { entry } = await apis.getObtainSettleMonth();
    setsettleMonth(entry ?? "");
  };
  const handleExport = async () => {
    const { status } = await apis.downloadLiveItemLinkfee(anchorlinkPageParams);
    if (status) {
      pushExportHistory();
    }
  };
  const anchorLinkReload = () => {
    anchorLinkactionRef.current.reload();
  };
  useEffect(() => {
    fetchSettleMonth();
  }, []);
  const rowSelection: TableRowSelection<any> = {
    selectedRowKeys: selectedKeys,
    columnWidth: 50,
    onChange(selected, selectedRows) {
      setSelectedKeys(selected);
      setSelectedRows(selectedRows);
    }
  };
  const editable: any = {
    type: "single",
    onSave: async (_, editData) => {
      const { edOkiharaMonth, settleMonth } = editData;
      const params = {
        ...editData,
        edOkiharaMonth,
        settleMonth
      };

      const { status } = await apis.saveOrUpdateLiveItemLinkfeeOperate(params);
      if (status) {
        message.success("修改成功！");
      }
      anchorLinkReload();
    },
    editableKeys,
    onChange: setEditableRowKeys,
    actionRender: (row, config, { save, cancel }) => [save, cancel]
  };
  const batchRedpunchProps = {
    title: "批量红冲",
    visible: showBatchRedpunch,
    selectedRows: selectedRows,
    fresh: () => {
      anchorLinkReload();
    },
    close: () => {
      setshowBatchRedpunch(false);
    }
  };
  const batchEditProps = {
    title: "批量修改",
    visible: showBatchEdit,
    selectedRows: selectedRows,
    settleMonth,
    fresh: () => {
      anchorLinkReload();
    },
    close: () => {
      setshowBatchEdit(false);
    }
  };
  const confirmProps = {
    title: "入账确认",
    visible: showConfirm,
    settleMonth,
    fresh: () => {
      anchorLinkReload();
    },
    close: () => {
      setshowConfirm(false);
    }
  };
  // 返回操作按钮
  const returnActions = () => {
    return [
      <>
        {activeMenu === menuKeys.redpunch ? (
          <AuthButton key="redpunch" pageUrl={PageUrl} code={btn_auths.btn_linkfee_ledger_index_batchredlink}>
            <Button
              type="primary"
              size={SIZE}
              disabled={!selectedRows.length}
              onClick={() => {
                setshowBatchRedpunch(true);
              }}
            >
              批量红冲
            </Button>
          </AuthButton>
        ) : null}
      </>,
      <>
        {activeMenu === menuKeys.historyMonthUnconfirm ? (
          <AuthButton key="batchEdit" pageUrl={PageUrl} code={btn_auths.btn_linkfee_ledger_index_batchEdit}>
            <Button
              type="primary"
              size={SIZE}
              disabled={!selectedRows.length}
              onClick={() => {
                setshowBatchEdit(true);
              }}
            >
              批量修改
            </Button>
          </AuthButton>
        ) : null}
      </>,
      <>
        {activeMenu === menuKeys.curMonthUnconfirm ? (
          <AuthButton key="batchEdit" pageUrl={PageUrl} code={btn_auths.btn_linkfee_ledger_index_confirm}>
            <Button
              type="primary"
              size={SIZE}
              onClick={() => {
                setshowConfirm(true);
              }}
            >
              确认
            </Button>
          </AuthButton>
        ) : null}
      </>,
      <AuthButton key="export" pageUrl={PageUrl} code={btn_auths.btn_linkfee_ledger_index_export}>
        <Button size={SIZE} onClick={() => handleExport()}>
          导出
        </Button>
      </AuthButton>
    ];
  };
  // 返回菜单
  const returnMenuItems = useMemo(() => {
    return [
      {
        key: menuKeys.redpunch,
        label: "建议红冲"
      },
      {
        key: menuKeys.historyMonthUnconfirm,
        label: "历月未确认"
      },
      {
        key: menuKeys.curMonthUnconfirm,
        label: "当月未确认"
      },
      {
        key: menuKeys.hasconfirm,
        label: "已确认"
      },
      {
        key: menuKeys.all,
        label: "全部"
      }
    ];
  }, []);
  return (
    <>
      <TableList
        form={{
          initialValues: {
            settleMonth: [dayjs().subtract(1, "month"), dayjs()]
          }
        }}
        advanceColumns={advance_columns_search(advanceColumnsProps)}
        formRef={anchorLinkformRef}
        actionRef={anchorLinkactionRef}
        columns={anchorLink_detail_columns(anchorLink_detail_columnsProps)}
        api={apis.getLiveItemLinkfeePage}
        summaryApi={apis.getLiveItemLinkfeePageTotal}
        scroll={{ y: "calc(100vh - 370px)" }}
        preFetch={handlePageParmas}
        params={{ subTabType: +activeMenu }}
        paramsChange={setanchorlinkPageParams}
        rowKey="liveItemLinkfeeCompositeKey"
        rowSelection={activeMenu === menuKeys.redpunch || activeMenu === menuKeys.historyMonthUnconfirm ? rowSelection : false}
        toolbar={{
          menu: {
            type: "tab",
            activeKey: activeMenu,
            items: returnMenuItems,
            onChange: key => {
              setActiveMenu(key as string);
            }
          },
          actions: returnActions()
        }}
        editable={editable}
      />
      {batchRedpunchProps.visible ? <BatchRedpunch {...batchRedpunchProps} /> : null}
      {batchEditProps.visible ? <BatchEdit {...batchEditProps} /> : null}
      {confirmProps.visible ? <Confirm {...confirmProps} /> : null}
    </>
  );
};

const AliveRecord = () => (
  <KeepAlive name={PageUrl}>
    <DetailTable />
  </KeepAlive>
);
export default React.memo(AliveRecord);
