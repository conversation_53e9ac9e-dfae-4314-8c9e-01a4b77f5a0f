import { returnEllipsisTooltip, returnItemImgUrl, filterOptionLabel, replaceMiddleStr } from "@/utils";
import { Select, DatePicker, Image, Button, Space } from "antd";
import DepartmentSelect from "@/components/DepartmentSelect";
import AnchorSelect from "@/components/AnchorSelect";
import BrandDebounSelect from "@/components/BrandDebounSelect";
import SupplierNameSelect from "@/components/SupplierNameSelect";
import { NOVALUE, SIZE } from "@/utils/constant";
import AuthButton from "@/wrappers/authButton";
import { btn_auths } from "./auths";
import { PageUrl } from "./index";
import dayjs from "dayjs";
export const advance_columns_search = ({ isNeedRedPunchOptions, linkfeeTypeOptions, confirmStatusOptions }: any): Array<TableListItem> => {
  return [
    {
      title: "基础信息",
      valueType: "group",
      colProps: { span: 24 },
      columns: [
        {
          title: "品牌",
          dataIndex: "brandNameList",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem() {
            return <BrandDebounSelect key="brandName" showSearch allowClear maxTagCount="responsive" mode="multiple" />;
          }
        },
        {
          title: "店铺名称",
          dataIndex: "shopName",
          hideInSearch: false,
          hideInTable: true
        },
        {
          title: "我司主体",
          dataIndex: "companyName",
          hideInSearch: false,
          hideInTable: true
        },

        {
          title: "链接费类型",
          dataIndex: "linkfeeTypeList",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem() {
            return (
              <Select
                placeholder="请选择"
                options={linkfeeTypeOptions}
                filterOption={filterOptionLabel}
                showSearch
                allowClear
                maxTagCount="responsive"
                mode="multiple"
              />
            );
          }
        },
        {
          title: "是否红冲",
          dataIndex: "isNeedRedPunchList",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem() {
            return (
              <Select
                placeholder="请选择"
                options={isNeedRedPunchOptions}
                filterOption={filterOptionLabel}
                showSearch
                allowClear
                maxTagCount="responsive"
                mode="multiple"
              />
            );
          }
        },
        {
          title: "是否确认",
          dataIndex: "confirmStatus",
          hideInSearch: false,
          hideInTable: true,
          renderFormItem() {
            return <Select placeholder="请选择" options={confirmStatusOptions} filterOption={filterOptionLabel} showSearch allowClear />;
          }
        }
      ]
    }
  ];
};
const anchorLink_detail_search = ({}: any) => {
  return [
    {
      title: "直播日期",
      dataIndex: "liveDate",
      hideInSearch: false,
      hideInTable: true,
      valueType: "dateRange"
    },
    {
      title: "结算月份",
      dataIndex: "settleMonth",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return <DatePicker.RangePicker picker="month" allowClear />;
      }
    },
    {
      title: "主播",
      dataIndex: "anchorNameList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem() {
        return <AnchorSelect showSearch allowClear maxTagCount="responsive" mode="multiple" />;
      }
    },

    {
      title: "商品ID",
      dataIndex: "itemIdList",
      hideInSearch: false,
      hideInTable: true,
      fieldProps: {
        placeholder: "多个使用英文逗号/空格"
      }
    },
    {
      title: "商品名称",
      dataIndex: "itemTitle",
      hideInSearch: false,
      hideInTable: true
    },
    {
      title: "大部门",
      dataIndex: "bigDepartmentList",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem: () => {
        return <DepartmentSelect key="bigDepartmentList" maxTagCount="responsive" mode="multiple" />;
      }
    },
    {
      title: "供应商名称",
      dataIndex: "supplierName",
      hideInSearch: false,
      hideInTable: true,
      renderFormItem() {
        return <SupplierNameSelect />;
      }
    }
  ];
};
export const anchorLink_detail_columns = ({ isNeedRedPunchObj, linkfeeTypeObj, confirmStatusObj, settleMonth }: any): Array<TableListItem> => {
  return [
    ...anchorLink_detail_search({}),
    {
      title: "序号",
      dataIndex: "rowNo",
      width: 60,
      hideInSearch: true,
      fixed: "left",
      editable: false
    },
    { title: "主播", editable: false, dataIndex: "anchorName", fixed: "left", width: 90, hideInSearch: true },
    { title: "直播日期", editable: false, dataIndex: "liveDateStr", fixed: "left", width: 100, hideInSearch: true },
    {
      title: "品牌",
      editable: false,
      dataIndex: "brandName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "店铺名称",
      editable: false,
      dataIndex: "shopName",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "店铺ID", editable: false, dataIndex: "shopId", width: 100, hideInSearch: true },
    {
      title: "供应商名称",
      editable: false,
      dataIndex: "supplierName",
      width: 180,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "我司主体",
      editable: false,
      dataIndex: "companyName",
      width: 180,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "大部门（必填）", editable: false, dataIndex: "bigDepartment", width: 120, hideInSearch: true },
    { title: "对接人", editable: false, dataIndex: "purchasePrincipal", width: 80, hideInSearch: true },
    {
      title: "商品信息",
      editable: false,
      dataIndex: "itemInfo",
      width: 300,
      hideInSearch: true,
      render(_, record) {
        const { itemMainImg, itemId } = record;
        let ellipsisItemId = itemId;
        if (itemId.indexOf(",") > -1) {
          ellipsisItemId = replaceMiddleStr({ str: itemId, len: 6 });
        }
        return (
          <div className="columnItemInfo">
            <Image width={50} height={50} src={returnItemImgUrl(itemMainImg)} />
            <div className="info">
              <div className="id">
                ID:
                {returnEllipsisTooltip({
                  title: ellipsisItemId || NOVALUE,
                  tooltipOption: { tooltip: itemId },
                  typographyProps: { copyable: { text: itemId } }
                })}
              </div>
              <div>{returnEllipsisTooltip({ title: record?.itemTitle, rows: 1, width: 250 })}</div>
            </div>
          </div>
        );
      }
    },
    {
      title: "商品编码",
      editable: false,
      dataIndex: "itemCode",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "链接费类型",
      editable: false,
      dataIndex: "linkfeeType",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: linkfeeTypeObj[text] || NOVALUE });
      }
    },
    { title: "应收链接费", editable: false, dataIndex: "arLinkfeeAmountStr", width: 140, hideInSearch: true },
    { title: "待收链接费", editable: false, dataIndex: "residueLinkfeeAmountStr", width: 140, hideInSearch: true },
    {
      title: "收/付款主体",
      editable: false,
      dataIndex: "payeeGroup",
      width: 180,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "收款日期",
      editable: false,
      dataIndex: "payeeDate",
      width: 100,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    { title: "收/付款链接费", editable: false, dataIndex: "receivedLinkfeeAmountStr", width: 120, hideInSearch: true },
    { title: "净链接费", editable: false, dataIndex: "netLinkfeeAmountStr", width: 120, hideInSearch: true },
    { title: "公司金额", editable: false, dataIndex: "companyAmountStr", width: 120, hideInSearch: true },
    { title: "主播金额", editable: false, dataIndex: "anchorAmountStr", width: 120, hideInSearch: true },
    { title: "分成比例", editable: false, dataIndex: "divideRatio", width: 100, hideInSearch: true },
    {
      title: "结算月份",
      editable: true,
      dataIndex: "settleMonth",
      width: 100,
      hideInSearch: true,
      formItemProps: () => {
        return {
          rules: [{ required: true, message: "此项为必填项" }]
        };
      },
      valueType: "dateMonth",
      fieldProps: { size: SIZE, picker: "month", minDate: dayjs(settleMonth) }
    },
    {
      title: "红冲原月份",
      editable: false,
      dataIndex: "edOkiharaMonth",
      width: 100,
      hideInSearch: true,
      valueType: "dateMonth",
      fieldProps: { size: SIZE, picker: "month" }
    },
    { title: "确认时间", editable: false, dataIndex: "gmtConfirmStr", width: 180, hideInSearch: true },
    {
      title: "是否红冲",
      editable: false,
      dataIndex: "isNeedRedPunch",
      width: 140,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: isNeedRedPunchObj[text] || NOVALUE });
      }
    },
    {
      title: "是否确认",
      editable: false,
      dataIndex: "confirmStatus",
      width: 80,
      hideInSearch: true,
      render(text) {
        return returnEllipsisTooltip({ title: confirmStatusObj[text] || NOVALUE });
      }
    },
    {
      title: "链接费备注",
      editable: true,
      dataIndex: "memo",
      width: 100,
      hideInSearch: true,
      fieldProps: { size: SIZE },
      render(text) {
        return returnEllipsisTooltip({ title: text });
      }
    },
    {
      title: "操作",
      width: 100,
      dataIndex: "$option",
      valueType: "option",
      fixed: "right",
      render: (text: string, record: any, index: number, action: any) => {
        const { liveItemLinkfeeCompositeKey, confirmStatus } = record;
        return (
          <Space size={SIZE}>
            <AuthButton key="edit" code={btn_auths.btn_linkfee_ledger_index_edit} pageUrl={PageUrl}>
              <Button
                type="link"
                size={SIZE}
                disabled={confirmStatus === 1}
                onClick={() => {
                  action?.startEditable(liveItemLinkfeeCompositeKey);
                }}
              >
                编辑
              </Button>
            </AuthButton>
          </Space>
        );
      }
    }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      ...item,
      align: "center",
      title: returnEllipsisTooltip({ title })
    };
  });
};
