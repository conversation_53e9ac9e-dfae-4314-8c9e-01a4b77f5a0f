export const targetKeys = {
  hasReconcili: -1,
  unReconcili: -2,
  receivable_amount: 1,
  has_receivable: -3,
  wait_receivable: 2,
  payable_amount: 3,
  has_payable: -4,
  wait_payable: 4,
  un_payable_60: -5
};
export const targetOptions = [
  {
    value: targetKeys.hasReconcili,
    label: "已对账商品数",
    name: "reconciledItemCount",
    children: ""
  },
  {
    value: targetKeys.unReconcili,
    label: "未对账商品数",
    name: "unreconciledItemCount",
    children: ""
  },
  {
    value: targetKeys.receivable_amount,
    label: "应收金额",
    name: "receivableAmount",
    select: true,
    children: ""
  },
  {
    value: targetKeys.has_receivable,
    label: "已收金额",
    name: "receivedAmount",
    children: ""
  },
  {
    value: targetKeys.wait_receivable,
    label: "待收金额",
    name: "collectedAmount",
    select: true,
    children: ""
  },
  {
    value: targetKeys.payable_amount,
    label: "应付金额",
    name: "payableAmount",
    select: true,
    children: ""
  },
  {
    value: targetKeys.has_payable,
    label: "已付金额",
    name: "paidAmount",
    children: ""
  },
  {
    value: targetKeys.wait_payable,
    label: "待付金额",
    name: "pendingPaymentAmount",
    select: true,
    children: ""
  },
  {
    value: targetKeys.un_payable_60,
    label: "超60天未回款金额",
    name: "overdue60DaysAmount",
    type: "waring",
    children: ""
  }
];
