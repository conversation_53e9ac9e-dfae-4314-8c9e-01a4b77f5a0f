import React, { useState } from "react";
import { Row, Col } from "antd";
import KeepAlive from "react-activation";
import HeaderSearch from "./components/header-search";
import Targets from "./components/supplier-targets";
import SupplierRank from "./components/supplier-rank";
import SupplierTable from "./components/supplier-table";
import styles from "./index.less";
import classNames from "classnames";
import { ifEmptyObj } from "@/utils";

export const FieldsContext = React.createContext<any>(null);
export const PageUrl = "/supplierOverview/overview";
const Res: React.FC = () => {
  const [params, setParams] = useState({});
  const onParamsChange = val => {
    setParams(val);
  };
  return (
    <FieldsContext.Provider value={{ params }}>
      <div className={styles["supplierOverview"]}>
        <div className={classNames(styles["supplierOverview-search"], "header-search")}>
          <HeaderSearch onChange={onParamsChange} />
        </div>
        {ifEmptyObj(params) ? (
          <div className={styles["supplierOverview-content"]}>
            <Row gutter={[10, 10]}>
              <Col>
                <Targets />
              </Col>
              <Col>
                <SupplierRank />
              </Col>
              <Col>
                <SupplierTable />
              </Col>
            </Row>
          </div>
        ) : null}
      </div>
    </FieldsContext.Provider>
  );
};
const Alive = props => (
  <KeepAlive name={PageUrl}>
    <Res {...props} />
  </KeepAlive>
);

export default React.memo(Alive);
