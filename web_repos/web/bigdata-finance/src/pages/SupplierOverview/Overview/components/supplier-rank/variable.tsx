import { Button } from "antd";
import { returnEllipsisTooltip } from "@/utils";

export const anchorRank_columns = ({ handleDetail }: any): TableListItem[] => {
  return [
    {
      title: "序号",
      dataIndex: "$index",
      width: 70,
      fixed: "left",
      render: (_, record, index) => {
        return index + 1;
      }
    },
    {
      title: "供应商",
      dataIndex: "supplierName",
      width: 160,
      render(_) {
        return returnEllipsisTooltip({ title: _ });
      }
    },
    {
      title: "账期",
      dataIndex: "periodStr",
      width: 90,
      render(_) {
        return returnEllipsisTooltip({ title: _ });
      }
    },
    {
      title: "应收付金额",
      dataIndex: "receivableAmountStr",
      sortDirections: ["descend", "ascend"],
      sorter: true,
      width: 130,
      render(_) {
        return returnEllipsisTooltip({ title: _ });
      }
    },
    {
      title: "已收付金额",
      dataIndex: "receivedAmountStr",
      sortDirections: ["descend", "ascend"],
      sorter: true,
      width: 130,
      render(_) {
        return returnEllipsisTooltip({ title: _ });
      }
    },
    {
      title: "待收付金额",
      dataIndex: "collectedAmountStr",
      sortDirections: ["descend", "ascend"],
      sorter: true,
      width: 130,
      render(_) {
        return returnEllipsisTooltip({ title: _ });
      }
    },
    {
      title: "操作",
      width: 100,
      valueType: "option",
      fixed: "right",
      render: (_: string, record: any) => {
        return (
          <Button type="link" onClick={() => handleDetail(record)}>
            详情
          </Button>
        );
      }
    }
  ].map((item: TableListItem) => {
    const { title } = item;
    return {
      ...item,
      align: "center",
      title: returnEllipsisTooltip({ title })
    };
  });
};
