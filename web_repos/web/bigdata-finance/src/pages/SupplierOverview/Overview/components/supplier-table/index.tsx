import React, { useContext } from "react";
import { Card, Space } from "antd";
import TableList from "@/components/TableList";
import apis from "@/services/SupplierOverview/overview";
import { columns } from "./variable";
import styles from "../../index.less";
import { FieldsContext } from "@/pages/SupplierOverview/Overview";
const Res = () => {
  const { params } = useContext(FieldsContext);
  const handleParams = p => {
    const { current, ...rest } = p;
    return {
      pageNo: current,
      ...rest
    };
  };
  const returnTitle = () => {
    return (
      <Space>
        <span>问题对账单</span>
        <span className={styles["subtitle"]}>超60天未回款</span>
      </Space>
    );
  };
  return (
    <Card title={returnTitle()} className={styles["problem-table"]}>
      <TableList
        search={false}
        options={false}
        columns={columns({})}
        params={params}
        api={apis.getProblemReconcilePage}
        preFetch={handleParams}
        rowKey="itemReconcileDetailId"
      />
    </Card>
  );
};

export default React.memo(Res);
