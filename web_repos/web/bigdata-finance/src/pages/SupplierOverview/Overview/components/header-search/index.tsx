import React, { useRef, useEffect } from "react";
import { Form, DatePicker, Button, Input } from "antd";
import { ProForm } from "@ant-design/pro-components";
import type { ProFormInstance, SubmitterProps } from "@ant-design/pro-components";
import AnchorSelect from "@/components/AnchorSelect";
import SupplierNameSelect from "@/components/SupplierNameSelect";
import { SIZE } from "@/utils/constant";
import dayjs from "dayjs";
import DepartmentSelect from "@/components/DepartmentSelect";
interface IProps {
  onChange?: (val: Record<string, any>) => void;
}
const HeaderSearch: React.FC<IProps> = props => {
  const { onChange } = props;
  const formRef = useRef<ProFormInstance>(null);
  const initialValues = useRef({
    liveDate: [dayjs().subtract(6, "month"), dayjs()]
  });
  const handleParams = val => {
    const { liveDate = [], principalNameList, financeReconcileList, deptNameList, supplierNameList, ...rest } = val;
    rest.$_fetch = +new Date();
    if (liveDate.length) {
      const [liveDateStart, liveDateEnd] = liveDate;
      rest.liveDateStart = dayjs(liveDateStart).format("YYYY-MM-DD");
      rest.liveDateEnd = dayjs(liveDateEnd).format("YYYY-MM-DD");
      const historyDate = dayjs("2023-01-01");
      // 直播开始时间小于 2023-01-01 添加 includedStatementHistory
      if (historyDate.isAfter(dayjs(liveDateStart), "day")) {
        rest.includedStatementHistory = true;
      }
    } else {
      // 直播日期为空时，添加 includedStatementHistory
      rest.includedStatementHistory = true;
    }
    if (principalNameList) {
      rest.principalNameList = [principalNameList];
    }
    if (deptNameList) {
      rest.deptNameList = [deptNameList];
    }
    if (supplierNameList) {
      rest.supplierNameList = [supplierNameList];
    }
    if (financeReconcileList) {
      rest.financeReconcileList = [financeReconcileList];
    }
    return rest;
  };
  const onFinish = val => {
    onChange?.(handleParams(val));
  };
  const onReset = () => {
    onChange?.(handleParams(initialValues.current));
  };
  const submitButtonRender = (props: SubmitterProps) => {
    return [
      <Button
        key="reset"
        onClick={() => {
          props?.form?.resetFields();
          onReset();
        }}
      >
        重置
      </Button>,
      <Button key="submit" type="primary" onClick={() => props?.form?.submit()}>
        查询
      </Button>
    ];
  };

  useEffect(() => {
    onChange?.(handleParams(initialValues.current));
  }, []);
  return (
    <ProForm
      layout="inline"
      initialValues={initialValues.current}
      formRef={formRef}
      size={SIZE}
      onFinish={onFinish}
      onReset={onReset}
      submitter={{ render: submitButtonRender }}
    >
      <Form.Item label="直播日期" name="liveDate">
        <DatePicker.RangePicker></DatePicker.RangePicker>
      </Form.Item>
      <Form.Item label="主播" name="anchorNameList">
        <AnchorSelect maxTagCount="responsive" mode="multiple" />
      </Form.Item>
      <Form.Item label="供应商名称" name="supplierNameList">
        <SupplierNameSelect style={{ width: 170 }} />
      </Form.Item>
      <Form.Item label="部门" name="deptNameList">
        <DepartmentSelect style={{ width: 170 }} />
      </Form.Item>
      <Form.Item label="业务负责人" name="principalNameList">
        <Input placeholder="业务负责人" allowClear />
      </Form.Item>
      <Form.Item label="财务对账人" name="financeReconcileList">
        <Input placeholder="财务对账人" allowClear />
      </Form.Item>
    </ProForm>
  );
};
export default React.memo(HeaderSearch);
