import React, { useEffect, useMemo, useState, useContext } from "react";
import { Card, Select, Spin, Row, Col, Tabs } from "antd";
import { EchartType, dataZoom } from "@/components/EchartsCard";
import { SIZE, tabs_tabBarStyle } from "@/utils/constant";
import { targetOptions, targetKeys } from "../../variable";
import styles from "./index.less";
import apis from "@/services/SupplierOverview/overview";
import { anchorRank_columns } from "./variable";
import { FieldsContext } from "@/pages/SupplierOverview/Overview";
import { history } from "@umijs/max";
import queryString from "query-string";
import { menus } from "@/pages/Supplierbill/Reconciliationbill/reconcili-bill";
import { formatYuanToAmount, returnSortParams } from "@/utils";
const tab_supplier = "1";
const tab_department = "2";
const tab_finance = "3";
type TabKey = typeof tab_supplier | typeof tab_department | typeof tab_finance;
interface IParams {
  groupType?: TabKey;
  [key: string]: any;
}
const tabObj: Record<TabKey, string> = {
  [tab_supplier]: "供应商",
  [tab_department]: "部门",
  [tab_finance]: "财务负责人"
};
const height = 400;
const Res: React.FC = () => {
  const { params } = useContext(FieldsContext);
  const [pageParams, setPageParams] = useState<IParams>({
    rankType: targetKeys.wait_receivable,
    groupType: tab_supplier
  });
  const [tableParams, setTableParams] = useState<IParams>({});
  // 图表loading
  const [loading, setLoading] = useState(false);
  // 图表数据
  const [chartData, setChartData] = useState([]);
  const [selectOptions] = useState(() => targetOptions.filter(item => item.select));
  const fetchList = async params => {
    setLoading(true);
    const { status, entry } = await apis.getReconcileRank(params);
    if (status) {
      setChartData(
        entry.map(item => {
          const { amount, amountStr, groupName } = item;
          return {
            label: groupName,
            value: amount / 100,
            labelValue: amountStr
          };
        })
      );
    } else {
      setChartData([]);
    }
    setLoading(false);
  };
  const onClickMonth = (value: Record<string, any>, key: string) => {
    const { name } = value;
    setTableParams({ ...tableParams, [key]: name, $_fetch: +new Date() });
  };
  const onSelectChange = e => {
    setPageParams({ ...pageParams, rankType: e });
  };
  const handleTabChange = e => {
    setPageParams({ ...pageParams, groupType: e });
    setTableParams({ groupType: e });
  };
  const handleDetail = record => {
    const { supplierName, gmtReconcilePeriodStart, gmtReconcilePeriodEnd } = record;
    history.push({
      pathname: "/supplierbill/reconciliationbill",
      search: queryString.stringify({
        supplierName,
        settleDateStart: gmtReconcilePeriodStart,
        settleDateEnd: gmtReconcilePeriodEnd,
        menu: menus.menu_hasReconcili
      })
    });
  };
  const handleOptions = (options, data) => {
    options.grid = {
      left: "18%",
      right: "10%",
      bottom: "36px",
      top: "10%",
      containLabel: false
    };
    options.xAxis = {
      ...options.xAxis,
      name: "金额",
      axisLabel: {
        ...options.yAxis.axisLabel,
        formatter: function (value) {
          return formatYuanToAmount(value, 2);
        }
      },
      splitLine: {
        show: true
      }
    };
    options.yAxis = {
      ...options.yAxis,
      name: tabObj?.[pageParams.groupType] || "",
      data: data.map((el: any) => (el?.name?.length > 5 ? el?.name?.substring(0, 5) + "..." : el?.name || ""))
    };
    options.series.push({
      type: "bar",
      tooltip: {
        show: false
      },
      data: data,
      silent: true,
      barWidth: 20,
      barMaxWidth: "20px",
      barMinHeight: 60,
      barGap: "-100%",
      dataZoom:
        data.length > 8
          ? [
              {
                type: "slider",
                startValue: 0,
                endValue: 8,
                xAxisIndex: [0],
                filterMode: "empty",
                width: "90%",
                height: 0,
                ...dataZoom
              }
            ]
          : []
    });

    return options;
  };
  const handleParams = (p, sort) => {
    const { current, ...rest } = p;
    const sortParams = returnSortParams(sort);
    return {
      pageNo: current,
      ...rest,
      ...sortParams
    };
  };
  const tabItems = [
    {
      key: tab_supplier,
      label: tabObj[tab_supplier],
      children: (
        <Spin spinning={loading}>
          <EchartType
            optionAfter={handleOptions}
            height={height}
            data={chartData}
            type="rank"
            onClickMonth={p => onClickMonth(p, "statementSupplierName")}
          />
        </Spin>
      )
    },
    {
      key: tab_department,
      label: tabObj[tab_department],
      children: (
        <Spin spinning={loading}>
          <EchartType
            optionAfter={handleOptions}
            height={height}
            data={chartData}
            type="rank"
            onClickMonth={p => onClickMonth(p, "statementDeptName")}
          />
        </Spin>
      )
    },
    {
      key: tab_finance,
      label: tabObj[tab_finance],
      children: (
        <Spin spinning={loading}>
          <EchartType
            optionAfter={handleOptions}
            height={height}
            data={chartData}
            type="rank"
            onClickMonth={p => onClickMonth(p, "statementFinanceReconcile")}
          />
        </Spin>
      )
    }
  ];
  const extraNode = useMemo(() => {
    return (
      <Select style={{ width: 140 }} defaultValue={targetKeys.wait_receivable} size={SIZE} options={selectOptions} onChange={onSelectChange}></Select>
    );
  }, [selectOptions, pageParams]);
  const rankColumnProps = {
    handleDetail
  };
  useEffect(() => {
    fetchList({ ...params, ...pageParams });
    setTableParams({ groupType: pageParams.groupType });
  }, [params, pageParams]);
  return (
    <Row gutter={10} className={styles["supplier-rank"]}>
      <Col span={12} className={styles["supplier-rank-tabs"]}>
        <Tabs
          size={SIZE}
          activeKey={pageParams.groupType}
          items={tabItems}
          tabBarStyle={tabs_tabBarStyle}
          tabBarExtraContent={extraNode}
          onChange={handleTabChange}
        ></Tabs>
      </Col>

      <Col span={12}>
        <Card title="对账单">
          <EchartType
            type="table"
            height={height}
            tableProps={{
              columns: anchorRank_columns(rankColumnProps),
              rowKey: record => {
                const { supplierName, gmtReconcilePeriodStart, gmtReconcilePeriodEnd } = record;
                return supplierName + gmtReconcilePeriodStart + gmtReconcilePeriodEnd;
              },
              params: { ...tableParams, ...params },
              api: apis.getSupplierReconcilePage,
              preFetch: handleParams
            }}
          />
        </Card>
      </Col>
    </Row>
  );
};

export default React.memo(Res);
