// @ts-nocheck
// 全局常量
import { SizeType } from "antd/es/config-provider/SizeContext";
// 不知道为什么 从 @/utils/index 导出不了
// 返回 目标数组对象 的 对象 对应关系  [{label,value}]
export const returnTargetObject = (target: IOptions[]): Record<string, any> => {
  return target.reduce((total, item) => {
    total[item.value] = item.label;
    return total;
  }, {});
};
// 无值
export const NOVALUE = "--";
// 字符分割标识
export const SPLIT_FLAG = "$_$";
// Select 全部
export const AllValue = {
  label: "全部",
  value: ""
};
// 组件大小
export const SIZE: SizeType = "small";
export const Protable_FROM_CONFIG = {
  span: 6,
  labelWidth: 100,
  size: SIZE,
  labelWrap: true,
  defaultCollapsed: false,
  searchGutter: 5
};
export const FormLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 16 }
};

// 结算开始时间
export const SettlementStartTime = "202401";

// antd Tabs tabBarStyle 样式
export const tabs_tabBarStyle = {
  marginBottom: 0,
  background: "#fff",
  padding: "0 8px"
};
