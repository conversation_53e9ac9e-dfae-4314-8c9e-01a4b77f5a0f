import { history } from "@umijs/max";
import { TooltipProps, Typography, Tooltip, Popconfirm, message, Button } from "antd";
import { NOVALUE, SPLIT_FLAG, SIZE } from "@/utils/constant";
import queryString from "query-string";
import { originHost } from "@/utils/url";
import React from "react";
import { ParagraphProps } from "antd/es/typography/Paragraph";
export const protoTotring = Object.prototype.toString;
// 处理表格排序参数
export const returnSortParams = sort => {
  let rest: Record<string, any> = {};
  if (Object.keys(sort).length) {
    const [key, value] = Object.entries(sort)?.[0];
    rest["orderField"] = key?.replace("Str", "");
    rest["orderType"] = value?.replace("end", "");
  }
  return rest;
};
// 替换长字符的中间内容
export function replaceMiddleStr({ str = "", len = 5, suffix = "......" }) {
  return str.substr(0, len) + suffix + str.substr(-len);
}
// string[]  数组值 ，格式化为  {label,value} 格式
export const returnOptions = (target: string[]): IOptions[] => {
  return (
    target?.map((item: string) => {
      return {
        label: item,
        value: item
      };
    }) || []
  );
};
export const goNext = () => {
  const params: any = queryString.parse(window.location.href.split("?")[1]);
  const { redirect }: { redirect: string } = params;
  if (redirect) {
    window.location.href = redirect;
    return;
  }
  window.location.replace(originHost);
};
// 统一跳转 登录
export const navToLogin = () => {
  const reg = /^#\/login/gi;
  // 如果是登录页 不需要再次跳转
  if (reg.test(window.location.hash)) return;
  history.replace("/login");
};
// 处理字符串前后空格
export const handleStringTrim = (obj: { [key: string]: any }) => {
  // eslint-disable-next-line guard-for-in
  for (let key in obj) {
    const item = obj[key];
    if (typeof item === "string") {
      obj[key] = item.trim();
    }
  }
  return obj;
};
// 根据 空格/英文逗号 分隔
export const splitSpaceComma = (val: string, isComma = false): string[] => {
  // 英文逗号分隔
  const commaReg = /[,]/;
  // 空格/英文逗号 分隔
  const spaceComma = /[,\s+]/;
  return val
    ?.trim()
    ?.split(isComma ? commaReg : spaceComma)
    .filter(item => item !== "")
    .map(item => item.trim());
};
export const paginationOptions = {
  showLessItems: true,
  defaultPageSize: 20,
  pageSizeOptions: ["20", "50", "100", "200"],
  showTotal: total => `共 ${total} 条`
};
// 返回 目标数组对象 的 对象 对应关系  [{label,value}]
export const returnTargetObject = (target: IOptions[]): Record<string, any> => {
  return target.reduce((total, item) => {
    total[item.value] = item.label;
    return total;
  }, {});
};
interface returnEllipsisTooltipProps {
  title: string | React.ReactNode;
  rows?: number;
  width?: number;
  tooltipOption?: TooltipProps;
  typographyProps?: ParagraphProps;
}
interface returnEllipsisTitleProps {
  title: string | React.ReactNode;
  breakClassName?: "etc-2" | "etc";
  tooltip?: string | React.ReactNode;
}

// 返回超出两行显示省略号的 title
export const returnEllipsisTitle = ({ title = "", breakClassName = "etc-2", tooltip }: returnEllipsisTitleProps) => {
  return (
    <Tooltip placement="top" title={tooltip ?? title}>
      <div className={breakClassName}>{title || NOVALUE}</div>
    </Tooltip>
  );
};
// 返回超出两行显示省略号的 Tooltip
export const returnEllipsisTooltip = ({ title, width, rows = 2, tooltipOption = {}, typographyProps = {} }: returnEllipsisTooltipProps) => {
  return (
    <Typography.Paragraph
      style={{ marginBottom: 0, width: !width ? "100%" : width - 10 }}
      ellipsis={{
        rows: rows,
        tooltip: title,
        ...tooltipOption
      }}
      {...typographyProps}
    >
      {title || NOVALUE}
    </Typography.Paragraph>
  );
};
// 处理百分数
export const handlePercentage = (val: any, pre = 2): number => {
  return typeof +val === "number" && !isNaN(+val) ? +(val * 100).toFixed(pre) : 0;
};
// 小数转为百分比小数
export const handleNumToPercentage = (val: number, pre = 4): number => {
  return +(val / 100).toFixed(pre);
};
// 跳转到导出页面
export const pushExportHistory = () => {
  history.push("/records/exportRecord");
};
// 跳转到导入页面
export const pushImportHistory = () => {
  history.push("/records/importRecord");
};
// 返回树结构，各级叶子节点的 name 合值
export const returnTreeNames = (flattenTrees = [], id: unknown, parentId = "pid", name = "name") => {
  let result = "";

  const cur =
    flattenTrees.filter(item => {
      return item.id === id;
    })[0] || {};
  if (cur?.[parentId]) {
    result = `${returnTreeNames(flattenTrees, cur[parentId], parentId, name)}${SPLIT_FLAG}${cur[name]}`;
  } else {
    result = cur[name] + result;
  }
  return result;
};
// 平铺数组
export function flattenTree<T>(target: T[] = [], key: string = "routes"): T[] {
  return (target || []).reduce((total: T[], cur: T) => {
    const children = cur[key] || [];
    return total.concat(children.length > 0 ? [cur, ...flattenTree(children, key)] : cur);
  }, []);
}
// 默认商品图片
export const defaultItemImgUrl = "https://s.xinc818.com/files/webcilnmnbvjzj25qzk/商品却省图.png";
export const returnItemImgUrl = url => {
  return url || defaultItemImgUrl;
};
// Select filterOption 过滤 label
export const filterOptionLabel = (inputValue, option) => {
  let value: any = option!.label;
  return value?.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1;
};
// 千分位
export const millennialsNumber = (num: number, options?: MillennialOptions) => {
  if (typeof num !== "number") return num;
  return (num || 0).toLocaleString("en-US", {
    maximumFractionDigits: 10,
    ...(options ?? {})
  });
};
export const ifEmptyObj = (obj: { [key: string]: any }): boolean => {
  const keys = Object.keys(obj || {});
  return keys.length > 0 ? true : false;
};
// 判断是否有值
export const judgeHasValue = (val: any) => {
  if (protoTotring.call(val) === "[object Object]") {
    return ifEmptyObj(val);
  }
  if (protoTotring.call(val) === "[object Array]") {
    return val.length > 0 ? true : false;
  }
  if (val !== null && val !== undefined) {
    return true;
  }
  return false;
};
// 判断valueIsWhole中的参数是否都已被赋值
export function judgeWholeValue(judgeTarge, target) {
  for (let item in target) {
    if (target.hasOwnProperty(item)) {
      judgeTarge[item] = judgeHasValue(target[item]) ? true : false;
    }
  }
  return judgeTarge;
}

export const DeleteBtn = props => {
  const { params = {}, action = undefined, api, btnProps = {} } = props;

  const confirm = () => {
    if (Object.keys(params).length && action) {
      api?.(params)
        .then(res => {
          if (res.status) {
            message.success("删除成功！");
          } else {
            message.error(res.message);
          }
        })
        .finally(() => {
          action.reload();
        });
    }
  };
  return (
    <Popconfirm
      title="是否确认删除?"
      onConfirm={confirm}
      onCancel={() => {}}
      okText="确认"
      cancelText="取消"
      okButtonProps={{ size: SIZE }}
      cancelButtonProps={{ size: SIZE }}
    >
      <Button size={SIZE} type="primary" danger {...btnProps}>
        删除
      </Button>
    </Popconfirm>
  );
};
// 返回值 为了添加 值为空时，默认显示
export const returnValueForNoValue = (item = void 0) => {
  return item ?? NOVALUE;
};
// 元 格式化 带单位
export const formatYuanToAmount = (num: number | string, precision = 2): string => {
  if (typeof num === "string") return num;
  // 万
  const ten_thousand = 10000;
  // 千万
  const must = 10000000;
  // 亿
  const hundred_million = 100000000;
  const abs_num = Math.abs(num);
  let res = "0";
  if (abs_num >= ten_thousand && abs_num < must) {
    res = (abs_num / ten_thousand).toFixed(precision) + "万";
  } else if (abs_num >= must && abs_num < hundred_million) {
    res = (abs_num / ten_thousand).toFixed(precision) + "万";
  } else if (abs_num >= hundred_million) {
    res = (abs_num / hundred_million).toFixed(precision) + "亿";
  } else {
    res = abs_num.toFixed(precision);
  }
  return num >= 0 ? res : `-${res}`;
};
