// 处理页面 行列权限

import { SPLIT_FLAG } from "./constant";
// 写权限格式
export const writePermissionFormat = `${SPLIT_FLAG}write`;
// 读权限格式
export const readPermissionFormat = `${SPLIT_FLAG}read`;
// 不可见
export const COLUMNS_INVISIBLE = -1;
// 可读
export const COLUMNS_READ = 0;
// 可写
export const COLUMNS_WRITE = 1;

export const handleColumnsAuth = (authList = []) => {
  return authList?.reduce((total, item) => {
    const { authType, columnName } = item;
    total[columnName] = authType;
    return total;
  }, {});
};
export const hasColumnsReadAuth = (auths, key) => {
  return auths[key] >= COLUMNS_READ;
};
export const hasColumnsWriteAuth = (auths, key) => {
  return auths[key] >= COLUMNS_WRITE;
};
/**
 *
 * @param auths 拥有的权限
 * @param name 当前需要控制的权限 名称
 */
export const hasPermission = (auths = [], name: string): boolean => {
  return auths?.includes(name);
};
// 拥有读权限
export const hasReadPermission = (auths = [], name: string): boolean => {
  const filterAuth = auths.filter(item => item.indexOf(name + readPermissionFormat) > -1);
  return filterAuth.length > 0;
};
// 拥有写权限
export const hasWritePermission = (auths = [], name: string): boolean => {
  const filterAuth = auths.filter(item => item.indexOf(name + readPermissionFormat + writePermissionFormat) > -1);
  return filterAuth.length > 0;
};
