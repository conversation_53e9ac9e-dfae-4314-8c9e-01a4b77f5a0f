import { name } from "./../../package.json";
export const env = (() => {
  const host = window.location.host;
  if (
    host.indexOf("localhost") > -1 ||
    host.indexOf("dev") > -1 ||
    host.indexOf("mock") > -1 ||
    host.indexOf("192") > -1 ||
    host.indexOf("127") > -1
  ) {
    return `dev`;
  } else if (host.indexOf("daily") > -1) {
    return `daily`;
  } else if (host.indexOf("gray.") > -1) {
    return "gray";
  }
  return "prod";
  // return "daily";
})();

export const ajaxBaseUrl = (() => {
  const hrefs = {
    dev: `https://mass-api-dev.xinc818.com/`,
    daily: `https://mass-api-daily.xinc818.com/`,
    gray: `https://mass-api-gray.xinc818.com/`,
    prod: `https://mass-api.xinc818.com/`
  };
  return localStorage.env || hrefs[env];
})();

export const uploadhref = (() => {
  const hrefs = {
    daily: `https://api-daily.xinc818.net/`,
    dev: `https://api-dev.xinc818.net/`,
    gray: `https://api-gray.xinc818.net/`,
    prod: `https://api.xinc818.com/`
  };
  return localStorage.env || hrefs[env];
})();

const devHrefs = ["https://web-dev.xinc818.com", "https://web-daily.xinc818.com", `https://web-gray.xinc818.com`, "https://web.xinc818.com"];
// 当前页面host
export const originHost = (() => {
  const host = window.location.origin;
  return devHrefs.includes(host) ? `${host}/${name}` : `${host}`;
})();
// 天机页面各个环境地址
export const mysteryEnvUrl = (() => {
  const projectUrls = {
    dev: `https://web-dev.xinc818.com/mystery`,
    daily: `https://web-daily.xinc818.com/mystery`,
    gray: `https://web-gray.xinc818.com/mystery`,
    prod: `https://web.xinc818.com/mystery`
  };
  return projectUrls[env] || projectUrls["prod"];
})();
