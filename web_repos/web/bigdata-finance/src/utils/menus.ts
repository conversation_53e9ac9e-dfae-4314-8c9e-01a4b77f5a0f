import { history, IRoute } from "@umijs/max";
import { flattenTree } from "./index";
// 检查是否是一级路由 /xxxx
export const primaryRoute = (pathname: string) => {
  return pathname.split("/").length > 2 ? false : true;
};
// 合并远程路由和 本地路由
export function assignMenus(menuList: any[], flattenedRoutes: any[]): any[] {
  return (
    menuList.map(menu => {
      let childResult = [];
      const { path = "", children = [] } = menu;
      // 将本地路由合并到远程路由的同时，给具有远程路由权限的本地路由 添加 auth = true
      let originRoute;
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { routes, ...filterRoute } = (originRoute =
        flattenedRoutes.filter(route => {
          if (path === route.path) return true;
          return false;
        })[0] || {});

      // route 的数据地址没有发生改变，所以在originRoute 上添加属性会生效
      originRoute.auth = true;
      menu.auth = true;
      if (children?.length > 0) {
        childResult = assignMenus(children, flattenedRoutes);
      }
      return { ...filterRoute, ...menu, children: childResult };
    }) || []
  );
}

export function redirect(pathname = "/") {
  history.replace(pathname);
}
/**
 * 遍历路由数组，通过 pathname 去匹配一级路由地址 格式 /xxxx ，获取一级路由下的 第一个子路由
 * 进行 路由重定向
 *
 * 注意： 最多只遍历3层 ，
 * 路由只有下面这2中格式
 * 1.导航/菜单/路由
 * 2.导航/路由
 *
 * @param pathname {string} 路由名称
 * @param routes {array} 路由数组
 * @returns
 */
export function handleRedirect(pathname: string, routes: any[] = []) {
  // 是否一级路由
  const pathnameSplit = pathname.split("/");
  const isPrimaryRoute = primaryRoute(pathname);
  if (isPrimaryRoute && pathnameSplit?.[1] !== "") {
    for (let route of routes) {
      const { path = "", children = [] } = route;
      if (path === pathname) {
        if (children?.length) {
          for (let firstChild of children || []) {
            const { path = "", children = [], auth, hideInMenu = false } = firstChild;
            if (children?.length) {
              for (let secondChild of children || []) {
                const { path = "", auth, hideInMenu = false } = secondChild;
                if (auth && !hideInMenu) return redirect(path);
              }
            } else {
              if (auth && !hideInMenu) return redirect(path);
            }
          }
        } else {
          return;
        }
      }
    }
  } else if (isPrimaryRoute && pathnameSplit[1] === "") {
    const { children: firstChild = [], path: firstLevelPath, auth: firstLevelAuth } = routes?.[0] || {};
    if (firstChild?.length > 0) {
      // 带有 children 的 一级菜单，继续向下找到 最后一级菜单
      const { path, children: secondChild = [], auth, hideInMenu = false } = firstChild?.[0] || {};
      if (secondChild?.length > 0) {
        const { path = "", auth, hideInMenu = false } = secondChild?.[0] || {};
        if (auth && !hideInMenu) return redirect(path);
      } else {
        if (auth && !hideInMenu) return redirect(path);
      }
    } else if (!firstChild?.length) {
      // 不带 children 的一级菜单，直接跳转 一级菜单
      if (firstLevelAuth) return redirect(firstLevelPath);
    }
  }
}
// 从路由中过滤出，当前路由
export function filterCurrentRouteFromRoute(routes: IRoute[], pathname: string) {
  // 将所有路由平铺，匹配出第一个 路径相等的 路由
  const flatroutes = flattenTree(routes, "children");
  for (let item of flatroutes) {
    const { path } = item;
    if (path === pathname) {
      return item;
    }
  }
}
