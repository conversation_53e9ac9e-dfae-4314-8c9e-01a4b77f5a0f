import { request } from "@umijs/max";
import { ajaxBaseUrl } from "@/utils/url";
import { handleStringTrim } from "@/utils";

// 遍历接口
function loop(urls: { [key: string]: string }, method: "POST" | "GET" | "url") {
  const re: { [key: string]: any } = {};
  if (!urls) return re;
  Object.keys(urls)
    .sort()
    .forEach((item: string) => {
      let key = item;
      let url = urls[item];
      url = url.replace(/^\/+/, ""); // 兼容多个斜杠开头的链接url

      if (method === "url") {
        re[key] = ajaxBaseUrl + url;
      } else {
        re[key] = async (params: any, config: any) => {
          const options = {
            ...config
          };

          if (method === "GET") {
            options.params = handleStringTrim(params);
          } else if (method === "POST") {
            options.data = handleStringTrim(params);
            options.method = "POST";
          }

          return await request(ajaxBaseUrl + url, options);
        };
      }
    });
  return re;
}
/**
 * 封装请求
 * @param {Object} getUrls get 请求的url列表
 * @param {Object} postUrls post 请求的url列表
 * @param {Object} urls 直接返回url，用于upload这种场景
 */
export default function wrapper(getUrls = {}, postUrls = {}, urls = {}) {
  return {
    ...loop(getUrls, "GET"),
    ...loop(postUrls, "POST"),
    ...loop(urls, "url")
  };
}
