import type { ColumnsState } from "@ant-design/pro-components";
import { DrawerProps } from "antd";
import { ButtonSize } from "antd/es/button";

export { ColumnsState };
export interface TargetConfigProps {
  pageUrl: string;
  columns: TableListItem[];
  trigger?: JSX.Element;
  drawerProps?: DrawerProps;
  size?: ButtonSize;
  title?: JSX.Element;
  onChange?: (checked: Record<string, ColumnsState>) => void;
}
