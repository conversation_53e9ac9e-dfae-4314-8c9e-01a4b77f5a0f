// 指标配置
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { <PERSON><PERSON>, Drawer, Space, Checkbox, Row, Col, Input } from "antd";
import { SIZE } from "@/utils/constant";
import styles from "./index.less";
import apis from "@/services/common";
import { returnEllipsisTooltip } from "@/utils";
import { TargetConfigProps, ColumnsState } from "./types";
import { debounce } from "lodash-es";

const TargetConfig: React.FC<TargetConfigProps> = props => {
  const { columns, title = "指标配置", size = SIZE, trigger, drawerProps, onChange, pageUrl } = props;
  const [open, setOpen] = useState(false);
  // 搜索关键字
  const [searchValue, setSearchValue] = useState("");
  // 过滤掉 不需要显示的表格列
  const [showColumns] = useState(() => {
    return columns.filter((item: TableListItem) => item.showInGroup !== false);
  });
  // 处理后的 表格列
  const [treatedColumns, setTreatedColumns] = useState<TableListItem[]>(showColumns);
  const [wholeCheckValue, setWholeCheckValue] = useState<string[]>([]);
  // 最终确定的值 点击保存后 请求接口
  const [finnalChecked, setFinnalChecked] = useState<string[]>([]);

  // 返回选择列的 dataIndex
  const returnCheckedValue = useCallback((treatedColumns: TableListItem[]) => {
    const val = [];
    for (let item of treatedColumns) {
      const { checked, dataIndex } = item;
      if (checked) {
        val.push(dataIndex);
      }
    }
    return val;
  }, []);
  const checkboxValues = useMemo(() => {
    return returnCheckedValue(treatedColumns);
  }, [treatedColumns, returnCheckedValue]);

  const showDrawer = () => {
    setOpen(true);
  };

  const onClose = () => {
    setOpen(false);
  };

  const handleOk = async () => {
    const { status } = await apis.savePageFieldContext({ pageFieldContext: JSON.stringify(checkboxValues), pageKey: pageUrl });
    if (status) {
      onClose();
      setFinnalChecked(checkboxValues);
    }
  };
  // 全选状态
  const indeterminate = checkboxValues.length > 0 && checkboxValues.length < wholeCheckValue.length;

  // 全选操作
  const onCheckAllChange = (checked: boolean) => {
    const res = showColumns.map(column => {
      return {
        ...column,
        checked: checked ? true : false
      };
    });
    setTreatedColumns(res);
  };
  // 重置
  const handleReset = () => {
    onCheckAllChange(true);
  };
  // 请求
  const fetchList = async () => {
    if (!pageUrl) return;
    const { entry } = await apis.getPageField({ pageKey: pageUrl });
    const { pageFieldContext } = entry || {};
    let treated = [];
    try {
      // 根据接口返回的 保存值 确定需要 checkbox checked 状态
      let targets = JSON.parse(pageFieldContext) || [];
      treated = showColumns.map((column: TableListItem) => {
        const { dataIndex } = column;
        if (targets.length === 0) {
          return {
            ...column,
            checked: true
          };
        }
        return { ...column, checked: targets.includes(dataIndex) };
      });
    } catch {
      // 默认状态 checkbox 全选择
      treated = showColumns.map((column: TableListItem) => {
        return {
          ...column,
          checked: true
        };
      });
    }
    setTreatedColumns(treated);
    setFinnalChecked(returnCheckedValue(treated));
    setWholeCheckValue(treated.map(item => item.dataIndex));
  };
  // 返回 column 的 title, title 可能会被组件包裹
  const returnColumnTitle = useCallback((title: JSX.Element | string): string => {
    let name = title;
    if (typeof name === "object") {
      const {
        props: { children }
      } = title;
      name = children;
    }
    return name as string;
  }, []);

  // 返回可选择的值
  const handleCheckboxItems = useCallback(
    data => {
      const map: Record<string, boolean> = {};
      return data
        .filter((item: TableListItem) => {
          const { dataIndex } = item;
          if (!map[dataIndex]) {
            map[dataIndex] = true;
            return true;
          }
          return false;
        })
        .map(item => {
          const { title } = item;
          return {
            ...item,
            showInCheckbox: returnColumnTitle(title)?.toUpperCase().indexOf(searchValue.toUpperCase()) !== -1
          };
        });
    },
    [searchValue]
  );

  // 对表头进行分组
  const returnColumns = () => {
    const groupMap: Record<string, any> = {};
    let showColumns = handleCheckboxItems(treatedColumns);
    for (let item of showColumns) {
      const { title, dataIndex, groupName = "基础信息", showInCheckbox } = item;
      if (groupMap[groupName]) {
        groupMap[groupName].push({ dataIndex, title, showInCheckbox });
      } else {
        groupMap[groupName] = [{ dataIndex, title, showInCheckbox }];
      }
    }
    return groupMap;
  };

  // 触发节点
  const triggerDOM = useMemo(() => {
    if (trigger) {
      return trigger;
    }
    return (
      <Button type="link" size={size}>
        {title}
      </Button>
    );
  }, [trigger]);

  // Drawer title
  const returnTitle = (
    <div className={styles["title"]}>
      <div>{title}</div>
      <Space>
        <Button size={size} key="cancel" onClick={onClose}>
          取消
        </Button>
        <Button size={size} key="reset" onClick={handleReset}>
          重置
        </Button>
        <Button size={size} key="ok" type="primary" onClick={handleOk}>
          确认
        </Button>
      </Space>
    </div>
  );
  // 单个 checkbox 选择操作
  const onCheckboxChange = e => {
    const { value, checked } = e.target;
    const res = treatedColumns.map((column: TableListItem) => {
      const { dataIndex } = column;
      if (dataIndex === value) {
        return { ...column, checked };
      }
      return column;
    });
    setTreatedColumns(res);
  };

  // 返回 Checkbox 节点
  const returnItems = useCallback(
    (item: [string, TableListItem[]]) => {
      const [name, group] = item;
      const filtered = (group || []).filter(item => item.showInCheckbox);
      return (
        <>
          {filtered.length ? (
            <>
              <Col span={24} className={styles["group-title"]}>
                {name}
              </Col>
              {filtered.map((item: TableListItem) => {
                const { dataIndex, title } = item;
                let name = returnColumnTitle(title);
                return (
                  <Col key={dataIndex} span={6}>
                    <Checkbox value={dataIndex} style={{ width: "100%" }} onChange={onCheckboxChange}>
                      {returnEllipsisTooltip({ title: name, rows: 1, width: 140 })}
                    </Checkbox>
                  </Col>
                );
              })}
            </>
          ) : null}
        </>
      );
    },
    [treatedColumns]
  );
  // checkbox 节点
  const checkboxItemsDOM = useMemo(() => {
    const keys = Object.entries(returnColumns());
    return keys.map(returnItems).filter(item => item);
  }, [searchValue, checkboxValues]);
  // 搜索
  const onSearch = debounce(val => {
    setSearchValue(val);
  }, 500);

  const onSearchChange = e => {
    const val = e.target.value;
    onSearch(val);
  };

  useEffect(() => {
    fetchList();
  }, []);

  useEffect(() => {
    const res: Record<string, ColumnsState> = {};
    for (let item of wholeCheckValue) {
      if (!finnalChecked.includes(item)) {
        res[item] = { show: false };
      }
    }
    // 监听变化函数
    onChange?.(res);
  }, [finnalChecked, wholeCheckValue]);

  return (
    <>
      <div onClick={showDrawer}>{triggerDOM}</div>
      <Drawer
        classNames={{
          body: styles["drawer-body"]
        }}
        width={700}
        maskClosable={false}
        title={returnTitle}
        {...drawerProps}
        onClose={onClose}
        open={open}
      >
        <Row gutter={[4, 4]}>
          <Col span={24}>
            <Input.Search placeholder="搜索" onSearch={val => onSearch(val)} allowClear onChange={onSearchChange} enterButton size={size} />
          </Col>
          <Col span={24}>
            <Checkbox
              indeterminate={indeterminate}
              onChange={e => onCheckAllChange(e.target.checked)}
              checked={checkboxValues.length === wholeCheckValue.length}
            >
              全选
            </Checkbox>
          </Col>
        </Row>
        <Checkbox.Group style={{ width: "100%" }} value={checkboxValues}>
          <Row gutter={[4, 4]}>{checkboxItemsDOM}</Row>
        </Checkbox.Group>
      </Drawer>
    </>
  );
};
export default React.memo(TargetConfig);
