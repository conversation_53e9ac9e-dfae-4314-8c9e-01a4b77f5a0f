import React, { useCallback } from "react";
import { Select } from "antd";
import { SPLIT_FLAG } from "@/utils/constant";
const { Option } = Select;
const EditSelect = (props: any) => {
  const { value, onChange, options = [], needLabelAndValue = false, ...rest } = props;
  const handleChange = useCallback((val, options) => {
    let result = val;
    if (needLabelAndValue) {
      const { value, children } = options;
      result = `${children}${SPLIT_FLAG}${value}`;
    }
    onChange?.(result);
  }, []);
  const filterOption = (inputValue, option) => {
    let value: any = option!.children;
    return value?.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1;
  };
  // 处理默认值 如果 为 null/undefined 直接返回，
  // 否则 转为字符类型
  const returnDefalutValue = val => {
    if (val === null || val === void 0) {
      return val;
    }
    return val + "";
  };
  return (
    <Select defaultValue={returnDefalutValue(value)} onChange={handleChange} filterOption={filterOption} placeholder="请选择" allowClear {...rest}>
      {options.map(item => {
        return (
          <Option {...item} value={item.value + ""} key={item.value}>
            {item.label}
          </Option>
        );
      })}
    </Select>
  );
};

export default React.memo(EditSelect);
