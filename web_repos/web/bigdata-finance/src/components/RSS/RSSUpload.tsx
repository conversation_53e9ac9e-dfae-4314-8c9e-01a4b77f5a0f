// @ts-nocheck
import { useEffect, useState } from "react";
import { Upload, message, Spin } from "antd";
import { PlusOutlined } from "@ant-design/icons";
import { uploadhref, env } from "@/utils/url.js";
import axios from "axios";
import styles from "./index.less";

const RSSUpload = props => {
  const {
    typeCode = "BIG_DATA_FINANCE",
    type = [],
    height = 0,
    width = 0,
    size,
    employeeId = "123",
    fileChange,
    fileRemove,
    fileLength = 0,
    fileList = [],
    listType = "picture-card",
    accept = "image/*",
    multiple = false,
    disabled = false
  } = props;

  const [urlArr, setUrlArr] = useState([]);
  const [uploading, setUploading] = useState(false);
  // 生成随机名
  const randomFileName = () => {
    return Date.now().toString(36) + Math.random().toString(36).substr(2, 6);
  };
  const scaleCheck = async file => {
    return new Promise(resolve => {
      let _URL = window.URL || window.webkitURL;
      let img = new Image();
      img.onload = function () {
        resolve(img);
      };
      img.src = _URL.createObjectURL(file.originFileObj);
    });
  };
  // 上传前检测
  const beforeUploadCheck = async file => {
    // 文件类型检测
    let fileTypeResult = true;
    if (!type.includes(file.type) && type.length > 0) {
      fileTypeResult = false;
      message.warning("上传文件类型不正确，请重试！");
    }
    // 文件大小检测
    let sizeResult = true;
    if (file.size / 1024 > size) {
      sizeResult = false;
      message.warning(`上传文件大于${size}k，请重试！`);
    }
    // 宽高检测
    let scaleResult = true;
    if (height || width) {
      let imgFile = await scaleCheck(file);
      if (height && imgFile.height !== height) {
        scaleResult = false;
        message.warning(`上传文件高度不正确，请重试！`);
      }
      if (width && imgFile.width !== width) {
        scaleResult = false;
        message.warning(`上传文件宽度不正确，请重试！`);
      }
    }
    return fileTypeResult && sizeResult && scaleResult;
  };

  // oss上传
  const uploadOSS = async (file, entry, fileName) => {
    let host = "https://test-static.xinc818.com";
    let ossResultUrl = "";
    if (env === "prod" || env === "gray") {
      host = "https://static.xinc818.com";
    }
    if (file.type.includes("image/")) {
      let imgFile = await scaleCheck(file);
      ossResultUrl = encodeURI(`${host}/${entry?.key}`) + `?_w=${imgFile.width}&_h=${imgFile.height}&_size=${file.size}`;
    } else {
      ossResultUrl = encodeURI(`${host}/${entry?.key}`);
    }
    const ossData = new FormData();
    ossData.append("key", entry.key);
    ossData.append("policy", entry.policy);
    ossData.append("OSSAccessKeyId", entry.accessId);
    ossData.append("success_action_status", 200);
    ossData.append("Signature", entry.signature);
    ossData.append("file", file.originFileObj);
    axios
      .post(entry.host, ossData, {
        headers: {
          "Content-Type": "multipart/form-data"
        }
      })
      .then(() => {
        fileChange?.(ossResultUrl, file, fileName);
        setUploading(false);
      })
      .catch(() => {
        setUploading(false);
      });
  };
  // RSS 服务处理
  const RSSRequest = async file => {
    setUploading(true);
    const fileName = file.name;
    const checkResult = await beforeUploadCheck(file);
    if (checkResult) {
      file.name = `${randomFileName() + fileName.substr(fileName.lastIndexOf("."))}`;
      axios
        .post(`${uploadhref}rss/public/signature/getSignature`, {
          typeCode: typeCode,
          resourceName: file.name,
          createTask: false,
          operator: employeeId
        })
        .then(res => {
          uploadOSS(file, res.data.entry || {}, fileName);
        })
        .catch(() => {
          setUploading(false);
        });
    } else {
      setUploading(false);
    }
  };

  const onChangeHandle = (fileObj: any) => {
    const { file } = fileObj;
    if (file.status === "done") {
      RSSRequest(file);
    }
  };

  const onRemoveHandle = file => {
    fileRemove?.(file);
  };

  useEffect(() => {
    let arr = [];
    fileList.forEach((item, index) => {
      arr.push({
        uid: index,
        url: item
      });
    });
    setUrlArr(arr);
  }, [fileList]);

  return (
    <Spin spinning={uploading}>
      {listType === "picture-card" ? (
        <div className={styles.xzUpload} style={{ display: "flex", justifyContent: "flex-start" }}>
          <Upload listType={listType} method="GET" fileList={urlArr} onRemove={onRemoveHandle} style={{ width: "auto" }} disabled={disabled}></Upload>
          {(!fileLength || fileLength > fileList.length) && (
            <Upload
              action={require("../../assets/images/uploadDefault.jpg")}
              method="GET"
              accept={accept}
              listType={listType}
              multiple={multiple}
              showUploadList={false}
              onChange={onChangeHandle}
              disabled={disabled}
            >
              <div>
                <PlusOutlined />
                <div style={{ marginTop: 8 }}>点击上传</div>
              </div>
            </Upload>
          )}
        </div>
      ) : (
        <Upload
          action={require("../../assets/images/uploadDefault.jpg")}
          method="GET"
          accept={accept}
          listType={listType}
          fileList={urlArr}
          multiple={multiple}
          onChange={onChangeHandle}
          onRemove={onRemoveHandle}
          disabled={disabled}
        >
          {(!fileLength || fileLength > fileList.length) && (
            <div>
              <PlusOutlined />
              <div style={{ marginTop: 8 }}>点击上传</div>
            </div>
          )}
        </Upload>
      )}
    </Spin>
  );
};

export default RSSUpload;
