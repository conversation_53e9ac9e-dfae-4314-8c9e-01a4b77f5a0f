import React, { useCallback, useMemo } from "react";
import DebounceSelect from "@/components/DebounceSelect";
import { SPLIT_FLAG } from "@/utils/constant";

const EditDebounceSelect = (props: any) => {
  const { value, onChange, fetch, placeholder = "请搜索", ...rest } = props;
  const handleChange = useCallback(val => {
    const names = val.reduce((total, cur) => {
      total.push(cur.value.split(SPLIT_FLAG)[0]);
      return total;
    }, []);
    onChange?.(names.join("/"));
  }, []);
  const values = useMemo(() => {
    if (value === "") {
      return [];
    }
    return value?.split("/").map(item => {
      return {
        label: item,
        value: item
      };
    });
  }, [value]);
  return (
    <DebounceSelect
      value={values}
      inputHeight={"30px"}
      placeholder={placeholder}
      onChange={handleChange}
      fetchOptions={fetch ?? (async () => {})}
      {...rest}
    />
  );
};

export default React.memo(EditDebounceSelect);
