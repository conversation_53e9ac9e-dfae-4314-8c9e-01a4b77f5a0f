import React, { useCallback } from "react";
import SearchSelectInput from "@/components/SearchSelectInput";
const EditSearchSelectInput = (props: any) => {
  const { value, onChange, fetch, placeholder = "请搜索", ...rest } = props;
  const handleChange = useCallback(val => {
    onChange?.(val);
  }, []);

  return (
    <SearchSelectInput
      defaultValue={value}
      inputHeight={"30px"}
      placeholder={placeholder}
      onChange={handleChange}
      fetchOptions={fetch ?? (async () => {})}
      {...rest}
    />
  );
};

export default React.memo(EditSearchSelectInput);
