import React, { useCallback, useMemo } from "react";
import styles from "./index.less";
import RSSUpload from "@/components/RSS/RSSUpload";

const EditRSSUpload = (props: any) => {
  const { value, onChange, customStyle = {}, ...rest } = props;
  let item;
  try {
    item = JSON.parse(value || "[]");
  } catch {
    item = [value];
  }
  const fileList = useMemo(() => {
    return item ? item : [];
  }, [item]);
  const fileChange = useCallback(url => {
    item.push(url);
    onChange?.(JSON.stringify(item));
  }, []);
  const fileRemove = useCallback(({ url }: any) => {
    const index = item.indexOf(url);
    item.splice(index, 1);
    onChange?.(JSON.stringify(item));
  }, []);
  return (
    <div className={styles["editrssupload"]} style={customStyle} key={value}>
      <RSSUpload
        fileChange={fileChange}
        fileRemove={fileRemove}
        onRemove={false}
        multiple
        size={10 * 1024 * 1024}
        type={["image/jpg", "image/jpeg", "image/png"]}
        fileLength={50}
        fileList={fileList}
        {...rest}
      />
    </div>
  );
};

export default React.memo(EditRSSUpload);
