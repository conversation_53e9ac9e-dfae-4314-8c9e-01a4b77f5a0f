import React, { useState, useEffect } from "react";
import { Modal, Form, Button } from "antd";
import RSSUpload from "@/components/RSS/RSSUpload";
import styles from "./index.less";
import { pushImportHistory } from "@/utils";
import { SIZE } from "@/utils/constant";
export interface UploadExcelModalProps {
  // 显示隐藏 modal
  visible: boolean;
  // 关闭modal
  close: () => void;
  // 关闭 modal后 刷新页面
  fresh?: () => void;
  // 标题
  title: React.ReactNode;
  // 上传文件模板
  tempUrl?: string;
  // 上传接口
  api?: any;
  // 上传文件类型
  targetType?: number;
  // 提示信息
  infos?: React.ReactNode[];
}
const UploadExcelModal = (props: UploadExcelModalProps) => {
  const { visible, close, fresh, title, tempUrl, api, targetType, infos = [] } = props;
  const [form] = Form.useForm();
  const [uploadParams, setUploadParams] = useState(() => ({ filePath: "", fileName: "", targetType }));
  const [modalOkDisable, setModalOkDisable] = useState<boolean>(true);
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);

  useEffect(() => {
    setModalOkDisable(uploadParams.filePath ? false : true);
  }, [uploadParams]);

  const handleExcelCancel = () => {
    form.resetFields();
    close?.();
  };

  const requestAddItemFromExcel = async () => {
    const { status } = await api?.(uploadParams);
    setConfirmLoading(false);
    if (status) {
      close?.();
      fresh?.();
      pushImportHistory();
    }
  };

  const handleExcelOk = (e: React.BaseSyntheticEvent) => {
    e.preventDefault();
    setConfirmLoading(true);
    requestAddItemFromExcel();
  };

  const fileChangeHandle = (url: string, fileName: string) => {
    setUploadParams({ ...uploadParams, fileName, filePath: url });
  };
  const onRemoveHandle = () => {
    setUploadParams({ ...uploadParams, fileName: "", filePath: "" });
  };
  // 下载导入商品Excel模板
  const handleDownloadExample = (e: React.BaseSyntheticEvent) => {
    e.stopPropagation();
    window.open(tempUrl);
  };
  return (
    <Modal
      centered={true}
      className={styles.uploadExcelModal}
      title={title}
      open={visible}
      onOk={e => handleExcelOk(e)}
      onCancel={e => handleExcelCancel(e)}
      maskClosable={false}
      confirmLoading={confirmLoading}
      okButtonProps={{
        size: SIZE,
        disabled: modalOkDisable
      }}
      cancelButtonProps={{
        size: SIZE
      }}
    >
      <Form form={form}>
        <Form.Item>
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              flexDirection: "column",
              alignItems: "center"
            }}
          >
            <RSSUpload
              accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
              fileChange={(ossResultUrl: string, file: any, fileName: string) => fileChangeHandle(ossResultUrl, fileName)}
              fileRemove={() => onRemoveHandle()}
              onRemove={false}
              size={30 * 1024 * 1024}
              type={["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "application/vnd.ms-exce", "text/csv"]}
              fileLength={1}
              fileList={uploadParams.filePath ? [uploadParams.filePath] : []}
            />
            <ol className={styles.infoList}>
              <li>
                <span>请下载此模板</span>
                <Button type="link" onClick={handleDownloadExample}>
                  [下载模板]
                </Button>
              </li>
              {infos?.map((info, i) => {
                return <li key={i}>{info}</li>;
              })}
            </ol>
          </div>
        </Form.Item>
      </Form>
    </Modal>
  );
};
export default React.memo(UploadExcelModal);
