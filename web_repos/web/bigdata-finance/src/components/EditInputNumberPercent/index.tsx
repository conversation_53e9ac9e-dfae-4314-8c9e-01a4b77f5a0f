import React, { useCallback } from "react";
import { InputNumber } from "antd";
import { handlePercentage, handleNumToPercentage } from "@/utils";
import { SIZE } from "@/utils/constant";
const EditInputNumber = (props: any) => {
  const { value, onChange, ...rest } = props;
  const handleChange = useCallback(val => {
    let res;
    if (typeof val === "number") {
      res = handleNumToPercentage(val);
    } else {
      res = val;
    }
    onChange?.(res);
  }, []);
  const handleValue = val => {
    if (typeof val === "number") {
      return handlePercentage(val);
    }
    return val;
  };
  return (
    <InputNumber
      size={SIZE}
      value={handleValue(value)}
      controls={false}
      onChange={handleChange}
      parser={value => value?.replace("%", "")}
      {...rest}
    />
  );
};

export default React.memo(EditInputNumber);
