import { Modal, message } from "antd";
import { useEffect, useState, useRef } from "react";
import { ProForm, ProFormText } from "@ant-design/pro-components";
import type { ProFormInstance } from "@ant-design/pro-components";
import { ValidateStatus } from "antd/es/form/FormItem";
import { judgeWholeValue } from "@/utils";
import { SIZE } from "@/utils/constant";
import apis from "@/services/users";
import { useModel } from "@umijs/max";
interface IvalueIsWhole {
  oldPassword?: boolean;
  newPassword?: boolean;
  confirmNewPassword?: boolean;
  [key: string]: any;
}
export const PASSWORDMESSAGE = "密码由大小写字母、数字组成,长度7-29位";
// 密码正则表达式
// 数字字母
export const PASSWORDREG = /([a-zA-Z0-9]).{7,29}/;
const ModifyPasswordModal = (props: any) => {
  const { initialState } = useModel("@@initialState");
  const { loginout } = initialState;
  const { close, visible } = props;
  const formRef = useRef<ProFormInstance>();
  const [modalBtnDisable, setModalBtnDisable] = useState<boolean>(true);
  const [tempFormValue, setTempFormValue] = useState<object>({});
  const [oldPasswordValidateStatus, setOldPasswordValidateStatus] = useState<ValidateStatus>("");
  const [helpObj, setHelpObj] = useState<object>({});

  const valueIsWhole: IvalueIsWhole = {
    oldPassword: false,
    newPassword: false,
    confirmNewPassword: false
  };
  // 内容变化时，设置确定按钮的 disable状态
  useEffect(() => {
    const wholeValue = judgeWholeValue(valueIsWhole, tempFormValue);
    // 是佛存在未赋值的参数
    const hasFalse = Object.keys(wholeValue).some((item: string) => {
      return wholeValue[item] === false;
    });
    setModalBtnDisable(hasFalse ? true : false);
  }, [tempFormValue]);
  const handleCancel = () => {
    setHelpObj({});
    close?.();
  };
  const handleAddOk = () => {
    formRef?.current?.validateFields().then(async (values: any) => {
      const { status = false, code, message: info } = await apis.modifyPassword(values);
      if (status) {
        loginout();
        message.success("修改成功！");
        handleCancel();
      } else if (code === 1000105) {
        setOldPasswordValidateStatus("error");
        setHelpObj({ help: info || "旧密码错误" });
      }
    });
  };

  const changeValues = (values: { [key: string]: any }) => {
    let FormValue = JSON.parse(JSON.stringify(tempFormValue));
    let result = {};
    const keys = Object.keys(values);
    const key = keys[0];
    const item = values[key];
    if (typeof item === "string") {
      if (item === "") {
        result = {
          ...FormValue,
          [key]: ""
        };
      } else {
        result = {
          ...FormValue,
          ...values
        };
      }
    }
    setTempFormValue({
      ...result
    });
  };
  return (
    <Modal
      title={"修改用户登录密码"}
      open={visible}
      onOk={handleAddOk}
      onCancel={handleCancel}
      maskClosable={false}
      centered={true}
      closable={false}
      okButtonProps={{
        disabled: modalBtnDisable,
        size: SIZE
      }}
      cancelButtonProps={{
        size: SIZE
      }}
    >
      <ProForm
        size={SIZE}
        layout="horizontal"
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 16 }}
        submitter={{ render: () => "" }}
        onValuesChange={changeValues}
        formRef={formRef}
      >
        <ProFormText.Password
          name="oldPassword"
          label="您的登录密码"
          placeholder="请输入"
          rules={[{ required: true, message: "请填写旧密码" }]}
          hasFeedback
          validateStatus={oldPasswordValidateStatus}
          {...helpObj}
        />
        <ProFormText.Password
          name="newPassword"
          label="用户新密码"
          placeholder="请输入"
          validateTrigger="onBlur"
          rules={[
            { required: true, message: "请填写新密码" },
            {
              pattern: PASSWORDREG,
              message: PASSWORDMESSAGE
            }
          ]}
        />
        <ProFormText.Password
          name="confirmNewPassword"
          label="确认用户新密码"
          placeholder="请输入"
          validateTrigger="onBlur"
          rules={[
            {
              required: true,
              pattern: PASSWORDREG,
              validator: async (rule: any, value) => {
                const pass = formRef?.current?.getFieldValue("newPassword");
                if (!rule?.pattern.test(value)) {
                  return Promise.reject(PASSWORDMESSAGE);
                }
                if (pass !== value) {
                  return Promise.reject("密码不一致");
                }
                return Promise.resolve();
              }
            }
          ]}
        />
      </ProForm>
    </Modal>
  );
};
export default ModifyPasswordModal;
