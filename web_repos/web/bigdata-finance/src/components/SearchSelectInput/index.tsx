import type { AutoCompleteProps } from "antd";
import React, { useMemo, useRef, useState, useEffect } from "react";
import { AutoComplete, Input, Form } from "antd";
import { debounce } from "lodash-es";
import { SearchOutlined } from "@ant-design/icons";
import styles from "./index.less";
import { SIZE } from "@/utils/constant";

export interface SearchSelectInputProps<VT> extends Omit<AutoCompleteProps, "onSearch" | "options" | "children"> {
  fetchOptions: (search: string) => Promise<VT[]>;
  /** 为了在当前组件内部能够修改 AutoComplete组件的值，onSelect 包装一层调用 */
  onSelectChange: (value: string, option: IOptions) => void;
  debounceTime?: number;
  placeholder?: string;
}

function SearchSelectInput<ValueType extends { disabled?: boolean; value: React.Key; placeholder?: string; label: React.ReactNode } = any>({
  fetchOptions,
  debounceTime = 666,
  placeholder = "搜索",
  onSelectChange,
  ...props
}: SearchSelectInputProps<ValueType>) {
  const [searchOptions, setSearchOptions] = useState<any>([]);
  const fetchRef = useRef(0);
  const [inputForm] = Form.useForm();
  const formRef = useRef(null);
  useEffect(() => {
    inputForm?.setFieldsValue({ input: props.value });
  }, [props.value]);

  const handleSearch = useMemo(() => {
    const fetchApi = (value: string) => {
      setSearchOptions([]);
      if (value) {
        fetchRef.current++;
        const fetchId = fetchRef.current;
        fetchOptions(value).then(opts => {
          if (fetchId !== fetchRef.current) return;
          setSearchOptions(opts);
        });
      }
    };
    return debounce(fetchApi, debounceTime);
  }, [fetchOptions, debounceTime]);

  return (
    <div className={styles["search-select-input"]}>
      <Form form={inputForm} ref={formRef} size={SIZE}>
        <Form.Item name="input">
          <AutoComplete
            allowClear
            style={{ width: "100%" }}
            filterOption={false}
            onSearch={handleSearch}
            options={searchOptions}
            onSelect={(value, option) => {
              /** 在组件 onSelect 事件之前手动修改 组件的值， 避免出现值的闪烁 */
              inputForm?.setFieldsValue({ input: option?.name });
              onSelectChange?.(value, option);
            }}
            {...props}
          >
            <Input placeholder={placeholder} prefix={<SearchOutlined />} />
          </AutoComplete>
        </Form.Item>
      </Form>
    </div>
  );
}

export default React.memo(SearchSelectInput);
