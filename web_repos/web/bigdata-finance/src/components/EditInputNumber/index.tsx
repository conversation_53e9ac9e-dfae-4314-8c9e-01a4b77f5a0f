import React, { useCallback } from "react";
import { InputNumber } from "antd";
import BigNumber from "bignumber.js";
const EditInputNumber = (props: any) => {
  const { value, divide = 100, onChange, ...rest } = props;
  const handleValue = val => {
    return +new BigNumber(val ?? 0).dividedBy(divide);
  };
  const handleChange = useCallback(val => {
    onChange?.(+new BigNumber(val).multipliedBy(divide));
  }, []);

  return <InputNumber value={handleValue(value)} placeholder="请输入" onChange={handleChange} controls={false} {...rest} />;
};

export default React.memo(EditInputNumber);
