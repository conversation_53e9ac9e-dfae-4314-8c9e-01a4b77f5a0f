import React from "react";
import styles from "./index.less";
import classNames from "classnames";

const Res: React.FC<IOptions> = props => {
  const { value, label, type, children } = props;
  return (
    <div key={value} className={styles["target-item"]}>
      <span className={styles["target-item-label"]}>{label}</span>
      <span
        className={classNames(
          styles["target-item-value"],
          type === "waring" ? styles["target-item-value_waring"] : styles["target-item-value_common"]
        )}
      >
        {children}
      </span>
    </div>
  );
};

export default React.memo(Res);
