export interface CategoryProps {
  onChange?: (val: any) => void;
  style?: React.CSSProperties;
  multiple?: boolean;
  label?: string;
  colon?: true;
  align?: "none" | "center";
  cateLevel?: number;
  value?: any;
  returnFormat?: string;
}

export type CategoryListProps = Array<{
  label?: string;
  value?: string;
  title?: string;
  children: ChildrenProps;
}>;
export interface ICategoryValue {
  stdCatLv1IdList: string[];
  stdCatLv2IdList: string[];
  stdCatLv3IdList: string[];
  stdCatLv4IdList: string[];
  stdCatLv5IdList: string[];
}
export type ChildrenProps = CategoryListProps;
