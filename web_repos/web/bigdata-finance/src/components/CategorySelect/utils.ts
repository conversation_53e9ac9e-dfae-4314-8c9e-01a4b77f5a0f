// 根据下标获取数组/字符串 某一项，默认 最后一项
export const getLastItem = (value: string | Array<any>, index = -1) => {
  if (!value?.length) return "";
  if (index + 1 > value.length) return "";
  if (index === -1) return value[value.length - 1];
  return value[index];
};

// 类目树
export const handleTreeDataDeep = ({ list = [], parent = "", isMultiple, maxLevel }) => {
  return list.map(({ categoryName, id, child, level }) => {
    let label, value;
    let newParent;
    if (isMultiple) {
      label = "title";
      value = "value";
      newParent = parent ? parent + "-->" + id : "" + id;
    } else {
      label = "label";
      value = "value";
      newParent = id;
    }
    return {
      [label]: categoryName,
      [value]: newParent,
      ...(child?.length && level < maxLevel ? { children: handleTreeDataDeep({ list: child, parent: newParent, isMultiple, maxLevel }) } : {})
    };
  });
};
