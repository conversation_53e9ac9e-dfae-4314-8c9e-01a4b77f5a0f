.echart-card {
  &-title {
    &_label {
      height: 28px;
      font-weight: 800;
      font-size: 14px;
      color: #1d2129;
      line-height: 28px;
    }
  }

  :global(.ant-card-head) {
    padding: 0 !important;
    border: none;
  }

  :global(.ant-card-body) {
    padding: 0 !important;
  }

  :global(.ant-card-head-wrapper) {
    display: block;
  }

  :global(.ant-card-head-title) {
    padding: 10px 0 !important;
  }

  :global(.ant-select-selector) {
    height: 28px !important;
  }

  :global(.ant-select-selection-item) {
    line-height: 26px !important;
  }

  :global(.ant-select-selection-placeholder) {
    line-height: 26px !important;
  }

  :global(.ant-radio-group-small .ant-radio-button-wrapper) {
    height: 28px;
    line-height: 28px;
    padding: 0 12px;
  }

  :global(.ant-radio-button-wrapper:first-child) {
    border-radius: 2px 0 0 2px;
  }

  :global(.ant-radio-button-wrapper:last-child) {
    border-radius: 0 2px 2px 0;
  }

  :global(.ant-empty-description) {
    color: rgba(0, 0, 0, 25%);
  }
  // :global(.ant-tabs-nav)::before {
  //   display: none;
  // }

  // :global(.ant-tabs-small > .ant-tabs-nav .ant-tabs-tab) {
  //   padding: 10px 0;
  // }
}

.card-content {
  overflow-y: hidden;
  overflow-x: auto;
}

.chart-container {
  height: 100%;
  width: 100%;
  overflow: hidden;
  .chart-container-table {
    :global(.ant-spin-container) {
      padding: 0px !important;
    }
  }
}

.odd {
  background-color: #f7f8fa;

  :global(.ant-table-cell-fix-left) {
    background-color: #f7f8fa;
  }
}

.even {
  background-color: #fff;

  :global(.ant-table-cell-fix-left) {
    background-color: #fff;
  }
}

.chart-container-table {
  height: 100%;
  width: 100%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  margin-bottom: 20px;
}
