import React, { useState, useRef, useEffect } from "react";
import { Card, Spin, Select, Space, Radio, Tabs } from "antd";
import styles from "./index.less";
import type { IEchartsCardProps } from "./types";
import { EchartType } from "./index";
const classPrefix = "echart-card";
const getTabInfo = (list: any[], tabKey: any) => {
  const [item = {}]: any = list.filter((el: any) => el.value === tabKey);
  return {
    ...item,
    type: item.type ?? "bar",
    tabKey,
    $label1: item.$label1 ?? "",
    $label2: item.$label2 ?? ""
  };
};
const EchartsCard: React.FC<IEchartsCardProps> = props => {
  // height = 230;
  const { label, api, selects = [], radios, tabs, searchParams = {}, bodyStyle = {} } = props;

  const [loading, setLoading] = useState(false);
  const divRef = useRef<any>();
  const [data, fillData] = useState<any[]>([]);
  const [dataSource, setDataSource] = useState([]);
  const [selectValue, setSelectValue] = useState(() => {
    return selects?.[0]?.defaultValue;
  });
  const [tabInfo, setTabInfo] = useState(() => {
    if (tabs) {
      return getTabInfo(tabs?.options ?? [], tabs?.defaultValue);
    }
    return getTabInfo(radios?.options ?? [], radios?.defaultValue);
  });

  useEffect(() => {
    setLoading(true);
    if (api && searchParams.startTime) {
      api?.({ ...searchParams }, tabInfo)
        .then(res => {
          setDataSource(res);
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [searchParams, tabInfo]);
  useEffect(() => {
    if (selectValue) {
      fillData(
        dataSource.map(item => {
          return { ...item, value: item[selectValue] || 0 };
        })
      );
    } else {
      fillData(dataSource);
    }
  }, [dataSource, selectValue]);
  const returnTitle = () => {
    return (
      <>
        {selects?.length || label ? (
          <div className="ant-card-head-wrapper">
            <div className="ant-card-head-title">
              <Space size={16}>
                {label ? <div className={styles[`${classPrefix}-title_label`]}>{label}</div> : null}
                {selects.map(item => {
                  return (
                    <Select
                      key={item.value}
                      placeholder="请选择"
                      style={{ width: "160px" }}
                      {...item}
                      onChange={e => {
                        setSelectValue(e);
                      }}
                    ></Select>
                  );
                })}
              </Space>
            </div>
          </div>
        ) : null}
      </>
    );
  };
  const returnRadio = () => {
    return (
      <>
        {radios?.options?.length > 1 ? (
          <div style={{ textAlign: "center" }}>
            <Radio.Group
              defaultValue={radios?.defaultValue}
              onChange={e => {
                setTabInfo(getTabInfo(radios?.options ?? [], e.target.value));
                radios?.onChange?.(e.target.value);
              }}
              size="small"
            >
              {(radios?.options ?? []).map((item: any) => (
                <Radio.Button key={item.value} value={item.value}>
                  {item.label}
                </Radio.Button>
              ))}
            </Radio.Group>
          </div>
        ) : null}
      </>
    );
  };
  const returnTabsTitle = () => {
    return (
      <div className="ant-card-head-wrapper customerservice-global-tabs">
        {tabs?.options?.length > 1 ? (
          <Tabs
            className="ant-card-head-title"
            defaultActiveKey={tabs.defaultValue}
            onChange={e => {
              setTabInfo(getTabInfo(tabs?.options ?? [], e));
              tabs?.onChange?.(e);
            }}
            size="small"
          >
            {(tabs?.options ?? []).map((item: any) => (
              <Tabs.TabPane tab={item.label} key={item.value}></Tabs.TabPane>
            ))}
          </Tabs>
        ) : null}
      </div>
    );
  };
  const EchartTypeProps: any = {
    data: data,
    selectValue: selectValue,
    ...tabInfo,
    columns: (typeof tabInfo.columns === "function" ? tabInfo.columns(searchParams) : tabInfo.columns) ?? [],
    tableProps: tabInfo.tableProps ?? {},
    containerStyle: tabInfo.containerStyle ?? {},
    optionAfter: tabInfo.optionAfter,
    $label1: tabInfo.$label1,
    $label2: tabInfo.$label2
  };
  return (
    <Card
      className={styles[`${classPrefix}`]}
      bordered={false}
      styles={{
        body: {
          padding: 0,
          overflow: "hidden",
          ...bodyStyle
        }
      }}
    >
      {tabs ? returnTabsTitle() : null}
      {returnTitle()}
      <Spin spinning={loading}>
        <div ref={divRef}>
          {returnRadio()}
          <EchartType {...EchartTypeProps} />
        </div>
      </Spin>
    </Card>
  );
};
export default React.memo(EchartsCard);
