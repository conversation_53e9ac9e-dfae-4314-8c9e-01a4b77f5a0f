import type { SelectProps } from "antd/es/select";
import type { IProps as TableListProps } from "@/components/TableList";
import React from "react";

export interface IChartProps {
  type?: "bar" | "line" | "table" | "rank" | "pie";
  tableProps?: TableListProps;
  [key: string]: any;
}
export interface ISelectProps extends SelectProps {
  name: string;
}

export interface IRadiosProps {
  defaultValue: string;
  onChange?: (val) => void;
  options: {
    type: IChartProps["type"];
    label?: string;
    value: string;
    // 图表相关
    optionAfter?: (options, data, selectValue?: string) => any;

    columns?: any[] | ((p) => Record<string, any>);
    tableProps?: any;
    containerStyle?: React.CSSProperties;
  }[];
}

export interface IEchartsCardProps {
  label?: string | React.ReactNode;
  // 带有 select 属性时，只进行第一次查询，之后的查询只是进行值的切换，目前只处理了 select 长度为1时的场景
  selects?: ISelectProps[];
  bodyStyle?: React.CSSProperties;
  height?: number;
  // children?: React.ReactNode;
  api?: Promise<any>;
  searchParams?: Record<string, any>;
  radios?: IRadiosProps;
  tabs?: IRadiosProps;
  onTabChange?: (val) => void;
  onRadioChange?: (val) => void;
}
