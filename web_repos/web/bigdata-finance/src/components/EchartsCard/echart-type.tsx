import React, { useEffect, useRef, useState } from "react";
import { Empty } from "antd";
import { debounce } from "lodash-es";
import styles from "./index.less";
import * as echarts from "echarts";
import TableList from "@/components/TableList";
import type { IChartProps } from "./types";
import { getColor } from "./config";
export const tooltipsHtml = ({ name, valueLabel, $label1 = "", $label2 = "" }: any) => {
  return `<div style="margin: 0px 0 0;line-height:1;"><div style="font-size:14px;color:#666;font-weight:400;line-height:1;">${$label1}${name}</div><div style="margin: 10px 0 0;line-height:1;"><div style="margin: 0px 0 0;line-height:1;"><div style="margin: 0px 0 0;line-height:1;"><span style="float:left;font-size:14px;color:#666;font-weight:900">${$label2}${valueLabel}</span><div style="clear:both"></div></div><div style="clear:both"></div></div><div style="clear:both"></div></div><div style="clear:both"></div></div>`;
};

export const tooltipsAxisHtml = ({ label, valueList }) => {
  let valueListHtml = valueList.map(item => `<div>${item.label}：${item.value}</div>`).join("");
  return `<div style="font-size:14px;line-height:24px;"><div>${label}</div>${valueListHtml}</div>`;
};
export const dataZoom: any = {
  showDetail: false,
  showDataShadow: false,
  zoomLock: true,
  backgroundColor: "rgba(47,69,84,0)"
};
const defaultData: any[] = [];

const EchartType = (props: IChartProps) => {
  const {
    height,
    data = defaultData,
    type = "bar",
    tableProps,
    optionAfter = (d: any) => d,
    $label1,
    $label2,
    containerStyle,
    onLegendChange = undefined,
    onClickMonth
  } = props;
  const myChart = useRef<any>();
  const divRef = useRef<any>();
  const containerRef = useRef<any>();
  const [isEmpty, setEmpty] = useState<boolean>(false);
  const [width, setWidth] = useState<number | undefined>();
  const getSeriesData = (list: any[]) => {
    return list.map(({ value, label, color, ...item }: any, index: number) => {
      return {
        value,
        name: label,
        itemStyle: {
          color: color ?? getColor(index)
        },
        ...item
      };
    });
  };
  useEffect(() => {
    setEmpty(false);

    if (type === "table") {
      if (myChart.current) {
        myChart.current.dispose();
        myChart.current = undefined;
      }
    } else {
      if (containerRef.current) {
        myChart.current = echarts.init(containerRef.current, undefined, {
          renderer: "svg"
        });
      }

      const newData = data;
      if (newData.length) {
        myChart.current?.clear();
        const defalutOption: any = {
          tooltip: {
            trigger: "axis",
            formatter: ([res]: any) => {
              const sdata: any = res.data;
              if (sdata) {
                return tooltipsHtml({
                  $label1: $label1 ? `${$label1}：` : "",
                  $label2: $label2 ? `${$label2}：` : "",
                  name: sdata?.name,
                  valueLabel: sdata.labelValue ?? sdata.value
                });
              }
              return "";
            },
            axisPointer: {
              type: "shadow"
            },
            confine: true
          },
          grid: {
            left: "68px",
            right: "12px",
            bottom: "36px",
            top: "24px",
            containLabel: false
          }
        };
        let option = {};

        switch (type) {
          case "line":
            option = {
              ...defalutOption,
              xAxis: [
                {
                  data: data?.map(item => item.label),
                  type: "category",
                  axisTick: {
                    alignWithLabel: true
                  },
                  axisLabel: {
                    formatter: v => {
                      return `${v}`;
                    }
                  }
                }
              ],
              yAxis: [],
              series: []
            };
            break;
          case "rank":
            option = {
              ...defalutOption,

              grid: {
                left: "12px",
                right: "80px",
                bottom: "0",
                top: "12px",
                containLabel: true
              },
              xAxis: {
                // max 属性会导致 小于0的数据不显示
                // max: "dataMax",
                splitLine: {
                  show: false
                },
                axisTick: {
                  show: false
                },
                axisLine: {
                  show: false
                },
                show: false
              },
              yAxis: {
                inverse: true,
                type: "category",
                data: data.map((el: any) => el.label)
              },
              tooltip: {
                show: false
              },
              series: [
                {
                  type: "bar",
                  data: getSeriesData(data),
                  label: {
                    show: true,
                    position: "inside",
                    color: "#fff",
                    fontSize: 12,
                    // valueAnimation: true,
                    formatter: ({ data }: any) => {
                      return `${data.labelValue}`;
                    }
                  },
                  itemStyle: {
                    borderRadius: [0, 4, 4, 0]
                  },
                  barWidth: 20,
                  barMaxWidth: "20px",
                  barMinHeight: 60,
                  barGap: "-100%"
                }
              ],
              dataZoom:
                data.length > 10
                  ? [
                      {
                        type: "slider",
                        startValue: 0,
                        endValue: 10,
                        yAxisIndex: [0],
                        filterMode: "empty",
                        height: "90%",
                        width: 0,
                        right: "16px",
                        ...dataZoom
                      }
                    ]
                  : []
            };
            break;
          case "pie":
            option = {
              ...defalutOption,
              tooltip: {
                trigger: "item",
                formatter: function (parms) {
                  const sdata: any = parms.data;
                  if (sdata) {
                    return tooltipsHtml({
                      $label1: $label1 ? `${$label1}：` : "",
                      $label2: $label2 ? `${$label2}：` : "",
                      name: sdata?.name,
                      valueLabel: sdata.labelValue ?? sdata.value,
                      color: sdata.color
                    });
                  }
                }
              },
              series: [
                {
                  type: "pie",
                  data: getSeriesData(data),
                  // radius: [0, "70%"]
                  center: ["53%", "53%"],
                  radius: ["60%", "90%"]
                }
              ]
            };
            break;
          case "bar":
            option = {
              ...defalutOption,
              xAxis: {
                type: "category",
                data: data.map((el: any) => el.label),
                axisLabel: {
                  ...(data.length <= 6 && width && width > 450 ? {} : { interval: 0, rotate: 0, fontSize: 12 }),
                  overflow: "truncate",
                  lineHeight: 14,
                  height: 14
                  // inside: true
                }
              },
              yAxis: {
                type: "value",
                axisLabel: {
                  formatter: function (value: any, index: number) {
                    if (index === 0) return data[0]?.unit ? `(${data[0].unit})` : value;
                    return value;
                  }
                }
              },
              series: [
                {
                  type: "bar",
                  data: getSeriesData(data),
                  barWidth: "100%",
                  barMaxWidth: "20px"
                }
              ],
              dataZoom:
                data.length > 10
                  ? [
                      {
                        type: "slider",
                        startValue: 0,
                        endValue: 10,
                        xAxisIndex: [0],
                        filterMode: "empty",
                        width: "90%",
                        height: 0,
                        ...dataZoom
                      }
                    ]
                  : []
            };
            break;
        }
        myChart.current.setOption(optionAfter(option, getSeriesData(data)));
      } else {
        myChart.current?.clear();
        setEmpty(true);
      }
    }
  }, [data]);

  useEffect(() => {
    const refresh = debounce(() => {
      setWidth(divRef.current?.offsetWidth);
    }, 200);
    window.addEventListener("resize", refresh, false);
    setTimeout(() => {
      setWidth(divRef.current?.offsetWidth);
    }, 1000);
    return () => {
      window.removeEventListener("resize", refresh, false);
      if (myChart.current) {
        myChart.current.dispose();
      }
    };
  }, []);

  useEffect(() => {
    if (myChart.current) {
      if (onLegendChange) {
        myChart.current.on("legendselectchanged", onLegendChange);
      }
      myChart?.current.on("click", params => {
        onClickMonth?.(params);
      });
    }
    return () => {
      if (onLegendChange) {
        myChart.current.off("click");
        myChart.current.off("legendselectchanged", onLegendChange);
      }
    };
  }, [myChart.current, onLegendChange]);
  useEffect(() => {
    if (width && myChart.current) myChart.current.resize();
  }, [width]);
  return (
    <div ref={divRef} className={styles["chart-container"]} style={{ height, ...containerStyle }}>
      {type === "table" ? (
        <div className={styles["chart-container-table"]}>
          <TableList search={false} options={false} pagination={false} sticky {...tableProps} />
        </div>
      ) : null}
      <div
        ref={containerRef}
        style={{
          height: "100%",
          width,
          display: type === "table" ? "none" : ""
        }}
      ></div>
      <div
        style={{
          pointerEvents: "none",
          display: isEmpty ? "flex" : "none",
          position: "absolute",
          top: 0,
          left: 0,
          bottom: 0,
          right: 0,
          height: "100%",
          justifyContent: "center",
          alignItems: "center"
        }}
      >
        <Empty />
      </div>
    </div>
  );
};
export default React.memo(EchartType);
