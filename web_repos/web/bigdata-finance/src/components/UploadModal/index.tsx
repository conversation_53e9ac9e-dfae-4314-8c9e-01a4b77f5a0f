import React, { useState, useEffect } from "react";
import { Modal, Form, Button, Alert, AlertProps } from "antd";
import RSSUpload from "@/components/RSS/RSSUpload";
import styles from "./index.less";
import { SIZE } from "@/utils/constant";
import { pushImportHistory } from "@/utils";

export interface UploadModalProps {
  // 显示隐藏 modal
  visible: boolean;
  // 关闭modal
  close: () => void;
  // 关闭 modal后 刷新页面
  fresh?: () => void;
  // 自定义确认后操作
  afterOk?: () => void;
  // 标题
  title: React.ReactNode;
  // 上传文件模板
  tempUrl?: string;
  // 上传接口
  api?: any;
  // 上传文件类型
  targetType?: number;
  message?: AlertProps["message"];
}
const UploadModal = (props: UploadModalProps) => {
  const { visible, close, fresh, afterOk, title, tempUrl, api, targetType, message } = props;
  const [form] = Form.useForm();
  const [uploadParams, setUploadParams] = useState(() => ({ filePath: "", fileName: "", targetType }));
  const [modalOkDisable, setModalOkDisable] = useState<boolean>(true);
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);

  useEffect(() => {
    setModalOkDisable(uploadParams.filePath ? false : true);
  }, [uploadParams]);

  const handleExcelCancel = () => {
    Modal.destroyAll();
    close?.();
  };

  const handleExcelOk = async () => {
    form.validateFields().then(async values => {
      setConfirmLoading(true);
      const { status } = await api?.({ ...uploadParams, ...values });
      setConfirmLoading(false);
      if (status) {
        if (afterOk) {
          afterOk?.();
        } else {
          pushImportHistory();
          close?.();
          fresh?.();
        }
      }
    });
  };

  const fileChangeHandle = (url: string, fileName: string) => {
    setUploadParams({ ...uploadParams, fileName, filePath: url });
  };
  const onRemoveHandle = () => {
    setUploadParams({ ...uploadParams, fileName: "", filePath: "" });
  };
  // 下载导入商品Excel模板
  const handleDownloadExample = (e: React.BaseSyntheticEvent) => {
    e.stopPropagation();
    window.open(tempUrl);
  };
  return (
    <Modal
      centered={true}
      className={styles.uploadModal}
      title={title}
      open={visible}
      onOk={() => handleExcelOk()}
      onCancel={() => handleExcelCancel()}
      maskClosable={false}
      confirmLoading={confirmLoading}
      okButtonProps={{
        size: SIZE,
        disabled: modalOkDisable
      }}
      cancelButtonProps={{
        size: SIZE
      }}
    >
      <Form size={SIZE} form={form}>
        {message ? (
          <Form.Item>
            <Alert message={message} type="info" showIcon></Alert>
          </Form.Item>
        ) : null}

        <Form.Item>
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              flexDirection: "column",
              alignItems: "center"
            }}
          >
            <RSSUpload
              accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
              fileChange={(ossResultUrl: string, file: any, fileName: string) => fileChangeHandle(ossResultUrl, fileName)}
              fileRemove={() => onRemoveHandle()}
              onRemove={false}
              size={30 * 1024 * 1024}
              type={["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "application/vnd.ms-exce", "text/csv"]}
              fileLength={1}
              fileList={uploadParams.filePath ? [uploadParams.filePath] : []}
            />
          </div>
        </Form.Item>
        <Form.Item>
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              flexDirection: "column",
              alignItems: "center"
            }}
          >
            <Button type="link" onClick={handleDownloadExample}>
              [下载模板]
            </Button>
          </div>
        </Form.Item>
      </Form>
    </Modal>
  );
};
export default React.memo(UploadModal);
