import React, { useState, useEffect } from "react";
import styles from "./index.less";
export type WaterMarkProps = {
  gapX?: number;
  gapY?: number;
  zIndex?: number;
  width?: number;
  height?: number;
  rotate?: number;
  // 水印为图片水印相关属性
  // image?: string;
  // imageWidth?: number;
  // imageHeight?: number;
  content?: string;
  fontColor?: string;
  fontStyle?: "none" | "normal" | "italic" | "oblique";
  fontFamily?: string;
  fontWeight?: "normal" | "light" | "weight" | number;
  fontSize?: number | string;
  fullPage?: boolean;
};
const WaterMark: React.FC<WaterMarkProps> = (props: WaterMarkProps) => {
  const {
    zIndex = 2000,
    gapX = 24,
    gapY = 48,
    width = 120,
    height = 64,
    rotate = -22,
    // image,
    // imageWidth = 120,
    // imageHeight = 64,
    content = "",
    fontStyle = "normal",
    fontWeight = "normal",
    fontColor = "rgba(0,0,0,.15)",
    fontSize = 14,
    fontFamily = "sans-serif"
  } = props;
  const [base64Url, setBase64Url] = useState("");
  useEffect(() => {
    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");
    const ratio = window.devicePixelRatio;

    const canvasWidth = `${(gapX + width) * ratio}px`;
    const canvasHeight = `${(gapY + height) * ratio}px`;

    const markWidth = width * ratio;
    const markHeight = height * ratio;

    canvas.setAttribute("width", canvasWidth);
    canvas.setAttribute("height", canvasHeight);
    if (ctx) {
      if (content) {
        ctx.textBaseline = "middle";
        ctx.textAlign = "center";
        // 文字绕中间旋转
        ctx.translate(markWidth / 2, markHeight / 2);
        ctx.rotate((Math.PI / 180) * Number(rotate));

        const markSize = Number(fontSize) * ratio;
        ctx.font = `${fontStyle} normal ${fontWeight} ${markSize}px/${markHeight}px ${fontFamily}`;
        ctx.fillStyle = fontColor;

        ctx.fillText(content, 0, 0);
        ctx.restore();
        setBase64Url(canvas.toDataURL());
      }
    }
  }, [gapX, gapY, rotate, fontStyle, fontWeight, width, height, fontFamily, fontColor, content, fontSize]);
  return (
    <div
      className={styles["waterMark"]}
      style={{
        zIndex,
        backgroundSize: `${gapX + width}px`,
        backgroundImage: `url('${base64Url}')`
      }}
    ></div>
  );
};
export default WaterMark;
