import React, { useEffect, useState } from "react";
import { IRoute, history, useLocation } from "@umijs/max";
import { Tabs } from "antd";
import { useAliveController } from "react-activation";
import { filterCurrentRouteFromRoute } from "@/utils/menus";
import styles from "./index.less";
import { PageUrl as WorkspacePageUrl } from "@/pages/Workspace/Workspace/index";
import { SIZE, tabs_tabBarStyle } from "@/utils/constant";
interface IProps {
  routes: IRoute[];
}
interface IItems {
  key: string;
  label: React.ReactNode;
  search: string;
}
type TargetKey = React.MouseEvent | React.KeyboardEvent | string;
const classPrefix = "navigation";
const Res = (props: IProps) => {
  const location = useLocation();
  const { dropScope } = useAliveController();
  const { routes } = props;

  const [tabItems, setTabItems] = useState<IItems[]>([]);
  const [curTab, setCurTab] = useState("");

  useEffect(() => {
    const { pathname, search } = location;
    setCurTab(pathname);
    if (tabItems.findIndex(item => item.key === pathname) > -1) return;
    const matchRoute = filterCurrentRouteFromRoute(routes, pathname);
    if (matchRoute) {
      setTabItems([
        ...tabItems,
        {
          key: pathname,
          label: matchRoute.name,
          search: search
        }
      ]);
    }
  }, [location]);
  const handleTabChange = key => {
    const curTab =
      tabItems.filter(item => {
        return item.key === key;
      })?.[0] || {};
    history.push({
      pathname: curTab.key,
      search: curTab.search
    });
  };
  const remove = (targetKey: TargetKey) => {
    const targetIndex = tabItems.findIndex(pane => pane.key === targetKey);
    const newPanes = tabItems.filter(pane => pane.key !== targetKey);
    // 最后一个tab 不允许被删除
    if (newPanes.length === 0) return;
    if (newPanes.length) {
      const { key, search } = newPanes[targetIndex === newPanes.length ? targetIndex - 1 : targetIndex];
      history.push({
        pathname: key,
        search
      });
      const reg = new RegExp(`^${targetKey}`, "ig");
      dropScope?.(reg);
      setCurTab(key);
      setTabItems(newPanes);
    }
  };
  const handleTabEdit = (targetKey: TargetKey, action: "add" | "remove") => {
    if (action === "remove") {
      remove(targetKey);
    }
  };
  return (
    <>
      {WorkspacePageUrl !== curTab ? (
        <nav className={styles[classPrefix]}>
          <Tabs
            size={SIZE}
            activeKey={curTab}
            items={tabItems}
            tabBarStyle={tabs_tabBarStyle}
            type="editable-card"
            hideAdd
            onChange={handleTabChange}
            onEdit={handleTabEdit}
          ></Tabs>
        </nav>
      ) : null}
    </>
  );
};

export default React.memo(Res);
