.navigation {
  background-color: #fff;
  min-height: 30px;
  :global {
    .ant-tabs-nav .ant-tabs-tab {
      padding: 4px 6px !important;
    }
    .ant-tabs-nav .ant-tabs-tab-active {
      background-color: #ecf0ff;
    }
    .ant-tabs-tab-active,
    .ant-tabs-nav .ant-tabs-tab:hover {
      font-weight: 400;
      color: #376ff6;
      background-color: #ecf0ff;
      &::after {
        background-color: #456fff;
        bottom: 0;
        left: 0;
        content: "";
        position: absolute;
        height: 2px;
        width: 100%;
      }
    }
  }
}
