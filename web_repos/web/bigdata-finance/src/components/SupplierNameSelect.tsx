import React, { useCallback } from "react";
import { SelectProps } from "antd";
import DebounceSelect from "./DebounceSelect";
import apis from "@/services/common";
import { returnOptions } from "@/utils";
type IProps = SelectProps;
const BrandSelect = (props: IProps) => {
  const { onChange } = props;
  const fetchList = useCallback(async (value: string) => {
    const { entry = [], status = false } = await apis.getSupplierNameList({ supplierName: value });
    if (status) {
      return returnOptions(entry);
    } else {
      return [];
    }
  }, []);
  const handleChange = useCallback(options => {
    if (Array.isArray(options)) {
      onChange?.(options?.map(item => item.value));
    } else {
      onChange?.(options?.value);
    }
  }, []);
  return <DebounceSelect placeholder="搜索供应商" showSearch allowClear fetchOptions={fetchList} {...props} onChange={handleChange} />;
};
export default React.memo(BrandSelect);
