import React from "react";
import { filterOptionLabel } from "@/utils";
import { Select, SelectProps } from "antd";
import { useEffect, useState } from "react";
interface IProps extends SelectProps {
  type?: number;
  field?: string;
  api: Promise<any> | undefined;
}
const SelectLabel = (props: IProps) => {
  const { type, field, api, ...rest } = props;

  const [options, setOptions] = useState<any>([]);
  const getPullDownOpstions = async () => {
    const { entry, status } = await api?.({ type, field });
    if (status) {
      const resultData = entry.map((item: string) => {
        return { value: item, label: item };
      });
      setOptions(resultData);
    }
  };

  useEffect(() => {
    getPullDownOpstions();
  }, []);

  return <Select allowClear showSearch placeholder="请选择" options={options} filterOption={filterOptionLabel} {...rest}></Select>;
};
export default React.memo(SelectLabel);
