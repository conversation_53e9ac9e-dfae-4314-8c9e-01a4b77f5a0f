import React, { useEffect, useState } from "react";
import { Radio, DatePicker, Space, Cascader } from "antd";
import type { SizeType } from "antd/es/config-provider/SizeContext";
import { activetyColumns } from "./big-promotion-picker";
import dayjs from "dayjs";
import { SIZE } from "@/utils/constant";
import { OptionProps } from "antd/es/select";
const { RangePicker } = DatePicker;

type radios = "year" | "month" | "week" | "bigPromotion";
export interface RangeDateProps {
  size?: SizeType;
  onChange?: (val: any) => void;
  defaultDate?: any[];
  radios?: radios[];
}

export const rangeDateValues = {
  // 年
  year: 0,
  // 月
  month: 1,
  // 周
  week: 2,
  // 大促
  bigPromotion: 3,
  // 自定义
  self: -1
};
const radioOptions: Omit<OptionProps, "children"> = [
  {
    label: "年",
    value: rangeDateValues.year,
    key: "year"
  },
  {
    label: "月",
    value: rangeDateValues.month,
    key: "month"
  },
  {
    label: "周",
    value: rangeDateValues.week,
    key: "week"
  },
  {
    label: "大促",
    value: rangeDateValues.bigPromotion,
    key: "bigPromotion"
  }
];
function disabledDate(current: any) {
  return current && current >= dayjs();
}
const dateFormat: string = "YYYYMMDD";

function RangeDate({ size = SIZE, onChange, defaultDate, radios = ["year", "month", "week", "bigPromotion"] }: RangeDateProps) {
  const [values, fillValues] = useState<any>({});
  const [selectValue, setSelectValue] = useState<any>(undefined);
  const [DatePickerType, setDatePickerType] = useState<any>();
  const [RangePickerValue, setRangePickerValue] = useState<any>([]);
  const [radioMenu] = useState(() => {
    return radioOptions.filter(item => radios.includes(item.key as any));
  });

  const [dateType, setDateType] = useState(() => {
    return radioMenu?.[0]?.value;
  });

  useEffect(() => {
    if (onChange && values.startTime) onChange(values);
  }, [values]);

  const displayRender = labels => {
    const lastLabel = labels[labels.length - 1];
    return lastLabel;
  };
  const onDatePickerSelect = (e: any) => {
    setSelectValue(e);
    const nowDate = dayjs().startOf("day");
    let yearLastDay = dayjs(e).endOf("year");
    let monthLastDay = dayjs(e).endOf("month");
    let weekLastDay = dayjs(e).endOf("week");
    if (dateType === rangeDateValues.year) {
      // 年
      // 年的开始 年的结束
      if (nowDate <= yearLastDay) {
        // 如果当前时间小于本年最后一天 取当前时间
        yearLastDay = nowDate;
      }

      fillValues({
        dateType: rangeDateValues.year,
        startTime: dayjs(e).startOf("year").format(dateFormat),
        endTime: yearLastDay.format(dateFormat)
      });
      return;
    } else if (dateType === rangeDateValues.month) {
      // 月
      // 月的开始、月的结束
      if (nowDate <= monthLastDay) {
        // 如果当前时间小于本月最后一天 取当前时间
        monthLastDay = nowDate;
      }
      fillValues({
        dateType: rangeDateValues.month,
        startTime: dayjs(e).startOf("month").format(dateFormat),
        endTime: monthLastDay.format(dateFormat)
      });
      return;
    } else if (dateType === rangeDateValues.week) {
      // 周
      // 周的开始，周的结束
      if (nowDate <= weekLastDay) {
        // 如果当前时间小于本周最后一天 取当前时间
        weekLastDay = nowDate;
      }
      fillValues({
        dateType: rangeDateValues.week,
        startTime: dayjs(e).startOf("week").format(dateFormat),
        endTime: weekLastDay.format(dateFormat)
      });
      return;
    } else if (dateType === rangeDateValues.bigPromotion) {
      // 大促
      // 默认选择 最新的大促时间
      const [startPromotion, endPromotion] = activetyColumns?.[0]?.children?.[0]?.value?.split("-");
      fillValues({
        dateType: rangeDateValues.bigPromotion,
        startTime: startPromotion,
        endTime: endPromotion
      });
      return;
    }
  };

  const onRangePicker = e => {
    setDateType(undefined);
    setRangePickerValue(e);
    if (e && e.length === 2) {
      const [startTime, endTime] = e;
      fillValues({
        dateType: rangeDateValues.self,
        startTime: dayjs(startTime).format(dateFormat),
        endTime: dayjs(endTime).format(dateFormat)
      });
    }
  };

  const onChangeValue = value => {
    if (!value) return;
    const [startTime, endTime] = value[1]?.split("-");
    fillValues({
      dateType: rangeDateValues.bigPromotion,
      startTime: startTime,
      endTime: endTime
    });
  };

  useEffect(() => {
    if (typeof dateType !== "undefined") {
      setDatePickerType(["year", "month", "week"][dateType]);
      onDatePickerSelect(dayjs());
      setRangePickerValue([]);
    } else {
      setDatePickerType(undefined);
      setSelectValue(undefined);
    }
  }, [dateType]);
  useEffect(() => {
    // 设置默认值
    if (defaultDate && defaultDate.length) {
      onRangePicker([dayjs(defaultDate[0]), dayjs(defaultDate[1])]);
    }
  }, []);

  return (
    <Space>
      <Radio.Group
        value={dateType}
        size={size}
        style={{ whiteSpace: "nowrap" }}
        onChange={(e: any) => {
          setDateType(e.target.value);
        }}
      >
        {radioMenu.map((item: OptionProps) => {
          return (
            <Radio.Button value={item.value} key={item.value}>
              {item.label}
            </Radio.Button>
          );
        })}
      </Radio.Group>
      {dateType !== rangeDateValues.bigPromotion && (
        <DatePicker
          size={size}
          value={selectValue}
          disabledDate={disabledDate}
          style={{ width: 120 }}
          onChange={onDatePickerSelect}
          picker={DatePickerType}
          disabled={!DatePickerType}
          allowClear={false}
        />
      )}
      {dateType === rangeDateValues.bigPromotion && (
        <Cascader
          style={{ width: 135 }}
          displayRender={displayRender}
          size={size}
          options={activetyColumns}
          allowClear={false}
          defaultValue={[activetyColumns?.[0]?.value || "2023", activetyColumns?.[0]?.children?.[0]?.value || "20230520-20230618"]}
          onChange={onChangeValue}
        />
      )}
      <div style={{ whiteSpace: "nowrap" }}>
        <span>自定义时间:</span>
        <RangePicker
          value={RangePickerValue}
          // style={{ minWidth: "130px" }}
          disabledDate={disabledDate}
          size={size}
          allowClear={false}
          onChange={onRangePicker}
        />
      </div>
    </Space>
  );
}

export default React.memo(RangeDate);
