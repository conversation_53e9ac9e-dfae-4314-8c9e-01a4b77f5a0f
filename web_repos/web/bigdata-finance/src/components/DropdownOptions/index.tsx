import React, { useMemo } from "react";
import { Dropdown, Menu, Space } from "antd";
import { DownOutlined } from "@ant-design/icons";

const DropdownOptions = (props: any) => {
  const { handles } = props;
  const menu = useMemo(() => {
    return (
      <Menu>
        {handles.map((item: any) => {
          return <Menu.Item key={item.props.children}>{item}</Menu.Item>;
        })}
      </Menu>
    );
  }, [handles]);

  return (
    <Dropdown overlay={menu} trigger={["hover"]}>
      <Space>
        <a className="ant-dropdown-link" onClick={e => e.preventDefault()}>
          操作 <DownOutlined />
        </a>
      </Space>
    </Dropdown>
  );
};
export default React.memo(DropdownOptions);
