import { ProTable, ProTableProps } from "@ant-design/pro-components";
import type { ProFormColumnsType } from "@ant-design/pro-components";
import { formatYuanToAmount, millennialsNumber, paginationOptions, returnEllipsisTitle, pushExportHistory, flattenTree } from "@/utils";
import { NOVALUE, SIZE, Protable_FROM_CONFIG } from "@/utils/constant";
import { Table, Space, Button, ButtonProps, Tooltip, TooltipProps } from "antd";
import { useState, useEffect, useRef, useMemo, useImperativeHandle } from "react";
import AdvanceSearch from "@/components/AdvanceSearch";
import type { DataItem, AdvanceRef } from "@/components/AdvanceSearch";
import styles from "./index.less";
import classNames from "classnames";
export type TableListRef = {
  advanceRef: AdvanceRef | null | undefined;
  onReset?: () => void;
};
export interface IProps extends ProTableProps<any, any> {
  tableListRef?: React.Ref<TableListRef>;
  // 是否可展开
  hasChildren?: boolean;
  //  一键展开
  oneExpansion?: {
    // 位置
    position: "headerTitle" | "actions";
    // 显示
    show: boolean;
  };
  // 是否默认展开
  defaultExpandAll?: {
    // 0：默认展开所有可展开数据
    // 1：只展开第一层
    deep: 0 | 1;
  };
  params?: Record<string, any>;
  api?: Promise<any> | undefined;
  downloadApi?: Promise<any> | undefined;
  // 下载按钮
  downloadBtn?: {
    // 按钮属性
    btnProps: ButtonProps;
    // tooltip 属性
    tooltipProps: TooltipProps;
  };
  // 处理下载参数
  handleDownLoadParams?: (p: Record<string, any>) => Record<string, any>;
  summaryApi?: Promise<any> | undefined;
  handleDatas?: (p: Record<string, any>[]) => Record<string, any>[];
  preFetch?: (p: Record<string, any>, sort?: Record<string, any>) => Record<string, any>;
  paramsChange?: (p: Record<string, any>) => void;
  // 列变化
  columnsStateChange?: (map: Record<string, any>) => void;
  advanceColumns?: ProFormColumnsType<DataItem>[];
}
// 返回合计单元格内容
export const reuturnSummaryValue = (item: TableListItem, index: number, data: Record<string, any>, rest: any) => {
  const { summaryTooltip, dataIndex } = item;
  if ((rest.rowSelection ? index + 1 : index) === 0) {
    return "总计";
  }
  const { divisor = 1, millennials, hoverMillennials, formatYuan, hoverMillennialsRenderMillennials, millennialsOptions = {} } = summaryTooltip || {};
  const to_yuan = data?.[dataIndex] / divisor || 0;

  if (millennials) {
    // 千分位格式
    return millennialsNumber(to_yuan, millennialsOptions);
  }
  if (formatYuan) {
    // 元格式
    return formatYuanToAmount(to_yuan || NOVALUE);
  }
  if (hoverMillennials) {
    // 默认展示元格式
    // hover 展示前端的千分位格式
    return returnEllipsisTitle({
      title: formatYuanToAmount(to_yuan || NOVALUE),
      tooltip: millennialsNumber(to_yuan, millennialsOptions)
    });
  }
  if (hoverMillennialsRenderMillennials) {
    // 这种情况，接口必须返回 字段格式化，
    // tooltip 展示前端自己的千分位格式化
    const targetStr = data?.[dataIndex + "Str"] || 0;
    return returnEllipsisTitle({
      title: formatYuanToAmount(targetStr),
      tooltip: millennialsNumber(to_yuan, millennialsOptions)
    });
  }
  if (data?.[dataIndex + "Str"]) {
    // 如果接口有返回格式化，直接使用
    return data?.[dataIndex + "Str"];
  }
  return data?.[dataIndex] ?? NOVALUE;
};
const Res = (props: IProps) => {
  const [summaryData, setSummary] = useState({});
  const {
    tableListRef,
    api,
    downloadApi,
    downloadBtn,
    handleDownLoadParams,
    summaryApi,
    preFetch,
    handleDatas,
    paramsChange,
    scroll,
    columnsStateChange,
    advanceColumns = [],
    form,
    params,
    oneExpansion,
    defaultExpandAll,
    hasChildren,
    className,
    ...rest
  } = props;
  const [isExpand, setIsExpand] = useState<boolean>(false);
  const [expandedRowKeys, setExpandedRowKeys] = useState<Array<string>>([]);
  const [tableAllKeys, setTableAllKeys] = useState<Array<string>>([]);
  const advanceRef = useRef<AdvanceRef>(null);
  const [pageParams, setPageParams] = useState({});
  const [downloadParams, setDownloadParams] = useState({});

  const showAdvance = useRef(() => {
    return advanceColumns?.length ? true : false;
  });

  const fetchsummaryData = async params => {
    const { entry } = (await summaryApi?.(params)) || [];
    if (entry?.length) {
      setSummary(entry[0]);
    } else {
      setSummary({});
    }
  };

  const returnSummaryItem = (item: TableListItem, index: number) => {
    const { dataIndex, align } = item;
    return (
      <Table.Summary.Cell align={align} key={dataIndex} index={rest.rowSelection ? index + 1 : index}>
        {reuturnSummaryValue(item, index, summaryData, rest)}
      </Table.Summary.Cell>
    );
  };
  const summaryComponent = () => {
    // 如果有列变化 总计行需要过滤掉 变化的列
    // value中存在的 所有列表都表示隐藏
    const { value = {} } = rest?.columnsState || {};
    const columnsStateValues = Object.keys(value || {});
    return (
      <Table.Summary fixed="top">
        <Table.Summary.Row style={{ textAlign: "center" }}>
          {rest.rowSelection ? <Table.Summary.Cell index={0}>总计</Table.Summary.Cell> : null}
          {rest?.columns
            ?.filter(item => !item.hideInTable)
            ?.filter(item => {
              const { dataIndex } = item;
              if (columnsStateValues.includes(dataIndex)) {
                return false;
              }
              return true;
            })
            ?.map(returnSummaryItem)}
        </Table.Summary.Row>
      </Table.Summary>
    );
  };
  useEffect(() => {
    // 一键展开/收起
    if (oneExpansion?.show) {
      if (isExpand) {
        setExpandedRowKeys(tableAllKeys);
      } else {
        setExpandedRowKeys([]);
      }
    }
  }, [isExpand, tableAllKeys, oneExpansion]);
  useEffect(() => {
    setIsExpand(defaultExpandAll ? true : false);
    // 默认展开子节点
    if (defaultExpandAll) {
      setExpandedRowKeys(tableAllKeys);
    }
  }, [tableAllKeys, defaultExpandAll]);

  const handleExport = async () => {
    const p = handleDownLoadParams?.(downloadParams) || downloadParams;
    const { status } = await downloadApi?.(p);
    if (status) {
      pushExportHistory();
    }
  };
  const expendCom = () => {
    // 一键展开/收起 组件
    return (
      <Button
        type="primary"
        size={SIZE}
        onClick={() => {
          setIsExpand(!isExpand);
        }}
      >
        {isExpand ? "一键收起" : "一键展开"}
      </Button>
    );
  };
  const headerTitle = () => {
    return <Space>{expendCom()}</Space>;
  };

  const returnSearchDOM = (searchConfig, dom) => {
    const { form } = searchConfig;
    const { btnProps = {}, tooltipProps = {} } = downloadBtn || {};
    const preDOM = [
      <>
        {showAdvance.current() ? (
          <AdvanceSearch key="advanceSearch" tableForm={form} advanceRef={advanceRef} setParams={setPageParams} columns={advanceColumns} />
        ) : null}
      </>
    ];
    const postDOM = [
      <>
        {downloadApi ? (
          <Tooltip {...tooltipProps}>
            <Button onClick={handleExport} size={SIZE} {...btnProps}>
              导出
            </Button>
          </Tooltip>
        ) : null}
      </>
    ];
    return [...preDOM, ...dom, ...postDOM];
  };
  const expandable = {
    onExpandedRowsChange: setExpandedRowKeys as any,
    expandedRowKeys: expandedRowKeys
  };
  const handleReset = () => {
    advanceRef?.current?.form.resetFields();
    advanceRef?.current?.onOk();
  };
  const handleSearch = () => {
    advanceRef?.current?.onOk();
  };
  const returnTableClass = useMemo(() => {
    return classNames(styles["table-content"], hasChildren ? styles["table-children"] : "");
  }, [hasChildren]);
  // 返回默认展开的数据
  const returnExpandKeys = (datas: any[]) => {
    const flattened = defaultExpandAll?.deep === 0 ? flattenTree(datas, "children") : datas;
    return (flattened || []).map(item => item[props?.rowKey || "id"]);
  };
  // 暴露给父组件的属性
  useImperativeHandle(
    tableListRef,
    () => ({
      advanceRef: advanceRef.current,
      onReset: handleReset,
      rest: rest
    }),
    [form]
  );
  return (
    <ProTable
      className={classNames(styles["table-list"], className)}
      tableClassName={returnTableClass}
      size={SIZE}
      columnEmptyText={NOVALUE}
      headerTitle={oneExpansion?.position === "headerTitle" ? headerTitle() : null}
      scroll={{ x: "max-content", ...scroll }}
      form={{ ...Protable_FROM_CONFIG, ...form }}
      columnsState={{
        onChange(map) {
          columnsStateChange?.(map);
        }
      }}
      options={false}
      expandable={expandable}
      summary={summaryApi ? summaryComponent : undefined}
      request={async (p, sort) => {
        const params = preFetch?.(p, sort) || p;
        paramsChange?.(params);
        setDownloadParams(params);
        if (summaryApi) {
          fetchsummaryData(params);
        }
        const { entry = [], status = false, totalRecordSize = 0 } = await api?.(params);
        if (status) {
          const data = handleDatas ? handleDatas(entry || []) : entry || [];
          setTableAllKeys(returnExpandKeys(data));
          return {
            data: data,
            success: true,
            total: totalRecordSize
          };
        } else {
          setTableAllKeys([]);
          return {
            data: [],
            success: true,
            total: 0
          };
        }
      }}
      pagination={{
        showQuickJumper: true,
        ...paginationOptions
      }}
      rowKey="id"
      onReset={handleReset}
      onSubmit={handleSearch}
      search={{
        defaultCollapsed: false,
        collapseRender: false,
        // collapseRender: !showAdvance.current(),
        optionRender: (searchConfig, formProps, dom) => {
          return returnSearchDOM(searchConfig, dom);
        }
      }}
      {...rest}
      // 注意：这种写法后，导致 toolBarRender 方法失效
      toolbar={{
        menu: rest.toolbar?.menu,
        actions: [oneExpansion?.position === "actions" ? expendCom() : null, ...(rest.toolbar?.actions ?? [])]
      }}
      params={{ ...params, ...pageParams }}
    />
  );
};

export default Res;
