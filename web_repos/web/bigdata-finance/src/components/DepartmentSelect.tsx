import React, { useEffect, useState } from "react";
import { SelectProps, Select } from "antd";
import apis from "@/services/common";
import { filterOptionLabel } from "@/utils";
type IProps = SelectProps;
const DepartmentSelect = (props: IProps) => {
  const [lists, setList] = useState<any>([]);
  const getList = async () => {
    let { entry = [], status } = await apis.getDepartmentOptions();
    if (status && entry?.length) {
      setList(entry.map(item => ({ label: item.name, value: item.value })));
    }
  };
  useEffect(() => {
    getList();
  }, []);
  return (
    <Select
      style={{ width: 170, textAlign: "left" }}
      placeholder="请选择部门"
      options={lists}
      filterOption={filterOptionLabel}
      showSearch
      allowClear
      {...props}
    />
  );
};
export default React.memo(DepartmentSelect);
