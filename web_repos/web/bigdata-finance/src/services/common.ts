import wrapApi from "@/utils/request/wrapApis";

export default wrapApi(
  {
    // 获取全部主播列表
    getAnchorList: "/data-finance/system/common/biz/getSettleAnchorList",
    // 获取导出记录列表
    queryDownloadRecord: "/data-finance/commonService/getDownloadList",
    // 获取导入记录列表
    queryUploadList: "/data-finance/commonService/getUploadList",
    // 获取部门信息
    getDepartmentOptions: "/data-finance/commonService/getDepartmentOptions",
    // 获取页面显示字段缓存
    getPageField: "/data-finance/system/common/biz/getPageField",
    // 获取标准类目集合
    getCommonCatList: "/data-finance/system/common/biz/getCommonCatList",
    // 供应商列表
    getSupplierNameList: "/data-finance/commonService/getSupplierNameList",
    // 获取年框扣费主体
    getCompanyNameList: "/data-finance/commonService/getCompanyNameList"
  },
  {
    // 获取品牌搜索结果-分页数据
    getSearchBrandPage: "/data-finance/commonService/getSearchBrandPage",
    // 保存页面显示字段缓存
    savePageFieldContext: "/data-finance/system/common/biz/savePageFieldContext"
  }
);
