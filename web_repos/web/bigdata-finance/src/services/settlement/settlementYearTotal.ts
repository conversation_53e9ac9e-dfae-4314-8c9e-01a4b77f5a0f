import wrapApi from "@/utils/request/wrapApis";

export default wrapApi({
  // 公司主体年框-列表
  companyPage: "/data-finance/report/market/year/company/page",
  // 公司主体年框-列表-导出
  companyPageDownload: "/data-finance/report/market/year/company/page-download",
  // 公司主体年框-列表-汇总
  companyPageSummary: "/data-finance/report/market/year/company/page-summary",

  // 主播年框明细-列表
  detailPage: "/data-finance/report/market/year/detail/page",
  //  主播年框明细-列表-导出
  detailPageDownload: "/data-finance/report/market/year/detail/page-download",
  //  主播年框明细-列表-汇总
  detailPageSummary: "/data-finance/report/market/year/detail/page-summary",

  // 月度年框汇总-列表
  monthPage: "/data-finance/report/market/year/month/page",
  // 月度年框汇总-列表-导出
  monthPageDownload: "/data-finance/report/market/year/month/page-download",
  //  月度年框汇总-列表-汇总
  monthPageSummary: "/data-finance/report/market/year/month/page-summary"
});
