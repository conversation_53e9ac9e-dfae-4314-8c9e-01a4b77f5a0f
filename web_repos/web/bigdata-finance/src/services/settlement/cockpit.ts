import wrapApi from "@/utils/request/wrapApis";

export default wrapApi({
  // 下载直播详情列表
  downloadLiveDetailList: "/data-finance/anchor/report/financeBoard/downloadLiveDetailList",
  // 下载直播详情列表-主播
  downloadLiveDetailListForAnchor: "/data-finance/anchor/report/financeBoard/downloadLiveDetailListForAnchor",
  // 获取直播详情列表-主播
  getLiveAnchorDetailList: "/data-finance/anchor/report/financeBoard/getLiveAnchorDetailList",
  // 获取直播详情列表
  getLiveDetailList: "/data-finance/anchor/report/financeBoard/getLiveDetailList",
  // 获取最新截止时间
  getNewsTime: "/data-finance/anchor/report/financeBoard/getNewsTime",
  // 获取指标数据
  getOverviewIndex: "/data-finance/anchor/report/financeBoard/getOverView",
  // 获取销售趋势
  getSaleTrend: "/data-finance/anchor/report/financeBoard/getSaleTrend",
  // 获取排名列表
  getSaleTrendRank: "/data-finance/anchor/report/financeBoard/getSaleTrendRank",
  // 获取销售额部门列表数据
  getSalesDistributionList: "/data-finance/anchor/report/financeBoard/getSalesDistributionList",
  // 获取销售额部门占比图片数据
  getSalesDistributionPicture: "/data-finance/anchor/report/financeBoard/getSalesDistributionPicture"
});
