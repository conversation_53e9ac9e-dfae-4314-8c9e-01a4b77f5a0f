import wrapApi from "@/utils/request/wrapApis";

export default wrapApi(
  {},
  {
    // 获取财务系统 - 短视频商品每日销售明细-分页数据
    getVideoDailySummaryRfPage: "/data-finance/accounting/videoDailySummaryRf/getVideoDailySummaryRfPage",
    // 下载获取财务系统 - 短视频商品每日销售明细--页面所有数据(包含分页)
    downloadVideoDailySummaryRf: "/data-finance/accounting/videoDailySummaryRf/downloadVideoDailySummaryRf",
    // 获取财务系统 - 短视频商品每日结算明细-分页数据
    getVideoDailySettleSummaryRfPage: "/data-finance/accounting/videoDailySettleSummaryRf/getVideoDailySettleSummaryRfPage",
    // 下载获取财务系统 - 短视频商品每日结算明细--页面所有数据(包含分页)
    downloadVideoDailySettleSummaryRf: "/data-finance/accounting/videoDailySettleSummaryRf/downloadVideoDailySettleSummaryRf"
  }
);
