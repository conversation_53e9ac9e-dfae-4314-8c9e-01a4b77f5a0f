import wrapApi from "@/utils/request/wrapApis";

export default wrapApi(
  {
    // 工单赔付-赔付工单类型
    getWorkOrderTypeNameList: "/data-finance/anchor/report/workOrderPaid/getWorkOrderTypeNameList"
  },
  {
    // 工单赔付-工单赔付报表
    detailList: "/data-finance/anchor/report/workOrderPaid/getDetailList",
    // 工单赔付-工单赔付报表下载
    downloadDetailList: "/data-finance/anchor/report/workOrderPaid/downloadDetailList",
    // 工单赔付-工单赔付报表订单明细
    getOrderDetailList: "/data-finance/anchor/report/workOrderPaid/getOrderDetailList",
    // 工单赔付-工单赔付报表订单明细下载
    downloadOrderDetailList: "/data-finance/anchor/report/workOrderPaid/downloadOrderDetailList"
  }
);
