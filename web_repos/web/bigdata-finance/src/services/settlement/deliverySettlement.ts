import wrapApi from "@/utils/request/wrapApis";

export default wrapApi({
  // 获取所有的下拉筛选
  getPullDownData: "/data-finance/anchor/report/deliverSettle/getPullDownData",

  // 获取新规则主播快递统计表
  getAnchorExpressSummary: "/data-finance/anchor/report/deliverSettle/getAnchorExpressSummary",

  // 获取新规则快递明细数据
  getExpressFeeDetailList: "/data-finance/anchor/report/deliverSettle/getExpressFeeDetailList",

  // 获取快递费明细
  getExpressDetailList: "/data-finance/anchor/report/deliverSettle/getExpressDetailList",

  // 获取聚水潭快递结算表
  getJTSExpressSettle: "/data-finance/anchor/report/deliverSettle/getJTSExpressSettle",

  // 获取聚水潭商品发货表
  getJTSItemExpress: "/data-finance/anchor/report/deliverSettle/getJTSItemExpress",

  // 下载新规则主播快递统计表
  downloadAnchorSummary: "/data-finance/anchor/report/deliverSettle/downloadAnchorExpressSummary",

  // 下载新规则快递明细数据
  downloadExpressFeeDetailList: "/data-finance/anchor/report/deliverSettle/downloadExpressFeeDetailList",

  // 下载快递明细数据
  downloadExpressDetailList: "/data-finance/anchor/report/deliverSettle/downloadExpressDetailList",

  // 下载聚水潭快递结算表
  downloadJSTDeliverySettle: "/data-finance/anchor/report/deliverSettle/downloadJTSExpressSettle",

  // 下载聚水潭商品发货表
  downloadJTSItemExpress: "/data-finance/anchor/report/deliverSettle/downloadJTSItemExpress"
});
