import wrapApi from "@/utils/request/wrapApis";

export default wrapApi({
  // 聚水潭查询售后退款
  getAfterRefundList: "/data-finance/adsJSTAfterSellSettle/afterRefundList",
  // 聚水潭查询售后退款导出
  downloadAfterRefund: "/data-finance/adsJSTAfterSellSettle/downloadAfterRefund",
  // 聚水潭查询售后退款
  getAfterOperateFeeList: "/data-finance/adsJSTAfterSellSettle/afterOperateFeeList",
  // 聚水潭查询售后退款导出
  downloadAfterOperateFee: "/data-finance/adsJSTAfterSellSettle/downloadAfterOperateFee"
});
