import wrapApi from "@/utils/request/wrapApis";

export default wrapApi(
  {},
  {
    // 通过excel导入供应商对账
    addInvoicingSettlementAnalysisFromExcel: "/data-finance/anchor/report/invoicingAnalysis/addInvoicingSettlementAnalysisFromExcel",
    // 下载获取结算报表-进销存结算分析--页面所有数据(包含分页)
    downloadInvoicingSettlementAnalysis: "/data-finance/anchor/report/invoicingAnalysis/downloadInvoicingSettlementAnalysis",
    //  获取结算报表-进销存结算分析-分页数据
    getInvoicingSettlementAnalysisPage: "/data-finance/anchor/report/invoicingAnalysis/getInvoicingSettlementAnalysisPage"
  }
);
