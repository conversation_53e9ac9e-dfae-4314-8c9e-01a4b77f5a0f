import wrapApi from "@/utils/request/wrapApis";

export default wrapApi(
  {
    // 获取-安心钱包主体--下拉选项
    getCpsAnxinCompanyNameList: "/data-finance/accounting/fund/cpsSettleMonth/getCpsAnxinCompanyNameList"
  },
  {
    // 下载获取 主播分销-佣金账单统计表--页面所有数据(包含分页)
    downloadCpsSettleMonth: "/data-finance/accounting/fund/cpsSettleMonth/downloadCpsSettleMonth",
    // 获取 主播分销-佣金账单统计表-分页数据
    getCpsSettleMonthPage: "/data-finance/accounting/fund/cpsSettleMonth/getCpsSettleMonthPage",
    // 获取 主播分销-佣金账单统计表-汇总行
    getCpsSettleMonthPageTotal: "/data-finance/accounting/fund/cpsSettleMonth/getCpsSettleMonthPageTotal",

    // 下载获取 主播分销-资金账单统计表--页面所有数据(包含分页)
    downloadCpsBillMonth: "/data-finance/accounting/fund/cpsBillMonth/downloadCpsBillMonth",
    // 获取 主播分销-资金账单统计表-分页数据
    getCpsBillMonthPage: "/data-finance/accounting/fund/cpsBillMonth/getCpsBillMonthPage",
    // 获取 主播分销-资金账单统计表-汇总行
    getCpsBillMonthPageTotal: "/data-finance/accounting/fund/cpsBillMonth/getCpsBillMonthPageTotal"
  }
);
