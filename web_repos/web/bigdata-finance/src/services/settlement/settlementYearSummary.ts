import wrapApi from "@/utils/request/wrapApis";

export default wrapApi(
  {
    // 主播维度结算时间GMV统计-导出
    anchorSettleTimeExport: "/data-finance/anchorSettleStatement/anchorSettleTimeExport",
    // 主播维度结算时间GMV统计-导出 上年直播
    anchorSettleTimeLastExport: "/data-finance/anchorSettleStatement/anchorSettleTimeLastExport",

    // 主播维度结算时间GMV统计
    anchorSettleTime: "/data-finance/anchorSettleStatement/anchorSettleTime",
    // 主播维度结算时间GMV统计总计
    anchorSettleTimeSummary: "/data-finance/anchorSettleStatement/anchorSettleTimeSummary",
    // 月份维度结算时间GMV统计-导出
    monthSettleTimeExport: "/data-finance/anchorSettleStatement/monthSettleTimeExport",
    // 月份维度结算时间GMV统计
    monthSettleTime: "/data-finance/anchorSettleStatement/monthSettleTime",
    // 月份维度结算时间GMV统计总计
    monthSettleTimeSummary: "/data-finance/anchorSettleStatement/monthSettleTimeSummary",

    // 主播维度结算时间GMV统计-上年直播
    lastYearAnchorSettleTimeLast: "/data-finance/anchorSettleStatement/anchorSettleTimeLast",
    // 主播维度结算时间GMV统计总计-上年直播
    lastYearanchorSettleTimeSummaryLast: "/data-finance/anchorSettleStatement/anchorSettleTimeSummaryLast"
  },
  {}
);
