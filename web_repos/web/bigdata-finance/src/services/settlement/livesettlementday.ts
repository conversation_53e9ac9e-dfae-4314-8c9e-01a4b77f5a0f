import wrapApi from "@/utils/request/wrapApis";

export default wrapApi(
  {
    // 下载spu数据
    downloadSPUList: "/data-finance/anchor/report/liveDistSettle/downloadSPUList",
    // 获取--分销维度结算--页面
    getListForSPU: "/data-finance/anchor/report/liveDistSettle/getListForSPU"
  },
  {
    // 下载获取交易域-主播商品维度交易指标轻度汇总表-日--页面所有数据(包含分页)
    downloadLiveSettleAnchorItem: "/data-finance/accounting/liveSettlementDay/downloadLiveSettleAnchorItem",
    // 获取-直播结算-日-分页数据获取-直播结算-日-分页数据
    getLiveSettleAnchorItemPage: "/data-finance/accounting/liveSettlementDay/getLiveSettleAnchorItemPage"
  }
);
