import wrapApi from "@/utils/request/wrapApis";

export default wrapApi(
  {
    // 获取结算月份-用于红冲
    getSettleMonth: "/data-finance/anchorSettleDetail/getSettleMonth",
    // 调账记录-删除
    delete: "/data-finance/anchorSettleDetail/adjustment/delete",
    // 调账记录-更新
    update: "/data-finance/anchorSettleDetail/adjustment/update",
    // 调账记录-上传
    upload: "/data-finance/anchorSettleDetail/adjustment/upload",
    // 列表:
    getList: "/data-finance/anchorSettleDetail/getList",
    // 红冲
    redInk: "/data-finance/anchorSettleDetail/redInk",
    // 查询一级类目列表
    catLv1Name: "/data-finance/anchorSettleDetail/catLv1Name",
    // 查询品类列表
    categoryName: "/data-finance/anchorSettleDetail/categoryName",
    // 查询结算月份
    settleMonth: "/data-finance/anchorSettleDetail/settleMonth",
    // 批量取消确认
    batchCancelConfirmed: "/data-finance/anchorSettleDetail/batchCancelConfirmed",
    // 下载-列表-全部
    downloadListAll: "/data-finance/anchorSettleDetail/downloadListAll",
    // 下载-明细
    downloadDetail: "/data-finance/anchorSettleDetail/downloadDetail",
    // 下载-类目汇总列表
    downloadList: "/data-finance/anchorSettleDetail/category/summary/downloadList",
    // 类目汇总列表
    getCategoryList: "/data-finance/anchorSettleDetail/category/summary/getList",
    // 列表-汇总
    getListSummary: "/data-finance/anchorSettleDetail/getListSummary"
  },
  {
    // 手动确认
    batchConfirm: "/data-finance/anchorSettleDetail/batchConfirm"
  }
);
