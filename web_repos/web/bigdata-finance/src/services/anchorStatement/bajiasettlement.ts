import wrapApi from "@/utils/request/wrapApis";

export default wrapApi(
  {
    // 确认
    confirm: "/data-finance/financeBajiaUpstream/confirm",
    // 删除多条数据
    delete: "/data-finance/financeBajiaUpstream/delete",
    // 汇总-下载
    downloadCollectList: "/data-finance/financeBajiaUpstream/downloadCollectList",
    // 下载-列表
    downloadList: "/data-finance/financeBajiaUpstream/downloadList",
    // 汇总-列表
    getCollectList: "/data-finance/financeBajiaUpstream/getCollectList",
    // 汇总-汇总行
    getCollectListTotal: "/data-finance/financeBajiaUpstream/getCollectListTotal",
    // 列表
    getList: "/data-finance/financeBajiaUpstream/getList",
    // 取消确认
    unConfirm: "/data-finance/financeBajiaUpstream/unConfirm",
    // 更新一条数据
    update: "/data-finance/financeBajiaUpstream/update",
    // 上传
    uploadFinancePromotion: "/data-finance/financeBajiaUpstream/uploadFinancePromotion",
    // 获取主体集合
    getMainBodyList: "/data-finance/financeBajiaUpstream/getMainBodyList",
    // 获取结算月份
    getSettleMonth: "/data-finance/financeBajiaUpstream/getSettleMonth",
    // 确认前统计列表
    getPreConfirmSummaryList: "/data-finance/financeBajiaUpstream/getPreConfirmSummaryList",
    // 获取科目集合
    getSubjectList: "/data-finance/financeBajiaUpstream/getSubjectList",
    // 列表-统计汇总
    getListSummary: "/data-finance/financeBajiaUpstream/getListSummary",
    // 确认记录
    // 下载-列表
    downloadListConfirm: "/data-finance/financeBajiaConfirmedLog/downloadList",
    // 列表
    getListConfirm: "/data-finance/financeBajiaConfirmedLog/getList"
  },
  {}
);
