import wrapApi from "@/utils/request/wrapApis";

export default wrapApi(
  {
    // 删除详情
    delete: "/data-finance/financeExpenditureAnchorUpstream/delete",
    // 下载-列表
    downloadList: "/data-finance/financeExpenditureAnchorUpstream/downloadList",
    // 编辑详情
    edit: "/data-finance/financeExpenditureAnchorUpstream/edit",
    // 详情列表
    getList: "/data-finance/financeExpenditureAnchorUpstream/getList",
    // 查询结算月份
    settleMonth: "/data-finance/financeExpenditureAnchorUpstream/settleMonth",
    // 科目名称下拉框
    accountName: "/data-finance/financeExpenditureAnchorUpstream/accountName",
    // 账套下拉框
    accountingBook: "/data-finance/financeExpenditureAnchorUpstream/accountingBook",
    // 店铺项目-部门下拉框
    shopProject: "/data-finance/financeExpenditureAnchorUpstream/shopProject"
  },
  {
    // 批量确认
    batchConfirmed: "/data-finance/financeExpenditureAnchorUpstream/batchConfirmed",
    // 批量修改
    updateBatch: "/data-finance/financeExpenditureAnchorUpstream/updateBatch",
    // 上传-辛选费用支出数据
    addAnchorLiveExpendFromExcel: "/data-finance/financeExpenditureAnchorUpstream/addAnchorLiveExpendFromExcel"
  }
);
