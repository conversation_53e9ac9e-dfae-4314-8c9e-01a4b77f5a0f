import wrapApi from "@/utils/request/wrapApis";

export default wrapApi(
  {
    // 调账记录-删除
    delete: "/data-finance/anchor/settle/shortVideo/adjustment/delete",
    // 调账记录-更新
    update: "/data-finance/anchor/settle/shortVideo/adjustment/update",
    // 调账记录-上传
    upload: "/data-finance/anchor/settle/shortVideo/adjustment/upload",
    // 列表:
    getList: "/data-finance/anchor/settle/shortVideo/getList",
    // 红冲
    redInk: "/data-finance/anchor/settle/shortVideo/redInk",
    // 查询一级类目列表
    catLv1Name: "/data-finance/anchor/settle/shortVideo/catLv1Name",
    // 查询品类列表
    categoryName: "/data-finance/anchor/settle/shortVideo/categoryName",
    // 查询结算月份
    // settleMonth: "/data-finance/anchor/settle/shortVideo/settleMonth",
    // 下载-列表-全部
    downloadListAll: "/data-finance/anchor/settle/shortVideo/downloadListAll",
    // 下载-明细
    downloadDetail: "/data-finance/anchor/settle/shortVideo/downloadDetail",
    // 列表-汇总
    getListSummary: "/data-finance/anchor/settle/shortVideo/getListSummary"
  },
  {
    // 手动确认
    batchConfirm: "/data-finance/anchor/settle/shortVideo/batchConfirm"
  }
);
