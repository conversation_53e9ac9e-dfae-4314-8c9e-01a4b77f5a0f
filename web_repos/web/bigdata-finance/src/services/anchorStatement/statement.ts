import wrapApi from "@/utils/request/wrapApis";

export default wrapApi(
  {},
  {
    // 主播结算-巴伽支出-汇总
    getBaJiaExpendCollectPage: "/data-finance/anchor/settle/baJia/expend/getBaJiaExpendCollectPage",
    // 主播结算-巴伽支出-汇总-汇总行
    getBaJiaExpendCollectPageTotal: "/data-finance/anchor/settle/baJia/expend/getBaJiaExpendCollectPageTotal",
    // 主播结算-巴伽支出-汇总-下载
    downloadBaJiaExpendCollect: "/data-finance/anchor/settle/baJia/expend/downloadBaJiaExpendCollect",

    // 主播结算-巴伽支出-明细
    getBaJiaExpendDetailPage: "/data-finance/anchor/settle/baJia/expend/getBaJiaExpendDetailPage",
    // 主播结算-巴伽支出-明细-汇总行
    getBaJiaExpendDetailPageTotal: "/data-finance/anchor/settle/baJia/expend/getBaJiaExpendDetailPageTotal",
    // 主播结算-巴伽支出-明细-下载
    downloadBaJiaExpendDetail: "/data-finance/anchor/settle/baJia/expend/downloadBaJiaExpendDetail",

    // 主播结算-巴伽收入-汇总
    getBaJiaIncomeCollectPage: "/data-finance/anchor/settle/baJia/income/getBaJiaIncomeCollectPage",
    // 主播结算-巴伽收入-汇总-汇总行
    getBaJiaIncomeCollectPageTotal: "/data-finance/anchor/settle/baJia/income/getBaJiaIncomeCollectPageTotal",
    // 主播结算-巴伽收入-汇总-下载
    downloadBaJiaIncomeCollect: "/data-finance/anchor/settle/baJia/income/downloadBaJiaIncomeCollect",

    // 主播结算-巴伽收入-明细
    getBaJiaIncomeDetailPage: "/data-finance/anchor/settle/baJia/income/getBaJiaIncomeDetailPage",
    // 主播结算-巴伽收入-明细-汇总行
    getBaJiaIncomeDetailPageTotal: "/data-finance/anchor/settle/baJia/income/getBaJiaIncomeDetailPageTotal",
    // 主播结算-巴伽收入-明细-下载
    downloadBaJiaIncomeDetail: "/data-finance/anchor/settle/baJia/income/downloadBaJiaIncomeDetail",

    // 主播结算-结算汇总-汇总
    getCollectCollectPage: "/data-finance/anchor/settle/collect/getCollectCollectPage",
    // 主播结算-结算汇总-汇总-汇总行
    getCollectCollectPageTotal: "/data-finance/anchor/settle/collect/getCollectCollectPageTotal",
    // 主播结算-结算汇总-打印
    downloadAnchorSettle: "/data-finance/anchor/settle/collect/downloadAnchorSettle",

    // 主播结算-辛选支出-汇总
    getLiveExpendCollectPage: "/data-finance/anchor/settle/live/expend/getLiveExpendCollectPage",
    // 主播结算-辛选支出-汇总-汇总行
    getLiveExpendCollectPageTotal: "/data-finance/anchor/settle/live/expend/getLiveExpendCollectPageTotal",
    // 主播结算-辛选支出-汇总-下载
    downloadLiveExpendCollect: "/data-finance/anchor/settle/live/expend/downloadLiveExpendCollect",

    // 主播结算-辛选支出-明细
    getLiveExpendDetailPage: "/data-finance/anchor/settle/live/expend/getLiveExpendDetailPage",
    // 主播结算-辛选支出-明细-汇总行
    getLiveExpendDetailPageTotal: "/data-finance/anchor/settle/live/expend/getLiveExpendDetailPageTotal",
    // 主播结算-辛选支出-明细-下载
    downloadLiveExpendDetail: "/data-finance/anchor/settle/live/expend/downloadLiveExpendDetail",

    // 主播结算-直播收入-汇总
    getLiveIncomeCollectPage: "/data-finance/anchor/settle/live/income/getLiveIncomeCollectPage",
    // 主播结算-直播收入-汇总-汇总行
    getLiveIncomeCollectPageTotal: "/data-finance/anchor/settle/live/income/getLiveIncomeCollectPageTotal",
    // 主播结算-直播收入-汇总-下载
    downloadLiveIncomeCollect: "/data-finance/anchor/settle/live/income/downloadLiveIncomeCollect"
  }
);
