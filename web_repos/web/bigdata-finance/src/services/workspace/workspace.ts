import wrapApi from "@/utils/request/wrapApis";

export default wrapApi(
  {
    // 获取主播结算进度
    getAnchorSettleProcess: "/data-finance/workbench/anchor/settle/getAnchorSettleProcess"
  },
  {
    // 工作台-供应商对账-对账单情况
    getStatementCondition: "/data-finance/workbench/statement/getStatementCondition",
    //  工作台-供应商对账-对账进度
    getStatementProgress: "/data-finance/workbench/statement/getStatementProgress",
    // 工作台-直播利润预估
    getLiveProfitCondition: "/data-finance/workbench/profit/getLiveProfitCondition",
    // 工作台-短视频利润预估
    getShortVideoProfitCondition: "/data-finance/workbench/profit/getShortVideoProfitCondition"
  }
);
