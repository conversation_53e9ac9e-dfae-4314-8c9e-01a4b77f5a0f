/*
 * @Author: your name
 * @Date: 2021-01-07 17:52:26
 * @LastEditTime: 2021-01-07 17:53:07
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /xmember/src/services/common/index.js
 */

import wrapApi from "@/utils/request/wrapApis";

export default wrapApi(
  {
    queryResourceList: "/data-finance/security/resource/list", //获取资源列表 -
    queryRoleAuthorizedResources: "/data-finance/security/resource/roleAuthorizedResources" // 为角色分配资源的列表
  },
  {
    bodyResourceAdd: "/data-finance/security/resource/add", // 添加资源
    bodyResourceEdit: "/data-finance/security/resource/edit", // 更新资源
    bodyAuthorizeRoles: "/data-finance/security/authorize/authorizeRoles", //资源对角色授权
    bodyUnauthorizeRoles: "/data-finance/security/authorize/unauthorizeRoles" //资源对角色取消授权
  }
);
