import wrapApi from "@/utils/request/wrapApis";

export default wrapApi(
  {
    queryColumnAuths: "/data-finance/security/authorize/columnAuths", // 获取用户的列权限。即模块下的敏感字段
    queryResourceAuthorizedRoles: "/data-finance/security/authorize/resourceAuthorizedRoles", //获取带有资源授权信息的角色列表
    queryRoleAuthorizedResources: "/data-finance/security/authorize/roleAuthorizedResources" //为角色分配资源的列表
  },
  {
    bodyAuthorizeRoles: "/data-finance/security/authorize/authorizeRoles", //资源对角色授权
    bodyUnauthorizeRoles: "/data-finance/security/authorize/unauthorizeRoles", //资源对角色取消授权
    queryAuthorizeResources: "/data-finance/security/authorize/authorizeResources", //角色对资源授权
    queryUnauthorizeResources: "/data-finance/security/authorize/unauthorizeResources" //角色对资源取消授权
  }
);
