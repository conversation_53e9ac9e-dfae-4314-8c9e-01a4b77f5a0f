/*
 * @Author: your name
 * @Date: 2021-01-07 17:52:26
 * @LastEditTime: 2021-01-07 17:53:07
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /xmember/src/services/common/index.js
 */

import wrapApi from "@/utils/request/wrapApis";

export default wrapApi(
  {
    queryRoleList: "data-finance/security/role/list", // 角色列表。支持模糊搜索 -
    getRoleFieldValues: "data-finance/security/role/getRoleFieldValues", // 获取角色行权限
    queryRoleAuthorizedUsers: "/data-finance/security/authorize/roleAuthorizedUsers", //获取带有角色授权信息的用户列表
    queryRoleNameExists: "data-finance/security/role/roleNameExists" // 角色名称是否存在 -
  },
  {
    bodyRoleAdd: "data-finance/security/role/add", // 添加角色 -
    bodyRoleEdit: "data-finance/security/role/edit", // 更新角色 -
    bodyCopyRole: "/data-finance/security/role/copyRole", //复制角色 -
    bodyUpdataRow: "/data-finance/security/role/updateRoleFieldValues", //更新角色行权限 -
    bodyUnauthorizeUsers: "/data-finance/security/authorize/unauthorizeUsers", //角色对用户取消授权
    bodyAuthorizeUsers: "/data-finance/security/authorize/authorizeUsers" //角色对用户授权
  }
);
