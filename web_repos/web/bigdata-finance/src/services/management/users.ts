/*
 * @Author: your name
 * @Date: 2021-01-07 17:52:26
 * @LastEditTime: 2021-01-07 17:53:07
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /xmember/src/services/common/index.js
 */

import wrapApi from "@/utils/request/wrapApis";

export default wrapApi(
  {
    queryUserList: "data-finance/security/user/list", // 用户列表。支持模糊搜索 -
    queryResetPassword: "/data-finance/security/user/resetPassword", //重置密码 -
    querySearch: "/data-finance/wechat/remote/search", //搜索企微用户 -
    queryRowAuths: "/data-finance/security/authorize/rowAuths", //获取用户的行权限
    queryUserAuthorizedResources: "/data-finance/security/authorize/userAuthorizedResources", // 用户拥有的资源列表
    queryUserUnauthorizedRoles: "/data-finance/security/authorize/userUnauthorizedRoles", //获取用户未授权的角色列表
    // 用户手机号解密
    decryptPhone: "/data-finance/security/user/decryptPhone"
  },
  {
    bodyUserAdd: "data-finance/security/user/add", // 添加用户 -
    bodyUserEdit: "data-finance/security/user/edit", // 更新用户 -
    // 更新手机号 -
    modifyPhone: "/data-finance/security/user/modifyPhone"
  }
);
