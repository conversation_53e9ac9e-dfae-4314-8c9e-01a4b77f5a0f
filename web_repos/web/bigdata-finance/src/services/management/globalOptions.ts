import wrapApi from "@/utils/request/wrapApis";

export default wrapApi(
  {
    // getTypeList-查询类型列表
    getTypeList: "/data-finance/system/common/getTypeList",
    // getById-查询单条记录
    getById: "/data-finance/system/common/getById",
    // 根据类型获取选项
    getOptionByType: "/data-finance/system/common/getOptionByType"
  },
  {
    // edit-编辑选项
    saveOrUpdateDimOption: "/data-finance/system/common/saveOrUpdateDimOption",
    // del-删除选项
    delDimOption: "/data-finance/system/common/delDimOption",
    // 获取系统-公共-枚举选项维度-分页数据
    getList: "/data-finance/system/common/getList"
  }
);
