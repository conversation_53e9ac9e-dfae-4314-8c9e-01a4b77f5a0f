import wrapApi from "@/utils/request/wrapApis";

export default wrapApi(
  {},
  {
    // 通过excel导入加购商品成本
    addLiveBuyItemCostFromExcel: "/data-finance/profit/liveBuyItemCost/addLiveBuyItemCostFromExcel",
    // 合并sku-通过excel导入合并sku信息
    addLiveBuyItemMappingFromExcel: "/data-finance/profit/liveBuyItemCost/addLiveBuyItemMappingFromExcel",
    // delete-删除加购商品成本记录
    delete: "/data-finance/profit/liveBuyItemCost/delete",
    // 合并sku-获取系统sku列表
    downloadLiveBuyItemSkuList: "/data-finance/profit/liveBuyItemCost/downloadLiveBuyItemSkuList",
    // get-获取加购商品成本详情
    getLiveBuyItemCost: "/data-finance/profit/liveBuyItemCost/getLiveBuyItemCost",
    // 获取财务-直播-加购商品成本表-分页数据
    downloadFinanceShortVideoRecord: "/data-finance/profit/liveBuyItemCost/getLiveBuyItemCostPage",
    // 根据主播ID,直播日期,商品ID获取加购商品成本概要信息
    getLiveBuyItemCostBaseInfo: "/data-finance/profit/liveBuyItemCost/getLiveBuyItemCostBaseInfo",
    // edit-加购商品成本
    saveOrUpdateLiveBuyItemCost: "/data-finance/profit/liveBuyItemCost/saveOrUpdateLiveBuyItemCost",
    // 根据主播ID,直播日期,商品ID获取-sku列表信息
    getLiveBuyItemSkuList: "/data-finance/profit/liveBuyItemCost/getLiveBuyItemSkuList"
  }
);
