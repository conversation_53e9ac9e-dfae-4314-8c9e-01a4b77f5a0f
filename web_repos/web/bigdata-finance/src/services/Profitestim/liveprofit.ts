import wrapApi from "@/utils/request/wrapApis";

export default wrapApi(
  {},
  {
    // 通过excel导入直播利润预估
    addLiveProfitFromExcel: "/data-finance/profit/liveRecord/addLiveProfitFromExcel",
    // 下载获取直播利润预估表--页面所有数据(包含分页)
    downloadFinanceLiveRecord: "/data-finance/profit/liveRecord/downloadFinanceLiveRecord",
    // 获取直播利润预估表-分页数据
    getFinanceLiveRecordPage: "/data-finance/profit/liveRecord/getFinanceLiveRecordPage",
    // edit-直播利润预估表
    saveOrUpdateLiveProfit: "/data-finance/profit/liveRecord/saveOrUpdateLiveProfit",
    // 获取直播利润预估表-汇总行
    getFinanceLiveRecordTotal: "/data-finance/profit/liveRecord/getFinanceLiveRecordTotal",
    // 下载--对账商品导出
    exportStatementGoods: "/data-finance/profit/liveRecord/exportStatementGoods",

    // 下载获取实际直播利润预估表--页面所有数据(包含分页)
    downloadLiveItemActualForecast: "/data-finance/profit/liveRecord/downloadLiveItemActualForecast",
    // 获取实际直播利润预估表-分页数据
    getLiveActualForecastPage: "/data-finance/profit/liveRecord/getLiveActualForecastPage",
    // 获取实际直播利润预估表 汇总
    getFinanceLiveItemActualForecastTotal: "/data-finance/profit/liveRecord/getFinanceLiveItemActualForecastTotal"
  }
);
