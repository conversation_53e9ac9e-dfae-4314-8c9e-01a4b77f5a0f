import wrapApi from "@/utils/request/wrapApis";

export default wrapApi(
  {},
  {
    // 下载获取实际直播利润预估表--页面所有数据(包含分页)
    downloadFinanceShortVideoItemActualForecast: "/data-finance/profit/shortVideoRecord/downloadFinanceShortVideoItemActualForecast",
    //  获取实际直播利润预估表
    getFinanceShortVideoItemActualForecastPage: "/data-finance/profit/shortVideoRecord/getFinanceShortVideoItemActualForecastPage",
    //  获取实际直播利润预估表 汇总
    getFinanceShortVideoItemActualForecastTotal: "/data-finance/profit/shortVideoRecord/getFinanceShortVideoItemActualForecastTotal",
    // 通过excel导入短视频利润预估
    addShortVideoProfitFromExcel: "/data-finance/profit/shortVideoRecord/addShortVideoProfitFromExcel",
    // 获取财务--短视频记录-分页数据
    getFinanceShortVideoRecordPage: "/data-finance/profit/shortVideoRecord/getFinanceShortVideoRecordPage",
    // 下载获取财务--短视频记录--页面所有数据(包含分页)
    downloadFinanceShortVideoRecord: "/data-finance/profit/shortVideoRecord/downloadFinanceShortVideoRecord",
    // 获取财务--短视频记录-汇总行
    getFinanceShortVideoRecordTotal: "/data-finance/profit/shortVideoRecord/getFinanceShortVideoRecordTotal",
    // edit-短视频利润预估表
    saveOrUpdateShortVideoProfit: "/data-finance/profit/shortVideoRecord/saveOrUpdateShortVideoProfit"
  }
);
