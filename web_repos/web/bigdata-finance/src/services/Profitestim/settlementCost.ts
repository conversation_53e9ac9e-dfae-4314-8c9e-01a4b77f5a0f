import wrapApi from "@/utils/request/wrapApis";

export default wrapApi(
  {
    // getOrderType-获取订单类型
    getOrderType: "/data-finance/profit/liveSettleCostNumber/getOrderType"
  },
  {
    // 获取查询列表
    getList: "/data-finance/profit/liveSettleCostNumber/getLiveSettleCostNumberPage",

    // 获取查询列表
    downloadFile: "/data-finance/profit/liveSettleCostNumber/downloadLiveSettleCostNumberPage"
  }
);
