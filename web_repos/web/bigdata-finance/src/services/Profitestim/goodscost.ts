import wrapApi from "@/utils/request/wrapApis";

export default wrapApi(
  {},
  {
    // 合并sku-通过excel导入合并sku信息
    addShortVideoBuyItemMappingFromExcel: "/data-finance/profit/shortVideoBuyItemCost/addShortVideoBuyItemMappingFromExcel",
    // delete-删除加购商品成本记录
    delete: "/data-finance/profit/shortVideoBuyItemCost/delete",
    // 合并sku-获取系统sku列表
    downloadShortVideoBuyItemSkuList: "/data-finance/profit/shortVideoBuyItemCost/downloadShortVideoBuyItemSkuList",
    // get-获取加购商品成本详情
    getShortVideoBuyItemCost: "/data-finance/profit/shortVideoBuyItemCost/getShortVideoBuyItemCost",
    // 获取财务-短视频-加购商品成本表-分页数据
    getShortVideoBuyItemCostPage: "/data-finance/profit/shortVideoBuyItemCost/getShortVideoBuyItemCostPage",
    // 根据主播ID,直播日期,商品ID获取加购商品成本概要信息
    getVideoBuyItemCostBaseInfo: "/data-finance/profit/shortVideoBuyItemCost/getVideoBuyItemCostBaseInfo",
    // 根据主播ID,直播日期,商品ID获取-sku列表信息
    getVideoBuyItemSkuList: "/data-finance/profit/shortVideoBuyItemCost/getVideoBuyItemSkuList",
    // edit-加购商品成本
    saveOrUpdateShortVideoBuyItemCost: "/data-finance/profit/shortVideoBuyItemCost/saveOrUpdateShortVideoBuyItemCost"
  }
);
