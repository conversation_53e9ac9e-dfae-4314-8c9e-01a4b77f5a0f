import wrapApi from "@/utils/request/wrapApis";

export default wrapApi(
  {},
  {
    // 商品直播机制预处理
    listLiveItemInfos: "/data-finance/profit/livePreprocessing/getLiveMechanismPreprocessPage",
    // 下载--合并直播加购商品Sku集合
    downloadMergeLiveBuyItemSkuList: "/data-finance/profit/livePreprocessing/downloadMergeLiveBuyItemSkuList",
    // 下载--商品直播机制预处理表
    downloadSPUList: "/data-finance/profit/livePreprocessing/downloadLiveMechanismPreprocessPage"
  }
);
