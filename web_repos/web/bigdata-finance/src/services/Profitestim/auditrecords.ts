import wrapApi from "@/utils/request/wrapApis";

export default wrapApi(
  {},
  {
    // 审核记录
    // 利润预估审核 批量审批
    batchAudit: "/data-finance/profit/audit/batchAudit",
    // 下载获取财务--利润预估审核记录--页面所有数据(包含分页)
    downloadFinanceProfitAuditRecord: "/data-finance/profit/audit/downloadFinanceProfitAuditRecord",
    // 获取财务--利润预估审核记录-分页数据
    getFinanceProfitAuditRecordPage: "/data-finance/profit/audit/getFinanceProfitAuditRecordPage"
  }
);
