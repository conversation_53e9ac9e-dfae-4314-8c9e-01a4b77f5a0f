import wrapApi from "@/utils/request/wrapApis";

export default wrapApi(
  {},
  {
    // 下载(售后主题分析)-数据(包含分页)
    downloadJstRefund: "/data-finance/data/center/jstRefund/downloadJstRefund",
    // 获取(售后主题分析)-分页数据
    getJstRefundPage: "/data-finance/data/center/jstRefund/getJstRefundPage",
    // 获取-收货仓库
    getConfirmWarehouseList: "/data-finance/data/center/jstRefund/getConfirmWarehouseList"
  }
);
