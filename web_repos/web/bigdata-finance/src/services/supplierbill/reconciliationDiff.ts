import wrapApi from "@/utils/request/wrapApis";

export default wrapApi(
  {},
  {
    // 差异明细
    // 确认差异对账账期
    confirmDiffReconcilePeriod: "/data-finance/supplier/statement/dailyDiff/confirmDiffReconcilePeriod",
    // 下载-每日对账--差异明细--页面所有数据(包含分页)
    downloadStatementDailyDiff: "/data-finance/supplier/statement/dailyDiff/downloadStatementDailyDiff",
    // 获取--确认差异对账商品集合
    getConfirmDiffReconcileItemList: "/data-finance/supplier/statement/dailyDiff/getConfirmDiffReconcileItemList",
    // 获取财务-供应商-每日对账 差异明细-分页数据
    getStatementDailyDiffPage: "/data-finance/supplier/statement/dailyDiff/getStatementDailyDiffPage"
  }
);
