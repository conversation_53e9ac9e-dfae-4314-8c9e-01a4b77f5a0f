import wrapApi from "@/utils/request/wrapApis";

export default wrapApi(
  {
    // 排班历史 列表
    historyPage: "/data-finance/supplier/statement/reconcile/schedule/history/page",
    // 排班历史 导出
    historyPageDownload: "/data-finance/supplier/statement/reconcile/schedule/history/page-download",
    // 获取财务-供应商-对账单-排班-财务对账人列表
    getFinanceReconciles: "/data-finance/supplier/statement/schedule/getFinanceReconciles"
  },
  {
    // delete-删除对账SKU记录
    deleteSupplierScheduleSku: "/data-finance/supplier/statement/schedule/sku/deleteSupplierScheduleSku",
    // 下载获取财务-供应商-对账单-排班-对账SKU--页面所有数据(包含分页)
    downloadSupplierScheduleSku: "/data-finance/supplier/statement/schedule/sku/downloadSupplierScheduleSku",
    //  获取财务-供应商-对账单-排班-对账SKU-差异列表-分页数据
    getDiffSupplierScheduleSkuPage: "/data-finance/supplier/statement/schedule/sku/getDiffSupplierScheduleSkuPage",
    // 根据基础查询条件 获取加购商品成本概要信息
    getSupplierScheduleBaseInfoVO: "/data-finance/supplier/statement/schedule/sku/getSupplierScheduleBaseInfoVO",
    // 根据主播ID,直播日期,商品ID获取-sku列表信息
    getScheduleItemSkuList: "/data-finance/supplier/statement/schedule/sku/getScheduleItemSkuList",
    // get-获取对账SKU详情
    getSupplierScheduleSku: "/data-finance/supplier/statement/schedule/sku/getSupplierScheduleSku",
    // 获取财务-供应商-对账单-排班-对账SKU-分页数据
    getSupplierScheduleSkuPage: "/data-finance/supplier/statement/schedule/sku/getSupplierScheduleSkuPage",
    // edit-对账SKU
    saveOrUpdateSupplierScheduleSku: "/data-finance/supplier/statement/schedule/sku/saveOrUpdateSupplierScheduleSku",

    // 通过excel导入供应商-对账单-排班--校验
    checkStatementScheduleFromExcel: "/data-finance/supplier/statement/schedule/checkStatementScheduleFromExcel",
    // 通过excel导入供应商-对账单-排班（1001043）
    addStatementScheduleFromExcel: "/data-finance/supplier/statement/schedule/addStatementScheduleFromExcel",
    // 删除对账排班
    delStatementSchedule: "/data-finance/supplier/statement/schedule/delStatementSchedule",
    // 下载-财务-供应商-对账单-排班--页面所有数据(包含分页)
    downloadFinanceSupplierStatementSchedule: "/data-finance/supplier/statement/schedule/downloadFinanceSupplierStatementSchedule",
    //  获取财务-供应商-对账单-排班-分页数据
    getFinanceSupplierStatementSchedulePage: "/data-finance/supplier/statement/schedule/getFinanceSupplierStatementSchedulePage",
    // 获取财务-供应商-对账单-排班-快照-分页数据
    getFinanceSupplierStatementScheduleSnapshotPage: "/data-finance/supplier/statement/schedule/getFinanceSupplierStatementScheduleSnapshotPage",
    // 保存和修改对账排班
    saveOrUpdateStatementSchedule: "/data-finance/supplier/statement/schedule/saveOrUpdateStatementSchedule",

    // 排班历史 删除
    historyDelete: "/data-finance/supplier/statement/reconcile/schedule/history/delete",
    // 排班历史 更新
    historyUpdate: "/data-finance/supplier/statement/reconcile/schedule/history/update",
    // 排班历史 上传
    historyUpload: "/data-finance/supplier/statement/reconcile/schedule/history/upload"
  }
);
