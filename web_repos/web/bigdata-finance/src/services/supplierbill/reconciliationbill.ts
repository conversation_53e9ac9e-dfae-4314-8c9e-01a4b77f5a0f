import wrapApi from "@/utils/request/wrapApis";

export default wrapApi(
  {
    // 列表
    historyPage: "/data-finance/supplier/statement/reconcile/history/page",
    // 导出
    historyDownload: "/data-finance/supplier/statement/reconcile/history/page-download",
    //  列表-汇总
    historySummary: "/data-finance/supplier/statement/reconcile/history/page-summary",

    // 多供应商 列表
    supplierPage: "/data-finance/supplier/statement/reconcile/supplier/page",
    // 多供应商 导出
    supplierPageDownload: "/data-finance/supplier/statement/reconcile/supplier/page-download",
    // 多供应商 汇总
    supplierPageSummary: "/data-finance/supplier/statement/reconcile/supplier/page-summary"
  },
  {
    // 通过excel导入供应商-对账商品
    addSupplierReconcileItemFromExcel: "/data-finance/supplier/statement/item/addSupplierReconcileItemFromExcel",
    // 下载获取财务-供应商-对账-商品对账明细--页面所有数据(包含分页)
    downloadStatementItemReconcileDetail: "/data-finance/supplier/statement/item/downloadStatementItemReconcileDetail",
    // 获取财务-供应商-对账-商品对账明细-分页数据
    getStatementItemReconcileDetailPage: "/data-finance/supplier/statement/item/getStatementItemReconcileDetailPage",
    // 编辑保存-供应商-商家商品对账明细
    saveOrUpdateStatementItemReconcile: "/data-finance/supplier/statement/item/saveOrUpdateStatementItemReconcile",
    // 获取财务-供应商-对账-商品对账明细-统计
    getStatementItemReconcileDetailTotal: "/data-finance/supplier/statement/item/getStatementItemReconcileDetailTotal",

    //  删除草稿对账单
    deleteDraftSupplierReconcile: "/data-finance/supplier/statement/reconcile/deleteDraftSupplierReconcile",
    // 批量确认对账单
    doConfirmSupplierReconcile: "data-finance/supplier/statement/reconcile/doConfirmSupplierReconcile",
    // 批量取消确认对账单
    doUnConfirmSupplierReconcile: "/data-finance/supplier/statement/reconcile/doUnConfirmSupplierReconcile",

    // 通过excel导入供应商-对账单-商家对账（1001050）
    addStatementReconcileFromExcel: "/data-finance/supplier/statement/reconcile/addStatementReconcileFromExcel",
    // 下载供应商-对账单
    downloadSupplierReconcile: "/data-finance/supplier/statement/reconcile/downloadSupplierReconcile",
    // 下载获取财务-供应商-对账-商家对账--页面所有数据(包含分页)
    downloadSupplierStatementReconcile: "/data-finance/supplier/statement/reconcile/downloadSupplierStatementReconcile",
    // 获取财务-供应商-对账-商家对账-分页数据
    getSupplierStatementReconcilePage: "/data-finance/supplier/statement/reconcile/getSupplierStatementReconcilePage",
    // 编辑保存-供应商-商家对账
    saveOrUpdateSupplierStatementReconcile: "/data-finance/supplier/statement/reconcile/saveOrUpdateSupplierStatementReconcile",
    // 供应商-商家对账-进行红冲
    toProcessRedPunch: "/data-finance/supplier/statement/reconcile/toProcessRedPunch",
    // 获取财务-供应商-对账-商家对账-获取合计
    getSupplierStatementReconcileTotal: "/data-finance/supplier/statement/reconcile/getSupplierStatementReconcileTotal",
    // 获取财务-供应商-对账-商品对账明细
    getSupplierReconcileItemDetail: "/data-finance/supplier/statement/item/getSupplierReconcileItemDetail",
    // 修改供应商名称
    doChangeSupplierName: "/data-finance/supplier/statement/reconcile/doChangeSupplierName",

    // 删除
    historyDelete: "/data-finance/supplier/statement/reconcile/history/delete",
    // 更新
    historyUpdate: "/data-finance/supplier/statement/reconcile/history/update",
    // 上传
    historyUpload: "/data-finance/supplier/statement/reconcile/history/upload",

    // 多供应商 删除
    supplierDelete: "/data-finance/supplier/statement/reconcile/supplier/delete",
    // 多供应商 更新
    supplierUpdate: "/data-finance/supplier/statement/reconcile/supplier/update",
    // 多供应商 上传
    supplierUpload: "/data-finance/supplier/statement/reconcile/supplier/upload",

    // 获取财务-供应商-对账-商品对账明细-日志-分页数据
    getStatementReconcileItemDetailLog: "/data-finance/supplier/statement/item/getStatementReconcileItemDetailLog"
  }
);
