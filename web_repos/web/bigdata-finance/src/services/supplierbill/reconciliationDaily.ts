import wrapApi from "@/utils/request/wrapApis";

export default wrapApi(
  {},
  {
    // 生成对账单
    // 商品信息核对
    checkSupplierItemInfo: "/data-finance/supplier/statement/generate/checkSupplierItemInfo",
    // 模式检查
    checkSupplierItemPattern: "/data-finance/supplier/statement/generate/checkSupplierItemPattern",
    // 账期检查
    checkSupplierPeriod: "/data-finance/supplier/statement/generate/checkSupplierPeriod",
    // 获取待生成供应商名称集合
    getGenerateSupplierNameList: "/data-finance/supplier/statement/generate/getGenerateSupplierNameList",
    // 获取供应商账单预览
    getSupplierBillPreview: "/data-finance/supplier/statement/generate/getSupplierBillPreview",
    // 修改对账单商品信息
    modifyStatementScheduleItemOrSkuInfo: "/data-finance/supplier/statement/generate/modifyStatementScheduleItemOrSkuInfo",
    // 修改对账单商品模式
    modifyStatementScheduleItemOrSkuPattern: "/data-finance/supplier/statement/generate/modifyStatementScheduleItemOrSkuPattern",
    //  确认生成草稿
    toFinishConfirm: "/data-finance/supplier/statement/generate/toFinishConfirm",
    // 确认账期第一步确认
    toFirstStepConfirm: "/data-finance/supplier/statement/generate/toFirstStepConfirm",
    // 确认账期第二步确认
    toSecondStepConfirm: "/data-finance/supplier/statement/generate/toSecondStepConfirm",

    // 确认对账账期
    confirmReconcilePeriod: "/data-finance/supplier/statement/dailyDetail/confirmReconcilePeriod",
    // 下载获取财务-供应商-每日对账明细--页面所有数据(包含分页)
    downloadStatementDailyDetail: "/data-finance/supplier/statement/dailyDetail/downloadStatementDailyDetail",
    // 下载获取财务-供应商-每日对账明细-已对账--页面所有数据(包含分页)
    downloadStatementDailyDetailHaveReconcile: "/data-finance/supplier/statement/dailyDetail/downloadStatementDailyDetailHaveReconcile",
    // 获取--确认对账商品集合
    getConfirmReconcileItemList: "/data-finance/supplier/statement/dailyDetail/getConfirmReconcileItemList",
    // 获取财务-供应商-每日对账明细-已对账-分页数据
    getStatementDailyDetailHaveReconcilePage: "/data-finance/supplier/statement/dailyDetail/getStatementDailyDetailHaveReconcilePage",
    // 获取财务-供应商-每日对账明细-分页数据
    getStatementDailyDetailPage: "/data-finance/supplier/statement/dailyDetail/getStatementDailyDetailPage",

    // 未对账明细 更新时间
    getMaxProcTime: "/data-finance/supplier/statement/dailyDetail/getMaxProcTime"
  }
);
