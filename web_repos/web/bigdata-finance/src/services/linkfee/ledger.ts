import wrapApi from "@/utils/request/wrapApis";

export default wrapApi(
  {
    // 链接费台账--获取结算月份
    getObtainSettleMonth: "/data-finance/accounting/liveItemLinkfee/getObtainSettleMonth"
  },
  {
    // 链接费台账--批量红冲
    batchRedPunch: "/data-finance/accounting/liveItemLinkfee/batchRedPunch",
    // 链接费台账--批量取消确认结算
    batchCancelConfirmSettle: "/data-finance/accounting/liveItemLinkfee/batchCancelConfirmSettle",
    // 链接费台账--批量修改
    batchSaveOrUpdateLiveItemLinkfeeOperate: "/data-finance/accounting/liveItemLinkfee/batchSaveOrUpdateLiveItemLinkfeeOperate",
    // 链接费台账--确认结算月份数据
    doConfirmSettleMonthData: "/data-finance/accounting/liveItemLinkfee/doConfirmSettleMonthData",
    // 链接费台账--获取结算月份待确认数据
    getObtainSettleMonthConfirmData: "/data-finance/accounting/liveItemLinkfee/getObtainSettleMonthConfirmData",
    // 下载获取财务-直播-链接费台账--页面所有数据(包含分页)
    downloadLiveItemLinkfee: "/data-finance/accounting/liveItemLinkfee/downloadLiveItemLinkfee",
    //  获取财务-直播-链接费台账-分页数据
    getLiveItemLinkfeePage: "/data-finance/accounting/liveItemLinkfee/getLiveItemLinkfeePage",
    // edit-链接费台账
    saveOrUpdateLiveItemLinkfeeOperate: "/data-finance/accounting/liveItemLinkfee/saveOrUpdateLiveItemLinkfeeOperate",
    // 获取财务-直播-链接费台账-分页数据-汇总
    getLiveItemLinkfeePageTotal: "/data-finance/accounting/liveItemLinkfee/getLiveItemLinkfeePageTotal"
  }
);
