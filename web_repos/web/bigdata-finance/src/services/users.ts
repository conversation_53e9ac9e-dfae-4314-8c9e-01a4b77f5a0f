//@ts-ignore
// import { request } from "@umijs/max";
import wrapApi from "@/utils/request/wrapApis";

export default wrapApi(
  {
    // 获取用户授权的角色列表
    getUserRoleList: "/data-finance/security/authorize/getUserRoleList",
    // 菜单列表
    getUserInfo: "/data-finance/security/authenticate/userInfo",
    // 退出登陆
    loginout: "/data-finance/security/authenticate/logout",
    // 获取验证码
    validateCode: "/data-finance/security/authenticate/validateCode"
  },
  {
    // 登录
    getLogin: "/data-finance/security/authenticate/login",
    // 更新用户密码
    modifyPassword: "/data-finance/security/user/modifyPassword"
  }
);
