import wrapApi from "@/utils/request/wrapApis";

export default wrapApi(
  {},
  {
    // 通过excel导入京东--POP商家数据
    addJdPopMerchantFromExcel: "/data-finance/data/center/jd/addJdPopMerchantFromExcel",
    // 下载获取数据中心--京东--POP商家(包含分页)
    downloadJdPopMerchant: "/data-finance/data/center/jd/downloadJdPopMerchant",
    // 数据中心--京东--POP商家-分页数据
    getJdPopMerchantPage: "/data-finance/data/center/jd/getJdPopMerchantPage",

    // 通过excel导入京东--平台补贴数据
    addJdPlatformSubsidyFromExcel: "/data-finance/data/center/jd/addJdPlatformSubsidyFromExcel",
    // 下载获取数据中心--京东--平台补贴(包含分页)
    downloadJdPlatformSubsidy: "/data-finance/data/center/jd/downloadJdPlatformSubsidy",
    // 数据中心--京东--平台补贴-分页数据
    getJdPlatformSubsidyPage: "/data-finance/data/center/jd/getJdPlatformSubsidyPage"
  }
);
