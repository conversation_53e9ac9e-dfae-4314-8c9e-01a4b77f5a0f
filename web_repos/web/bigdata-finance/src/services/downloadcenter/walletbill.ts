import wrapApi from "@/utils/request/wrapApis";

export default wrapApi(
  {},
  {
    // 分销
    // 下载(钱包账单-分销)-数据(包含分页)
    downloadCpsBillFlow: "/data-finance/data/center/cpsSettleFlow/downloadCpsBillFlow",
    // 获取(钱包账单-分销)-分页数据
    getCpsBillFlowPage: "/data-finance/data/center/cpsSettleFlow/getCpsBillFlowPage",

    // 货款
    // 下载(钱包账单-货款)-数据(包含分页)
    downloadShopBillFlow: "/data-finance/data/center/shopSettleFlow/downloadShopBillFlow",
    // 获取(钱包账单-货款)-分页数据
    getShopBillFlowPage: "/data-finance/data/center/shopSettleFlow/getShopBillFlowPage"
  }
);
