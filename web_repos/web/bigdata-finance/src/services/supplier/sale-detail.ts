import wrapApi from "@/utils/request/wrapApis";

export default wrapApi(
  {},
  {
    // 通过excel导入供应商对账-交易明细
    addSupplierTranDetailFromExcel: "/data-finance/supplier/transaction/addSupplierTranDetailFromExcel",
    // 下载获取供应商对账-交易明细--页面所有数据(包含分页)
    downloadSupplierTranDetailUpload: "/data-finance/supplier/transaction/downloadSupplierTranDetailUpload",
    // 获取供应商对账-交易明细-分页数据
    getSupplierTranDetailUploadPage: "/data-finance/supplier/transaction/getSupplierTranDetailUploadPage"
  }
);
