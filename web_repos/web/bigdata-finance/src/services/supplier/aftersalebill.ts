import wrapApi from "@/utils/request/wrapApis";

export default wrapApi(
  {},
  {
    // 通过excel导入供应商对账-超售后账单
    addAfterSalesFromExcel: "/data-finance/supplier/afterSales/addAfterSalesFromExcel",
    // 下载获取供应商对账-超售后账单--页面所有数据(包含分页)
    downloadAfterSales: "/data-finance/supplier/afterSales/downloadAfterSales",
    // 获取供应商对账-超售后账单-分页数据
    getFinanceSupplierAfterSalesUploadPage: "/data-finance/supplier/afterSales/getFinanceSupplierAfterSalesUploadPage"
  }
);
