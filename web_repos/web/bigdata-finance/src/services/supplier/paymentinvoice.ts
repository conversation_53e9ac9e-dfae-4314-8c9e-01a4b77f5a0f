import wrapApi from "@/utils/request/wrapApis";

export default wrapApi(
  {},
  {
    // 通过excel导入供应商对账-货款账单
    addSupplierPaymentFromExcel: "/data-finance/supplier/payment/addSupplierPaymentFromExcel",
    // 下载获取供应商对账-货款账单--页面所有数据(包含分页)
    downloadSupplierPaymentUpload: "/data-finance/supplier/payment/downloadSupplierPaymentUpload",
    // 获取供应商对账-货款账单-分页数据
    getFinanceSupplierPaymentStatementUploadPage: "/data-finance/supplier/payment/getFinanceSupplierPaymentStatementUploadPage"
  }
);
