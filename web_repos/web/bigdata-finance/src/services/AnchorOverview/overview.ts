import wrapApi from "@/utils/request/wrapApis";

export default wrapApi(
  {},
  {
    // 主播结算-主播结算概览-预估结算概览
    estimatedSettleProfits: "/data-finance/anchor/settle/overview/estimated-settle-profits",
    // 主播结算-主播结算概览-预估结算概览-下载
    estimatedsettleprofitsdownload: "/data-finance/anchor/settle/overview/estimated-settle-profits-download",
    // 主播结算-主播结算概览-指标卡
    indicators: "/data-finance/anchor/settle/overview/indicators",
    // 主播结算-主播结算概览-月度结算趋势
    monthlyTrends: "/data-finance/anchor/settle/overview/monthly-trends",
    // 主播结算-主播结算概览-主播榜单
    ranks: "/data-finance/anchor/settle/overview/ranks",
    // 主播结算-主播结算概览-结算利润分析
    settleProfits: "/data-finance/anchor/settle/overview/settle-profits",
    // 主播结算-主播结算概览-结算利润分析-下载
    settleprofitsdwonload: "/data-finance/anchor/settle/overview/settle-profits-dwonload",
    // 主播结算-主播结算概览-结算利润分析-更新
    settleprofitsupdate: "/data-finance/anchor/settle/overview/settle-profits-update"
  }
);
