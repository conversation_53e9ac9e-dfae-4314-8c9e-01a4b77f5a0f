import { TableColumnProps } from "antd";

export {};
declare global {
  type Nullable<T> = T | null;
  type Recordable<T = any> = Record<string, T>;
  type ReadonlyRecordable<T = any> = {
    readonly [key: string]: T;
  };

  type DeepPartial<T> = {
    [P in keyof T]?: DeepPartial<T[P]>;
  };

  type TimeoutHandle = ReturnType<typeof setTimeout>;
  type IntervalHandle = ReturnType<typeof setInterval>;
  type IResponse = {
    entry: any;
    message: string;
    responseCode: string;
    status: boolean;
    totalRecordSize: number;
    [key: string]: any;
  };
  // 千分位配置;
  interface MillennialOptions {
    // 使用的整数数字的最小数目。可能的值是从1到21，默认值是1。
    minimumIntegerDigits?: number;
    // 使用的小数位数的最小数目，可能的值是从 0 到 20。
    minimumFractionDigits?: number;
    // 使用的小数位数的最大数目。可能的值是从 0 到 20。
    maximumFractionDigits?: number;
    // 使用的有效数字的最小数目。可能的值是从1到21；默认值是1。
    minimumSignificantDigits?: number;
    // 使用的有效数字的最大数量。可能的值是从1到21；默认是 21。
    maximumSignificantDigits?: number;
  }
  type TableListItem = TableColumnProps & {
    // 是否在分组中显示
    showInGroup?: boolean;
    // 分组名称
    groupName?: string;
    summaryTooltip?: {
      // 除数 将数字转为 元
      divisor: number;
      // 千分位
      millennials?: boolean;
      // 千分位配置
      millennialsOptions?: MillennialOptions;
      // 格式化为元
      formatYuan?: boolean;
      // 默认元格式化，hover 时，展示千分位
      hoverMillennials?: boolean;
      // 默认展示 千分格式，hover 时，展示接口返回格式化
      hoverMillennialsRenderMillennials?: boolean;
    };
  };
  type IOptions = {
    label: string;
    value: any;
    [key: string]: any;
  };
}
